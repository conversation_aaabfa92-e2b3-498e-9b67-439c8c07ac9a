{"__meta": {"id": "X4768e9602849e93e2b87eeeca48d1718", "datetime": "2025-07-14 18:30:23", "utime": **********.071754, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517822.659998, "end": **********.071772, "duration": 0.4117741584777832, "duration_str": "412ms", "measures": [{"label": "Booting", "start": 1752517822.659998, "relative_start": 0, "end": **********.019477, "relative_end": **********.019477, "duration": 0.3594789505004883, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.019486, "relative_start": 0.35948801040649414, "end": **********.071774, "relative_end": 1.9073486328125e-06, "duration": 0.052288055419921875, "duration_str": "52.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00281, "accumulated_duration_str": "2.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.048314, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.836}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.059563, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.836, "width_percent": 20.996}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.065237, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.833, "width_percent": 13.167}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-264017638 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-264017638\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1401231728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1401231728\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1997441689 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997441689\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-672084443 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/goal</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517820079%7C42%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InMxODdDZ0s3RlJsM25mT3FKanJFR0E9PSIsInZhbHVlIjoiUUVKakNYNW9DbmZGNWxEd0tWek9vaGFHZlpQQy9rQ0VBbDI0UVcwK2w1WXp6RXVQeVFwU2ZkQlQ1Mzl3SHpsRnpjcWhkdHlGM3F6OXFXVm1LY2tqaU9sOGFjdXNtSmtxQ2YzdWlQSjZCTmpkS2syQ2Nza2MxanJzeDVnMllSUnNZcmUvUGdremhVaW54MitPY1cyd0V2MmdiNHhqZkRBMUpGbG52MmdmejFvMitOMnAwdXJLSnlVaExtZFQwR3R6R3RNMER4c3BCMlBrTWJyVjJKODEwMjFKQ1NRTy90NmpxaTRFeThuOVBOdjVWK2tjaDBoWktoeE1DclJXZytDMVcwbGk2aTAxR0ExanRGSnhsTk0yUFRuUTlVUjNXaHdTWDdxOXNvc0x4VU5SM0RTdkVCcmpFb3VtOE92cFdKVURiNUdpT2IrOWNIU0JsbWVwYnlXL2NlNUIwQXVIT1phZXFjL3hHeng0YVljNHd5QjRKVzJFempacGNTVG5BcHhob3M2dFJUeUdnTFlzUUdZcWNmTHB1VjNJM1N2V0pFZWFHVjNFb0Vrek1kaFZjTlU3YUVUNWg2QXZVc3E0MksyVXdSL2pUZkF5Q1llajlRcWlOaFdXZ1dYYWoydjFmL213SUJLUFZFNzBlSWdSMUJ6b0t3VVdNRXVKRU1MaElyamIiLCJtYWMiOiIyMDllMGFlYTIyYjYzODRiNTk0ZGYxYjc0NmI4YjE0M2QxMTNiYzA3NWFiMmU0YzRlMTcwZGI2MWVjODRjMjIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ildqbi91L01hK0VNbG9wdC9tNHFGckE9PSIsInZhbHVlIjoicHliSDVUS01HODQ0VjUybEt1WVQ5MHZjc2R1TUN5M1RKYTZHV0UyOExxakpOaFZ5VW9vY0VpaW9ZbWZqSjNvUE1GUGxESEx3TnZJUkV1a1B1bEJ0T2dOWUFhUVBobDdQaWh3SFYwN2tpMDVmZ0NDd015a1pPa3A5eVBZZHRqS3B1VXJKNWRFNDVqQUxQbkRZaDlWQVA4clYvcXhnY3ZnUzlhazVxVEFjYWtqdzEyK3VoS1ZlcE40SlJ6T1FLbm9TeTFVZnVKVEFIOWJ1UmEvTnIxZmdxYzlpRVFJeXlMQ0J1bngrei84eXByWEFLbFBiS0hnTy8rYjUxeHpHYzluYnJGOXpDT04xQ2hEYVpTZ29QTHdTWUtPNy95bTBETTQxeUZ2bk53QkdpME53d0RCU0hjYTVNNFI5aVRsbnJ5NTU2SHBzZXZXdnpSRWViREVSdEZMZFUza0l5aEc4Nll0U2lKaFBHdC9RdnlSNTdHWEI2WW83Tks3eXQ5TUxpNlZKSjNBamR0WDZlb2FpTUZKbEh4bXMyT2tITFltVWNoOTZZcHluTE5JUExUWGpsbHM2Qk5oZHFSblhiUi96d0I0aXJ6N0hxc3JZZFVBWHlwT0Y1VEpvUmIwZ2M2T2U4NWlqbVoyeVVzUUdQcFlGTnh0ei9tNkcydC8xQ1I4OS92LzgiLCJtYWMiOiJjNDUwMGI1MDdkZTQyZjIxZDNiOGEyNDVmZWIzMzJmY2JkZmVmMDg0YWQ1NTdkYzc5MWFjNGYxNGI4ODEwOGMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-672084443\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-243438513 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243438513\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-400609185 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:30:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imo2WmZtQ3h3S3N4dkhVY0s4cmZrWFE9PSIsInZhbHVlIjoidmdYR0NuL1c0NkQyc0RJTEY5QXo4ZHQ0NCtTT1VwTGNHSGJwQVdCdW1JaWtwdC9xOUE0OXE2ek9RTHhJMG9EbjRMTitaUFJIbFVNcnFaaGg0MmdBUStVWnpXclFyT3FEZXFJS0hhSWhlWlhhdmpQeTk0WFVQR2R4RVJXWEJlZTd6eVJ5UE1kTHh0VnFnSFJncGpJa1QyUWsraW1wOEk2dW0xR0Fyb0Fjbi9sL3A3aWhDbmJoQ3IxN2xMU0k5bVBMUnc1K0dORGE0RFZxcjVpSXJDdmpnN3ljajNQZG5mc0dmVmZoVHRTQktwbmdLQTVyYk9lVWYwUlBMcXpCRk8zUndFN2tTZlBKcGVzdW5kdG93SXAwM3ZuM3h1V3lZRzhaZ0VJMFV5eFk0TWg1UGVrYzh0djhsYWY1Tk9PZG1LVEgvem5NSWhJUWF0Y0JBZzRXeHk1OUZQanhDZ09uWDNaelZrUHJMcERaZXNjT3JLSmQ3bTJJTVRoOTFveFBrNFIwSjdJSnptK1cyVDREaVNVZTNaWVBmTHo5c0Z6V1UxUERHYTYvdjBRWCtRZS93ajRURjg3NlBCaEtHb3J6UURPc21SeVVFZkU5cFd4K3pXSVUwRUJWL0hvN1gwTzNZc2UzWGM5b1YrcnVPZGRFZCtlb0pLV3FmbUpveCtGbi9XVlAiLCJtYWMiOiIzZDA1ZjNkMjcyODJiMzQxMmY1ZmFkNTkzMmQ0NjhlZTFmMmQ5MGJlMWRkODllMDgzM2RiYjhkYWNhZmRjM2IyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhpQmZFdTdoUmRjamhlYnIrWUdyTmc9PSIsInZhbHVlIjoiMkxLOUtaVzNla1JXMFhNY3BqTjNBaVR3NG9lRXdneHR5RnlhcHFZYTcwbXlyZWhxNGdlMW9mU20zMUFEMk5ZejM4WVUxc25IMXZYenM0aHJpZ3NzTlNRcFlycVFoU0dOWWRsWHRTekUybXhEanZwcFNaUW9qalltV3NiMmc1QkJTZllvanJ4MXR2c2tqWmtaVFU1TEFQZlZpK25kbjgvc1dSZmhKOUdTbGo3WDJmRVl6L21iOGVMMDF4cnh2ZUNpV25SUmxld1duZktjTVJLdWVUWHJEZzZGQVpZSTFjRzFOSURKZ29VOC9NTUxBZXRXTWt4TDFLc3k3SktTYnpPN2ZnbkM0cWoyUEdvUFJjb2FQaVFkTTM1NHZoNlFQUTBSazdvYmZYZmxBTXVnU0NPTFZLdUpyT1FwaW5SU3FYV0MxNWNqc0kvRlRtRXgyT3BOK2RNRTc3RDZCeEl5b3BHR0doeGFGWXc2RkNzQW12dzhVeWdqWllraStKNEZTeElkeVk3c0JndmtmN0cxOUFSSVRqR3lUSHlTMDFwcmdBMjN0WS9JQXRLbXNaWTNYZ1J5TVFLcXdqa2ZCOXdJZEpsN1dHSG1EcFdPa291WEQ2cU9oWTVadmVmM0hKT1JwQ3gzdUkwcnQ4UXM3eG5lclIra1RsL1Z2ck8razREODV6UjgiLCJtYWMiOiI1MmYwNzIxNDYxMDBiNWNiMTY1NzlkNzZmOTdiMGRkNTMxYmFiZmQ4OGJlZjhlNTg4NmI0ODc4NGYzNzAxYzIwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imo2WmZtQ3h3S3N4dkhVY0s4cmZrWFE9PSIsInZhbHVlIjoidmdYR0NuL1c0NkQyc0RJTEY5QXo4ZHQ0NCtTT1VwTGNHSGJwQVdCdW1JaWtwdC9xOUE0OXE2ek9RTHhJMG9EbjRMTitaUFJIbFVNcnFaaGg0MmdBUStVWnpXclFyT3FEZXFJS0hhSWhlWlhhdmpQeTk0WFVQR2R4RVJXWEJlZTd6eVJ5UE1kTHh0VnFnSFJncGpJa1QyUWsraW1wOEk2dW0xR0Fyb0Fjbi9sL3A3aWhDbmJoQ3IxN2xMU0k5bVBMUnc1K0dORGE0RFZxcjVpSXJDdmpnN3ljajNQZG5mc0dmVmZoVHRTQktwbmdLQTVyYk9lVWYwUlBMcXpCRk8zUndFN2tTZlBKcGVzdW5kdG93SXAwM3ZuM3h1V3lZRzhaZ0VJMFV5eFk0TWg1UGVrYzh0djhsYWY1Tk9PZG1LVEgvem5NSWhJUWF0Y0JBZzRXeHk1OUZQanhDZ09uWDNaelZrUHJMcERaZXNjT3JLSmQ3bTJJTVRoOTFveFBrNFIwSjdJSnptK1cyVDREaVNVZTNaWVBmTHo5c0Z6V1UxUERHYTYvdjBRWCtRZS93ajRURjg3NlBCaEtHb3J6UURPc21SeVVFZkU5cFd4K3pXSVUwRUJWL0hvN1gwTzNZc2UzWGM5b1YrcnVPZGRFZCtlb0pLV3FmbUpveCtGbi9XVlAiLCJtYWMiOiIzZDA1ZjNkMjcyODJiMzQxMmY1ZmFkNTkzMmQ0NjhlZTFmMmQ5MGJlMWRkODllMDgzM2RiYjhkYWNhZmRjM2IyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhpQmZFdTdoUmRjamhlYnIrWUdyTmc9PSIsInZhbHVlIjoiMkxLOUtaVzNla1JXMFhNY3BqTjNBaVR3NG9lRXdneHR5RnlhcHFZYTcwbXlyZWhxNGdlMW9mU20zMUFEMk5ZejM4WVUxc25IMXZYenM0aHJpZ3NzTlNRcFlycVFoU0dOWWRsWHRTekUybXhEanZwcFNaUW9qalltV3NiMmc1QkJTZllvanJ4MXR2c2tqWmtaVFU1TEFQZlZpK25kbjgvc1dSZmhKOUdTbGo3WDJmRVl6L21iOGVMMDF4cnh2ZUNpV25SUmxld1duZktjTVJLdWVUWHJEZzZGQVpZSTFjRzFOSURKZ29VOC9NTUxBZXRXTWt4TDFLc3k3SktTYnpPN2ZnbkM0cWoyUEdvUFJjb2FQaVFkTTM1NHZoNlFQUTBSazdvYmZYZmxBTXVnU0NPTFZLdUpyT1FwaW5SU3FYV0MxNWNqc0kvRlRtRXgyT3BOK2RNRTc3RDZCeEl5b3BHR0doeGFGWXc2RkNzQW12dzhVeWdqWllraStKNEZTeElkeVk3c0JndmtmN0cxOUFSSVRqR3lUSHlTMDFwcmdBMjN0WS9JQXRLbXNaWTNYZ1J5TVFLcXdqa2ZCOXdJZEpsN1dHSG1EcFdPa291WEQ2cU9oWTVadmVmM0hKT1JwQ3gzdUkwcnQ4UXM3eG5lclIra1RsL1Z2ck8razREODV6UjgiLCJtYWMiOiI1MmYwNzIxNDYxMDBiNWNiMTY1NzlkNzZmOTdiMGRkNTMxYmFiZmQ4OGJlZjhlNTg4NmI0ODc4NGYzNzAxYzIwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400609185\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-252516929 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252516929\", {\"maxDepth\":0})</script>\n"}}