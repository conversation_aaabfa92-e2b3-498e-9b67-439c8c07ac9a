{"__meta": {"id": "X0004d4bb910f701daf72eafdd17b9b54", "datetime": "2025-07-23 18:22:50", "utime": **********.235895, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.811281, "end": **********.235909, "duration": 0.*****************, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.811281, "relative_start": 0, "end": **********.176536, "relative_end": **********.176536, "duration": 0.*****************, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.176544, "relative_start": 0.****************, "end": **********.23591, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "59.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.208693, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.176}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.219849, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.176, "width_percent": 15.016}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.227777, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 80.192, "width_percent": 19.808}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C**********181%7C4%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZieEszbXdPSGUwSFh1WmtLb3dWN2c9PSIsInZhbHVlIjoiNDAzN1M3cnp3M1V2b1lXSzQxSHdmNkZzWGNhbTlQRlRqZ0Q4RWdadmJ6a3h5TUw3ZENUSVZHR0luY0QxWlNmNjhtSVlYdVZpUFdxTW5zNVBUUGxibDJtS2R5NEUyYlJ1VDdKL1BxQU9vam5JMG9td2ZkNC9EUlVBYmNNa2wvTlh1YXlGcHh0SFFJUFJtWmVPYzVNejhHNFFGa1ZvR2ltN1FjUnUwdFk0V2c5ZkV6Sms2Yk1FZG45bDlOTkp5d1NYM2lmbzMrbUo4NERHOEk3YlNVSXhlN3djTGdlN1pVOHpTc1VNQ3VRZ0xCQ3dCUXVnR3JadVhPaTdzRForWWFZR2FEVTg3ckxKTXFTcTJHWGJZSmNjNnpFN2VhNEttTUZXc0pMeVhuR29zU3g4YnlzS1F1c2FNcFhZaXVYQkFaMzBwMXF3KzR5VVFQY2pORnNiQ1pSKy9kenNYQUkxRzM2VFBwM0F4cjhZSm1pRjRrcFhwRnpvTktwY29yb1cxMjlkZ2tHbFVXWWVlZ1I2WVdJZkRvNGlpeHRkZVVCUnVCTEtYUFdnL0VScGFSK01yRVNUTFRvVEVaSUFsL1BPRWo3TWVrOWI4QkZRZHZkRkRqLzdsaERXRTRYQTRpZWNlUGtNb0R6ZVBSZEVnZkpuT0dYbzRsZTM5WFYrRjN2NFE2bU0iLCJtYWMiOiJiZmE5NjE5YjM3OGQyYzY5NmMyMzQ4MTFiNmJlYTk5NGQzZTc3NjQ2NmZmZjFjZTExNDI1YzEyNjQ4MDgyNmY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNuaytkL0NzaU1zNDAxUWpBTFVFMHc9PSIsInZhbHVlIjoiMnVENTBEZ2Vnc1M0cjdzVFVWV2p2L0FBb2kvc0Y2SXBhRzJnM2h3UUY1aHQxYis4V3k3MVdTM0JYQ21OdGVRMUJLUjhNNEJETWF0TVBHaEovZnA5RjlVRUlZSXU3RVNaUk8vZ2pkNDFzZ3JQSjRqbHVuTktWM1lBQkU1TU9UTWpLclViR0IvRzhjQ0FjdFlMVWx5aXRmZWtrMlFqN1lKUDhHMTdNSE9WUVQvYzNLb010SmtYbDc3YjlDOUpkSGt2cWYyUlpWMll5MURTdlAxV0NJK3F5ejJsMVZZM2syWnZJN05reDRLTXkzaDViRkxqRlE2alNOZHRydEtmN2hWV29nNDlsUGtuZmZKSnpqMFpoMjY1Z0NkMVc1dS9KV3M0K0dkbDM1ei9YUERZaVkrcVA5RHRxNi96dS9XZGVodWJFYW1uWmlwaDdQS25sVzUydW05d3crck1VZC90aWh4L0UyRHpjOTg4YXZ4UkZVZ3JaSW0wNU9VVndPeGc4ZEYwcG5wV2srdnNtUkVyUkxPWWpKeHE0NEljUytxTVBESW0rNFJpeFl3OGQzZ3loVGQxMEdOY0wzRXVCMm1qUWFzMGdmOXdMbjBuMDZIL0dqSXZNejlTZnFwelQxajBqdGxMdGVkcnl4alZZOWFqRlJkNTZSVkthaUczYmpWVVZ5alciLCJtYWMiOiI3NDc5OGU1NGE3ODIyMTMzYjZiOGUzYTkxYjY4NDNlYTYxODc5MDJmZWUxZjJmYmY1NGMxZjg3MDRlN2RmNjE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1037112444 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6f86AL2UyJbEsnP8wClSGR79UCod8lW0WDs0fxKW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037112444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxiSEdBZlhMZzR4VE9nZk45YUI2L2c9PSIsInZhbHVlIjoieU5FemhLS3ZUUVBrQkVoQTdSczVhSkxYa1BqK0R2RzJJRkw2Y21LRGNyVGxQSGlyUTlLS0ErN2NGL1N3YjdodnNRVy85MzdqY2hidDNIb2s1SVlJdC9ENkE1TER1dGROS2tpTk5iRUgyekoxYjhkV3I3L3phLytQU1pZTkFJcWVlbmN6dlFkODhsZlpBTEZIMlJpQ3dmQ0tmS3dEakRjTFZzcTUxTStBbHgyVk4xSm0zTEc2eHFRamMrWDlZLzBSS2J5emJaTjFUdmxDb0RSTXFrdWd6cGhpRkd6TWdtR3VqZ2tqUE1tWE1UVmFhOVVZZS9GenVXWGI1WC9kOUZSeHYvdTNFMGJQQy9JZTdDeFM3M0hTWlNFTHorRDlHSnBiNzdjcnZ5OC8wN2FJa00wbjVzMW52dDRLaVJ6Z2w1TzZUQzdXMEJWdWFwNXc0SWZ3a2RhQU9wK2MxczBFQzUxQ2tFUWFKc1IwcldZS3hxT1hCS1ZQUG8yM2ErUXJkN0FHQW5rQUsybnVTM2dhOFJmOFR6Y0Z1ZTVmMklrUWJYSWdZaVZkNGdXeU1uRVE5OFF0aGp6U0gvOVpyenVxRTVmNUZXVzM1T09JOHZuRWhPaEVQWTlRa3RaTHo0U2RYRENKbS9vUExTenowSVBpYXVZZitLOC9nYVZDWTdXVms1UTYiLCJtYWMiOiI3MTUwM2ZkZTliMGMyYTE2NmIzMDA3ZTI5MjRjZDhkM2Q2ZDJlOGEyYjllMGNhZTVlOTRlYjI4MzIwZWI2NzM0IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IiswU0VBTkFmTWNxbEY4MVdvK3FsRlE9PSIsInZhbHVlIjoiaE1CbDFrU0d3N09SY2laTGdxWVVXVklseDJycHlDb0ZpKzZXRzFoMFUvQU9GU3YvV0ZTdC9yS3MxQmJhUG1vMlVuUDYrenhUL3lwV2VZbnVVZ3djc09sUUJ0SnVkeklkRy93bTdlb0VYSm5KYzNNa0JFVGJmbG9pTTA5VmdOc2VCbmVtci9EY2d6bDRTY3JVK1JpdzU2YTRCYU1iSWhldjFoTWRTamx2MjdlZ3ZWb2pwc0tQaEhyNE53MUhteVdHRjIzcTc1cnVhU3RoOTdMSzJIejd5OXk0VGlkQ0x5SjQzQm5xRmlqcU1ySHZrc29qQkdBR05zK3dlVVZmK0lZYWM2aktHSzNXSmJCS01teFFQdXptR1FEWVQ0bnhXakprbGlvN3Y1OHQ2czl3SlF1aXBlY3FKT3BaYzArUXQ1REc2dkdMYjZyK3FESEJpZmVBNURJbCs4WFhxRkJxZTlwR2xpZjhNVmdNNUdXMnFGeG0rOGhFNkVvK1NyZ0JRZXVuMGhtNWNwNDBOU240Qm96c0c1YSs0U3V6ZUFNaEpNcTQxS25neHA5VW51bDk5Y05ERC9jWG1uUEFWSWZPU3lMU3lzVlR3RVc4Q3p2UHZZWVNSWlFCU01wZ1RzZ2dIcmYxODZFemdzSHN4QnhsOXNCWkZSUExjbGE3azNTMk1PQm0iLCJtYWMiOiIzYjhhYTFjODE3NDJkOTUyNDAzZjM1OGY3NzI1MGU4NDdmMTU3MGUzMTY0MmE2ODdmOWU3MzgxZjNlNTRjNTc4IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxiSEdBZlhMZzR4VE9nZk45YUI2L2c9PSIsInZhbHVlIjoieU5FemhLS3ZUUVBrQkVoQTdSczVhSkxYa1BqK0R2RzJJRkw2Y21LRGNyVGxQSGlyUTlLS0ErN2NGL1N3YjdodnNRVy85MzdqY2hidDNIb2s1SVlJdC9ENkE1TER1dGROS2tpTk5iRUgyekoxYjhkV3I3L3phLytQU1pZTkFJcWVlbmN6dlFkODhsZlpBTEZIMlJpQ3dmQ0tmS3dEakRjTFZzcTUxTStBbHgyVk4xSm0zTEc2eHFRamMrWDlZLzBSS2J5emJaTjFUdmxDb0RSTXFrdWd6cGhpRkd6TWdtR3VqZ2tqUE1tWE1UVmFhOVVZZS9GenVXWGI1WC9kOUZSeHYvdTNFMGJQQy9JZTdDeFM3M0hTWlNFTHorRDlHSnBiNzdjcnZ5OC8wN2FJa00wbjVzMW52dDRLaVJ6Z2w1TzZUQzdXMEJWdWFwNXc0SWZ3a2RhQU9wK2MxczBFQzUxQ2tFUWFKc1IwcldZS3hxT1hCS1ZQUG8yM2ErUXJkN0FHQW5rQUsybnVTM2dhOFJmOFR6Y0Z1ZTVmMklrUWJYSWdZaVZkNGdXeU1uRVE5OFF0aGp6U0gvOVpyenVxRTVmNUZXVzM1T09JOHZuRWhPaEVQWTlRa3RaTHo0U2RYRENKbS9vUExTenowSVBpYXVZZitLOC9nYVZDWTdXVms1UTYiLCJtYWMiOiI3MTUwM2ZkZTliMGMyYTE2NmIzMDA3ZTI5MjRjZDhkM2Q2ZDJlOGEyYjllMGNhZTVlOTRlYjI4MzIwZWI2NzM0IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IiswU0VBTkFmTWNxbEY4MVdvK3FsRlE9PSIsInZhbHVlIjoiaE1CbDFrU0d3N09SY2laTGdxWVVXVklseDJycHlDb0ZpKzZXRzFoMFUvQU9GU3YvV0ZTdC9yS3MxQmJhUG1vMlVuUDYrenhUL3lwV2VZbnVVZ3djc09sUUJ0SnVkeklkRy93bTdlb0VYSm5KYzNNa0JFVGJmbG9pTTA5VmdOc2VCbmVtci9EY2d6bDRTY3JVK1JpdzU2YTRCYU1iSWhldjFoTWRTamx2MjdlZ3ZWb2pwc0tQaEhyNE53MUhteVdHRjIzcTc1cnVhU3RoOTdMSzJIejd5OXk0VGlkQ0x5SjQzQm5xRmlqcU1ySHZrc29qQkdBR05zK3dlVVZmK0lZYWM2aktHSzNXSmJCS01teFFQdXptR1FEWVQ0bnhXakprbGlvN3Y1OHQ2czl3SlF1aXBlY3FKT3BaYzArUXQ1REc2dkdMYjZyK3FESEJpZmVBNURJbCs4WFhxRkJxZTlwR2xpZjhNVmdNNUdXMnFGeG0rOGhFNkVvK1NyZ0JRZXVuMGhtNWNwNDBOU240Qm96c0c1YSs0U3V6ZUFNaEpNcTQxS25neHA5VW51bDk5Y05ERC9jWG1uUEFWSWZPU3lMU3lzVlR3RVc4Q3p2UHZZWVNSWlFCU01wZ1RzZ2dIcmYxODZFemdzSHN4QnhsOXNCWkZSUExjbGE3azNTMk1PQm0iLCJtYWMiOiIzYjhhYTFjODE3NDJkOTUyNDAzZjM1OGY3NzI1MGU4NDdmMTU3MGUzMTY0MmE2ODdmOWU3MzgxZjNlNTRjNTc4IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-291051241 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291051241\", {\"maxDepth\":0})</script>\n"}}