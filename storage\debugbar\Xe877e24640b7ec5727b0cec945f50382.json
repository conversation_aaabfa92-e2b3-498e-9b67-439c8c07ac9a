{"__meta": {"id": "Xe877e24640b7ec5727b0cec945f50382", "datetime": "2025-07-14 18:07:10", "utime": **********.917436, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.400101, "end": **********.917452, "duration": 0.5173511505126953, "duration_str": "517ms", "measures": [{"label": "Booting", "start": **********.400101, "relative_start": 0, "end": **********.835545, "relative_end": **********.835545, "duration": 0.4354441165924072, "duration_str": "435ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.835555, "relative_start": 0.4354541301727295, "end": **********.917454, "relative_end": 1.9073486328125e-06, "duration": 0.08189892768859863, "duration_str": "81.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034600000000000004, "accumulated_duration_str": "3.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.885901, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.162}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.89982, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.162, "width_percent": 19.075}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9087138, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.237, "width_percent": 16.763}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-622250297 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-622250297\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1806797594 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1806797594\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1980466374 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980466374\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1356073555 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515944651%7C12%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjYxSExrUmpkVGlNRzJSYytZK0pkcnc9PSIsInZhbHVlIjoiT1lDblI5VlA1cXJUdjdTcVh4R1d1ajB4ZndDbFJvenByZjBud3NOTUJ0a2hxaHVRbzBxR3VqbEZKb2dSYkJ5ckZ1dDV0d3hIbmVMdDRZcWg5Y0JtOGRPNW8rQzNKcnl3OFl0MXhYWFkyeStLeVhYNUJJbVFTVGJXWk0wekh4OHA4MktOOGkyVWJBamh1d2VrTlloaU5id2l0RTFBblJwVVE3ajcyajhTREMybytadElpYUdwNmJmblN0OSsrNUF6NWxYLzVxbFVGbHBTakxmbFoxcmZaL0pOWW9XN2pYVjRRTWppZkJuQ0F1ekdGNHdpYWw2dGcvRWRqWWdWeWRqZW43VnVuenFVZmtMZUd5WDdYN2NsdjV4c0piY0d1aktkRlhuODE5bC8rZHYrQTV5eDRGSk9Gb2dYM2lVb0VGeGFtbHF1K25Yc3YzODJWcXhWS3M1aHIvSC8wTHpHb0JTbVZwTC9La3Nka1FzQUZDY29qdUxZUGZRMkx4c0lBR1kzNUpvalQvWlk5VVhlemJwZVlaeDZoeHJ2c05HbGpvZVF4aVFQZWpMWXkvVW1JU2JGeDRTMnpFZmEyNkVYdmZXNDRDdy9sWjdXekJEWTVjODA3Zi82dnNzbmFFUlFmNFE3Nng3Y3NlUk5USkREdW9JWWVyeldURkdzNlFqaVF0UWwiLCJtYWMiOiJiZTRhYWE5YjVmMTg0Yzk0NzllNmQ2ODZmODQ3MTE1YmNkYTNkZWU3NGZlMDFlZWI1ZDMxYTRiOWIyZDdlMzU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlUwMnVwNDVJdmxlNXEzV2RoYW1NMWc9PSIsInZhbHVlIjoiUHYyUStidW8yQUJiUXZWeW9LTE5oVXFIMGROY0t6QzlIbnpDYXJ1WGNad2tidGVESjRDeE5CRVdNWGJqci9jWmQ4ZHh0c0FtUHlkNTFBZ3AzU3JnTXdCY1VoL2NOaERyMUZiZGdzbVJyT3hmVklHQmFhV05XNmhaVHhQTnZGWXhWelRKT3JIZHRxODA0OUgvUjgyUFlBSmduSTQ0Qy9qSkhKSExaNzNKR2ZjOWhWMXo4dTd2TnZDeU05VkJ3b2hsREE4aW0vUkdmY2J6YWhqRXkwd1U0cFNTVGtpV2xXMGI3UXBrbkF4MjF5S2ViN3FuLy9hUUtXU2ZFT1JCaGR6eVlmVTFBY1FBM00xN1JTTFNYNjNzZDhod3JpczJicVNGa3N6MFAxcHhEZ1RNdFJCYm9uV1Y2enFFSUxWSFp0LzRUampjWVpBdXVXa2t4UmF0YnV4NlZHZ0lid2o4b2MyUHBIRnNncXAwNlJMSGFKYWQ3Z2RhTFMwb3NIbVRPYU4wSkFndnJPN1ZJSEg3bFNnOUFqN1A3SjdwSzVVdUFsaHo5NFhCVVNWU1dxOHV5c0JjOUdlU1lxSnA1RWl1cDNiZjZPR1l4aWI1K2xwN3VWWjVnVUpKOTk2Mi9PT2htS0c2bTQ4dkNDOEpwa1FFNktNT0xrc2ZOM1p5aG1TY1ovTi8iLCJtYWMiOiJhNWM1NWRkODBjNDE2NjVlYmM3MzQ5MmJjOGI2NzdkZWU5ZmFiMDdhNjVjOWZmNjdkZTE3MDZiNmQ5OGVlYjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356073555\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1827728078 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827728078\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2112403922 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:07:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitKajNTbkdKelAxRnpxTXRJQ0FRT0E9PSIsInZhbHVlIjoiUEhjM1QzUXJkQUFFdTcrY0tjQUtqb2VmZTJBa253dDZmM1ZvelhaTXorT3pIaEJ1aXFXTHNiQURMZWRYNC92ZlBpUXpwNWxoNThISzBWYktzYXVTTWpNeTdYU3IxUHNESlM3d24rY1BzUFZOT0tIV3FRUk9rK254N0RUZWxWZ1NxOUpzK3hmTktvR3BCbXJaeGZSNU51a3JjT1JOS3Z3ZFdnemFHNTBOZ04vdklucitRR25DTnF6V1VjQXdsb0tmUk9ENElUU3lPbCtqMEp4Tm1ZV292YjljREhLU3l2LzN4Sy9COUVUTXNrOGk0dk9DMWFaS1RFYWFPMll6QXV2Vy9FY2NvNmZ1SFJPNzVHRDJJSUpZYkJpTGV5WWJpTDRTOWg5K1RqK3ErR3FhOG9kSnp6WHF6cm1JK0w2SU14RkpsWXdqN2RxOWpmbVF6ZjRLeWcwZGQ5WElHeE9TZkY0Y0FTalNkMkx4UFlkVTZ4UVJSd2JDaEcwd1UyUUx4cWhSOEJuMVhFSWZYc1hwc000QlgzdFJKRVMzd1lwTFc5aE0zbHZvdXE2MzNjTnZDZk1CNEp0Rkc0cCtURFhIUnFTQjNTYnRZWUp0R0paL3Jkd3pWRW0zQVFTUjR4VTM5eitrdUtOaVMyQUdkcU1Dam1wOVQwclpxYy9UUDBxNWpwd2oiLCJtYWMiOiJhNGU0MTBhZjA0Y2M3MmU4OWFmYzMxYmIxZDZmYzFmNTg1ZWQxMTdmYTVhZmJmZjc4MDVlOTllNjQ4ODAxYmFmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:07:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iko2cXRYNDdEdFBMbllGOGpzZEdha2c9PSIsInZhbHVlIjoiaU8wNlF6RUVUWXJYTmJjSm1LdFFJZHltbDFpVDI3b3FtNDAraGdxTmNmUUM5a0xEMngvOEU0Q0ZpQTZmMlpId3ByZnM5Qkx5c0hRL2dLSXA5S1VyWmRpTC85K25ScDRjUkVRdW1aMmhuY3hWdjlGUHB4OGtmSjJISnY0VTV6NGEranEveXdtNzJTSjhqTzN0a3lULzJscUxhU05YWUwxU0FkOUdxdzZReXl6N3FMMGRubjN5Tm5lN3ZTS2pjTnZwRGNDVEsvVDYwMExxYXg5K0Ftayt5bEdlZHM2QkMwdjFweEY1aXk0anlBcFFTYlhVNEdnUS92aHpnME55SHRqZktndlpYNDFPb1FSK0Y3VGJYdWVaQy9xQVVvdzhZTGlFTFhhSEVPSEsyMm1kcGdzNzhUaGlOZ0dHUnpHVG1mNWxTN0gvTWNzZjhWQnhLcE9nUEVLWVB1Y21MbVFOdkVBYm9COWJMNzF0ZXBObms4QXJFdGR3RlpKVHpTRDkraUFLUXhMbWtBVFdXSUM3UVFkc1R2UmxTOWN3bWdHLzEvL3JySXN1Sms5UzZzU3daVmI0Y3JtUDhlYitmWUZweldSS3ZjTWNqeUhxWEprMysvU2NwTWtaQklFZWRPWmJtb1Z1NndhaWpaU0xSaXdJOGNGSG5Yek9wemJUWjdWMXVKUVUiLCJtYWMiOiIzNDEyOWZmY2E0YmE4ZjQ3ZWFlMGM5MzJjNTRkYjUxYWE2OGVhNjFhMDEyOGU3YzQ0NzBkNzJhYzk1NmM3ODRhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:07:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitKajNTbkdKelAxRnpxTXRJQ0FRT0E9PSIsInZhbHVlIjoiUEhjM1QzUXJkQUFFdTcrY0tjQUtqb2VmZTJBa253dDZmM1ZvelhaTXorT3pIaEJ1aXFXTHNiQURMZWRYNC92ZlBpUXpwNWxoNThISzBWYktzYXVTTWpNeTdYU3IxUHNESlM3d24rY1BzUFZOT0tIV3FRUk9rK254N0RUZWxWZ1NxOUpzK3hmTktvR3BCbXJaeGZSNU51a3JjT1JOS3Z3ZFdnemFHNTBOZ04vdklucitRR25DTnF6V1VjQXdsb0tmUk9ENElUU3lPbCtqMEp4Tm1ZV292YjljREhLU3l2LzN4Sy9COUVUTXNrOGk0dk9DMWFaS1RFYWFPMll6QXV2Vy9FY2NvNmZ1SFJPNzVHRDJJSUpZYkJpTGV5WWJpTDRTOWg5K1RqK3ErR3FhOG9kSnp6WHF6cm1JK0w2SU14RkpsWXdqN2RxOWpmbVF6ZjRLeWcwZGQ5WElHeE9TZkY0Y0FTalNkMkx4UFlkVTZ4UVJSd2JDaEcwd1UyUUx4cWhSOEJuMVhFSWZYc1hwc000QlgzdFJKRVMzd1lwTFc5aE0zbHZvdXE2MzNjTnZDZk1CNEp0Rkc0cCtURFhIUnFTQjNTYnRZWUp0R0paL3Jkd3pWRW0zQVFTUjR4VTM5eitrdUtOaVMyQUdkcU1Dam1wOVQwclpxYy9UUDBxNWpwd2oiLCJtYWMiOiJhNGU0MTBhZjA0Y2M3MmU4OWFmYzMxYmIxZDZmYzFmNTg1ZWQxMTdmYTVhZmJmZjc4MDVlOTllNjQ4ODAxYmFmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:07:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iko2cXRYNDdEdFBMbllGOGpzZEdha2c9PSIsInZhbHVlIjoiaU8wNlF6RUVUWXJYTmJjSm1LdFFJZHltbDFpVDI3b3FtNDAraGdxTmNmUUM5a0xEMngvOEU0Q0ZpQTZmMlpId3ByZnM5Qkx5c0hRL2dLSXA5S1VyWmRpTC85K25ScDRjUkVRdW1aMmhuY3hWdjlGUHB4OGtmSjJISnY0VTV6NGEranEveXdtNzJTSjhqTzN0a3lULzJscUxhU05YWUwxU0FkOUdxdzZReXl6N3FMMGRubjN5Tm5lN3ZTS2pjTnZwRGNDVEsvVDYwMExxYXg5K0Ftayt5bEdlZHM2QkMwdjFweEY1aXk0anlBcFFTYlhVNEdnUS92aHpnME55SHRqZktndlpYNDFPb1FSK0Y3VGJYdWVaQy9xQVVvdzhZTGlFTFhhSEVPSEsyMm1kcGdzNzhUaGlOZ0dHUnpHVG1mNWxTN0gvTWNzZjhWQnhLcE9nUEVLWVB1Y21MbVFOdkVBYm9COWJMNzF0ZXBObms4QXJFdGR3RlpKVHpTRDkraUFLUXhMbWtBVFdXSUM3UVFkc1R2UmxTOWN3bWdHLzEvL3JySXN1Sms5UzZzU3daVmI0Y3JtUDhlYitmWUZweldSS3ZjTWNqeUhxWEprMysvU2NwTWtaQklFZWRPWmJtb1Z1NndhaWpaU0xSaXdJOGNGSG5Yek9wemJUWjdWMXVKUVUiLCJtYWMiOiIzNDEyOWZmY2E0YmE4ZjQ3ZWFlMGM5MzJjNTRkYjUxYWE2OGVhNjFhMDEyOGU3YzQ0NzBkNzJhYzk1NmM3ODRhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:07:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112403922\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-566268473 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566268473\", {\"maxDepth\":0})</script>\n"}}