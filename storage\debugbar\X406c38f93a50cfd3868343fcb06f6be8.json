{"__meta": {"id": "X406c38f93a50cfd3868343fcb06f6be8", "datetime": "2025-07-21 01:57:37", "utime": **********.095959, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753063056.664984, "end": **********.095975, "duration": 0.43099093437194824, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1753063056.664984, "relative_start": 0, "end": **********.029199, "relative_end": **********.029199, "duration": 0.3642148971557617, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.029207, "relative_start": 0.36422300338745117, "end": **********.095976, "relative_end": 1.1920928955078125e-06, "duration": 0.06676912307739258, "duration_str": "66.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46005680, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01592, "accumulated_duration_str": "15.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.058666, "duration": 0.01498, "duration_str": "14.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.095}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.08258, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.095, "width_percent": 2.827}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.088668, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.922, "width_percent": 3.078}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_search=&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-123374860 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-123374860\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-882050343 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-882050343\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2040217461 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040217461\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1237125713 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"153 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_search=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753063050928%7C29%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhETWhud1FxNUhwcjNWVVo1SE1WcHc9PSIsInZhbHVlIjoiVzVnb0JZeGVBUjFzYTRMVmw3LzZuNTVXV2VsRmJGVHRTUUpYMWF0UXZ4bDFMeEZxZU4zTjVGcUtHT0YzVzVDbjdGUUd0L3FQZG1nbkRwc0MwemZxSkdDaVJoY2RYZXl1SlhCS2N2b21RWEJGZ0o3UTZYSHBXV3g1ZTNPSDFDemgzZXo1V3pUMnVtM1N1K1F3L29tRGFRakNqaTY5dTg4QXByMXBVWlVhRVgxaXg0akZNTVJVajZrY2M1OTJVbE50NU5kU3VMYTNwY1RUdFFFaUVKbHZHcnBmbXMzV2tBa0M1d2VDNHlPQ3ZBSzA1Y1RCQjJWSkgyYjUzOTNNT0JKNEtaNVQvcVlOMmtIaEorZ2tiUzBPcE4xZGhlMHlOU291RDhqMXF1aTZYamlaNlV2SmtIc0FEd3lKSU05a3ZXRFBiT0JqaTNPWlpxUnBGTDlYUG1FSFEwYlJHRUsrNmlQNnlrU2RwalE4L21uU3g0NTRSU0ZPelNoTjdMZ2ZkRmQwWXhjOTUydFFPYjdWVStMYnJiT1R0K2lzYmJBNGJtcDJ5SE1wUjhuTGJ4bzlVanFLUkpLK2Z0N0ljK0VTTi9XenpLMXQyM3grMURzUkE5Z1hybkZZVllYSUJMSDVnamRWTktxMEpDK0IxK2N0dkRoeElpb1R5cG01SnpMcXJVeFQiLCJtYWMiOiI2NDA2MDQzYWU3ODlhN2ExODZiZmQ0NzVjMDk0MDRlN2E2YzRhNGIxODBkMmNjMGQ3NWFkZmU5NTY3ZDBjMTM3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5ZUDFzUzJuOVlZYTlITFR2c3JhakE9PSIsInZhbHVlIjoiZHRSdkdQV2NVLytqa2FXV2VaSUIwbEVCR0RSL2cwZjlva2xxc0ExaUtZY1R6QmlQeFF5TVVFQVJCRmRxcUhLSWxqQ2JwUzZyVmlIcDNxUHhNcVE1NUlLYkxNUmswZ3hxUis2UFUrNHdrZzlTWS9mUFBOT0g4ZTZGM3ZiZVd4YzJFUUNDQXo5TG8zMFoyL2had3FFWm8xdWpnNGZrMUVwRnRtQUxKOVA3TzhwWFNVMEtqL2VOcTVNY0JoL1RzVGtMYXVNVmppb2tNVHIvU1poUVRVKzkxNHluMkdCQVEvdVNkaWZaZnpUV2w3Rk0zSE9NOFowRHppbkN1RmJ1S09VRXl4ZnZlQVFQaWgwWjl2SVRkQURhVlc0VklGamRxUGttSFh4Q29YU1BaOXZycERxZ2tWZUl3ME9mSHhrMktkZ3pSY2paZ1Y3NGgwV0VvblROVFFNM0FtY0doRXNOUHZObitNMFNYWWJkK1RSUmlQVjFmYVlBM1NJMGxOdzBENlFSV0tURy9QSkFtOS9Ya2VMRFY5K1JlellKZDMrRUV0NDJZYVBQQ0FMT2w5YloxSEkyQWh3SnpNWVFTN2YzaHNhaTZQT1JRY0JMYnNGSzVjTHJKdlpzSVR6TUpzZVFEalJGY3VYdHBRQmszRFVyZ3l3L3Y4YVlWSHJaNDB0RW1RZWwiLCJtYWMiOiIxNWY1ODU3MmNjNTBmYmQ4MjFmZjZjMWZmZWFmYWY4MmQ5OTNlMDgyNTUxZGY5ZGI2MzBjZDRjYzM1NzY4ZDQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237125713\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-694279324 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-694279324\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1065356434 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:57:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJXWXFEai9lYnZ1Q2dhTXIxenQzK0E9PSIsInZhbHVlIjoiYlNpMzFub2owMTVCcjQ1OTA1Ung5SDljY21FNkpxTkNkTi9nN3IzQ3UwNUdXRHQ2T2xUVFN6WUhoNW13dXpFamhJdktxOTRENnpXcFVNKzllOG56SjN0NnZiQ2haTzBkUldvV2RSY3lqSEV5VTZvSk1uK2w0TjN4SlRXV2hsbExlaE44YnRKM0tjZzQrY0E2MW1OYUNMNTVXRjdxZnJtUDF3SUZjK3VweDZ0QmpGcTI0T21tTTFrb1Y3bGdQalRkb254V1BCZnVUTUs0bDhJOVdmY0pmZmx4VzE1azJqNHpZTHpsSHFvUDArZGphNHFxUDI4ZTFPQzZTdXE3c0xDemdRZWs0cGhhUm4xQW8wMjVRMFdFMXlUazYwQmlwLzlLRmU2MWxsdnBaN0VrUGd5dEt6ZjVEY2tZdVV0TlB2dWRMeklxUWFqTFRPL1o1V1dwMlhSQUJWbWVlekV2c3NNeHFsMlVFc3UwOXk3azErbnhZNG5NMFc1RXFDcHR3ZnA0R0hUZURFYWQ1aFZhSEFUYlBPMHVxSS9KVkZGVU5oYmJrWFk3cXhTUGNZcTVuZHdlVmNMWmpuTHFIQmMzUTlWM1V6Lzk4TGZadXYrdUx0QnR3dkkyVTRZVmY4c1FIUWlqU1lOUTFDU2lKWEVlMGc4N0dKOU83SjFlcDFiR1dXQjMiLCJtYWMiOiI5ZDg1OTk1NTZhN2NhYmQzZDk3MTViZjk2ODc4MjQ0NDc5NzE2MTQyYjk0YzE3MTZkZDliMzVhODdhYzIxMThhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklJSC8xclNBUllXeVYvSnN0eWhJUXc9PSIsInZhbHVlIjoiM2V0MTV6NWV3NFQwWUZ0dFdDYjFobStLd1pSSzRDc001a1hoaUhidDJyaU5hV0diQ0tEZ2o5eW5hdXZkNTNzUHJ1dHlaT3ZSaFhTVnBROHhXMXp6dUtneHNlWmsrRGNjZWR5R3M1R2czNEcrZ3VXRWxNRFlmODBuRDl6S2hpMEMyNENMUjBxaDhrV2xqN2g2eUxyQkNGR2x5UVNQdTV1cU8vRCt6akxjL3dFSjRoSEF0YTVOcjRETTdtWmdlRmJMclpMbUdyaUNCb3ptTWxPdm83a1VObVlXRE1pTWx3L1JEK0s4bG8rK0FnWWh5QjlHY1JvNFdKcVgwaG1OSzl0YzYvSlV6KytBMVluRmVveksyRC9zOHdCMENvamxqTGxHZ2NUZTJwK3RJQ1pMakROeFJWK3VpZ0s5eVdWaTNiTHcvQUFiUEU2eW9XWWpGYzJnRURRcDdvYlNCU0xWbWJ5QWM4bXA1dDM0ckZ3cHJjN0UxRUZmZWdUSEdmTkJjRFJoUVEwR3BGamJ6am1xM042amZvQkJma0xUQW15ZHBObDMrbzIwYVNVS3FqRUxObVB4U2dpRGtCakZ3VmhiT0p2UjBGWC85bllzNFk4bW5aR2xHWEtOU1JsRGxIdGEyaTlVa0FKS0U1U1c5VHRQaDlCZkZtYU5idk41SCsyUTBnOXMiLCJtYWMiOiJjZDg1ZGZhMjBlZWQ4ZTEwYjlmMWY1MTg0MmMwNTMyYTM1YzcwZGNmNWEyMGQyOTZhNmFiOWQ4MDFlOTUwNjMzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJXWXFEai9lYnZ1Q2dhTXIxenQzK0E9PSIsInZhbHVlIjoiYlNpMzFub2owMTVCcjQ1OTA1Ung5SDljY21FNkpxTkNkTi9nN3IzQ3UwNUdXRHQ2T2xUVFN6WUhoNW13dXpFamhJdktxOTRENnpXcFVNKzllOG56SjN0NnZiQ2haTzBkUldvV2RSY3lqSEV5VTZvSk1uK2w0TjN4SlRXV2hsbExlaE44YnRKM0tjZzQrY0E2MW1OYUNMNTVXRjdxZnJtUDF3SUZjK3VweDZ0QmpGcTI0T21tTTFrb1Y3bGdQalRkb254V1BCZnVUTUs0bDhJOVdmY0pmZmx4VzE1azJqNHpZTHpsSHFvUDArZGphNHFxUDI4ZTFPQzZTdXE3c0xDemdRZWs0cGhhUm4xQW8wMjVRMFdFMXlUazYwQmlwLzlLRmU2MWxsdnBaN0VrUGd5dEt6ZjVEY2tZdVV0TlB2dWRMeklxUWFqTFRPL1o1V1dwMlhSQUJWbWVlekV2c3NNeHFsMlVFc3UwOXk3azErbnhZNG5NMFc1RXFDcHR3ZnA0R0hUZURFYWQ1aFZhSEFUYlBPMHVxSS9KVkZGVU5oYmJrWFk3cXhTUGNZcTVuZHdlVmNMWmpuTHFIQmMzUTlWM1V6Lzk4TGZadXYrdUx0QnR3dkkyVTRZVmY4c1FIUWlqU1lOUTFDU2lKWEVlMGc4N0dKOU83SjFlcDFiR1dXQjMiLCJtYWMiOiI5ZDg1OTk1NTZhN2NhYmQzZDk3MTViZjk2ODc4MjQ0NDc5NzE2MTQyYjk0YzE3MTZkZDliMzVhODdhYzIxMThhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklJSC8xclNBUllXeVYvSnN0eWhJUXc9PSIsInZhbHVlIjoiM2V0MTV6NWV3NFQwWUZ0dFdDYjFobStLd1pSSzRDc001a1hoaUhidDJyaU5hV0diQ0tEZ2o5eW5hdXZkNTNzUHJ1dHlaT3ZSaFhTVnBROHhXMXp6dUtneHNlWmsrRGNjZWR5R3M1R2czNEcrZ3VXRWxNRFlmODBuRDl6S2hpMEMyNENMUjBxaDhrV2xqN2g2eUxyQkNGR2x5UVNQdTV1cU8vRCt6akxjL3dFSjRoSEF0YTVOcjRETTdtWmdlRmJMclpMbUdyaUNCb3ptTWxPdm83a1VObVlXRE1pTWx3L1JEK0s4bG8rK0FnWWh5QjlHY1JvNFdKcVgwaG1OSzl0YzYvSlV6KytBMVluRmVveksyRC9zOHdCMENvamxqTGxHZ2NUZTJwK3RJQ1pMakROeFJWK3VpZ0s5eVdWaTNiTHcvQUFiUEU2eW9XWWpGYzJnRURRcDdvYlNCU0xWbWJ5QWM4bXA1dDM0ckZ3cHJjN0UxRUZmZWdUSEdmTkJjRFJoUVEwR3BGamJ6am1xM042amZvQkJma0xUQW15ZHBObDMrbzIwYVNVS3FqRUxObVB4U2dpRGtCakZ3VmhiT0p2UjBGWC85bllzNFk4bW5aR2xHWEtOU1JsRGxIdGEyaTlVa0FKS0U1U1c5VHRQaDlCZkZtYU5idk41SCsyUTBnOXMiLCJtYWMiOiJjZDg1ZGZhMjBlZWQ4ZTEwYjlmMWY1MTg0MmMwNTMyYTM1YzcwZGNmNWEyMGQyOTZhNmFiOWQ4MDFlOTUwNjMzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065356434\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-37206257 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"153 characters\">http://localhost/invoice/processing/invoice-processor?creator_search=&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37206257\", {\"maxDepth\":0})</script>\n"}}