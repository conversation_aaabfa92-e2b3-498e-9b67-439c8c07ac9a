{"__meta": {"id": "X3d3886940f60fec4e1f9352cfe17ab05", "datetime": "2025-07-21 01:34:21", "utime": **********.2784, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061660.846597, "end": **********.278415, "duration": 0.43181800842285156, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1753061660.846597, "relative_start": 0, "end": **********.225778, "relative_end": **********.225778, "duration": 0.3791811466217041, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.225789, "relative_start": 0.3791921138763428, "end": **********.278417, "relative_end": 2.1457672119140625e-06, "duration": 0.0526280403137207, "duration_str": "52.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46008272, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00237, "accumulated_duration_str": "2.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.25556, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.932}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2657988, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.932, "width_percent": 18.143}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.271269, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.076, "width_percent": 13.924}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=8&date_from=2025-07-01&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-214165203 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-214165203\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1725273808 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725273808\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1169340313 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169340313\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1478536427 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-01&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=&amp;customer_id=8&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061655922%7C11%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVweGl6Umg1N085UmV4VVJiQkowaXc9PSIsInZhbHVlIjoid2ZRVjlIUG1KVUIxanlPNlNncEdHR0ZBZy9YbDlLUGcySFlERGR4QVI3SWJ0b3lESnE0RmlLdXRoNjNwM1JlU05nOGU2cy9PdytHSXhReEtJdDVNSmxIV09BWHRqeVV6QzdNTHR0ajMwbG5pL1U3ekNFWWdiSzNhYTdJMjFyQXhVNi9hQ2w1eDdhYkk5S3FBbmdZMklGRmJsTmdySW1ZOEk2UzVsbk5GbVFkWVUvbTl6L3JhRlEzUlpnUWR4YVllV0FTL1VHMlVKcHFMQzZHbUgzNHNzZ2lCSXQ1dVowYVh4Q0pkYkFhRHg2dUZOU2tZOWlMcWFCZFVIZ20vREEyME5MTitCQWV2cW00aWRFWCthRUxNdVpNa0kzSmxVekIydWcrRWVzZjBmMVBCb0dOaTljTUU4dVZacldBd2tsZjJWcndNM05xZHdYd2VhNW5ZYlRWYnFZdWZISG5OTC95Z1k5WlRZckJhSHJUTTRvUGxWc0VUTStPTlo0blU3eTJNT2tSRUlGc2c5M25ST2dEUjhKbjhFMHowM1R2Rk9XK2lHN1NJMmVRVWEzUHN2cmx6dXM1SUVONXlIRmhObndRWHM4VW1SMm5zWThDb0RiTHVlY3dZYjgrVit5amtIZERLM3ZLam9vajR0QU1jMkpVNzkvVEJuVTdReGNPUTRyTnoiLCJtYWMiOiJkZDgxN2ZjNjkxNzE4OGQ3ZTA1N2FlOWU0Yjc0MmM0OGYxMzI5NmUwYTA5MTJjNWZlZmYwOWVjZmZiN2Q3Mjk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlAvV3lpS3ZKaXFtTHhvbWJMUEJTSnc9PSIsInZhbHVlIjoieUxYbVJFaWNVSWxPU2pIalJQWDFNUmtNSFZXTFRGejNUeTJkeXppR2QrYXRRMEg0YzAwNHd3alZCZXpyOUlPUWNqc08rdkhRbDN3QXBjMVpiQkdPcURiejBRUWFFNkFRVVlJMi9zN1duWjdQMzF1ekxIellNWTZ0dlRRUkFUVzJYQTZ3U1JEeEtKL25CajNnSHNCM1pOYkVPN1VZZWZ0VWVUU3IyVS82dmpuMkE3STIzNzJDeGxlWG04cmhTeGxZeUlUQ1YwNTlHbmZWREw5TW5NNE52SWJqZytEVW9ORml5TTlvVjYxRndLNUg2Q3NPenJRUStrY3I3UEQyc2xtS0owV28rR3IvSzd2cVRoZ2ZTOUlzWGkwTVB6dHlhd2dGSlRmNER1ZkZmVktyZTlBYUdBTWw2R1ZVS3hwWDhCMnZZRW9wdTYrMzMwMjNJbmN3MVUzUS85N0F3emZUajZtazBTSXovb2trZ2tPc2t0NytMQ2RhMzBwblMrcXBtVzJnV1gzalVxcHBuWmVtenNzK3RraFBmN0RtZXh6QUIyM2x6eFQwdE9JUEFmV3ZUUG51ZG1hTmNrZkNJbWgvdjVOV1VzYlVTcCtoWCtOeHVPTjZBaCt2ejhISU5rNlZRSi9NRUlMZ3lZcXZtbUNaL1VzSHRKSS90ZnIvdTRjaHlQa0wiLCJtYWMiOiJhN2M4YTcwOGJlMzYzOWE2NTI2MDExMzYwOTcyN2JmYmNkYmRhMTRmZmM2NGVkOTA2NjY2OWI5MGI5YTE2M2ZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478536427\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1120252312 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120252312\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-554570998 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InUzTVpEZHlYSW1vNWpTeU5ZVnRHUUE9PSIsInZhbHVlIjoiR3BJd3MwVlZNOWtOQzFRR1pXbFh0SGtvQTkwejA5aHhiaTdhaWpkSTBrL05BWk04RFlWTGVldWV6VXU0S2JpbzhaOU4rR2NsY1U0VUJMa0VGdlJEdm10aUQwa2ZmWE9zMERwL2Q2STBRSEtDOWw0SEhOVktYdUk4by9udWZtb3ZLZ2FzTmZlbGlrenpwTGFwbFdicXViNExHYXYwNDFCM2FiOWdRNlZablhGYWp3QjZyTStIMFFNZWc0YUV4Q2tidEpTYW1QdEFLMW5RcjhHbDlsUXppeVhnbHlrUmtTM1p3M1ZOckNZbjZtOTlnV00xdEl3V05GZGFiR3hqQlo1MXhBU1VRdVllS3pkZUhLUDN2OWdKejc0SEpEU0hVdUcwbGRSS0RsSUo2SE1Pd1RjMWR1Y1E5QTY1UnJkaWVQWWdtNTZuWFJRN3F0T3pZaWUweXM3a3JqSnB2V0lvVFZpK1pkRE5SSnRCWFFIc0RUOTJaRklLM1pPMGpFT2xMYjNaY1J1Qk5xdUs2ZzUxNFU1bkJ0R09ITDBYRndJbUpBN2diQWxqV1JSSW16T3lwaElvREk5ZlVpam5LdTFqSFpBN3VmUjM0NU5GbUNXNWoyUUhMc3ArUkpLb2Iyd2c0WTRSZ01VTi8rZUZaZUtFb0VEc2U4SWs4L2E4ZlBMRnQ0YXYiLCJtYWMiOiIxMGRmZGE0NTlhOTEyZTA2OWFhOGMwNWEzM2E5YjZhYmI2ODFkNTMwMGJkOTZjODAxOTVkYThjMDQwZjU5YWY3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkwwbTdQR3hRK0l0bEltRE1JQ2xzZHc9PSIsInZhbHVlIjoib1JGSWxGL2o1dG85MXR5T1czc3VPeSsvRDQxOFZNdmdDSWliSjhVSmNDclBBUEszcGxXdFJaNGI3YUM3RWp5OW9uU0hlakxFSlg2M1MybSs1WS9la0ZLeHNFbklTQ3pXQVlZQ1RldmdDeENCMzVjTnpPU0FvRUtKNjBSNXBIYUNFc1EyQ2o2ZXo2UXF2NCtkUnM0Z2ZScnRoRk03N3JNekJ2YXRLd0ttMUJIVjNtWWE1STZGYW9jSml4Q0xnRnNRYktKRmNVZ2NOSnhoR3N4eUpkSlNsZmtVRXN5eDNHK2RRVjJ0cmpocWNsV3VWY2RtOWxVZzBVWUY3dWMzeEdTdGxES3ZNa1V2UkNzM3VOc0FyeGRxRlZDUkFjWTlYcDdYTVFRYm5hVzNoelhLUVRjMUlWenMvUjJEYU9hck5CNVNiRm52djlveUJPaWJTRWpPUzRCMmdGYlZzOUV3Y1FyT21XdlNPeDZlbGg3NDMyMlVrekV6THFBS3V2WlhLWGJ3dTI2VUhvckIrWDJjbENSbUNTSTNLUEhlTmdIL0JIQ29GUnV0NkhUYW10dnpmY0ROMXkvTmdudmJIV2k1Q3V4NWl5L1l1eW1WakdoblJxazdBYmRrK1BDMXBxai9aemRVN1FxekFyZjlEM1pKeno0MmEwVE9JbUxZMU5TSkcvS2EiLCJtYWMiOiJkMDI3NTYyZjBhNTBkNDdmOTVlMGZiNjdmOWZhNjEwMjI3YTc2OTVhNDU5NWNiN2U1ODM5MDE0OGI3YzMwMmJhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InUzTVpEZHlYSW1vNWpTeU5ZVnRHUUE9PSIsInZhbHVlIjoiR3BJd3MwVlZNOWtOQzFRR1pXbFh0SGtvQTkwejA5aHhiaTdhaWpkSTBrL05BWk04RFlWTGVldWV6VXU0S2JpbzhaOU4rR2NsY1U0VUJMa0VGdlJEdm10aUQwa2ZmWE9zMERwL2Q2STBRSEtDOWw0SEhOVktYdUk4by9udWZtb3ZLZ2FzTmZlbGlrenpwTGFwbFdicXViNExHYXYwNDFCM2FiOWdRNlZablhGYWp3QjZyTStIMFFNZWc0YUV4Q2tidEpTYW1QdEFLMW5RcjhHbDlsUXppeVhnbHlrUmtTM1p3M1ZOckNZbjZtOTlnV00xdEl3V05GZGFiR3hqQlo1MXhBU1VRdVllS3pkZUhLUDN2OWdKejc0SEpEU0hVdUcwbGRSS0RsSUo2SE1Pd1RjMWR1Y1E5QTY1UnJkaWVQWWdtNTZuWFJRN3F0T3pZaWUweXM3a3JqSnB2V0lvVFZpK1pkRE5SSnRCWFFIc0RUOTJaRklLM1pPMGpFT2xMYjNaY1J1Qk5xdUs2ZzUxNFU1bkJ0R09ITDBYRndJbUpBN2diQWxqV1JSSW16T3lwaElvREk5ZlVpam5LdTFqSFpBN3VmUjM0NU5GbUNXNWoyUUhMc3ArUkpLb2Iyd2c0WTRSZ01VTi8rZUZaZUtFb0VEc2U4SWs4L2E4ZlBMRnQ0YXYiLCJtYWMiOiIxMGRmZGE0NTlhOTEyZTA2OWFhOGMwNWEzM2E5YjZhYmI2ODFkNTMwMGJkOTZjODAxOTVkYThjMDQwZjU5YWY3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkwwbTdQR3hRK0l0bEltRE1JQ2xzZHc9PSIsInZhbHVlIjoib1JGSWxGL2o1dG85MXR5T1czc3VPeSsvRDQxOFZNdmdDSWliSjhVSmNDclBBUEszcGxXdFJaNGI3YUM3RWp5OW9uU0hlakxFSlg2M1MybSs1WS9la0ZLeHNFbklTQ3pXQVlZQ1RldmdDeENCMzVjTnpPU0FvRUtKNjBSNXBIYUNFc1EyQ2o2ZXo2UXF2NCtkUnM0Z2ZScnRoRk03N3JNekJ2YXRLd0ttMUJIVjNtWWE1STZGYW9jSml4Q0xnRnNRYktKRmNVZ2NOSnhoR3N4eUpkSlNsZmtVRXN5eDNHK2RRVjJ0cmpocWNsV3VWY2RtOWxVZzBVWUY3dWMzeEdTdGxES3ZNa1V2UkNzM3VOc0FyeGRxRlZDUkFjWTlYcDdYTVFRYm5hVzNoelhLUVRjMUlWenMvUjJEYU9hck5CNVNiRm52djlveUJPaWJTRWpPUzRCMmdGYlZzOUV3Y1FyT21XdlNPeDZlbGg3NDMyMlVrekV6THFBS3V2WlhLWGJ3dTI2VUhvckIrWDJjbENSbUNTSTNLUEhlTmdIL0JIQ29GUnV0NkhUYW10dnpmY0ROMXkvTmdudmJIV2k1Q3V4NWl5L1l1eW1WakdoblJxazdBYmRrK1BDMXBxai9aemRVN1FxekFyZjlEM1pKeno0MmEwVE9JbUxZMU5TSkcvS2EiLCJtYWMiOiJkMDI3NTYyZjBhNTBkNDdmOTVlMGZiNjdmOWZhNjEwMjI3YTc2OTVhNDU5NWNiN2U1ODM5MDE0OGI3YzMwMmJhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554570998\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1929089625 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=8&amp;date_from=2025-07-01&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929089625\", {\"maxDepth\":0})</script>\n"}}