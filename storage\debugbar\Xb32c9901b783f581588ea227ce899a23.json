{"__meta": {"id": "Xb32c9901b783f581588ea227ce899a23", "datetime": "2025-07-14 14:25:05", "utime": **********.392982, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752503104.826515, "end": **********.392997, "duration": 0.5664820671081543, "duration_str": "566ms", "measures": [{"label": "Booting", "start": 1752503104.826515, "relative_start": 0, "end": **********.335476, "relative_end": **********.335476, "duration": 0.5089609622955322, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.335486, "relative_start": 0.5089709758758545, "end": **********.392999, "relative_end": 1.9073486328125e-06, "duration": 0.05751299858093262, "duration_str": "57.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030600000000000002, "accumulated_duration_str": "3.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3652341, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.092}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.376736, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.092, "width_percent": 19.935}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.385088, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.026, "width_percent": 17.974}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/journal-entry/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-557838279 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-557838279\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1489007963 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1489007963\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1749979973 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749979973\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-866962896 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; XSRF-TOKEN=eyJpdiI6IlpWSXhsajNIY3pQWFFzSlI0SkRBZVE9PSIsInZhbHVlIjoiNi82RGFmcXE2UFZEU011aWVRb093ZEx6eHdVcUQyaDBvWWpBMVFqODNGVTdVa1Z3K3M4UTEwZE5BZC9LNkJYTDNtWFgreDNWNFErNmpra1BPYlBXQlVVN1J0SFVVbFNxemRBL081WjVweTJUZzk2TUNCa1JSVjhQSHYwb2lNNWVubjFBeDFPcmh0WjIvdEpPNzdHcHBoVjdiU0YrRzZFL2lkbVBSMDg3cVlpRFp2ZVF0UWQxbHlwb2JDNlNEOFJERDd0UjBET2xIMS9IWkl1UmdybU52RWkzb0oraHhFMWpobmFXNU0xNWVvVVp2Q3FoWGk4RGYxcldHYlRma1Mwa0xmTE9JN1RMVzBxSzBEcW5HS0RQQkROc29nL0tsc0xFOGFNY3BKa1E0QWJSZTlBWDVpNUpQczk1cXpVQ2hvemU1WlZGdSs0d1ZMbjJOb3NIM1I5VlhTVWtRS3NwdC92NUlndzcxcDFDaVJhQ2R3TkVTNzBEd0hyNkp3TUVGV0JoaCtnSW1CeUhia2dON0MzS0xhckhiSHh4Um1vUUtFbFVTVngxdnFjMm5zaFlBWjVhUXlPaGNQR043c3RFazZlZ0JpQVZmVHdpUXcwclVHUHdrczc3OFFEanZ3MC9pbS9kOHN6ellIcVF3alpWWXlHWHNFYjVaRzRLd0hQZHBUWGoiLCJtYWMiOiJmMmIzZWVhYWY2YTcwZmE4ZThkMTgyYjdiMGEzZTU1MmJhYjgxNTJiNGEyNzZjMDdkZDAzZjdjNWQwNGFlMmU1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIvZW5ZSzhhcGpqUmsxcm9LdGNZWWc9PSIsInZhbHVlIjoiVnBMMk9WV3hrOFRGa3JrVFpFKzE5S2VITUlvV1R6Ty9ycFJGRFIxaGhkaUlVYWxlc1RacGhXZlJCbk5RbitKYlBOR1JxazFieXJ5OXhBY3lJVnFUc25mT1NqaFFRNGVFN2NZRnpRYWw1ODMzQW5KNVZIdGU4K2ZBeTlkWnNScDNYd3pkbnlkNTM4bGtKbmQ5UzZDdlZubkltSU1sbFQvUjMrK1dENU9udjA4dCtwampzQXRWbmhzZmRiOHlkUy9wZS9LbHN1T0lPenMxd2RmR1FUNUEzVmZIbzJVYmVlRUk0dDE0MG83b3UzV3lrbExNVUpWSjM0WUk1Ty9JUVJXNkQvMlREUjVvK2lKZnlJN0ZNaThpTnN2ZTI3QWxoZldCbnBHU2RXUi9tOXBUNUc4ZWMrbmU0WFIwTU9UcjR3dHQ2Z3BYekRpenpaZkJ5MmVFMm1qYUtuMUo1N2t2eEZITEVVY09sWTRFMjB1MXc1b1Eva0pLVTY4WjNTUmVFM2FnMXkxdUMyblpsZ3ZDdVp2S2ZjSGtPa01VRm8xb2ZUY2V0aEVDdjNmcGdxck1YbnB5aW1VZjFrTVJBR2Z2Wll6a1ZhaWsyaXNDM2tiZXlDQk56YUl4QWZqYjV1d0EzaGRZNHVlV1JMZzB1dVM1VUdPYlRSLzMwaWRwK1c3ZWdLOUEiLCJtYWMiOiJmODVhNzRjMjY1YmFjNzQ0ZjZhMDlmZDk0OWUyNDZiNzhlNjUxZDJjZmEzOTlhYzVkNjA5OWRmNTRkYzA4NTZjIiwidGFnIjoiIn0%3D; _clsk=1g7ulh6%7C1752503104708%7C12%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-866962896\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:25:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1LMm53Tmp6aWxBbXRwMWIrdzdLd1E9PSIsInZhbHVlIjoiWGlmbGViTUpNUk5MMFI4WjY4TmFTcGJHQThhMjVOMEc0YUMycGdFd0JhVFJHOHhPbjdyNUJsY2hEdys2RjVydEtIelhTSk9DL3RhcUdCaWRveTJ2MWo2SGFHTktNWEJwajFRNlArYkRNMGp3TlRZYnZVa2IyU0U5bVljSWRyVGZicFgvaGVjQzRNR3lDd2phUVZCVXFFcHRUbzVqRG84T09SeTNJYnhHRDNFWE9Gc1VHT1o4T2xRREo2bHI4bG5uY25NcWVDZ0RZcCtBOXlRRG1scmJQdExXTnlLNG9pSzkvTWVCcnpYMjd4T2ZLWlNOWXpYM1ZROUNjVlQyeU5aendOMUlOOWRVdVF3L2ZUdG45b25TdWdzOTgyQXVNRWxkQkMrMG5LVGlYWkYwR0J2STc0dXREWHVHM2lHT0xOTVdQaEtqcUNiTkxLMzlFV1JDRTVrTFBqKy9lZXowWVJMbVZvbmRkZUdLSHFxMTVXcTBaR1gxaEZmdE1WS0pFTzRCc1V4UStIRjY3RWlHTldjajVMK0lvR3AxUW4zMkZ3VU5EdG9iMXBGRlhMV0FTVlRYUjJ2OEdvVVVVYnRWMy9yOHcvMHA4Y0ZoaVBCK2RobFBLUjJJcUd3V0RaeGd5L1cvelVtRi8zRlhlMW5oUTRwV1V3RHpLa29MRTczM3hIRkMiLCJtYWMiOiJlNjgyNTVhMjgxNGI5MTM0ODZhNDRlMTkwOTE0ZDlhNjAzN2EzZWJiNDE0ZTAxZDBmNDhjYjUzNGE5ZmUzODBhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:25:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1sUDR1T2M5Ykt3UlBXU0NMVHpDV3c9PSIsInZhbHVlIjoiR2RKanBGbkFSZU9YRU0rQnBvSU5ndkdNemh3TkhUcXJPaTAyK25wdWM4T1JLdkpiK2pVMXVHVmc1TUt4b0o5dEUzdXg2OHlWNFJHVlE0RC95dCtpR1hmano3K0gxU3JGZ2FrSnEybWdjYm1UVnQzdVp1S1AvZXJtT05mRWVKeDdGZktsZUtCcnU2V0hMZGdmREJKMXp0MEkvN2svbm5jbGQ3Y0dWeFJtdDZYZnRZL3VubExuaEtleXVhYzZSbmN0cmlaeGJUYTNwN216elZQWDdKQ2NVOW5aTVNJOHZlbGkwR2JRWFE5UWlMM1BYaUdML0lTbGhxQnZWdzVGcGZmRkF1aFJsT3FHc3ZFYmVnUXJiaEhxdmozSGFHSmlXMmQxeFBpVjdvazlxOXNvVExwb2tlNG9LY2x1c2hkRTE0cmUxSUFSNXVSQSttVW1mUWI3Q2FzSmtJemJ0dndOKzlwMzdLMUJnanhKQWlzZU9BTzBPN2ZVL0tRQkdVaDc2Y21hVytKanFBZ1d2QUMwRE03K05nMVZOakh1RU5CT3JxaXhTWW5TL0VhWWpteGhQS3pVemlYQjJEb1J1TCttT05BV2xPcGo1WHN3VW9PT1hRcnhSK1U3MHB4OGpFSktVaTQ2OEt0TzJJRHdkb2RRdDA5eFZyZUtBNDNpTjdnL3oraTQiLCJtYWMiOiJhNDhjMzUzNjZiMGIwNDhmZDBmOGVjNDA0NTU4M2RhZmJkN2E4ZGJlMTBjMDMyNTM1ZWQ3ZjlkMjlkZjU1M2ViIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:25:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1LMm53Tmp6aWxBbXRwMWIrdzdLd1E9PSIsInZhbHVlIjoiWGlmbGViTUpNUk5MMFI4WjY4TmFTcGJHQThhMjVOMEc0YUMycGdFd0JhVFJHOHhPbjdyNUJsY2hEdys2RjVydEtIelhTSk9DL3RhcUdCaWRveTJ2MWo2SGFHTktNWEJwajFRNlArYkRNMGp3TlRZYnZVa2IyU0U5bVljSWRyVGZicFgvaGVjQzRNR3lDd2phUVZCVXFFcHRUbzVqRG84T09SeTNJYnhHRDNFWE9Gc1VHT1o4T2xRREo2bHI4bG5uY25NcWVDZ0RZcCtBOXlRRG1scmJQdExXTnlLNG9pSzkvTWVCcnpYMjd4T2ZLWlNOWXpYM1ZROUNjVlQyeU5aendOMUlOOWRVdVF3L2ZUdG45b25TdWdzOTgyQXVNRWxkQkMrMG5LVGlYWkYwR0J2STc0dXREWHVHM2lHT0xOTVdQaEtqcUNiTkxLMzlFV1JDRTVrTFBqKy9lZXowWVJMbVZvbmRkZUdLSHFxMTVXcTBaR1gxaEZmdE1WS0pFTzRCc1V4UStIRjY3RWlHTldjajVMK0lvR3AxUW4zMkZ3VU5EdG9iMXBGRlhMV0FTVlRYUjJ2OEdvVVVVYnRWMy9yOHcvMHA4Y0ZoaVBCK2RobFBLUjJJcUd3V0RaeGd5L1cvelVtRi8zRlhlMW5oUTRwV1V3RHpLa29MRTczM3hIRkMiLCJtYWMiOiJlNjgyNTVhMjgxNGI5MTM0ODZhNDRlMTkwOTE0ZDlhNjAzN2EzZWJiNDE0ZTAxZDBmNDhjYjUzNGE5ZmUzODBhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:25:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1sUDR1T2M5Ykt3UlBXU0NMVHpDV3c9PSIsInZhbHVlIjoiR2RKanBGbkFSZU9YRU0rQnBvSU5ndkdNemh3TkhUcXJPaTAyK25wdWM4T1JLdkpiK2pVMXVHVmc1TUt4b0o5dEUzdXg2OHlWNFJHVlE0RC95dCtpR1hmano3K0gxU3JGZ2FrSnEybWdjYm1UVnQzdVp1S1AvZXJtT05mRWVKeDdGZktsZUtCcnU2V0hMZGdmREJKMXp0MEkvN2svbm5jbGQ3Y0dWeFJtdDZYZnRZL3VubExuaEtleXVhYzZSbmN0cmlaeGJUYTNwN216elZQWDdKQ2NVOW5aTVNJOHZlbGkwR2JRWFE5UWlMM1BYaUdML0lTbGhxQnZWdzVGcGZmRkF1aFJsT3FHc3ZFYmVnUXJiaEhxdmozSGFHSmlXMmQxeFBpVjdvazlxOXNvVExwb2tlNG9LY2x1c2hkRTE0cmUxSUFSNXVSQSttVW1mUWI3Q2FzSmtJemJ0dndOKzlwMzdLMUJnanhKQWlzZU9BTzBPN2ZVL0tRQkdVaDc2Y21hVytKanFBZ1d2QUMwRE03K05nMVZOakh1RU5CT3JxaXhTWW5TL0VhWWpteGhQS3pVemlYQjJEb1J1TCttT05BV2xPcGo1WHN3VW9PT1hRcnhSK1U3MHB4OGpFSktVaTQ2OEt0TzJJRHdkb2RRdDA5eFZyZUtBNDNpTjdnL3oraTQiLCJtYWMiOiJhNDhjMzUzNjZiMGIwNDhmZDBmOGVjNDA0NTU4M2RhZmJkN2E4ZGJlMTBjMDMyNTM1ZWQ3ZjlkMjlkZjU1M2ViIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:25:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}