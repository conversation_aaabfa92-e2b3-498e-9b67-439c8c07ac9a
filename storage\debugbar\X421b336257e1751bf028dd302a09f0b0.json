{"__meta": {"id": "X421b336257e1751bf028dd302a09f0b0", "datetime": "2025-07-14 18:07:09", "utime": **********.662676, "method": "GET", "uri": "/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 259, "messages": [{"message": "[18:07:09] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55605, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556252, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556329, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556401, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556476, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556557, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556626, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55669, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556753, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556835, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.556927, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557073, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557138, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557201, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557269, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557332, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557395, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557468, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557539, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557603, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557667, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557731, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557795, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55786, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557928, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.557995, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55806, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558132, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558198, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558265, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558328, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558393, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558467, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558532, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558595, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55866, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558723, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.558791, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.55902, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559216, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559343, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559446, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559511, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559575, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559641, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559707, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559778, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559854, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559919, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.559983, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56005, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560114, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560177, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 18.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560242, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560304, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 18.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560367, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 19.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560432, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560502, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 19.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560569, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 20.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560635, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560698, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 20.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560767, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 21.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560837, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.5609, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 21.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.560963, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 22.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561029, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561092, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 22.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561162, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 23.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56123, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561293, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 23.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561357, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 24.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561423, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561486, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 24.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56155, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 25.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561615, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561678, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 25.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561744, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 26.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561818, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561885, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 26.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.561949, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 27.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562016, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56208, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 27.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562143, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 28.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562209, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562272, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 28.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562334, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 29.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.5624, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562484, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 29.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562553, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 30.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562622, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.5627, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 30.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562773, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 31.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562841, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562905, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 31.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.562974, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 32.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56304, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563108, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 32.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563175, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 33.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563242, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563305, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 33.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563368, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 34.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563433, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563495, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 34.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563557, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 35.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563623, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563697, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 35.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563769, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 36.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563838, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563903, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 36.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.563966, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 37.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564031, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564094, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 37.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564156, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 38.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564222, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564284, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 38.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564347, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 39.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56442, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564487, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 39.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56455, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 40.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564615, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564677, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 40.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564744, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 41.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564812, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564875, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 41.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.564938, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 42.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565005, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565067, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 42.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56514, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 43.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565207, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56527, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 43.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565332, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 44.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565399, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565463, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 44.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565526, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 45.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565591, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565655, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 45.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565718, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 46.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565794, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565859, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 46.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565922, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 47.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.565987, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566049, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 47.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566112, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 48.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566178, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566241, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 48.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566303, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 49.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566368, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566437, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 49.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566502, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 50.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566568, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56663, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 50.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566693, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 51.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566763, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566827, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 51.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566891, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 52.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.566956, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567019, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 52.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567089, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 53.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567158, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567222, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 53.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567284, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 54.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56735, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567412, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 54.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567475, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 55.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56754, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567604, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 55.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567666, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 56.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567742, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56781, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 56.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567875, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 57.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.567943, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568006, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 57.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568069, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 58.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568135, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568197, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 58.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568261, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 59.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568327, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568396, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 59.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568461, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 60.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568528, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56859, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 60.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568653, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 61.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568717, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568787, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 61.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56885, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 62.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568915, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.568978, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 62.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569048, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 63.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569116, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.56918, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 63.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569249, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 64.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569314, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569378, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 64.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569441, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 65.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569506, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569569, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 65.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569631, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 66.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569703, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569773, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 66.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569838, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 67.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569904, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.569973, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 67.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.570036, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 68.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.570101, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570169, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570232, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570295, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570367, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570433, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570497, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570563, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570626, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570688, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570772, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570837, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570901, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.570967, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571056, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571121, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571186, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571249, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571318, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.57139, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571507, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571749, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.571906, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572065, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572243, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572343, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572419, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572503, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572576, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.57264, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572704, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572776, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572842, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572913, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.572983, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573049, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573113, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573178, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573243, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573306, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573371, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.57344, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573503, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573575, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573641, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573711, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573785, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.57385, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573918, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.573984, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.574049, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.574112, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.574183, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.574252, "xdebug_link": null, "collector": "log"}, {"message": "[18:07:09] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.574316, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.023209, "end": **********.662901, "duration": 0.6396918296813965, "duration_str": "640ms", "measures": [{"label": "Booting", "start": **********.023209, "relative_start": 0, "end": **********.429609, "relative_end": **********.429609, "duration": 0.4063999652862549, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.429619, "relative_start": 0.40640997886657715, "end": **********.662903, "relative_end": 2.1457672119140625e-06, "duration": 0.23328399658203125, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55233288, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x invoice.view", "param_count": null, "params": [], "start": **********.543168, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.phpinvoice.view", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Finvoice%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "invoice.view"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.59023, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.594924, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.636279, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.649759, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.652209, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.652841, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET invoice/{invoice}", "middleware": "web, verified, auth, XSS, revalidate", "as": "invoice.show", "controller": "App\\Http\\Controllers\\InvoiceController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=377\" onclick=\"\">app/Http/Controllers/InvoiceController.php:377-413</a>"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.018930000000000002, "accumulated_duration_str": "18.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4701781, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.331}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.483313, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 18.331, "width_percent": 3.434}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.499803, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 21.764, "width_percent": 3.856}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.502144, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 25.621, "width_percent": 2.43}, {"sql": "select * from `invoices` where `invoices`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.508274, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 28.051, "width_percent": 2.8}, {"sql": "select * from `credit_notes` where `credit_notes`.`invoice` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5123591, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 30.851, "width_percent": 1.585}, {"sql": "select * from `invoice_payments` where `invoice_payments`.`invoice_id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.514151, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 32.435, "width_percent": 1.426}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.515677, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 33.862, "width_percent": 1.321}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.518374, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 35.182, "width_percent": 1.902}, {"sql": "select * from `invoice_attachments` where `invoice_attachments`.`invoice_id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5199862, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 37.084, "width_percent": 1.69}, {"sql": "select * from `invoice_payments` where `invoice_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 390}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5214272, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:390", "source": "app/Http/Controllers/InvoiceController.php:390", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=390", "ajax": false, "filename": "InvoiceController.php", "line": "390"}, "connection": "kdmkjkqknb", "start_percent": 38.774, "width_percent": 1.479}, {"sql": "select * from `customers` where `customers`.`id` = 8 and `customers`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 392}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5240371, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:392", "source": "app/Http/Controllers/InvoiceController.php:392", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=392", "ajax": false, "filename": "InvoiceController.php", "line": "392"}, "connection": "kdmkjkqknb", "start_percent": 40.254, "width_percent": 2.483}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 397}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.52565, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:397", "source": "app/Http/Controllers/InvoiceController.php:397", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=397", "ajax": false, "filename": "InvoiceController.php", "line": "397"}, "connection": "kdmkjkqknb", "start_percent": 42.736, "width_percent": 1.532}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 398}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5276408, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 44.268, "width_percent": 3.17}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'invoice' and `record_id` = 3", "type": "query", "params": [], "bindings": ["invoice", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 401}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5298321, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 47.438, "width_percent": 2.43}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'invoice'", "type": "query", "params": [], "bindings": ["15", "invoice"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 402}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.531934, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:402", "source": "app/Http/Controllers/InvoiceController.php:402", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=402", "ajax": false, "filename": "InvoiceController.php", "line": "402"}, "connection": "kdmkjkqknb", "start_percent": 49.868, "width_percent": 1.162}, {"sql": "select * from `credit_notes` where `invoice` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 404}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.53337, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:404", "source": "app/Http/Controllers/InvoiceController.php:404", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=404", "ajax": false, "filename": "InvoiceController.php", "line": "404"}, "connection": "kdmkjkqknb", "start_percent": 51.03, "width_percent": 1.057}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 99}, {"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 136}, {"index": 22, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 30}], "start": **********.547463, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 52.087, "width_percent": 3.011}, {"sql": "select * from `invoice_bank_transfers` where `invoice_bank_transfers`.`invoice_id` = 3 and `invoice_bank_transfers`.`invoice_id` is not null and `status` != 'Approved'", "type": "query", "params": [], "bindings": ["3", "Approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 726}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.58319, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "invoice.view:726", "source": "view::invoice.view:726", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Finvoice%2Fview.blade.php&line=726", "ajax": false, "filename": "view.blade.php", "line": "726"}, "connection": "kdmkjkqknb", "start_percent": 55.098, "width_percent": 3.909}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 728}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.585199, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 59.007, "width_percent": 3.487}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.591028, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 62.493, "width_percent": 2.694}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.592983, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 65.188, "width_percent": 1.796}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.597548, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 66.984, "width_percent": 1.849}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.599554, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 68.833, "width_percent": 2.007}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.636829, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 70.84, "width_percent": 4.754}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.639599, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 75.594, "width_percent": 19.229}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.645175, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 94.823, "width_percent": 1.796}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.647391, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 96.619, "width_percent": 1.743}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 6214}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.650201, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.362, "width_percent": 1.638}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\InvoiceProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=1", "ajax": false, "filename": "InvoiceProduct.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 78, "messages": [{"message": "[ability => show invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-31129569 data-indent-pad=\"  \"><span class=sf-dump-note>show invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">show invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31129569\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.506851, "xdebug_link": null}, {"message": "[ability => send invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-337682322 data-indent-pad=\"  \"><span class=sf-dump-note>send invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">send invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337682322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.549864, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2081858112 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081858112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550172, "xdebug_link": null}, {"message": "[\n  ability => create payment invoice,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-539874445 data-indent-pad=\"  \"><span class=sf-dump-note>create payment invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">create payment invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539874445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550533, "xdebug_link": null}, {"message": "[ability => show invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-998684075 data-indent-pad=\"  \"><span class=sf-dump-note>show invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">show invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998684075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.55098, "xdebug_link": null}, {"message": "[\n  ability => delete payment invoice,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1636707561 data-indent-pad=\"  \"><span class=sf-dump-note>delete payment invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">delete payment invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636707561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.582646, "xdebug_link": null}, {"message": "[ability => edit credit note, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2097537720 data-indent-pad=\"  \"><span class=sf-dump-note>edit credit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">edit credit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097537720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587648, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1547993590 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547993590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587899, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1074179279 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074179279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588878, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1603186186 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603186186\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.601905, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602269, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602483, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602658, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602959, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-732428212 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732428212\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603272, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1706352150 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706352150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603549, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-589340600 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589340600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603866, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-968906466 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-968906466\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.604135, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1010438643 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010438643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.60441, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-794601125 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794601125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.604739, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-269988749 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269988749\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.605199, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1881146630 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881146630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.605729, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1588539635 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588539635\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606476, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2023812989 data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023812989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607069, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1202170555 data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202170555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607366, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2008052410 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008052410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.608367, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1638108641 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1638108641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.608901, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1785492162 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785492162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.609999, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1839783005 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839783005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.610575, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-339712863 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339712863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.611028, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1235422546 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235422546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.611522, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1766514752 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766514752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.612172, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-133923820 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133923820\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.612805, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-311477943 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311477943\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.613438, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2112689131 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112689131\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.61409, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2095273962 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095273962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614608, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2041688826 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041688826\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.61512, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-76587192 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76587192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.615735, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1323408790 data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323408790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.616342, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.616913, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1634180377 data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634180377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.617495, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-219872889 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219872889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.618055, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2102294273 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102294273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.618623, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2025865312 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025865312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.619184, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1481746089 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481746089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.61981, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-722396942 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722396942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.620434, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1060445653 data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060445653\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.620866, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621592, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.622083, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.622667, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.623186, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1245761738 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245761738\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.623537, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-61434305 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61434305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.624047, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-482676855 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482676855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.624371, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.624623, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625057, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625522, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1869235638 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869235638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625738, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625936, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1806958273 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806958273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.626124, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1993188776 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993188776\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.626295, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1531654884 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1531654884\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.626474, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1277066886 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277066886\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.626819, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-771556858 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771556858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.627059, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-488146881 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488146881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.627301, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1780357053 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780357053\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.627555, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-219589195 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219589195\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.628335, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1381612498 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381612498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.629106, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1177597781 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177597781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.629915, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-943777790 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943777790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.630689, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-969824929 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969824929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63149, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1052456815 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052456815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.632301, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-315054690 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-315054690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633201, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1877963140 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1877963140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633995, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.634774, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1155660657 data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155660657\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.634949, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1961124301 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961124301\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63562, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1105657915 data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105657915\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.635904, "xdebug_link": null}]}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-813410008 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-813410008\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2021115471 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2021115471\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1004693054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1004693054\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-624221542 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515944651%7C12%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdHQnpGb0locW9Rdit4V0ZHTVdKZlE9PSIsInZhbHVlIjoieFRnKzkwZlRSczBkT05wYmJyV3NhVS9GNUt3cHoreE1yTE4wbktyeHJ0ZUpaU05kM29SS3d3UlFiSjEzSXlOMkhIU2RLbVpqNDZtc1Z0TXZLU1FzWVI5RkZGUUkrUFYzeG1xNS9jcFpxa3JsTVk0K1VsZFROcE9ZZzdkY3RFKzZlTS80WUdYRG5TQ0c1NTZlOHpyekF6cStLbzJSOGMrWm1OVWZPbzJBT0M1eEJadFdiQ1dkWjdJKzdZdHNvNElvWnQwKzJZdzVDUFhXMzI4eU5adEp1QnkvTXFoNWNOTFRJVXUxdGRCUnJXMXc2Qm4vZjVwODhjZDgreHVCRUkwRERyVXQxQTltY1pHd1hCOGVEQ0IybEU5QlVvM3YvREVpbE9NWi8vbGlqMFczTWd3YStObStNdXFHYkQ5UUNGOXk2cDZ2V1dBOWoxZHhxbFZHRHZhdFFUYVlKZlJ5RHJoZ0U4M0plMW5URThPcUl3eWF3bDY4dm10RlBvd1BVeXdwUDhRYS9iTWxhZm9YeUxnWWwyK093MkpOWEtVWjBOUHFQZm5OZDB6bG9IQTVJbHhTNXB2cFRwS29icSs0TzdNVXJYZzNzcXhiTXNKT2V0MWxxTUZYbEpadUMzYjY3YkI3TWp0Yjl6Z3poRGtIWkZ0dlBaZE5lN0VYWjk5cXZHQVgiLCJtYWMiOiI0NmE3NGE4YjEzMDc4YmE5ODFjM2E2NzJiNDAzMGVkMjA2MjhhM2RjYWQ1YWU4MjA3NTdiZWFkM2JiNWZkZGYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9sSmZsUHN5emdUS0ExaVRVcUo1bmc9PSIsInZhbHVlIjoiazEyYWhyN0k1aXhXR3N0bnhGVkQvMHliMVpYU2V0YVlGOTRjVUxGZ0hsTHFGMVdiRW9EZTIveWdSU3U0U1p2dE51MUdHK1dkaVVDZ25JMnFwMmhjV1ZYYVVUb3Y1MXMzWHU5OFZURlNjd2xXdXBiN0RiOVhRR3l3Y29hSWhHSE44UjF6M256Y3lEbFpTVWZWbFBaUEVrYW5nOFVEZ0ZCWFNZYWFVYUlOVis2dEhPUnpQVnJWcVl3RUVUVFVRVG51WjFZWVJMODNRY0NnQ0xGS2pwVjhOQUl1U1M1SmFib0VnYU5iSm9QS3NZRGJCK2FNV0tYNkttTFQ1QlozcjREUEc5U1F6d1ZBMk9qS0ZNU2YrRUNGVlJDSUJBbkdaR0E0THlyLzlEYzFydlRRNDR1eVl3ME5tV0Q5ZVk3RVJCSThZR1liaTM4dEttYWVMbThXZVlGRi9YZW5DRmsrRkNKTVlxaDJuTVRzM3R6OXY3aVpPSDB0OFRabzFJcmZ2NTM3S0Nad25ycHd4VlZKaFBCeklZMHdlSmtWcDZYekF6SlBtNnc3MCtxQ1Erd2l0ZnE0WWJRVkdUeW9QRnRpQlVDRFl6dEwvVDUzOVI1OHc2TnFOaEI3blNIREhzakxSdU5yZjF5VXl6cVRsKzl2K1E1OGEvWHY2dnlnL0oyZzhVTTYiLCJtYWMiOiIwZWQ3NjQ1NmRmZDljZDJkZDI4ODZjODg1MjZkMzg4NzZkYTA4YWE1MzBhNmY0ZDljMDgzMzg4YjEzMDhlYTliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624221542\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-715991672 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715991672\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1196217056 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:07:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjYxSExrUmpkVGlNRzJSYytZK0pkcnc9PSIsInZhbHVlIjoiT1lDblI5VlA1cXJUdjdTcVh4R1d1ajB4ZndDbFJvenByZjBud3NOTUJ0a2hxaHVRbzBxR3VqbEZKb2dSYkJ5ckZ1dDV0d3hIbmVMdDRZcWg5Y0JtOGRPNW8rQzNKcnl3OFl0MXhYWFkyeStLeVhYNUJJbVFTVGJXWk0wekh4OHA4MktOOGkyVWJBamh1d2VrTlloaU5id2l0RTFBblJwVVE3ajcyajhTREMybytadElpYUdwNmJmblN0OSsrNUF6NWxYLzVxbFVGbHBTakxmbFoxcmZaL0pOWW9XN2pYVjRRTWppZkJuQ0F1ekdGNHdpYWw2dGcvRWRqWWdWeWRqZW43VnVuenFVZmtMZUd5WDdYN2NsdjV4c0piY0d1aktkRlhuODE5bC8rZHYrQTV5eDRGSk9Gb2dYM2lVb0VGeGFtbHF1K25Yc3YzODJWcXhWS3M1aHIvSC8wTHpHb0JTbVZwTC9La3Nka1FzQUZDY29qdUxZUGZRMkx4c0lBR1kzNUpvalQvWlk5VVhlemJwZVlaeDZoeHJ2c05HbGpvZVF4aVFQZWpMWXkvVW1JU2JGeDRTMnpFZmEyNkVYdmZXNDRDdy9sWjdXekJEWTVjODA3Zi82dnNzbmFFUlFmNFE3Nng3Y3NlUk5USkREdW9JWWVyeldURkdzNlFqaVF0UWwiLCJtYWMiOiJiZTRhYWE5YjVmMTg0Yzk0NzllNmQ2ODZmODQ3MTE1YmNkYTNkZWU3NGZlMDFlZWI1ZDMxYTRiOWIyZDdlMzU0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:07:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlUwMnVwNDVJdmxlNXEzV2RoYW1NMWc9PSIsInZhbHVlIjoiUHYyUStidW8yQUJiUXZWeW9LTE5oVXFIMGROY0t6QzlIbnpDYXJ1WGNad2tidGVESjRDeE5CRVdNWGJqci9jWmQ4ZHh0c0FtUHlkNTFBZ3AzU3JnTXdCY1VoL2NOaERyMUZiZGdzbVJyT3hmVklHQmFhV05XNmhaVHhQTnZGWXhWelRKT3JIZHRxODA0OUgvUjgyUFlBSmduSTQ0Qy9qSkhKSExaNzNKR2ZjOWhWMXo4dTd2TnZDeU05VkJ3b2hsREE4aW0vUkdmY2J6YWhqRXkwd1U0cFNTVGtpV2xXMGI3UXBrbkF4MjF5S2ViN3FuLy9hUUtXU2ZFT1JCaGR6eVlmVTFBY1FBM00xN1JTTFNYNjNzZDhod3JpczJicVNGa3N6MFAxcHhEZ1RNdFJCYm9uV1Y2enFFSUxWSFp0LzRUampjWVpBdXVXa2t4UmF0YnV4NlZHZ0lid2o4b2MyUHBIRnNncXAwNlJMSGFKYWQ3Z2RhTFMwb3NIbVRPYU4wSkFndnJPN1ZJSEg3bFNnOUFqN1A3SjdwSzVVdUFsaHo5NFhCVVNWU1dxOHV5c0JjOUdlU1lxSnA1RWl1cDNiZjZPR1l4aWI1K2xwN3VWWjVnVUpKOTk2Mi9PT2htS0c2bTQ4dkNDOEpwa1FFNktNT0xrc2ZOM1p5aG1TY1ovTi8iLCJtYWMiOiJhNWM1NWRkODBjNDE2NjVlYmM3MzQ5MmJjOGI2NzdkZWU5ZmFiMDdhNjVjOWZmNjdkZTE3MDZiNmQ5OGVlYjVkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:07:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjYxSExrUmpkVGlNRzJSYytZK0pkcnc9PSIsInZhbHVlIjoiT1lDblI5VlA1cXJUdjdTcVh4R1d1ajB4ZndDbFJvenByZjBud3NOTUJ0a2hxaHVRbzBxR3VqbEZKb2dSYkJ5ckZ1dDV0d3hIbmVMdDRZcWg5Y0JtOGRPNW8rQzNKcnl3OFl0MXhYWFkyeStLeVhYNUJJbVFTVGJXWk0wekh4OHA4MktOOGkyVWJBamh1d2VrTlloaU5id2l0RTFBblJwVVE3ajcyajhTREMybytadElpYUdwNmJmblN0OSsrNUF6NWxYLzVxbFVGbHBTakxmbFoxcmZaL0pOWW9XN2pYVjRRTWppZkJuQ0F1ekdGNHdpYWw2dGcvRWRqWWdWeWRqZW43VnVuenFVZmtMZUd5WDdYN2NsdjV4c0piY0d1aktkRlhuODE5bC8rZHYrQTV5eDRGSk9Gb2dYM2lVb0VGeGFtbHF1K25Yc3YzODJWcXhWS3M1aHIvSC8wTHpHb0JTbVZwTC9La3Nka1FzQUZDY29qdUxZUGZRMkx4c0lBR1kzNUpvalQvWlk5VVhlemJwZVlaeDZoeHJ2c05HbGpvZVF4aVFQZWpMWXkvVW1JU2JGeDRTMnpFZmEyNkVYdmZXNDRDdy9sWjdXekJEWTVjODA3Zi82dnNzbmFFUlFmNFE3Nng3Y3NlUk5USkREdW9JWWVyeldURkdzNlFqaVF0UWwiLCJtYWMiOiJiZTRhYWE5YjVmMTg0Yzk0NzllNmQ2ODZmODQ3MTE1YmNkYTNkZWU3NGZlMDFlZWI1ZDMxYTRiOWIyZDdlMzU0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:07:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlUwMnVwNDVJdmxlNXEzV2RoYW1NMWc9PSIsInZhbHVlIjoiUHYyUStidW8yQUJiUXZWeW9LTE5oVXFIMGROY0t6QzlIbnpDYXJ1WGNad2tidGVESjRDeE5CRVdNWGJqci9jWmQ4ZHh0c0FtUHlkNTFBZ3AzU3JnTXdCY1VoL2NOaERyMUZiZGdzbVJyT3hmVklHQmFhV05XNmhaVHhQTnZGWXhWelRKT3JIZHRxODA0OUgvUjgyUFlBSmduSTQ0Qy9qSkhKSExaNzNKR2ZjOWhWMXo4dTd2TnZDeU05VkJ3b2hsREE4aW0vUkdmY2J6YWhqRXkwd1U0cFNTVGtpV2xXMGI3UXBrbkF4MjF5S2ViN3FuLy9hUUtXU2ZFT1JCaGR6eVlmVTFBY1FBM00xN1JTTFNYNjNzZDhod3JpczJicVNGa3N6MFAxcHhEZ1RNdFJCYm9uV1Y2enFFSUxWSFp0LzRUampjWVpBdXVXa2t4UmF0YnV4NlZHZ0lid2o4b2MyUHBIRnNncXAwNlJMSGFKYWQ3Z2RhTFMwb3NIbVRPYU4wSkFndnJPN1ZJSEg3bFNnOUFqN1A3SjdwSzVVdUFsaHo5NFhCVVNWU1dxOHV5c0JjOUdlU1lxSnA1RWl1cDNiZjZPR1l4aWI1K2xwN3VWWjVnVUpKOTk2Mi9PT2htS0c2bTQ4dkNDOEpwa1FFNktNT0xrc2ZOM1p5aG1TY1ovTi8iLCJtYWMiOiJhNWM1NWRkODBjNDE2NjVlYmM3MzQ5MmJjOGI2NzdkZWU5ZmFiMDdhNjVjOWZmNjdkZTE3MDZiNmQ5OGVlYjVkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:07:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196217056\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1045018182 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045018182\", {\"maxDepth\":0})</script>\n"}}