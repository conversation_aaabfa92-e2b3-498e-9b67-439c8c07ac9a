{"__meta": {"id": "X6f88a7e9ee40809dc86d2c9aada036d6", "datetime": "2025-07-14 17:58:26", "utime": **********.627347, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.145667, "end": **********.627363, "duration": 0.*****************, "duration_str": "482ms", "measures": [{"label": "Booting", "start": **********.145667, "relative_start": 0, "end": **********.543779, "relative_end": **********.543779, "duration": 0.*****************, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.543788, "relative_start": 0.****************, "end": **********.627365, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "83.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02445, "accumulated_duration_str": "24.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5760272, "duration": 0.02155, "duration_str": "21.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.139}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.608082, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 88.139, "width_percent": 4.744}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.61638, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 92.883, "width_percent": 7.117}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C**********042%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhBTTJ5ZFFkaEI1a3JBWEFLekU2NUE9PSIsInZhbHVlIjoiMlIyVTJjdDBLMzJ2aHJ4TXZ6ZEZaam1VYVZkWjIzVnZwSWQxc2MxYUZ2QkFrdldHdTdxcDcvQ3ZsM28rRy9SQ3NWVU56c0lQWjlXVEQybTVnQmR4cXNwV21MdllXM2R2RzdMUlJnRHBLMW1GUjNpVC91Y051cXdrREtYNWRhNGVMMFNIUHZWNUJWUU1JRnpVblpIRno3ZE91VkdpbzN6TEFmWURIeGkvTFQyTXN1N2VZUlVyOU5Oc2Y0UVFkVGVyUkNhTkplaE5hTDBRZ3cyZEFid0xYcW0yMXNUQnVrYlVkd2hLQnhuZldaa2EwQWwrV3hrY1BjL3VIT01pVE1Cc0ZSa2s3TnBpOTdkWG1XcGREQ0JiQ0JmOW5HSm9WZWxHb3BZc2ZqSWhRSWtyQlNwOTNJa1JWN09SWnl4bEE0T2gvWUFvQzhBck5QQWlMMmRmeEwvcHEwU0p5dEZhUW82cHdGb1V6MjlCQUNYUmtXTUxtNVFNRjZBN0NHQXZ4QkxNWERXTzNVZTBJREhZNWxOamRsVGtqSjF1YUx0SUZrL2ZTSHltcUNwZnIwbHRjZEllN0l2bnF0OGhscEM4cHpqY2Qva1A1VjlFVmJWaVdGR1BxYjA2MS9icmpVSDVwVmZYa0IvT1h0dFp6c2UrSXhIWWovOXJobXpxZVZlZW1kc0UiLCJtYWMiOiI2OGZlZGIzMDM0YjFhZGZhNjY1MjAyYTMxNTQzOTVlZjk4MDY3NDg1NDJiZDE2MzJiOWM2YjNjMDJiZjdhZTIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFuZTFXcHM5Ym5QNkdwZTc3ZzZ4T1E9PSIsInZhbHVlIjoiNWRMc0dUUzQwd2hUV3dUcDI4TjdOZHpZMzU3L2pWYlBUZWpNdlRSOElLQUlZakpLR2RYdjFHQkxBTlAzQ0pqWU9xZGMrcGRRUnRUS1J0MGM2cGFvN3hQWStPaXEraXFuOHZVNVBCNkEzMGFIMk9yUDlPSy9mazM3M1VCRnJ0QjZFSTZFczMyU09YNHNPYVF4TEJKcnZBaHVuVDIydTBnNnV1K2xCWEpjOXQrbVJaU1lIbkJ5YXlqdjhWTm9hOWV2QXFwMndPSUhqQlRkZlJEcVNhZTRVSlhvMzZLZFMyQVF0SjBrcFBpRUNJRFc1U1JmZC9BTlNSZnhPMFF0N29PUE1CR2NFbGg2ZjhPU3B6NjVjQjIzcDg4NmwwMjBsYVpPQWFxd2FkQXRGVzZTYmlVZFJvNnA5cktpMVEwRzBwc0dwMzdsSU1hVHJubGMrbDd2MEtZeWZvYXlscU9NOU9rZXZaVGlZTm5pUkRiSWx3OExKRTdpOW1CRUlpZmVRRllMR2JFbEZkK3hFeTVNeWhyTk9mQzlHdWJtSGZCR0pUclBZS1V4RFhyWUpPMGNKT2VrWTAzRWRGQzBRWTFsYUlZQUw5aUhTSTJidGZrekUrcFdoQTloQ3d6VE4ycEFQM1kwSE81WVplSUlzVWkrbkZrWXZ2T3NlU1plcy9lTTE0R0ciLCJtYWMiOiJjOGY1YTk0MmQzOGI5NWUyZDc0NDdlOTMwZjE3MjJlNDFlYjBiODU4MzI1MzhhMDEyMGQ2ZjNjNzM2MmZmMjg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1572879720 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572879720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2100226792 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRTTE9GeVpFUUgzR1hpY2ZRUTJxdUE9PSIsInZhbHVlIjoiTUlRbWZRWXg1aVRzc0h5QjhCMk5EZmhtQzNPU0tBUzF4NjNkTmM2VHM0UFNFZ0lmazlWRk9vWWNuQWd2bkJtUFg3T21iY2lGanJSQWxwYmZXMnBQcjN2U2psc0NUYUNKTjFPNUFRemtIcWdaQ2E4NzhwTTVEdWh0a3VoR3VjRDU2UkxTVU1xRDE1WXV1THF2R1Z1VnNWeThnaGdMVW9xVkVhbnZmWlhFQmFKY3hrSGN4OGhnVE5PZmdaUjdyR3E2eFpsOVA2NFlQS2JXQzEvYUNTOSs1clJhaXM0UHZOdUtkWjFPelQ4T01FVW4zN3FtK0crWWp1c3E2WGFKUjdkQnZRWS9QK2ZZbWtPWEltZjBmNkhMWUFwSUlJc1BxNC81bXpONFRoN2ZQVm1leSt2NFBIVE1wSENPa3ZMSklmSjVWWDhrbVV6bXZyUW5ydXQ2TUd2bFBUMUp0Q2tVanJXM3J5ZlFMNWcrYyt1N3ZQUjZHQlh2Y0drOWR6eSsrQWlzdEZnTXQ3a0k0Z1FES1dlMkgvTnRXdjZjRFcxTTk1My9YamhSamtCS2RjSklDMnZaVURINUxUckdXRGt6ZlJ5MklwSll1VW92MzlldDJDTzhrUHlhOVBoZ3h0bHBMelp1V2FjRnhDaWhYQVJ2NmVBUGpzVldFYnVPTjJpSE5KTWwiLCJtYWMiOiJhOWMwZGFkMWZiMzVjNWZmZjQ1ZTE4NGIyMDlmMjA4NWEwNTg2NmFmODVhOWE2YTc4NzYxNWMwYjU1ODcxYWYxIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imd0aE9mbUVTc01leENMMmlhRXpQd1E9PSIsInZhbHVlIjoieXZZUnE5azJkSSs5ek5jY1ZGdk5XaXJ3QnJMMzdVbnR6ZDdSbmQ3c2NaQUZrS1RuT0FNVjQ1ek1OaXVBeDZzakhLb2V2bUpSNUc1TEtnbjdOTUp2ZUh0Y3dIUTMxNklGMnJEdm9adEw5TjA5bGtLZXNHcW9ReHB5aTQ3K1E4OURyQzBTR1d0bXRveGNRbFA0WDZrbSsvZ2lTcEdla2xRUG9ZQURsMWJMTmhvSGxJcFZVZ2I5eVVHVmdDd3NaVlYrblQyTktEdHc3MXZHVVU2cy8yQldoLzQrOTFySi96WllmTXQzUWkyaGg3eTNLYW1rSVY1ajF3UVFQckFLWW9KUERWQUY3SjM0cThONnpNVVgyVE9tUkhHK2VkR3o4V3A1UFNzWnJoRjM1dmRBTkpzTDIvOW9CZitUTWd5T2E5TFNMN2dnMjNjR09IT2NUbVJKcmJyN01jNDJIaWd5WGlzSW5xT040cGRhcVdJSXYwb2VpT2MvN1NRem0rankxSVAveGZEdGV6YkpwdU9iMThNbDBXSXlvVmFUaWFnSEZDVW85Y3BlbElUR28wTmt3TS85eDNnZFkwNkNjT3BneERPa1lCYlN0RkVVczRVZ2NvcXh6cEtpVTVtZDJiMWp2UnRoSFIzdEZUdE5XbnVDRE44ZUc5dnFOa0JWcnh5OEFqYmciLCJtYWMiOiJkNjMwYzA3YTJmYTZjZTdhYTNhOTUwOTUwYjdlNzc2N2ViMWVkZmVkZmYzY2E2ZjliODMxODU0ODg1MTkwNmEwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRTTE9GeVpFUUgzR1hpY2ZRUTJxdUE9PSIsInZhbHVlIjoiTUlRbWZRWXg1aVRzc0h5QjhCMk5EZmhtQzNPU0tBUzF4NjNkTmM2VHM0UFNFZ0lmazlWRk9vWWNuQWd2bkJtUFg3T21iY2lGanJSQWxwYmZXMnBQcjN2U2psc0NUYUNKTjFPNUFRemtIcWdaQ2E4NzhwTTVEdWh0a3VoR3VjRDU2UkxTVU1xRDE1WXV1THF2R1Z1VnNWeThnaGdMVW9xVkVhbnZmWlhFQmFKY3hrSGN4OGhnVE5PZmdaUjdyR3E2eFpsOVA2NFlQS2JXQzEvYUNTOSs1clJhaXM0UHZOdUtkWjFPelQ4T01FVW4zN3FtK0crWWp1c3E2WGFKUjdkQnZRWS9QK2ZZbWtPWEltZjBmNkhMWUFwSUlJc1BxNC81bXpONFRoN2ZQVm1leSt2NFBIVE1wSENPa3ZMSklmSjVWWDhrbVV6bXZyUW5ydXQ2TUd2bFBUMUp0Q2tVanJXM3J5ZlFMNWcrYyt1N3ZQUjZHQlh2Y0drOWR6eSsrQWlzdEZnTXQ3a0k0Z1FES1dlMkgvTnRXdjZjRFcxTTk1My9YamhSamtCS2RjSklDMnZaVURINUxUckdXRGt6ZlJ5MklwSll1VW92MzlldDJDTzhrUHlhOVBoZ3h0bHBMelp1V2FjRnhDaWhYQVJ2NmVBUGpzVldFYnVPTjJpSE5KTWwiLCJtYWMiOiJhOWMwZGFkMWZiMzVjNWZmZjQ1ZTE4NGIyMDlmMjA4NWEwNTg2NmFmODVhOWE2YTc4NzYxNWMwYjU1ODcxYWYxIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imd0aE9mbUVTc01leENMMmlhRXpQd1E9PSIsInZhbHVlIjoieXZZUnE5azJkSSs5ek5jY1ZGdk5XaXJ3QnJMMzdVbnR6ZDdSbmQ3c2NaQUZrS1RuT0FNVjQ1ek1OaXVBeDZzakhLb2V2bUpSNUc1TEtnbjdOTUp2ZUh0Y3dIUTMxNklGMnJEdm9adEw5TjA5bGtLZXNHcW9ReHB5aTQ3K1E4OURyQzBTR1d0bXRveGNRbFA0WDZrbSsvZ2lTcEdla2xRUG9ZQURsMWJMTmhvSGxJcFZVZ2I5eVVHVmdDd3NaVlYrblQyTktEdHc3MXZHVVU2cy8yQldoLzQrOTFySi96WllmTXQzUWkyaGg3eTNLYW1rSVY1ajF3UVFQckFLWW9KUERWQUY3SjM0cThONnpNVVgyVE9tUkhHK2VkR3o4V3A1UFNzWnJoRjM1dmRBTkpzTDIvOW9CZitUTWd5T2E5TFNMN2dnMjNjR09IT2NUbVJKcmJyN01jNDJIaWd5WGlzSW5xT040cGRhcVdJSXYwb2VpT2MvN1NRem0rankxSVAveGZEdGV6YkpwdU9iMThNbDBXSXlvVmFUaWFnSEZDVW85Y3BlbElUR28wTmt3TS85eDNnZFkwNkNjT3BneERPa1lCYlN0RkVVczRVZ2NvcXh6cEtpVTVtZDJiMWp2UnRoSFIzdEZUdE5XbnVDRE44ZUc5dnFOa0JWcnh5OEFqYmciLCJtYWMiOiJkNjMwYzA3YTJmYTZjZTdhYTNhOTUwOTUwYjdlNzc2N2ViMWVkZmVkZmYzY2E2ZjliODMxODU0ODg1MTkwNmEwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100226792\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1670924344 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670924344\", {\"maxDepth\":0})</script>\n"}}