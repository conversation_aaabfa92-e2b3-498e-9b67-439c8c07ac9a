{"__meta": {"id": "X53cca2cfd5984d413b0eccce617efa83", "datetime": "2025-07-21 01:34:21", "utime": **********.29702, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061660.846597, "end": **********.297034, "duration": 0.450437068939209, "duration_str": "450ms", "measures": [{"label": "Booting", "start": 1753061660.846597, "relative_start": 0, "end": **********.22592, "relative_end": **********.22592, "duration": 0.37932300567626953, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.225928, "relative_start": 0.379331111907959, "end": **********.297036, "relative_end": 1.9073486328125e-06, "duration": 0.07110786437988281, "duration_str": "71.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02013, "accumulated_duration_str": "20.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255589, "duration": 0.01877, "duration_str": "18.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.244}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.282844, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.244, "width_percent": 3.08}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.28917, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.324, "width_percent": 3.676}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=8&date_from=2025-07-01&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-446352704 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-446352704\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-483065919 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-483065919\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1045151163 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045151163\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-177504558 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-01&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=&amp;customer_id=8&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061655922%7C11%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVweGl6Umg1N085UmV4VVJiQkowaXc9PSIsInZhbHVlIjoid2ZRVjlIUG1KVUIxanlPNlNncEdHR0ZBZy9YbDlLUGcySFlERGR4QVI3SWJ0b3lESnE0RmlLdXRoNjNwM1JlU05nOGU2cy9PdytHSXhReEtJdDVNSmxIV09BWHRqeVV6QzdNTHR0ajMwbG5pL1U3ekNFWWdiSzNhYTdJMjFyQXhVNi9hQ2w1eDdhYkk5S3FBbmdZMklGRmJsTmdySW1ZOEk2UzVsbk5GbVFkWVUvbTl6L3JhRlEzUlpnUWR4YVllV0FTL1VHMlVKcHFMQzZHbUgzNHNzZ2lCSXQ1dVowYVh4Q0pkYkFhRHg2dUZOU2tZOWlMcWFCZFVIZ20vREEyME5MTitCQWV2cW00aWRFWCthRUxNdVpNa0kzSmxVekIydWcrRWVzZjBmMVBCb0dOaTljTUU4dVZacldBd2tsZjJWcndNM05xZHdYd2VhNW5ZYlRWYnFZdWZISG5OTC95Z1k5WlRZckJhSHJUTTRvUGxWc0VUTStPTlo0blU3eTJNT2tSRUlGc2c5M25ST2dEUjhKbjhFMHowM1R2Rk9XK2lHN1NJMmVRVWEzUHN2cmx6dXM1SUVONXlIRmhObndRWHM4VW1SMm5zWThDb0RiTHVlY3dZYjgrVit5amtIZERLM3ZLam9vajR0QU1jMkpVNzkvVEJuVTdReGNPUTRyTnoiLCJtYWMiOiJkZDgxN2ZjNjkxNzE4OGQ3ZTA1N2FlOWU0Yjc0MmM0OGYxMzI5NmUwYTA5MTJjNWZlZmYwOWVjZmZiN2Q3Mjk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlAvV3lpS3ZKaXFtTHhvbWJMUEJTSnc9PSIsInZhbHVlIjoieUxYbVJFaWNVSWxPU2pIalJQWDFNUmtNSFZXTFRGejNUeTJkeXppR2QrYXRRMEg0YzAwNHd3alZCZXpyOUlPUWNqc08rdkhRbDN3QXBjMVpiQkdPcURiejBRUWFFNkFRVVlJMi9zN1duWjdQMzF1ekxIellNWTZ0dlRRUkFUVzJYQTZ3U1JEeEtKL25CajNnSHNCM1pOYkVPN1VZZWZ0VWVUU3IyVS82dmpuMkE3STIzNzJDeGxlWG04cmhTeGxZeUlUQ1YwNTlHbmZWREw5TW5NNE52SWJqZytEVW9ORml5TTlvVjYxRndLNUg2Q3NPenJRUStrY3I3UEQyc2xtS0owV28rR3IvSzd2cVRoZ2ZTOUlzWGkwTVB6dHlhd2dGSlRmNER1ZkZmVktyZTlBYUdBTWw2R1ZVS3hwWDhCMnZZRW9wdTYrMzMwMjNJbmN3MVUzUS85N0F3emZUajZtazBTSXovb2trZ2tPc2t0NytMQ2RhMzBwblMrcXBtVzJnV1gzalVxcHBuWmVtenNzK3RraFBmN0RtZXh6QUIyM2x6eFQwdE9JUEFmV3ZUUG51ZG1hTmNrZkNJbWgvdjVOV1VzYlVTcCtoWCtOeHVPTjZBaCt2ejhISU5rNlZRSi9NRUlMZ3lZcXZtbUNaL1VzSHRKSS90ZnIvdTRjaHlQa0wiLCJtYWMiOiJhN2M4YTcwOGJlMzYzOWE2NTI2MDExMzYwOTcyN2JmYmNkYmRhMTRmZmM2NGVkOTA2NjY2OWI5MGI5YTE2M2ZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177504558\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-722556802 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722556802\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-284033018 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlE3eXFuaEpCSzJ3VVBLQWlqMWlZOXc9PSIsInZhbHVlIjoiVXJIcmJnNUtwbTRBQlI4SzNTRnU3NkhkeUMrUjlETTBTRjNHRlkzNTRqcUxPQTNNd2IzMUhWSy9PV1NyQUo3Yk9KR3pvUHN3WEZESHlpUEJ6cER2ajQ3Nmd4NXJNaFZSbWlxUEh1bE9zeEV5cVJzS0dqUFc3WWpkVWdScWo0dlJRYmVTOS95anZTVEFXbVFYY2ljQndJWFhZMUJaR2F3cjhuZ0RtMEdXTlo0Uy8yT05oMGIzWE5McnZQOEVjSldIZzUzVklHZFVCVnhhazVzdHdqMGxySGhkeU53UGdxbU5qTlZJSWRUcHFGK0h3OUFWeVhOZld4KzNXaGVBVUsxVWVpaE1aT05LUitmUVpHaXZsQm5xb1FzL2s3bjBJb1ZjQWpnMURaVC9wdHZ3MXZVdGkwMG5oM2ZkY0Jkd2ZjT1ZNOXdMQnpqb2ttdnNBd3NzWThsMFJNK3o5VTNUNlZONUk4Z2hGR0kvQUNodDNSUmd6QVA5cDRzc09MM1hTeXFySkNGekhWcHNtanNNUFlwd29KczlyK20xZndEQTVvekdKdmVEd0Y3UGZvY2g3akxhZjFjYUNuZ0FXWXpuaGtXN3pKemVXa0JLL2xVR2xyU29Ed3h3VHdSL3U1aThKcmxXRm1uYlhWT21Qc2JaWkRMdUhJaG5UdHJPdEExQ2RZQXciLCJtYWMiOiI3ZGNkYTlkMTIyMmFhZTY5OGNkOTI3ZjhlZWZjNDRmZDBhZTk3YTlhN2RlNTVhMjYyZDQ2NzI3MmMxZDliYmNkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFmM1lxTFpMMGRiYVRndnF0OFpFckE9PSIsInZhbHVlIjoiQnoxSGdFc29FR0draFVpZGExOExNcGc0QXZLQSs5ZmtRL0VmL0dCZXlRUTlMM2Q0ekk5cDBPb1pabi9XS0FGVkJkaXZVTHFHMHJHYVRrR0hZS2VCVHpuTzh6Zk1LSTlXSjZVKzQrMlp2Y0NEaUs0eDZUajIzdkxVQ2VmWDh3QzBqbXRKRUlQcHc4bDhRNFZVdHNKUWFUNnQ4UElJS2loM2tQMjFtc3RpczVXM3hHbmdYUk5uR0FZcnYzamlQQ3YxMFJHeFRhQXAvQnQ4bVVsTmt4dGtrUC9aUERuMWkzT0JtL1JqNFB2Y0wvNFJyN2M1UmVrcEN2QUc5d3B4c2x2WmcyMzJWdVpqN2JOL2tBZlJLSjg2Z0s3WWIwUk45UUY2bFVzYXVkYmRVMUo4UW1pRU5SSTRzNW5zMVpTakd3eTNiMFJUV1lJMmtyc3JaVitxNnpyTnBvN1pEYUtoQmpQOERuTG54N0lFZlhqNHBZYVprREtuQTNOM21nMXlJVU91czdaYnVXbmhCbXFxby9RcXFOV3Z1bE9YdXM2Z05QdXltKzlMbjZGcmxtditjN01JUVhlMGVCaUV5Q2hvRTJJYXhXNU5MN0Q4cGlpbDEvWEU0VGs3SFY4R1VocnQ2UWIrNnpXbHJPa2hhUGlmMzI5Y21yd1B1eDU2RXgrWUJOM2MiLCJtYWMiOiIyY2Q5YTM5YWU2OGQyMTdmYWU3YTBmMDA5ZDJjMWNkMjMwMzg5OTljNzFhZmM5YzExMDA1YTE1MWZjZDNlNDc5IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlE3eXFuaEpCSzJ3VVBLQWlqMWlZOXc9PSIsInZhbHVlIjoiVXJIcmJnNUtwbTRBQlI4SzNTRnU3NkhkeUMrUjlETTBTRjNHRlkzNTRqcUxPQTNNd2IzMUhWSy9PV1NyQUo3Yk9KR3pvUHN3WEZESHlpUEJ6cER2ajQ3Nmd4NXJNaFZSbWlxUEh1bE9zeEV5cVJzS0dqUFc3WWpkVWdScWo0dlJRYmVTOS95anZTVEFXbVFYY2ljQndJWFhZMUJaR2F3cjhuZ0RtMEdXTlo0Uy8yT05oMGIzWE5McnZQOEVjSldIZzUzVklHZFVCVnhhazVzdHdqMGxySGhkeU53UGdxbU5qTlZJSWRUcHFGK0h3OUFWeVhOZld4KzNXaGVBVUsxVWVpaE1aT05LUitmUVpHaXZsQm5xb1FzL2s3bjBJb1ZjQWpnMURaVC9wdHZ3MXZVdGkwMG5oM2ZkY0Jkd2ZjT1ZNOXdMQnpqb2ttdnNBd3NzWThsMFJNK3o5VTNUNlZONUk4Z2hGR0kvQUNodDNSUmd6QVA5cDRzc09MM1hTeXFySkNGekhWcHNtanNNUFlwd29KczlyK20xZndEQTVvekdKdmVEd0Y3UGZvY2g3akxhZjFjYUNuZ0FXWXpuaGtXN3pKemVXa0JLL2xVR2xyU29Ed3h3VHdSL3U1aThKcmxXRm1uYlhWT21Qc2JaWkRMdUhJaG5UdHJPdEExQ2RZQXciLCJtYWMiOiI3ZGNkYTlkMTIyMmFhZTY5OGNkOTI3ZjhlZWZjNDRmZDBhZTk3YTlhN2RlNTVhMjYyZDQ2NzI3MmMxZDliYmNkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFmM1lxTFpMMGRiYVRndnF0OFpFckE9PSIsInZhbHVlIjoiQnoxSGdFc29FR0draFVpZGExOExNcGc0QXZLQSs5ZmtRL0VmL0dCZXlRUTlMM2Q0ekk5cDBPb1pabi9XS0FGVkJkaXZVTHFHMHJHYVRrR0hZS2VCVHpuTzh6Zk1LSTlXSjZVKzQrMlp2Y0NEaUs0eDZUajIzdkxVQ2VmWDh3QzBqbXRKRUlQcHc4bDhRNFZVdHNKUWFUNnQ4UElJS2loM2tQMjFtc3RpczVXM3hHbmdYUk5uR0FZcnYzamlQQ3YxMFJHeFRhQXAvQnQ4bVVsTmt4dGtrUC9aUERuMWkzT0JtL1JqNFB2Y0wvNFJyN2M1UmVrcEN2QUc5d3B4c2x2WmcyMzJWdVpqN2JOL2tBZlJLSjg2Z0s3WWIwUk45UUY2bFVzYXVkYmRVMUo4UW1pRU5SSTRzNW5zMVpTakd3eTNiMFJUV1lJMmtyc3JaVitxNnpyTnBvN1pEYUtoQmpQOERuTG54N0lFZlhqNHBZYVprREtuQTNOM21nMXlJVU91czdaYnVXbmhCbXFxby9RcXFOV3Z1bE9YdXM2Z05QdXltKzlMbjZGcmxtditjN01JUVhlMGVCaUV5Q2hvRTJJYXhXNU5MN0Q4cGlpbDEvWEU0VGs3SFY4R1VocnQ2UWIrNnpXbHJPa2hhUGlmMzI5Y21yd1B1eDU2RXgrWUJOM2MiLCJtYWMiOiIyY2Q5YTM5YWU2OGQyMTdmYWU3YTBmMDA5ZDJjMWNkMjMwMzg5OTljNzFhZmM5YzExMDA1YTE1MWZjZDNlNDc5IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284033018\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1094133099 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=8&amp;date_from=2025-07-01&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094133099\", {\"maxDepth\":0})</script>\n"}}