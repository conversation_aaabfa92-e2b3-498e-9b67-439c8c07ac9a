{"__meta": {"id": "X27881460070a9c54eaf1f8fed439070d", "datetime": "2025-07-21 01:34:06", "utime": **********.6543, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.212077, "end": **********.654315, "duration": 0.44223809242248535, "duration_str": "442ms", "measures": [{"label": "Booting", "start": **********.212077, "relative_start": 0, "end": **********.587304, "relative_end": **********.587304, "duration": 0.3752272129058838, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.587312, "relative_start": 0.37523508071899414, "end": **********.654316, "relative_end": 9.5367431640625e-07, "duration": 0.06700396537780762, "duration_str": "67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991608, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01588, "accumulated_duration_str": "15.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.616544, "duration": 0.015, "duration_str": "15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.458}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6405332, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.458, "width_percent": 2.771}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.647062, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.229, "width_percent": 2.771}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=&customer_id=8&date_from=2025-07-21&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1220285415 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1220285415\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-625446410 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-625446410\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-35190696 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35190696\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1493401444 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"149 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=&amp;customer_id=8&amp;creator_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061636318%7C9%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVOR1RPQjBpMnduS3RkdmlwZGZUb1E9PSIsInZhbHVlIjoiTC9JZk5oWS94M211cHpmZ0JHUFJ5VU9VVm9rNS92WTBvOGJjWWRudlV1WVh6QXFuR0NLYkV2SHptMWV4NTA5KzlRMUpxSk5POVJzQU1wLzNZeWFCcnVYTXNjVUViSUU4eFlYNW9HbDJkcnpabjRJYldjTjZsZWVwZXpGVUthc3cxdlF6NnpaQ3k4SWgycHhwMjcrNUtvTEFGcWI5ckE0RFJseDIreWJpdzdlV1ZsV2JzSHNwRXRYVDRURHE3UWQzVFM0ZVNGSzBTVmg5T3hldGIrYVFDMnZHTlBKMnFEOTNSZzRmem1yODdOSGsxQTJpRVNCa1Jsa0lzbGk4S3E2RjNJeGpoUEpDQlpEbGN6UEYyVkdIY0tyYkdRVDVmWlFDYUplTnlvNmtZbjV3UHFoaUFBTWRNUEdSa2w4YVRaZTNzSUlqMDVTeGV2T2lDY3ZneU9BcGQ1ZVE5ZWx0TWRsT0g4b2RaUHE0NzZvdnRGSDM5VzRhZHBSa3BJQi9SR3FUTlJ0QlVtQjYxYXM5NzMxWFBBVXQzWTVCMisxcFNWc01aQmhlSHFuMVlZOFpsZ2VpbTJ1MWJ4ZTVNWldXcHI0YXhvL2x1SFg3SWxVZUVZZ0hrUFpHUEJLMVgwUDdDSURZbFpxVFhCYzQ4L3lvd0JEU3A1NHNFOGt4bFhrTDdITGUiLCJtYWMiOiIwMThmZGExNDkwNjI5YTg2NzEyZDVhYWNiMTkyNDQyMWM1YTM5ZGEzN2Q3MzIwYTEzMTFmNWMyMTE4NjYyMGI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdDSHp4LzJ0OCtpZ2R2VjhlQ0RlYUE9PSIsInZhbHVlIjoiUG1ocy9Gemt1ZkZPaXRJS1NoSVV1WmwxM3dwenBxSnVYUVpOL1JoeXdzRzlHc1d5MFdIeDJUNVdPZDVnQXRna1krcVVrZGtrRHYrT3llV1hIc0tFS2FIRXJzWktLTHF3VDk5V2ljOG1QNUZuaHlqajJjVlJaWGlEdnRTNlptUEdiUzZDa1ZVZjl5OUdQK3ZiRVlSa3hMUzMzTlhkUmluNm9jRjlIRlBaM2dDVStsdnlqVVRLeXh2UkwwQXRDQ2I1SVRpSHpUWG1sNU11eE1CV3ozSGZFYm1wRmFXRnlDOFRSMkJtSjhUdGd1WGN2WHAwZ1JqQU16dnRQNGdWMFNtd3hTM3V6YjB6dVBZOUI3TTl5aXUwcUE4SEpETlQ2ODI0djR5SVV3dllHVkl6OWFJZVBTcjFnSFlValltRlY1anZDZ3BKdkVYQm8yNWdJTlJYV0ZXSFk5VDJmeXJpR2Q4OXJQdWNraTVQN1F5MTNmbHMvaFlicE1lTk44QXp3WUEwaW9MU0crN2lDME90NUVteUhjczBJWStFWUFQei8yTHFIemRWSElmamxSV3lBMW5RUVk3dnJMNGFIeWFvdFhSempyS1U1aHhCeEExK0lvL2cvbERWY0w1cE8yUFhBRXZGTFRTQzBOMDNiai9DeHh6elE4V0FQLy9ibUM1T2E3dnMiLCJtYWMiOiIyZTM1ZWQ5NDdhYmJiNGNiOTE3ZWY5ZTVjMTczMGFkYjVlMjkyY2M2NjZlMTM5NjA0YzM2ZmM0NGZlMzRkYWJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493401444\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-594176300 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594176300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-99846718 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImprSGlDUExmQUFPZDRHYmh1N2VCalE9PSIsInZhbHVlIjoiWmxpSDFTRmN6TjFrTEovOVBIUit6bS94R212S3pwbmY0UVlFMkZCNnV6T3kyd3NsdWVmOUpZa2lpaTA0OFg2UXRoQ3U1aFowUGhvR3ExYkNUbGs1Y21nVUw2amV1WmZsODZFeXZKRUlMNzZieEptdnQ0Zm4yN1FTYmgybEJUSzQ1dHpiQkVSMmh3dDB3UGxVR052enFVbXE1ZVdQQ1lKRHZSL0RvakdTVEtPTlAweHdWaitySVhjK2sxUjhDZzhRbkY4K29uWVQ5ZkVjZGErTGFFa0JxcXhhOGs4MkY3R0N6MWtoRklBY0toMFdhQzVHODdyR24zWUxZbXFPY3h5dklPVFVVSitrWGhhT2Q3WjRTcW9QSzdOWEFkK25nSWdKT3FHZlFCQUtmZXNmRGpJU2hzcmc4ZXhUY3ZuOEUyS2M4L1JwVlMvb1RXR1BNTnRURWhDRitxcUJ6WjlQY3RISy8xYUNWMXA1bVhUNTBtdlJFZnZwZGtkajNXM3J2d1Q1UXlGQ2cwV1pXRlBvL3ZqMHhaZWJGQ2J3WWs4dS9obnhHb2pzN2tvTEJhcUhqaFphWmNCQWlFUDRzQkVrM2xQdThEdDRPQ3A2T251WVozckdKWUtYTHdsZm10TmtzSG9hblVjT0lCL3pkR1FpQXpZRGM5UjYyK1pWbmJSazVBUDYiLCJtYWMiOiJlMDc3N2ZiNzdlMzJkYzk1NzcyOTIxYjRiMmNiMzE0ZjgxNDNmYTAxNzEzOGY0YTYyMTQ5MDE1ZDJkNzc0NTlmIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikg5QXFjeHVLb20reHNLREdYZnV6d0E9PSIsInZhbHVlIjoiVks2ditRak0zU2hIcktCMCtxU2ovMmExL2VuOEhWdXIxMkhkTU5YcWkzY2JwZHMyN0ZiRWdtZnhTTkllYmQ1SXRGU3puZkx3c1g1SHRBU2VxSTdGZ21uN0VKV0RQMWUyQnEvcDgyQTVMeFlaY0VuR1BJNGljVUVpRkRyWGxOOVlRa1orc2Q0WHk1MHRsZ281dVhaZlA4dTA2S051RU50bEZ1OW1jMHhJaGNTdExxNDR2K1RncjMyaHRKL1dGSUUxUzM3dGhKSTNPT0tVZkxKYzFpTllzMjdNVTY2dDVxaVlLam5KOTJKVjR5WG5zdHRFald1WmtHQ1Q3WUlCYWZtMlllMzhraFpOKzhJcmxFZVQ3d3hwSEFVdVpFYUNjeHVodGFzOUdGbk9pMExtZzlyYyt5MmR0MXA5VmJ4UUJNRE5ZN21jNktFeFJiNXpybEY4VmJzZFhHSGE0ZmhQMlRpVm9paUJiWDRWUmtwVFkxSk5ld0lvbEdXTEdkSU5VeHU1K1ZWcm1SQitvdlhNTE13MmdqRll1dGYvMkd5Z3MvR25vZllDSGZNdEFma21SMjc0TC9PSmRvWkxtdEtsSGRNdzB0YTl3eGcrVHJ4VUhhYkEzNUdaUzJwLzFIWnZYNExMaFVIQmZzZHZnV3VmRm1Gd0YvalVESVQ4STJKOG5TUG0iLCJtYWMiOiJjYTIzNGZjMTI2ODA0NTc5NTI2NGQ0MzU3YWRmZjgxODBhNzlkZjllY2ZhZGZkMjllODVkZDgzYmZiNjZiMDEwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImprSGlDUExmQUFPZDRHYmh1N2VCalE9PSIsInZhbHVlIjoiWmxpSDFTRmN6TjFrTEovOVBIUit6bS94R212S3pwbmY0UVlFMkZCNnV6T3kyd3NsdWVmOUpZa2lpaTA0OFg2UXRoQ3U1aFowUGhvR3ExYkNUbGs1Y21nVUw2amV1WmZsODZFeXZKRUlMNzZieEptdnQ0Zm4yN1FTYmgybEJUSzQ1dHpiQkVSMmh3dDB3UGxVR052enFVbXE1ZVdQQ1lKRHZSL0RvakdTVEtPTlAweHdWaitySVhjK2sxUjhDZzhRbkY4K29uWVQ5ZkVjZGErTGFFa0JxcXhhOGs4MkY3R0N6MWtoRklBY0toMFdhQzVHODdyR24zWUxZbXFPY3h5dklPVFVVSitrWGhhT2Q3WjRTcW9QSzdOWEFkK25nSWdKT3FHZlFCQUtmZXNmRGpJU2hzcmc4ZXhUY3ZuOEUyS2M4L1JwVlMvb1RXR1BNTnRURWhDRitxcUJ6WjlQY3RISy8xYUNWMXA1bVhUNTBtdlJFZnZwZGtkajNXM3J2d1Q1UXlGQ2cwV1pXRlBvL3ZqMHhaZWJGQ2J3WWs4dS9obnhHb2pzN2tvTEJhcUhqaFphWmNCQWlFUDRzQkVrM2xQdThEdDRPQ3A2T251WVozckdKWUtYTHdsZm10TmtzSG9hblVjT0lCL3pkR1FpQXpZRGM5UjYyK1pWbmJSazVBUDYiLCJtYWMiOiJlMDc3N2ZiNzdlMzJkYzk1NzcyOTIxYjRiMmNiMzE0ZjgxNDNmYTAxNzEzOGY0YTYyMTQ5MDE1ZDJkNzc0NTlmIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikg5QXFjeHVLb20reHNLREdYZnV6d0E9PSIsInZhbHVlIjoiVks2ditRak0zU2hIcktCMCtxU2ovMmExL2VuOEhWdXIxMkhkTU5YcWkzY2JwZHMyN0ZiRWdtZnhTTkllYmQ1SXRGU3puZkx3c1g1SHRBU2VxSTdGZ21uN0VKV0RQMWUyQnEvcDgyQTVMeFlaY0VuR1BJNGljVUVpRkRyWGxOOVlRa1orc2Q0WHk1MHRsZ281dVhaZlA4dTA2S051RU50bEZ1OW1jMHhJaGNTdExxNDR2K1RncjMyaHRKL1dGSUUxUzM3dGhKSTNPT0tVZkxKYzFpTllzMjdNVTY2dDVxaVlLam5KOTJKVjR5WG5zdHRFald1WmtHQ1Q3WUlCYWZtMlllMzhraFpOKzhJcmxFZVQ3d3hwSEFVdVpFYUNjeHVodGFzOUdGbk9pMExtZzlyYyt5MmR0MXA5VmJ4UUJNRE5ZN21jNktFeFJiNXpybEY4VmJzZFhHSGE0ZmhQMlRpVm9paUJiWDRWUmtwVFkxSk5ld0lvbEdXTEdkSU5VeHU1K1ZWcm1SQitvdlhNTE13MmdqRll1dGYvMkd5Z3MvR25vZllDSGZNdEFma21SMjc0TC9PSmRvWkxtdEtsSGRNdzB0YTl3eGcrVHJ4VUhhYkEzNUdaUzJwLzFIWnZYNExMaFVIQmZzZHZnV3VmRm1Gd0YvalVESVQ4STJKOG5TUG0iLCJtYWMiOiJjYTIzNGZjMTI2ODA0NTc5NTI2NGQ0MzU3YWRmZjgxODBhNzlkZjllY2ZhZGZkMjllODVkZDgzYmZiNjZiMDEwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99846718\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-287776691 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"149 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=&amp;customer_id=8&amp;date_from=2025-07-21&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287776691\", {\"maxDepth\":0})</script>\n"}}