{"__meta": {"id": "X75f692ccecceb9890c61abeaa20c4925", "datetime": "2025-07-14 18:29:03", "utime": **********.116743, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517742.65267, "end": **********.116761, "duration": 0.46409106254577637, "duration_str": "464ms", "measures": [{"label": "Booting", "start": 1752517742.65267, "relative_start": 0, "end": **********.035648, "relative_end": **********.035648, "duration": 0.3829782009124756, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.035661, "relative_start": 0.38299107551574707, "end": **********.116763, "relative_end": 2.1457672119140625e-06, "duration": 0.08110213279724121, "duration_str": "81.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024040000000000002, "accumulated_duration_str": "24.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.063499, "duration": 0.02271, "duration_str": "22.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.468}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.102199, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.468, "width_percent": 1.955}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.108532, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.423, "width_percent": 3.577}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-813359347 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-813359347\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1317616029 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1317616029\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-232907405 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232907405\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1155727690 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517736326%7C28%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJSYmZsWXF4eXcxa2Q4VmFQemxhU1E9PSIsInZhbHVlIjoiT3FoSUg4S1MzNkQwWkJJd1hUMXg1cnMxTWtzZ0RzYVQ1RFIrclF3YWNrYmVWeVV2MlJDc0NVbC9zM0l4RXQxeEVJMFAwRjhFenJ4MkhrTjhvVlR1VU1HOHpPUGxrejJSN3R2dEozcDhjRDNUUTVpNjFNenpFRThaajl2VitmMHdlaDdYVmNiUWEydlhoVmsvbWJLWG95bkIwaElCQ3ZMWEwzdEl4UXkyL1I5V0hCM2llYmU1ZHYvak1MQjB1SGIySkZhMUxHbkpQWk1lbGdMbnVDSUljSzBkMHk5aEhOYzVZVFBqdmdhVkNxVW5pYlpvQ0V2V2hhZ256ZXJvV0ZuYjBON0c3R29zZWo2ekZhSDVidmQvMlVwbHJ6dE9kb0VUYVZmc0VuejlPdExBMkd6NnpMamxIUlVoWGdSakRvMTVqZC9mTUdwZlZHaHBlcitIUVA2WUczNUJxM2FMcXFKQ0RyZ2t4b1hJM3o3RE9QWTNITThFODJsMVMvbXhpNXVLa0R2cjNEaHhuZndzSW05bTJzdjg1NmFFWXpWendPQ0pzbUpJQUZCbllvNUxnZXdVeGpHSHVsTXdoa2owbjdGWXpwUFdzajdJTElIWWtkZmVHNmVBb1NWeXhlaWp2b1FHMVdyeXQyU015SG0wNzRaM25EZGVmUjRrMnhCSlZaQVoiLCJtYWMiOiI3MmYwMzU0NTM0OGQ5Y2ExYTNiMjFhZGQ4YWJlODNlYWYzMDIzOGU0YzhlYTM5N2IyM2Y1NTRhNTA2MTczYjc0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllSMFkrSmMxNEJILys5NEtuelp5NkE9PSIsInZhbHVlIjoiK044RisxMDVEN2dCdkc3V3pyVVM2eHhXUkZlOVovbWs1RFU4K1RmL0UvYkZqdDlBTjREcEdjeEs3MXFGVWVMeUQvejFaN0N0Uy9RMWpzMU5obW9mYU9wWnJzT3FQTExreEJ5bytoUlFWVEtMQk5BQ3hWTmZMZGk5eHRGVU5SWXhQMGF1cjhoRDhnSVBReFlJU0F0dFE2dEVYSFNzamdsemRyekpHenErbVN2QVpnL3JVZHBtMnZDaFpaQXBmT05qVjBrcWd1TXVPUVVYTnkxWDFDY2dYZGQxbjFIMkpEU1Mza3ZJT2hINHdzZXF6aHFKOWsvanFxWEJpMnJXVVlLY1U0aDRJRUZrNHNHMkxLU2tUMjhSdWxnRVpmbm8zMEtZa1RXWU9sWkNDK0lRajhlellmRkl5QkJZZXdyOWZwSmF4YmdHQ1UrYTg0dEhCc3drMW9PNE5uZmtPMVdQQWdNWjlqTU5DZExGQWdVcGQ3WUkrdm0vYm9yeXdCS2dCSDZNd2hHOGIvbUdlUTh1Z0VzNDhLd2M5UzZJNVJqWGgxZlZSQklRanRIRm85T1pYR3V2T3djOXdnRjhTVzNTejJnT1gveUpZQU90N1AyVEZnRnpxUnFYckhjMWY3UnBZVmN5cnUyTWZHOVNSSHRhK1UzOWRPNVhMekZaZ1Q3WkpMUmYiLCJtYWMiOiI5OWM2NmIzNDYwMjVlZDcwZWExMDUzZjM4YmQxZTY0ZDU1ZTU2MWYzZjUwN2I5ZjNjNjkxNDQxMGFiNTRlZjcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155727690\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-53968927 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53968927\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1481259421 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZOT2pGR2JCTGQ1QVFqblJpdFNUdnc9PSIsInZhbHVlIjoiQmZVTmtNRG41SlhDNTg3TkQrcVBJV1gxVmZwOFhZUW8vbjU5VUN6Mzk3UC9Bd0s5T0tXc3NpdnhQZ0ZHeHBmRjVPYXFvVDB4bFVScjRGY3RUZVMvenEyamo2SWlrK0VVT1VHTVd3YnE3NWlGUTdUWlY5Snp1aVl6UW5EdlhkdEJTWVVnVUsxL2xuT1lURzhlZTZRV3QrdGhOT0k1dXZiYWJoNEhrWGg0SCtZUkdGYWRQQmh6dkxNM2pVaHVNTUZVOTlYcjRxT3J0c01wd3RqZ3hhNTJtTkV2MFllZDBuUVdVSWR0cUFqTTkxTklqVVB2R3pxWUdIT2JZTVRUaG1MRE9nWTlUaU14Q1FiUW9DSlBsUWNEZS80aGM0TElrRFVCc1B2Y2xheHRNMnBneE9aeHRzWnZTa1FRWHg1WWN6SHptNWFZYlJLRTdhRzFhUG5JZnJYamZlQXByaHE0MFJCRTJDT2Yya2R5TTQvSlI0ek9hVWcyNXdYZlhtY3d1VnoxWEFhMEJrYUZaeDVFYk0xM3NuU2wrU201YWFCVlhNZUE3M3VSUUFDSW9LV3B3MEhDenlMaFpqa0JDNWMrQjdhTFJXem1MczJ3WnZOdmhFUkx2ZVB2YkhmWkdpVDl2TDljc3ZkRHBya1hQNk5Hd0pEd1pSQW5zVGxDNWlLcFhzRG0iLCJtYWMiOiJmOTRkNjU5NTUyYjEwNmYxMmE2M2E4MGM2Y2M1N2NlMjY2ZjBmNGQ1YzM4OTRkNmI5ODM1NTJlZmQ3ODg2NjlhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRYc2NiLzh6ODNLTGc5ejJ0S1YyY0E9PSIsInZhbHVlIjoiU2dId1c3VFNOcXNaSGhuWG5BeDJsbWRKQ0dXUDZSbmpuNTV0NDUrbDNmZjlHUzZlMUVPRUVPL0hHSVkwcExUbzNHVzBOMXRKVEhKeEhNRUJkaktEYm1IQmZHd0I4djd2QzI1aUhUN3huK3AvTkJNaGFTOS9nR1RxV3V4bC91NW5zam9JTHhjRW9QZEtrMnpTTmxIN0RBQ1RtbjAvc0tENVlkSmZoc09VMGxZejZrVkV6V2s3UnI4dnBraWJWOEZyRWtYTTNnd0t5Q3ZCRVAvWWVMTjkwczBNa205dXFrK3ExaTdRMGVsMVpsOVZqVjNsTGUrbWUycEtId1ZQbUZFOEM0N3lVcXhPd3Uxb0FvM0VZWWQwN3M5Z3l2ZjFIT3UrSFN6MGdUcDkrZ0M5U29QbUdJWnozOERqeWUydXlnY3d5b0lDWG5rOWpmUkhmdTVrSGdlelRUTmwvOXdSeXNrcHVDeGFoQmhwRERwc01sVm9reE1FbS9oQnNyMTRSVlBMNm1ib1UzOEpZSitmMjJ6c3N1dWltaXBFeW5MRyt5RWtCZU9UWkUvVERjaWRXSXFhVVhpQ1JscldXS2Q0TjdoMC9meDVvQkNyYzRjY3dGRm5ENXJpbVV5bVc3U1ZSSDJvQ1VRNk52ekFrckp3bnFNZFpVa0NRaTYvRXc2MEJRMXYiLCJtYWMiOiI4MDUxODYzMzE4MjE3M2JmMjUzYjFkYmU4NDM0ZmMwYTUzNzQ0YWQ0NjY4MTVlMmRlNDYwNDM2MTQ1NTFlMDU3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZOT2pGR2JCTGQ1QVFqblJpdFNUdnc9PSIsInZhbHVlIjoiQmZVTmtNRG41SlhDNTg3TkQrcVBJV1gxVmZwOFhZUW8vbjU5VUN6Mzk3UC9Bd0s5T0tXc3NpdnhQZ0ZHeHBmRjVPYXFvVDB4bFVScjRGY3RUZVMvenEyamo2SWlrK0VVT1VHTVd3YnE3NWlGUTdUWlY5Snp1aVl6UW5EdlhkdEJTWVVnVUsxL2xuT1lURzhlZTZRV3QrdGhOT0k1dXZiYWJoNEhrWGg0SCtZUkdGYWRQQmh6dkxNM2pVaHVNTUZVOTlYcjRxT3J0c01wd3RqZ3hhNTJtTkV2MFllZDBuUVdVSWR0cUFqTTkxTklqVVB2R3pxWUdIT2JZTVRUaG1MRE9nWTlUaU14Q1FiUW9DSlBsUWNEZS80aGM0TElrRFVCc1B2Y2xheHRNMnBneE9aeHRzWnZTa1FRWHg1WWN6SHptNWFZYlJLRTdhRzFhUG5JZnJYamZlQXByaHE0MFJCRTJDT2Yya2R5TTQvSlI0ek9hVWcyNXdYZlhtY3d1VnoxWEFhMEJrYUZaeDVFYk0xM3NuU2wrU201YWFCVlhNZUE3M3VSUUFDSW9LV3B3MEhDenlMaFpqa0JDNWMrQjdhTFJXem1MczJ3WnZOdmhFUkx2ZVB2YkhmWkdpVDl2TDljc3ZkRHBya1hQNk5Hd0pEd1pSQW5zVGxDNWlLcFhzRG0iLCJtYWMiOiJmOTRkNjU5NTUyYjEwNmYxMmE2M2E4MGM2Y2M1N2NlMjY2ZjBmNGQ1YzM4OTRkNmI5ODM1NTJlZmQ3ODg2NjlhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRYc2NiLzh6ODNLTGc5ejJ0S1YyY0E9PSIsInZhbHVlIjoiU2dId1c3VFNOcXNaSGhuWG5BeDJsbWRKQ0dXUDZSbmpuNTV0NDUrbDNmZjlHUzZlMUVPRUVPL0hHSVkwcExUbzNHVzBOMXRKVEhKeEhNRUJkaktEYm1IQmZHd0I4djd2QzI1aUhUN3huK3AvTkJNaGFTOS9nR1RxV3V4bC91NW5zam9JTHhjRW9QZEtrMnpTTmxIN0RBQ1RtbjAvc0tENVlkSmZoc09VMGxZejZrVkV6V2s3UnI4dnBraWJWOEZyRWtYTTNnd0t5Q3ZCRVAvWWVMTjkwczBNa205dXFrK3ExaTdRMGVsMVpsOVZqVjNsTGUrbWUycEtId1ZQbUZFOEM0N3lVcXhPd3Uxb0FvM0VZWWQwN3M5Z3l2ZjFIT3UrSFN6MGdUcDkrZ0M5U29QbUdJWnozOERqeWUydXlnY3d5b0lDWG5rOWpmUkhmdTVrSGdlelRUTmwvOXdSeXNrcHVDeGFoQmhwRERwc01sVm9reE1FbS9oQnNyMTRSVlBMNm1ib1UzOEpZSitmMjJ6c3N1dWltaXBFeW5MRyt5RWtCZU9UWkUvVERjaWRXSXFhVVhpQ1JscldXS2Q0TjdoMC9meDVvQkNyYzRjY3dGRm5ENXJpbVV5bVc3U1ZSSDJvQ1VRNk52ekFrckp3bnFNZFpVa0NRaTYvRXc2MEJRMXYiLCJtYWMiOiI4MDUxODYzMzE4MjE3M2JmMjUzYjFkYmU4NDM0ZmMwYTUzNzQ0YWQ0NjY4MTVlMmRlNDYwNDM2MTQ1NTFlMDU3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481259421\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1874945807 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874945807\", {\"maxDepth\":0})</script>\n"}}