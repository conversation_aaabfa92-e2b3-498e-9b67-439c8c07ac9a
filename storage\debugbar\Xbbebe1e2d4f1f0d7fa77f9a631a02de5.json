{"__meta": {"id": "Xbbebe1e2d4f1f0d7fa77f9a631a02de5", "datetime": "2025-07-23 18:23:51", "utime": **********.042188, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753295030.609668, "end": **********.042204, "duration": 0.43253588676452637, "duration_str": "433ms", "measures": [{"label": "Booting", "start": 1753295030.609668, "relative_start": 0, "end": 1753295030.984137, "relative_end": 1753295030.984137, "duration": 0.3744690418243408, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753295030.984144, "relative_start": 0.37447595596313477, "end": **********.042206, "relative_end": 2.1457672119140625e-06, "duration": 0.058062076568603516, "duration_str": "58.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46007816, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00347, "accumulated_duration_str": "3.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.014209, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.741}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0252469, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.741, "width_percent": 16.138}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.031902, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.879, "width_percent": 14.121}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_search=&customer_id=&date_from=2025-07-01&date_to=2025-07-23&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1974040985 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1974040985\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1095641558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1095641558\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-829174138 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829174138\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1906539719 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"152 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-01&amp;date_to=2025-07-23&amp;warehouse_id=&amp;payment_method=&amp;customer_id=&amp;creator_search=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294980478%7C6%7C1%7Ca.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ik1XbTFQVEZ6a2NWeFRUVWxUcm1uY3c9PSIsInZhbHVlIjoiUjg3UWVqRkpwaVk0cFFZdERaSElpVXRLMDhJTnl1R0xTNmtxSVY5bWZ4RENUMTlEYmtPcStZbVYwZG9ZV0d4ekZ3Q3p1Z3VaNGlRWmF2cDR1RzdvMTZDRjhuaFM3dXRlbHlnMzEvZDM3azZERFMzYnhXWXlONG4rNUlzVFpidExha0FsYVBiZC8wVzZCeFJtYm1OY0htTklzZTk5OUs2K2RPQXM5OEdYMitEZjRCL3FoQllONnF5V2RnUVNjZk9ndFlpb3pqTi9paTZ1YzhaUEdRU2ZzTldLR1J4NjhWQ0NOaXVlcFVvMjFpQzZ1RnRsNGtBZzBVSUxIcFlqL2xwaDVqcTdKL0I1UjJzTXYrK0NNN0x0TkhRZXMvbnBQRVF1UDVJRWNLcDNjNzhTT3ArdFE3ZHJYa1E3RENKQXpQTW52VGNKQ2dpZ0tObGxub2JoWWgwaWdKcU5sZVlJaEE1K29tcldmVW5ib29HZmpqTFhzT1NBRUxIODhjRis2M3pwOEFqdFY5V1dWZmRuT1ZxeWtGOGIzN3k4UU9YN3RzbSsrbWtpblVWaE1NWU5EbmcvMzdjN1RIYkNGZHQ4N1o0RDhiZkZTT2dKcVNnaGlrQkZwS2ttUlhjV1pIaVhveUpLQjRHaFl3V0FNZDVaVVlpUms5SFJiWmF2KytQRk1JUWkiLCJtYWMiOiIwZTk0ODQ3NjcxMmRjOTQ1NjE4NjcyZDRlYzI3MzVlOGM4ZWY4NWM1ZjkxMTIzYjRlNDI1MDdhNTVkNzdiMjRkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdjeFkwWmpIZFNOOVBCbEFYWHV5RlE9PSIsInZhbHVlIjoieEZ3Sk1aR1YzdGZwQ1FQWk9La0k5NlhLRldGRnR1djFpdldJQklhWjA2T3lYQ0hJQkE3SW9ocEJhWS9aQ1dKZGVCU1pUc2Q4SDIvdGZDemxKaWVubHJKUVpJcjRxUytqUWp6ZEd5YmdUM3BRYkNoTXhtMTUrYUlOT3d1YkpZWFNoNm1sWVhielhwZHlmdTdnaElvblBnQlBKbkhBRFJ6R0xPQ2ZUVjgvM2dUQUlScnp2RnRua1hXLzRoNk9VUWJsdlRTTlczS0VPNTg4MTJKV0orakhPZFc4aDhHVGw2dUFaK0ZNYTNmMzZJZWx1angwUHNGQjFMZGcyb1hGSU1LaXFUUVhxOVVoWHBiUlJFbGYwZHRkdEdXTVh6ZVRvOUtvWG1sSFJINUlQVVlrVW05RjQzM0VvbVF5NEJMQ2FnTHdmSEMva2dYRUNiWC96blpsYm8yV3QrYnZQbU5NYWpsOFBjSi9XS0x2aUh2R2plNmJ2dE5BNUsxTVVDWnZhVE1SdFB5VVc4bVlmTHdLcGJCaG50cVB3S2JFdERodWZJbFFKeUxtOUt4L0dWMEQ5aHlaanB0NkUwVkN1VmFQQjBmejhmeXJkUkN3N2RCRDgveG50aUpycXlOUnE1QXdld1c3bWNUd1V6RG5qN29KMmh5ZHNwUmhqWlU3bmxvSFI1dVkiLCJtYWMiOiI2YjQ2OWIwNGVmNmJlYTI0NWNiYWEyZWIzMmFhYjU2MjZjZTM5MDliZWYyMzAyNTNmMDVmN2E4YzkwZDA4ODc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906539719\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1806886909 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xWPGJpNWbmDLkwLBtG0MdpEYkMbvhJtQ6DvLx5Cj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806886909\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1049038112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:23:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlcyOUYxd2tWVm9EblNBRis2bko2aVE9PSIsInZhbHVlIjoiSjJUL3drQ2FuZndvTlZOQUxlMGdPM1ZUOVg5eDMrbk5uVkIvMUw2RjlUR09CKzhLanFKT0s2QmtSVDkzZHVkQWVaN1pSUUpWVFNUSitXK3JLM2d4SmU3cEc4UGNiKzhRcW4xV3EwMGRRZ0xpaW9hWGpKaERSbjJObHNFSTJrNXIzUVRWRE1rL1dhVlEwK0MxRjVQTWd5VHZCZGExcy8vSGpLQVdzNUVnSUc5TElrZVNSK2dTTlVWbDVOSllYUEMydTY1NTVWSUx4cmZPUFZEZlBSbkdKd1l2cEFHckw1M0hXZllJQWpuWWkxcFplZ2QwbTIwU1NQMHV6NG13a3AwS2xXUFhURTNPK05rVEtHS1ZkWGxnZXRHb2RiYWtnKzkxRTdZOWtTek5sQ0dpRFBySkZNeVhpcmRHOTRyNWRIRlFYY1EySnlrY0JGeml2VHJoVmtpZUpsbDY2VmZmOUx0dkNNMkpPLzMyZVZkMEFxckZIZlZFVFFydGF5eFhNcjh5M05MWjhsM2RPSnpKZ2pMOWZTNWdadVgycGVJTWloVGVSWjQ3Mi96SmZwV0J0ZEsxVVIxdkYyL2kzOVIycEVyUTNJcUgvS095RzJvWUNWcCtVN3dsZmhMWGdzeDRxcmJZM3Z4c1lXNTJORzZXalBNR20waklGdEVaMXE1V0pQb2YiLCJtYWMiOiI2OWFlM2NkNjI2MDNhOWNjOWI0ODNlZTFkZWMxM2Y0Nzc5NTgwMGQzMWNmYzhkMGYyZGQ1MGQ5ZDQ5MWI0YmRkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im0wdEtqMFdyN3Vib2N6NVJIVFEvQUE9PSIsInZhbHVlIjoiTHJyQW5WM2ZvQ05Mck1QRjJscGtsUWJMeEFWdGJzV243SmFZRDhYQm5MZS8zQ2UydkhheHZISnQ2N0pKbFJpVjNKc3dvSHExSndQN2xpa1c5V29SUTBvZU1MNS82eElCSVhzN0lENXk1TStjR09vM0NTbm9iRnd4SGJSeU1jMnZaS3FwMS80cDE5UVFQYSswWG5uWkRNdlRkOWdvMDZLWFl1V3lIVGxnSWE5aEt2UTlWYzh3b1RKMW5BeTJ0YVh0QkRSdVdCSVovcUYxRTJ4ZS84VWZvV3JvaDhaYUtsUHNxOEpvbW5SUXhWVXZuODZuMWtJK3E5cy9wV1g1TFg3YXU5MWIwS3I3dlFsYkU5VGpnMTVKQlpPRVlHNzJwZ1JnODFheEdHNGw5ZEJmbjVOU1M3MG9NbndTNWJOMzUzZmlpRGxkZ3NCQmRqNmU2TURjOFcxTzdWcnc2d1plaGN0dWhZdGYwOVZ5M1J6Um1JazJuRm5ZQXR2VjBYb2NsN1I2RkFtQ1ExSE0ySnN6dWdqWm5TVys4STFQd2N5eDlReUZLWHk3SEhRc1NVNHMwYmp0VnpYUDY3cjZzRlNjMXg3akZPT3lFemxkc1p1VkUwbG91ZVU0RTJaSzFWODJSSEgwNUV2Q29TUStCek42d2lxVEZVZTZCUjd0SDluZnA4eTQiLCJtYWMiOiI4MDgxMDNlMDE0Y2NiODQ3ZTZjZTZhYTVmYjlhZmNjZDhmODM4MWEzMTY4MTVhN2JlMzQyZWVlMGU4MTMyMDA0IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlcyOUYxd2tWVm9EblNBRis2bko2aVE9PSIsInZhbHVlIjoiSjJUL3drQ2FuZndvTlZOQUxlMGdPM1ZUOVg5eDMrbk5uVkIvMUw2RjlUR09CKzhLanFKT0s2QmtSVDkzZHVkQWVaN1pSUUpWVFNUSitXK3JLM2d4SmU3cEc4UGNiKzhRcW4xV3EwMGRRZ0xpaW9hWGpKaERSbjJObHNFSTJrNXIzUVRWRE1rL1dhVlEwK0MxRjVQTWd5VHZCZGExcy8vSGpLQVdzNUVnSUc5TElrZVNSK2dTTlVWbDVOSllYUEMydTY1NTVWSUx4cmZPUFZEZlBSbkdKd1l2cEFHckw1M0hXZllJQWpuWWkxcFplZ2QwbTIwU1NQMHV6NG13a3AwS2xXUFhURTNPK05rVEtHS1ZkWGxnZXRHb2RiYWtnKzkxRTdZOWtTek5sQ0dpRFBySkZNeVhpcmRHOTRyNWRIRlFYY1EySnlrY0JGeml2VHJoVmtpZUpsbDY2VmZmOUx0dkNNMkpPLzMyZVZkMEFxckZIZlZFVFFydGF5eFhNcjh5M05MWjhsM2RPSnpKZ2pMOWZTNWdadVgycGVJTWloVGVSWjQ3Mi96SmZwV0J0ZEsxVVIxdkYyL2kzOVIycEVyUTNJcUgvS095RzJvWUNWcCtVN3dsZmhMWGdzeDRxcmJZM3Z4c1lXNTJORzZXalBNR20waklGdEVaMXE1V0pQb2YiLCJtYWMiOiI2OWFlM2NkNjI2MDNhOWNjOWI0ODNlZTFkZWMxM2Y0Nzc5NTgwMGQzMWNmYzhkMGYyZGQ1MGQ5ZDQ5MWI0YmRkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im0wdEtqMFdyN3Vib2N6NVJIVFEvQUE9PSIsInZhbHVlIjoiTHJyQW5WM2ZvQ05Mck1QRjJscGtsUWJMeEFWdGJzV243SmFZRDhYQm5MZS8zQ2UydkhheHZISnQ2N0pKbFJpVjNKc3dvSHExSndQN2xpa1c5V29SUTBvZU1MNS82eElCSVhzN0lENXk1TStjR09vM0NTbm9iRnd4SGJSeU1jMnZaS3FwMS80cDE5UVFQYSswWG5uWkRNdlRkOWdvMDZLWFl1V3lIVGxnSWE5aEt2UTlWYzh3b1RKMW5BeTJ0YVh0QkRSdVdCSVovcUYxRTJ4ZS84VWZvV3JvaDhaYUtsUHNxOEpvbW5SUXhWVXZuODZuMWtJK3E5cy9wV1g1TFg3YXU5MWIwS3I3dlFsYkU5VGpnMTVKQlpPRVlHNzJwZ1JnODFheEdHNGw5ZEJmbjVOU1M3MG9NbndTNWJOMzUzZmlpRGxkZ3NCQmRqNmU2TURjOFcxTzdWcnc2d1plaGN0dWhZdGYwOVZ5M1J6Um1JazJuRm5ZQXR2VjBYb2NsN1I2RkFtQ1ExSE0ySnN6dWdqWm5TVys4STFQd2N5eDlReUZLWHk3SEhRc1NVNHMwYmp0VnpYUDY3cjZzRlNjMXg3akZPT3lFemxkc1p1VkUwbG91ZVU0RTJaSzFWODJSSEgwNUV2Q29TUStCek42d2lxVEZVZTZCUjd0SDluZnA4eTQiLCJtYWMiOiI4MDgxMDNlMDE0Y2NiODQ3ZTZjZTZhYTVmYjlhZmNjZDhmODM4MWEzMTY4MTVhN2JlMzQyZWVlMGU4MTMyMDA0IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049038112\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1344079776 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"152 characters\">http://localhost/invoice/processing/invoice-processor?creator_search=&amp;customer_id=&amp;date_from=2025-07-01&amp;date_to=2025-07-23&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344079776\", {\"maxDepth\":0})</script>\n"}}