{"__meta": {"id": "X8e5b3185dc033c581b402ef789e14f5b", "datetime": "2025-07-21 01:17:54", "utime": **********.187531, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060673.645264, "end": **********.187547, "duration": 0.5422830581665039, "duration_str": "542ms", "measures": [{"label": "Booting", "start": 1753060673.645264, "relative_start": 0, "end": **********.09251, "relative_end": **********.09251, "duration": 0.44724607467651367, "duration_str": "447ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.09252, "relative_start": 0.44725608825683594, "end": **********.187549, "relative_end": 2.1457672119140625e-06, "duration": 0.09502911567687988, "duration_str": "95.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46520880, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02831, "accumulated_duration_str": "28.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.129738, "duration": 0.02699, "duration_str": "26.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.337}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.169496, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.337, "width_percent": 2.225}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1771681, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.563, "width_percent": 2.437}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1816857976 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNLS1ZGV1o0MW1oVC9pbUhCdDJmdlE9PSIsInZhbHVlIjoiRTMzbWpRUVBSeWt0MWNCRjFTWUhDZTJCWDRhTk1xL0JWa0RubHdjUHZOVHl0UDJZVHBtNjZ5SGsvNzIxSlhTcklReTg3ZHRZQm1MT2svRjNaNng1KzdialZtOUNtZ1djaHBnYlJldnRNNUVEWGl0UUMwdEVmM2hZM1NqcnIvQm9kZSsrTTFJYTQrM0ZuR1gxWGFYbFN2bC9uT05yaFJ1TUZsK1U1a3p3VDNZUFFrQ2dMZXlQd2VzckxOUHR4RE9xcmtuSEVTWEFqR0tOSWJESFBKNGRCQXlKVW1OK3dwWGFubUxjSk14d3B0V2J0czNYampvV2tYYTZITWFNbm45Z0pTd3ZlMkpiTVJlZnBNeW1DZzJOcmYzM3hraERuUFF3VElzL3BRV05rZjVML1phM0RWcG5NaUowZjNoMmdRVlRUSm82UVlMZlBaSHJEMHZuUHMrZlcyeTJ3OVplYXJjcjAwQ2Y0b1pFY2RTamtZQWdLNS9SVStjTi9reVZQYlRqOUdDcElFaVRFNjV2eVhnZSsrUHRFOTlPZ2k3SmRZRlNocW9Bb0hYOVN5SXFncldMZEowZzRYdTgrckRUNmdyZjNydXgxSXpvMVljYmwrSURtZWlIdkwwMC81ZnZJRXBZRmJNWnViVGxOMk5DcFhDdm5kcTI5K3BEMjQwbURkdzAiLCJtYWMiOiI1ZWVlZGU2MDY5ZDdlOTVmNGRhZjczZTRhYzE0ODgzMmM4MTQxYWVjZjMxZDRmNjI5NDBjYmZiMzhkYjcxNjNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlFNGlKL0w4Wm8zK2JNWUNhVGtZSWc9PSIsInZhbHVlIjoicjc2S21GRE1wRUJ4dHFZME1Qb2hxR2pLZk5XbjBBenFOU0N1ZjNYZStaVHIyeVpVTXZvbDhJMWI3THBHMFNBdWpicHlRRzhTdEpkY2ZCb0dORDdERE1VMHBxbmdSMlJTQ1JWTWtSSTYxdmhrckpoYmQvZ1VaTkx6REtZcnBMU3RnMDk5bXFCSHVKdnlZYjhrV0xDdXd6eE02Z0dBeU1mc3NHRjZjcUZNL0FJYzhaakNjajMzRGl1QnQ3YUxORnY5dWErNXpDK0FlaUxFbG5GQXNNM0dpajQ1aFIzajZST0w1UzRqMUVPQXJjc0tZZHl4YktBZHhET0RuV2IwMG55VDZOSjFGcEMwaTZScTAzTGpLVEY5RXIxSlRmWDBiaFIrOHhUK3JCaC9RbU1BOENPSTVUOVFscThMUU1walFjNHFLcWp1YmtkT09zZ1FWRE4zOW5JS0dEMnRMWHRVNGdxb2J4cW1pbFU4ckZ4MEJ4NGl1TXQ4bVI1Ly9PRzdHZVorOWpVRWV5cXBZZTNQNGRYbW9OOXRRcDNGZHkxcHNSdHhUamk2RmtNUEJXbDFOdGZsczByK0dOMTgrZGtlNy80c1pIR3R3YndVWjc5ZUlkV1Z3VWt0OGRxOXpXMnZLY2Nyei9ZSWt0TlMwUTdGdElROENzL2ZzOHRnLzZ0dGJqNmgiLCJtYWMiOiI3MTIzMzM0MjU2NjVlOTRkYWNiYTczMDQ5OGFkMDc1YWFhNzUyMTA2YjhkODljZjE4NTE2ZWY4Mjk3NTk1YTEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816857976\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-353543006 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353543006\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1142125078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNoRnBhRGFrcDlQdUxVVW5uSkkyQ1E9PSIsInZhbHVlIjoiYmREVm1yMHdGb1crNVdHTVdGRjZWMm13dVFGVTVyREd0VUtHTVhGMk5QL3VrS2hqTWZNUGRza2pqSUtxdG9BSTR1NnBCc01mcDdCVWZvS0NRQmtPZWpSa2M1K2pET1daYlM4YmRWUnJDTkFXL1hUWEFsWGVrT0ExT3pvdWd3d0VOVEJCTWExRlY1TFR2d1FmSVdNWk5yNytzcU1YT2t0eUMzelpXN2xCb0NHTWhUMHJydkpzSm9uM3lJNTlCVkhISks3dXU1RDB5K2JyVVpRMlJ4cFpTZ25YQkNYWlJzVVRrWlZrWHJuQWlCbURHc1FsOXNoODRBakFWZkFkQ0RLODlPOHBRQ1Q3OUhyY1Z6Z1hyelQwL1JzRHpvTGE1REV0UXJ4bnRkUkRqZ05YNGVBMldwcUFTbnhPUW82cWhaQUdUNmRyTjdZQlB6NTZmUkhmdHZ6NUFycHJPTDJpa1liRmpBVnhvK3RFdEJYN2x2c0VQM2E2QWJZOXJNRGpjbmlkV2FlNk1oSkc2azQyVW44UE1aMHNna0J4cU9pZjdvNzZxTjFnWmFUakZwRUEzYVR5L2FubHFUMnNhYWV4U3hCMmxoTXAwNVZqNGRNNS9STk9yUHBwQWg0TjkxaU5oU0RCRktkMmhTMDVRWENqVU9kbWJDTVJRSjZuaVIxeTVsaXUiLCJtYWMiOiJjMTQzMDM2YjY2M2MxOGE2OWYyN2ExOTlhYWNhNWExMjRkYTYzMjkzMGU2NjJlMmJkNDkzMGQ3ZjgwMDZiZGI0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVmNE9VVGhlSHV6VGtWWXhiQ2VEOWc9PSIsInZhbHVlIjoieklvY3VRaDJPZmxpTzRwTllpR1E4bkd1TGJvQXdwb1Vpbm1NK2R0djNCSXZCQlpyalZ1bzhlcnBVaXAzMGpqYjhQcTBESXJ5N1d0SG9janpVKzlBTE5mMGdFRUc2WU1GWHVDc05mbzV3UHBkMXMvUU5QQXZ5ZittMmtTZUNNN1hpelRDYjViWFZIa3lPd25POEVPWm01L212VmF1Y3dBdDVXdCtXQWFqQi9TRjFJc1hRSkcwSFlKUWJ1VzZEc09CWkh5eC9BVHhBdHZka0RtMkw0RzYyZnhZVFU2SWd5ZThJRFN6dElnZUVCRW10ZXg1T2wrRHlHYkVJYllXNE92R0JIZ2NpZzBhT1dqOEFOVHo0a0xmeVQrQXpCaVROMWFSM0N2OWNpaDRraXJBRGtpNXhjb2J0ZzJqYnU4QU45dlNSeGNaK2RKWWdJaTN1SVVvVE51MFRYQ0gyR0hJajYzYkhJR09tUkFwQ2h3eXU4YzhGaGVrNTB4QU1zUU4yNjlrcWFpOUdLZ2pRYjVsZ28xMFZURGQ3Y0ZtMlQxTGtPN0NxL0dYSGVKMkJGWEN4eFVBVTI3VnJhUjVBYzRxeE8yWGk4WldFREdJajVWV2ZmcElVK1BRQy9wS3c0TG5ZTGZXNDFpdTVVU0o4elNJWWdHZDZYNS9KUDYzWXFBV1RTWXYiLCJtYWMiOiIyZWU5NDdjZGFkMDAxZWQ0MmYyZDQzYjAyMTU3NTAwNzQ4OWIxNmVkMmRhMjcwNGVhNjYyZjAwNDA0NzI5ODYzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNoRnBhRGFrcDlQdUxVVW5uSkkyQ1E9PSIsInZhbHVlIjoiYmREVm1yMHdGb1crNVdHTVdGRjZWMm13dVFGVTVyREd0VUtHTVhGMk5QL3VrS2hqTWZNUGRza2pqSUtxdG9BSTR1NnBCc01mcDdCVWZvS0NRQmtPZWpSa2M1K2pET1daYlM4YmRWUnJDTkFXL1hUWEFsWGVrT0ExT3pvdWd3d0VOVEJCTWExRlY1TFR2d1FmSVdNWk5yNytzcU1YT2t0eUMzelpXN2xCb0NHTWhUMHJydkpzSm9uM3lJNTlCVkhISks3dXU1RDB5K2JyVVpRMlJ4cFpTZ25YQkNYWlJzVVRrWlZrWHJuQWlCbURHc1FsOXNoODRBakFWZkFkQ0RLODlPOHBRQ1Q3OUhyY1Z6Z1hyelQwL1JzRHpvTGE1REV0UXJ4bnRkUkRqZ05YNGVBMldwcUFTbnhPUW82cWhaQUdUNmRyTjdZQlB6NTZmUkhmdHZ6NUFycHJPTDJpa1liRmpBVnhvK3RFdEJYN2x2c0VQM2E2QWJZOXJNRGpjbmlkV2FlNk1oSkc2azQyVW44UE1aMHNna0J4cU9pZjdvNzZxTjFnWmFUakZwRUEzYVR5L2FubHFUMnNhYWV4U3hCMmxoTXAwNVZqNGRNNS9STk9yUHBwQWg0TjkxaU5oU0RCRktkMmhTMDVRWENqVU9kbWJDTVJRSjZuaVIxeTVsaXUiLCJtYWMiOiJjMTQzMDM2YjY2M2MxOGE2OWYyN2ExOTlhYWNhNWExMjRkYTYzMjkzMGU2NjJlMmJkNDkzMGQ3ZjgwMDZiZGI0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVmNE9VVGhlSHV6VGtWWXhiQ2VEOWc9PSIsInZhbHVlIjoieklvY3VRaDJPZmxpTzRwTllpR1E4bkd1TGJvQXdwb1Vpbm1NK2R0djNCSXZCQlpyalZ1bzhlcnBVaXAzMGpqYjhQcTBESXJ5N1d0SG9janpVKzlBTE5mMGdFRUc2WU1GWHVDc05mbzV3UHBkMXMvUU5QQXZ5ZittMmtTZUNNN1hpelRDYjViWFZIa3lPd25POEVPWm01L212VmF1Y3dBdDVXdCtXQWFqQi9TRjFJc1hRSkcwSFlKUWJ1VzZEc09CWkh5eC9BVHhBdHZka0RtMkw0RzYyZnhZVFU2SWd5ZThJRFN6dElnZUVCRW10ZXg1T2wrRHlHYkVJYllXNE92R0JIZ2NpZzBhT1dqOEFOVHo0a0xmeVQrQXpCaVROMWFSM0N2OWNpaDRraXJBRGtpNXhjb2J0ZzJqYnU4QU45dlNSeGNaK2RKWWdJaTN1SVVvVE51MFRYQ0gyR0hJajYzYkhJR09tUkFwQ2h3eXU4YzhGaGVrNTB4QU1zUU4yNjlrcWFpOUdLZ2pRYjVsZ28xMFZURGQ3Y0ZtMlQxTGtPN0NxL0dYSGVKMkJGWEN4eFVBVTI3VnJhUjVBYzRxeE8yWGk4WldFREdJajVWV2ZmcElVK1BRQy9wS3c0TG5ZTGZXNDFpdTVVU0o4elNJWWdHZDZYNS9KUDYzWXFBV1RTWXYiLCJtYWMiOiIyZWU5NDdjZGFkMDAxZWQ0MmYyZDQzYjAyMTU3NTAwNzQ4OWIxNmVkMmRhMjcwNGVhNjYyZjAwNDA0NzI5ODYzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142125078\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2********\", {\"maxDepth\":0})</script>\n"}}