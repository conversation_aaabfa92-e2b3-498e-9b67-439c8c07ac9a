{"__meta": {"id": "Xb1a485f75092a2ce6dc77b6546f5e266", "datetime": "2025-07-23 18:22:46", "utime": **********.104739, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.635812, "end": **********.104752, "duration": 0.*****************, "duration_str": "469ms", "measures": [{"label": "Booting", "start": **********.635812, "relative_start": 0, "end": **********.049547, "relative_end": **********.049547, "duration": 0.*****************, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.049557, "relative_start": 0.****************, "end": **********.104754, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "55.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00325, "accumulated_duration_str": "3.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.077627, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.923}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.088752, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.923, "width_percent": 14.769}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.095981, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 75.692, "width_percent": 24.308}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C**********225%7C3%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJFWDdYZERKclZVanpVdHhydVV1WGc9PSIsInZhbHVlIjoiRi9QL2NlNHg4MDdqNmZRalNUcGxzWm5QbFlzNkNOdVBGVGVHcGJUTFhkb1V6TkhBazlxMWEvai9DTG9uSGlWTTg1MHNSRXNnUVhLRGp2L3B6QWo0aFVqZ3E5SDVwTXUrS2JhS1dVc2VFZFJ3ZXBETnJGNGJTNU5vcFM4T2lBNGN6ZEoyQk1nUTdSbjEvaFZJRXpVTW8wRjRCTlZJZ2tLYnZRdjk4RVppdy9zRFJkZmFpTmJ4cjB4RUJmblUyRUZKRFE2cS92djhvWXBqWTVYaWZBZk04VEVRSmdLUDZZWGRIc3pRck4yOVNIY2g3TkNSNXYyVnErMDc1cDdIcy80TTIwMXlNazNZTjNGRVJpTW5URXFsVEN4em1sd1BFY1dHZXdKc3NqK3VzdlI4ckdmVFhJQU9MRStrcU5CampSOE5nd00zQ1NGK1l3QWpKeWJPcEgxdTFteXRUMkxPL0Jka3VsZ2RJamovcTdYRVl5RDRYT1lxZDJrbXFTS1dPTVNGbVdVYS81eG53VkVqWXVTMmtHNGExQVBFNG1EUWUvSkFxVjZSQ0VpWEdkNjZicC9GNmVZbnRsb2tCQVMwQVBRSWVoNStVbG9RMzM5S2lWOHRwY0ZZeVE5ZDVlSWxqa3FKamh4aWdLQU53bTNRVU82RDBLakxQWnM0cHdsUUUvNlIiLCJtYWMiOiIxMTk2YTY4YjZhMDJjODc2ZGFmODBjNjNmMDZhODM3YjBhMjUxYTdiYmYyMWNmMDE4MzdlMDYxNWRhNDE1ZWQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkFKZDlqZExVS1NNTDJKelVkdlU5Tnc9PSIsInZhbHVlIjoiWUxPMVlKa0FPWU9YUEc0UXJOQkVSMTdyanAya3dybzBuSVhnSzlmc3Rob1AyVEpFV0FSWGNKRGkzYXJOMjRBYnByU0xPS29MNWxXaEdpQmR2eDdPQWlZTC9EaXlua2t5Tk1BWEZyaFQrZmx0bDZ0Y291bWdRMHZaVFFPUldzTHdvQ2h2azl3SWVubFhDVTQxQkNDa1N3RnRpSnY2d1haNGR5dlZCZ255Rjl2bVVIRjBUdFhJbnBmenkxdkd5RzZHZndCSEhYNWkxM014VTQvYkhIWld2REp2Qm1Jd2VhckFxeTdZV0lWeldCTVRiUXZrU1ZYNTRNVVlZWHp4ZjNUUzI1aHp5U3FTdE1kaXVuNWFaU3dtMERiNHBVMWRvT3A5TkEvSHpLdnNKQ00xNldrMjNaSUdRYUQzOC9TanZ2aGJSZCtUNnFKOXZ1bUFDQU1rMjBJLzI2ekNvU3hNNHpyM2NONWt1ejdzTThjS3hnaU1tVS9BUCtqQmpTdUJvYzBVbVpQMWJIMkl1SWtDbDhIZ0F0QXBUSHJaUzVmSmEyQkFMaCtrLzJFdTJ6SWxaeDMwRkRZV2g2ME1tN09aZXNQQjEyODJBZ3VtdE4wTS9YcVdoU21nMjdTVXd3elM2QkJraUNnR3NlQXVMSG1lQTBRS1d6SGdsdjRFUEV2NmZmcDkiLCJtYWMiOiI4MTMzMmY4ODA4NmE2OTNiMDM3NjQzMWQwZjQ5MzJlYzIwNjdhMGUwODQ3YTdlOWE4YzMwZmY1NTcwOGY0MTg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2060146749 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6f86AL2UyJbEsnP8wClSGR79UCod8lW0WDs0fxKW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060146749\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1998841681 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVJM1JyRHJMczVvd0R1Q216TWJyOUE9PSIsInZhbHVlIjoiWi9sYmN0NVY3STYzVUp6bkdKSjFwb2hGT0NnajhWQUMyeldJQWg2d2tYL0xIT2pqcjdXaENWZGpZY3doaXMvcHY5azJ0VTRMbUZiK1FJU2tKTUw0czEwZGdyeWpTdldKT2pGU1U4YzlhNDJZalJvc3VJVi9kVjlrTHhSYXZ4VnIwTFp0MTd0RjZrb3BnNXVSWXlBdnk1WWlXdFFBRXllMVVrTGZPUFBQWnVsMG9KNmdmUldtVWtjN2V0ZUlzdXhKUmViK0owckRwenVyRFdVVEVxM1pVOEFDSU5QODdYN09PVVptL3hyOTFIS2NJNnQrRDNBZ2hDZlM5d2FCZzMreG5UQ2dwbFZQSHNmU3hyVERjd2pBQWFnNHNvOHBWcGQwVjhUVGxwZkxXWmVpaFFwRUxWc1pJNTNGa1RJMXU2c2ZqckZCQlRRM0piZzdFME1Vd0UyYW5Gd0tMTGdRR0JiSXRoVFRRYnRCaHZnbzYrckFxYnhGS0NDVWhsRlRBeTQ4SkFmSXp1N2YyYys0aHI1ZzV0bFpBcHNNYmxva3NtMTB3ZGZVZnEzbjlZMDlkdjdoMzNtYTRZS0wvcGF4QU1SZm9JMU81bGFiaXVFSFJxUzBTRDJNSHo5TC9uZHR1WEN4Z2tjcWtJOWFqeXNPT0xjMm9xcEREQWMrd3FQUlJOS28iLCJtYWMiOiI5ZDk1MDBjNTY2ZDZkYzhkZTNkNjJlMDRhZTRhY2UxNzc0NzU0NTQ3YmYxYmU5YmE5NDUxMWFlMDMzNTQyMDRhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inova1ZMY2xQRU5ZSzJLM096a0swR3c9PSIsInZhbHVlIjoieERDeTEyd0xKbUlUT1Z6U0xnQlVXdDMrQXFFY1BxSmsxdDlxYzVsSFZkMDlHMlJhNHVkVE51RkFjSGRrU25PT2QyN2svd2ExQi9lUXhvaXBWVFJxbHc3dytDU0Ywb0RlRFpjdWpzaUtIUDV0Tm5RbE92d0JoTUNuMWdKMkNRenp5YWJwQWZiTzlTSVZVNEsxei9MdXhUVDZBbnpZcWZHKzFzVndkTFpSVWprRUYxVkFqdDMxWm8vSmpPckJKcHYva3JQNXZNUmpqakZFKzFpbWhXUGlhWXhjNldnTHRsb1Ntc0xpT1Y1UTZvQmVudStNSVUrcjE5NVMzdGVjZ0I4M1J5RXUwYzRMdDVCQ3oyT3ZrOVc2KzZhcEZ4TzZKNk5EYVExZDhuUU1veWl5OUo1M0pGL25jYXJ5cm5lRGFheDJlbFJzSjlrNjYyMUdmeUN2emVFejF0ekVudjNPKytoN3NhNmFZWHNPY3BwTHB1MDBKeEpSc3lWc3VyT3Q4S3M5WkFVMlFKbG85MjdJYTViVENZakx4RlBQZzcvZDBHUWFyclFOamQ2dlF2cEpQMXJRZ3ZVNWdzdm1OdHpGZ2tua2hwZ0dDQVM5VGViVXFqaHgyZlNTMEZMS1dQOFo2ckZCaEhQY3VJbWNZSnpmQWFaNnNZR1JsbmFwem5odFdHMlMiLCJtYWMiOiIxZDc2ZTgzMjQ4MDRhOGMxZjMxMzcwNThhMDY2MTI1MTllNGVmZDQxZDRhOGNiZmU1NTM5MmI0N2QwN2M5OGQzIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVJM1JyRHJMczVvd0R1Q216TWJyOUE9PSIsInZhbHVlIjoiWi9sYmN0NVY3STYzVUp6bkdKSjFwb2hGT0NnajhWQUMyeldJQWg2d2tYL0xIT2pqcjdXaENWZGpZY3doaXMvcHY5azJ0VTRMbUZiK1FJU2tKTUw0czEwZGdyeWpTdldKT2pGU1U4YzlhNDJZalJvc3VJVi9kVjlrTHhSYXZ4VnIwTFp0MTd0RjZrb3BnNXVSWXlBdnk1WWlXdFFBRXllMVVrTGZPUFBQWnVsMG9KNmdmUldtVWtjN2V0ZUlzdXhKUmViK0owckRwenVyRFdVVEVxM1pVOEFDSU5QODdYN09PVVptL3hyOTFIS2NJNnQrRDNBZ2hDZlM5d2FCZzMreG5UQ2dwbFZQSHNmU3hyVERjd2pBQWFnNHNvOHBWcGQwVjhUVGxwZkxXWmVpaFFwRUxWc1pJNTNGa1RJMXU2c2ZqckZCQlRRM0piZzdFME1Vd0UyYW5Gd0tMTGdRR0JiSXRoVFRRYnRCaHZnbzYrckFxYnhGS0NDVWhsRlRBeTQ4SkFmSXp1N2YyYys0aHI1ZzV0bFpBcHNNYmxva3NtMTB3ZGZVZnEzbjlZMDlkdjdoMzNtYTRZS0wvcGF4QU1SZm9JMU81bGFiaXVFSFJxUzBTRDJNSHo5TC9uZHR1WEN4Z2tjcWtJOWFqeXNPT0xjMm9xcEREQWMrd3FQUlJOS28iLCJtYWMiOiI5ZDk1MDBjNTY2ZDZkYzhkZTNkNjJlMDRhZTRhY2UxNzc0NzU0NTQ3YmYxYmU5YmE5NDUxMWFlMDMzNTQyMDRhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inova1ZMY2xQRU5ZSzJLM096a0swR3c9PSIsInZhbHVlIjoieERDeTEyd0xKbUlUT1Z6U0xnQlVXdDMrQXFFY1BxSmsxdDlxYzVsSFZkMDlHMlJhNHVkVE51RkFjSGRrU25PT2QyN2svd2ExQi9lUXhvaXBWVFJxbHc3dytDU0Ywb0RlRFpjdWpzaUtIUDV0Tm5RbE92d0JoTUNuMWdKMkNRenp5YWJwQWZiTzlTSVZVNEsxei9MdXhUVDZBbnpZcWZHKzFzVndkTFpSVWprRUYxVkFqdDMxWm8vSmpPckJKcHYva3JQNXZNUmpqakZFKzFpbWhXUGlhWXhjNldnTHRsb1Ntc0xpT1Y1UTZvQmVudStNSVUrcjE5NVMzdGVjZ0I4M1J5RXUwYzRMdDVCQ3oyT3ZrOVc2KzZhcEZ4TzZKNk5EYVExZDhuUU1veWl5OUo1M0pGL25jYXJ5cm5lRGFheDJlbFJzSjlrNjYyMUdmeUN2emVFejF0ekVudjNPKytoN3NhNmFZWHNPY3BwTHB1MDBKeEpSc3lWc3VyT3Q4S3M5WkFVMlFKbG85MjdJYTViVENZakx4RlBQZzcvZDBHUWFyclFOamQ2dlF2cEpQMXJRZ3ZVNWdzdm1OdHpGZ2tua2hwZ0dDQVM5VGViVXFqaHgyZlNTMEZMS1dQOFo2ckZCaEhQY3VJbWNZSnpmQWFaNnNZR1JsbmFwem5odFdHMlMiLCJtYWMiOiIxZDc2ZTgzMjQ4MDRhOGMxZjMxMzcwNThhMDY2MTI1MTllNGVmZDQxZDRhOGNiZmU1NTM5MmI0N2QwN2M5OGQzIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998841681\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-443780734 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443780734\", {\"maxDepth\":0})</script>\n"}}