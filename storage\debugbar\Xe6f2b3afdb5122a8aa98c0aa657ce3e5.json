{"__meta": {"id": "Xe6f2b3afdb5122a8aa98c0aa657ce3e5", "datetime": "2025-07-21 01:34:42", "utime": **********.195304, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061681.767772, "end": **********.195327, "duration": 0.4275550842285156, "duration_str": "428ms", "measures": [{"label": "Booting", "start": 1753061681.767772, "relative_start": 0, "end": **********.145408, "relative_end": **********.145408, "duration": 0.3776359558105469, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.145418, "relative_start": 0.37764596939086914, "end": **********.195328, "relative_end": 9.5367431640625e-07, "duration": 0.04991006851196289, "duration_str": "49.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991608, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00269, "accumulated_duration_str": "2.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.172701, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.491}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.182464, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.491, "width_percent": 13.383}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.187787, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.874, "width_percent": 14.126}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=8&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1110645187 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1110645187\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-466151225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-466151225\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1999278985 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999278985\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2141531986 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=8&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061664990%7C13%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZZVUw2SkE0bU5lVVE3cVFsZmwrRkE9PSIsInZhbHVlIjoiSHNTOG9DMjJUeTVxbUhLa21rRmM5ek95WDJpWDQ2N2FtelZCRDZtQTJ2V1F2KzV6MGZ2c0o3VzRnRFpadnVhcGxOU3prUVRjRHN0K3VGWUVOakJTTjFLUUFpRmMwaGZqbnRObTdYcDB0aVBDWjd0QmhkZEFZQ0tGN2poM3dvZURGYzlHMHZkNVc4RllBSmwrRW9wYWpMb28zQnBqTUdRa0NXcjJhRWhhOWM3MTgxeld3QjR5Wkc2NE5jTkxITG1DTzhrNjJINmJnTGt0N0VzUnluZWRpcDBXdXhVUEkwN2Q3MVhwSXZRWHNTbkF6a1VVcTdCSWhnN3o1dnh2TkMrWEJRbTdLVzFuWVNsWTgvTUliUEF0K2pQcU5PMnNiOGdINEwzNjNWZWJ6Y1hyRDJBZ1FPZldzNHpKOG1kSlVMMFRWQ1VMWnkyZzc3OGswZWhNd3lJL1dWSXBIRFRsUW5Ea1ZPbHJxUGhJRDc5SFFMaWFGMkw1RzU2MUxUR1hzTnVPZmlUT2YyRXZkU2lzV1QyamdIekRGRU5QWXloRlYrUEs5UWphNlE2Y0h5L3YxL0l5ZkpmL1NxVFF6VklrUDdYd2dFZzdhZkhQU3VlUFRLYStkU3JBT2U1VFRXZHB6TGlPNFZsN2JMTE1BNXAzZENlVy9hblZJQnh3UitmSk5pL2oiLCJtYWMiOiIyZTZmMTY5Yjg1ZGY3NWFmZjk4ZTM5ZDAzNTViOTUwM2JkZDY5MzM2MzgwZmI4N2NkYzQ5YTk0ZmU2MjQxNGVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlBKUkJBQ01ncTZPdFErQXBqdW1EOEE9PSIsInZhbHVlIjoiN3VGMEVjOThmdWZPOVpNSFNYYnpwc1RBYkZ3ZkU2SkN2VmttVU1UTjlJNTE1MGluNm1iZzJ5S1VBT2FaNFZnclpoUkd0ODhoTGgzbURrVmpBa2dSanVPMHo5bzBybnVFQXZNaGFzWVJTV2ZYcjhmMWhuTkg2ODJVeGRlOG1NbW12b3RCU0gyZGE4UDd4WC8xUEV6UG1DRlU3U0tiYm5lY3FRZVFnNTdsQkZyQVZRVjRmYUNUUEI5d08xaDZRSHE5c0I3U2ttQkRWc1Z3RkJZbWZUSTlFUmtLVC9RTmw4Z2g2L25HWlhWR3k0Y00vZXlQbnBRcElDcDdMZ0tINm81Y0x6QktML1lxSWN4V3Frc3VyRTV3SGYwbDdBakFUOGh2cVE1UWFtQXVibXNaZC9XQXJSbU5DZFl4d2tWbWlXK1Zrc20vVS8vSUxLN1J1T2NIbkZ3QW1vZUR4bjhWZy9ZOXZzalp0UGdyVFFSaVVXenZCc20wTTViMUNJelMzS0ZGWVlBd2p1YllzZnk5Wmt5NGV4YXlFWk9hbnN5TGZPRE5qMk9SMld6cWtFN3l0VFdBYWdVcVQ5cXhTa2hlU2xCQkY5Qld6Y2JhUmkrV0dKcmNjNkZhakF4bzM3YTBQNHBVOWdqTUQ1VHdaSk9GZWtHZkxUamZRR1RLNWxSS2cvZjUiLCJtYWMiOiIzYWQyZTVlNDg5MmUzY2RmNTliZDAyM2YxNzEwOTg5MDZhZGRkYmMyMTcyYjM2N2VmMWQyMTBjZDkxYjliYjhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141531986\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1110663762 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikg0eGpNbks1dW9LSDZ0Y0hZb2QyNlE9PSIsInZhbHVlIjoiQ3FKQm8wSTRvR1NBdUtHdGlCSkFWRGxoYzFVcEhxczk0V1QxV0F5U09OaVVaTktwRGRBYjFNTFVPU01FZUpEc0JUck9UOUhHeHJnb1hSclZKUU1DKytHMTFlbjN3Si83SzlFMmRSTnJNamZBWnFzczhjWkxNQ0lDM001VXpKWXZZZW13VVFNdmwyRXZFVXZTSXlNYjFWK3JQbE1UM1dkeTJ0TmlMNEN3Szcva3hvSDdCbEExZnBTQk9CemM3Z0dkL1dLdEhqOWV1aWhMT0JlVyttdUtGaXFiV0c3bEF1TnpiMU1TVzd5N0RsQlJPcnhoOWtxeDYxWnJmQmNOZWhBYi9nc3IzZG84Y1VHV2plMlNRdnkvY3NXZmt2RS9qTmZoYlBVUEpLVmZhQjYveVhjU0lFT0hzRDlvb28yVGdNajBGREVFQzVOZnFlL293aHViTm55cGpPd2hNMGNmTThHVmRXWlg2OHNVWXY5azZlNzA0Mm1DQ0xXZEF0ZWtJSko3NmxVWTVNV0NIYWJCZGhEbCtJU3RmNSt1R3lPdnErR1RUb0F4TEpHc3dudG14K3k4TjJxTnlzNFZIZC9qdDZhMnFrV1k0aCs2SUYwbnZRa29mSE9PUFJFN2xteW5RdGpkdGFGWEMxUEd5clpmK1hIaHR2SFg0VTdZTmNGMGhPcWMiLCJtYWMiOiI3NDQwMDQ4Yjk3YmNmYTU4MDI1YmY1Yzc4NTNhYzkzMGM3NDQyNDRlMjczZmE3NWU5N2U3OTIzMmFiNDJkZjgzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdsZjMwSS9pbEwzYkZXQVJreHlqNVE9PSIsInZhbHVlIjoiNEFWN0ZRTlhUdGxmYklyZm9wTWxDS0h6ekJsTVgvOStDY3BXMXVFQ2JhMk9NR2xmb0paU0FwU3lMWkh1NWJmTGZPSFdpMjhaYk00RjF4YWFhUE8ra1JINXZ6SnFld1R3SWcwRG1rbGlyZWlVdjNvekRFUEtGSWRZalV1YXIwUUFudEQrVm43dWR4dnduNVVzaHdwZHBJVXp0Uy82TWxFTC90WkVoRFNhYTFxQ20rR0pjVDZsNHEwMTgwRnBsWGpUbVhpVXdxdFBodmtzd0VvMGFrWmhsSk5lTThzUkYyZy9MbWg4SmVxOWNnTVQ5dUdYUjZkNkRJMzNJTTl4U3JYWjBUSFlycEp2Qis0Tzh1dEJhQmVxQ0M0UGlmbHNxQ0xhM2N0THpYRUg3ZDdxNXZGcjd0ZkxKY2NGNjIrTURQQlZqK0d2d2VLbllSOEwzSExCMkllZEJlTjA2eXFBZWFUOUkvRXBWR1J2TFhhNzdqV0c3bnAvemhGRjFuR3dpd2JwWDRkRDBpSVhSN21oeUdHc3UvVGt6L0p5WktiR3JxNDRYcUVPTVlNRkdiQVlKNWhFN0ZMTWhPUTNqSXhxUWFBTkNUcGw3eU1ZTUlTZ3Q2aWJKMmROWGJkdUJzK0lLYm5jZE1IZnVxZzJaYktMUjlGd1Nmc2p2Y0phYmJWcWdvZWgiLCJtYWMiOiIxZDA4ZTgwNjk3ZGNmNmMyMDUxYzViMzM5ZjdmNzIxYmRmOWMyNzVlZmNmYjEzYTQ3MGUxMzI0YWFlYWEzNDEyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikg0eGpNbks1dW9LSDZ0Y0hZb2QyNlE9PSIsInZhbHVlIjoiQ3FKQm8wSTRvR1NBdUtHdGlCSkFWRGxoYzFVcEhxczk0V1QxV0F5U09OaVVaTktwRGRBYjFNTFVPU01FZUpEc0JUck9UOUhHeHJnb1hSclZKUU1DKytHMTFlbjN3Si83SzlFMmRSTnJNamZBWnFzczhjWkxNQ0lDM001VXpKWXZZZW13VVFNdmwyRXZFVXZTSXlNYjFWK3JQbE1UM1dkeTJ0TmlMNEN3Szcva3hvSDdCbEExZnBTQk9CemM3Z0dkL1dLdEhqOWV1aWhMT0JlVyttdUtGaXFiV0c3bEF1TnpiMU1TVzd5N0RsQlJPcnhoOWtxeDYxWnJmQmNOZWhBYi9nc3IzZG84Y1VHV2plMlNRdnkvY3NXZmt2RS9qTmZoYlBVUEpLVmZhQjYveVhjU0lFT0hzRDlvb28yVGdNajBGREVFQzVOZnFlL293aHViTm55cGpPd2hNMGNmTThHVmRXWlg2OHNVWXY5azZlNzA0Mm1DQ0xXZEF0ZWtJSko3NmxVWTVNV0NIYWJCZGhEbCtJU3RmNSt1R3lPdnErR1RUb0F4TEpHc3dudG14K3k4TjJxTnlzNFZIZC9qdDZhMnFrV1k0aCs2SUYwbnZRa29mSE9PUFJFN2xteW5RdGpkdGFGWEMxUEd5clpmK1hIaHR2SFg0VTdZTmNGMGhPcWMiLCJtYWMiOiI3NDQwMDQ4Yjk3YmNmYTU4MDI1YmY1Yzc4NTNhYzkzMGM3NDQyNDRlMjczZmE3NWU5N2U3OTIzMmFiNDJkZjgzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdsZjMwSS9pbEwzYkZXQVJreHlqNVE9PSIsInZhbHVlIjoiNEFWN0ZRTlhUdGxmYklyZm9wTWxDS0h6ekJsTVgvOStDY3BXMXVFQ2JhMk9NR2xmb0paU0FwU3lMWkh1NWJmTGZPSFdpMjhaYk00RjF4YWFhUE8ra1JINXZ6SnFld1R3SWcwRG1rbGlyZWlVdjNvekRFUEtGSWRZalV1YXIwUUFudEQrVm43dWR4dnduNVVzaHdwZHBJVXp0Uy82TWxFTC90WkVoRFNhYTFxQ20rR0pjVDZsNHEwMTgwRnBsWGpUbVhpVXdxdFBodmtzd0VvMGFrWmhsSk5lTThzUkYyZy9MbWg4SmVxOWNnTVQ5dUdYUjZkNkRJMzNJTTl4U3JYWjBUSFlycEp2Qis0Tzh1dEJhQmVxQ0M0UGlmbHNxQ0xhM2N0THpYRUg3ZDdxNXZGcjd0ZkxKY2NGNjIrTURQQlZqK0d2d2VLbllSOEwzSExCMkllZEJlTjA2eXFBZWFUOUkvRXBWR1J2TFhhNzdqV0c3bnAvemhGRjFuR3dpd2JwWDRkRDBpSVhSN21oeUdHc3UvVGt6L0p5WktiR3JxNDRYcUVPTVlNRkdiQVlKNWhFN0ZMTWhPUTNqSXhxUWFBTkNUcGw3eU1ZTUlTZ3Q2aWJKMmROWGJkdUJzK0lLYm5jZE1IZnVxZzJaYktMUjlGd1Nmc2p2Y0phYmJWcWdvZWgiLCJtYWMiOiIxZDA4ZTgwNjk3ZGNmNmMyMDUxYzViMzM5ZjdmNzIxYmRmOWMyNzVlZmNmYjEzYTQ3MGUxMzI0YWFlYWEzNDEyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110663762\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1945157714 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=8&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945157714\", {\"maxDepth\":0})</script>\n"}}