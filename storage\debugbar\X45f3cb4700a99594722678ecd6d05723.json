{"__meta": {"id": "X45f3cb4700a99594722678ecd6d05723", "datetime": "2025-07-21 01:17:45", "utime": **********.801706, "method": "GET", "uri": "/pos-payment-type?vc_name=10&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.273431, "end": **********.801719, "duration": 0.5282878875732422, "duration_str": "528ms", "measures": [{"label": "Booting", "start": **********.273431, "relative_start": 0, "end": **********.631648, "relative_end": **********.631648, "duration": 0.3582170009613037, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.631658, "relative_start": 0.358227014541626, "end": **********.801721, "relative_end": 2.1457672119140625e-06, "duration": 0.17006301879882812, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54306824, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.749957, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1426\" onclick=\"\">app/Http/Controllers/PosController.php:1426-1534</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02165, "accumulated_duration_str": "21.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.667981, "duration": 0.01799, "duration_str": "17.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.095}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.695956, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.095, "width_percent": 2.448}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7108831, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 85.543, "width_percent": 3.788}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.713223, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.33, "width_percent": 2.494}, {"sql": "select * from `customers` where `name` = '10' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1437}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.718152, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1437", "source": "app/Http/Controllers/PosController.php:1437", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1437", "ajax": false, "filename": "PosController.php", "line": "1437"}, "connection": "kdmkjkqknb", "start_percent": 91.824, "width_percent": 2.494}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1438}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.720342, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1438", "source": "app/Http/Controllers/PosController.php:1438", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1438", "ajax": false, "filename": "PosController.php", "line": "1438"}, "connection": "kdmkjkqknb", "start_percent": 94.319, "width_percent": 1.848}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 533}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1442}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.723663, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PosController.php:533", "source": "app/Http/Controllers/PosController.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=533", "ajax": false, "filename": "PosController.php", "line": "533"}, "connection": "kdmkjkqknb", "start_percent": 96.166, "width_percent": 1.663}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 1521}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7393072, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1521", "source": "app/Http/Controllers/PosController.php:1521", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1521", "ajax": false, "filename": "PosController.php", "line": "1521"}, "connection": "kdmkjkqknb", "start_percent": 97.829, "width_percent": 2.171}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1354237874 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354237874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.717101, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1237861405 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237861405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.72303, "xdebug_link": null}]}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  1897 => array:9 [\n    \"name\" => \"option- خيار\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1897\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1896 => array:8 [\n    \"name\" => \"zucchini-كوسا\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"id\" => \"1896\"\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1558978907 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558978907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-809062812 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-809062812\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-240067075 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRCTjRBQTNiZVRCYVhuQ1dZdURUOGc9PSIsInZhbHVlIjoidVQ4U1kwWEJ0WUFLNVF3Njg5a1lRSDB5UXVrL2xjc2R0WFdjWVJ2U2lnR1F0bmMvaHoxNUdScHkzTktmRklqZHZOb2VhOW5xRThURDNpQTFZZHJ4ek43aGYzNlE0c2I3T0J0M2FENEV0L25kb2JmQmRZV1pOK3NRTlBFSDREQzdieGdoaGxXemtJRFlTc1NKQ3lqNXAwUVdjRE5yalRldTBTSTh2N0lnMVZBN20weXZsVTBzSHhEbWZ5S0s3d3gvYm8waVRZUkVacWhUWm41bGJDdmxUUXVyNFNVTnBXeit0SXBRRVJ1bmN5eVlRbVNYSW5IdjBLbjJhcnMrcDlBVjVaeVJvM2RVRHkxZVVudFhaa1RXeUVFYlBPVEp6eUJjWWpKb1JiOFVCZEozcWMzZnpoVmVEZ3hLb25CZlBWaFkvbTNHRFR4bzF5a0pkQ1R2bHFjZjdzd2dndzNzbzRuL2pUUjEzWTNsbVV2bkxUM2kyanU0bFZCZ1dBVVo4NmUrVFNzNlpUQmdGMXRhbVFoVUtRUW01VWVLdHpseVVQZUdqMzJDcDB3K09kM0VYb3Z6bFQrK294T1lqeUp5K2hNYnhLd1lKbFZydENWWi9aS09KOVBPbzBvMllsVnNTb2x3NGw1V1dPRndpdzd4UW1CbTRKcXhNM0l4YkJSMDQ4K2QiLCJtYWMiOiIwOGI1ODg2Y2Y1ZDAxNTk4ZTgwODhkZGQwOGNiM2EyMmFiMmQ3MDkyZWI2MmMxNGNkNGE5Y2I5YjdiN2U4ODBkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9FTmZQU3BxVGZCK2YwSDJuazg4SGc9PSIsInZhbHVlIjoiTGtlaVcrejMvdDhzNG5MOUw2T3BzTlMva3d3bWJVeG9WR0lzemhoRUpyTndnWWU5amdhbk5pY0VoQ252Mi9ySkg3OGFDZEJyMnZOU2ZmVTFKMTRRZE92Q05ST2FQNHd0QS9SdVRydDU0eXhnd0dSSHBDNVBrelpBWlFYSlo0RVFvSG9hMEJRUThVREFhR0gySEMxV0hMcWY4ZFJPSDhYM2tEN2tKVVROQURMQWZTdWhDbUN5Wll6TTZVc0k2bkoyQWVhNE5vWTNoUE1MQlpIeDlkajFoV0JscFRBRHZRQzR2cEx6NWlOcjV6NzRPWFM0RGRnN0NLTWFxbml6cGcwN09RTitBeE91OFhleHZjZWoremFROU1oQ3oxUDFnNFV4SjBWUlVKUkpvTHF5QlZQd09NekNlUDhCd0paN0MyczVMb0lpdkVCTU4wOEtaNFpMV09XYVlSZ09VendlMXhKR0Z6WjFsZHpla1pKMFZHWEhQbzZrYXFMcVQxNDZlbnBua1VCUC83eDlVVnNuR2NGOEplU3NmNk5KaXlNMVlZT29KeVhMc3pxT3E4b0R1VVhRcDQyNzR1a09KYWJQZVJacExha21KcFpLWW93aExWNnlUT09EYktJSnZ5SVk0dWVUOTFLVHJNdmVEWHdtcWpnRmwwbU9YMENnVndlLzdiRmYiLCJtYWMiOiJmOGEyN2VkM2M3NDllNTEwZTIxZDU5MGJmOTI2YzE4NDYxNTMzZGVmZDkwNmY2ZTZlNDlmMzg3YTNhOTdkMDk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240067075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-336247021 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-336247021\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1236571820 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imp1TXZ0WkdUaFc5RS9oZVFCcHVEQVE9PSIsInZhbHVlIjoiUmg3SWtYSk1OOWJPU2ZOK1JMZExUeXBlRFcyK1JOZUdSOTdRVXcrRWRuMGUyeGpLalJ1eUhwL3VjUmRkb1pVam9OQXBvVVRWKzQyVVREMjA0VS94SERyNDMwR2tpa1hBcnFiWDQzZEt1OExveFhMZFlxVkV5bU1NeWE5aWVpWGF1ajdLbmhIVWdrRjJ5NUYxWStULzdJcklzK3crMHQwZitxV0tyN3pHVmkvbE5Vc2VVNVBKem9lVVAxOTlka09GeGozM2NSNjcxWW13SUVVMFNsVGtKeDhQbUtZbkx2OTVtYThWOStIUGYvUEI0ejdhZmRIR2JIbitvY1dyYUNzeFluWjA1Q1pyRDBlWkozOEZMZktHMU5la2FiOTVwMlg1eDdNQ3g1V0xlaUpYZER2L3U2RzFaOURNOXVpcFNnbkN4TllYRG8rcVRVUEUySXFLQlFQdnRwVFRkek1vcTRBaWVPaGM3a3dOdlhuMkUxZjl2QkJJM0RFbTdueVlGdk1KSHVZY21qeGhZUlhjMFY1ZHNpa1FhVHI2bWVvSk8yelpFMUFTUUVHYkVrZ25hY0pXTnc0TlhUby9yY2Z3cVFEVWl4ZW96NFNQRFRTY2JVcStZWjVDbFNQNkFramNESGhMTkVNMTRnNDd6MmF0cGZVMTRNTWJEWjlpaUNTWGZCa2siLCJtYWMiOiJlMDQ0Y2U4M2I3MjNjNTZiODE4MTk4ZTBjYmVhY2U4ZTJjZjViNDJiZmZiYTRmYTRmYTNiYWVlODdjM2JiM2Y2IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtBSlhWcnRrQThjQ2pvdDdjbGdqU1E9PSIsInZhbHVlIjoiTHQ2WmorVDhKeTdWb2ljdG84NjQvWk1zdjUxMitpdWNzc21XcVRGenVhMmtaMTJYZE4vVTFLVldCMWI3V29CekVVTlgrR1JYbGQvRDZKcVJXMjZSM0oxdUJQU1VndGY5UUVsclFXL291UDgvQll2eXdWSDBJWUFTVFFQcUUzZURiWFR6Z3JaZEtiSlVuNDljYzY0WXJwY3RmQ1NBaHM3S00vZm9VTElpOE1tUWJBdnB0WUJGOHRPb2Z6bXZSWW8rZy9aY09nYjhUMUVVTmp5NVMzTWcrVzZLMGlMTmRaMlFhT3VzOEE4TGFYVXUxVys5MU80OS9PRTJUZlUyLzVHSzZWR2g2S3F5NTYrYUpSS09xYkRZZmUwN3Rxd1ZXNFlaQmhMTVNLQ29OUDBuRER4RlF2SVpVdm1iL1VBWnRJSUVvTGV2Tkhab2l2RjFZVlEya2E0TTRVdWkvSmxKZUhucE1Sa1NKdEtzdTA1SXJRWFlramZvV2VOZ0RzNVFNYlhOSmVHY0laUnFsMjd2Q3YxSGlvb0dIcFVFS0NTVHM1ZGVUUU1jRGlsSHNKVjVQVjk1Z1dLQmJxZkxlMmh6RG9KZE9ta0MybkVPVXVrdzVaNEF6VnlmUHlQZ1Z1c0hrdDJ6YVIwZ0oxeFIxRjBMMUZQYm56NmVNNDBVYTd6NU0vN2wiLCJtYWMiOiJkNDMxMWQzNDBhZTQwNWYyYjMyYmZhZDRjNGEyMTJmOGI4NGQyNzA3OTEwMjRiMmY0Yjc1NTBhZWY3YTc4ZmQ2IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imp1TXZ0WkdUaFc5RS9oZVFCcHVEQVE9PSIsInZhbHVlIjoiUmg3SWtYSk1OOWJPU2ZOK1JMZExUeXBlRFcyK1JOZUdSOTdRVXcrRWRuMGUyeGpLalJ1eUhwL3VjUmRkb1pVam9OQXBvVVRWKzQyVVREMjA0VS94SERyNDMwR2tpa1hBcnFiWDQzZEt1OExveFhMZFlxVkV5bU1NeWE5aWVpWGF1ajdLbmhIVWdrRjJ5NUYxWStULzdJcklzK3crMHQwZitxV0tyN3pHVmkvbE5Vc2VVNVBKem9lVVAxOTlka09GeGozM2NSNjcxWW13SUVVMFNsVGtKeDhQbUtZbkx2OTVtYThWOStIUGYvUEI0ejdhZmRIR2JIbitvY1dyYUNzeFluWjA1Q1pyRDBlWkozOEZMZktHMU5la2FiOTVwMlg1eDdNQ3g1V0xlaUpYZER2L3U2RzFaOURNOXVpcFNnbkN4TllYRG8rcVRVUEUySXFLQlFQdnRwVFRkek1vcTRBaWVPaGM3a3dOdlhuMkUxZjl2QkJJM0RFbTdueVlGdk1KSHVZY21qeGhZUlhjMFY1ZHNpa1FhVHI2bWVvSk8yelpFMUFTUUVHYkVrZ25hY0pXTnc0TlhUby9yY2Z3cVFEVWl4ZW96NFNQRFRTY2JVcStZWjVDbFNQNkFramNESGhMTkVNMTRnNDd6MmF0cGZVMTRNTWJEWjlpaUNTWGZCa2siLCJtYWMiOiJlMDQ0Y2U4M2I3MjNjNTZiODE4MTk4ZTBjYmVhY2U4ZTJjZjViNDJiZmZiYTRmYTRmYTNiYWVlODdjM2JiM2Y2IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtBSlhWcnRrQThjQ2pvdDdjbGdqU1E9PSIsInZhbHVlIjoiTHQ2WmorVDhKeTdWb2ljdG84NjQvWk1zdjUxMitpdWNzc21XcVRGenVhMmtaMTJYZE4vVTFLVldCMWI3V29CekVVTlgrR1JYbGQvRDZKcVJXMjZSM0oxdUJQU1VndGY5UUVsclFXL291UDgvQll2eXdWSDBJWUFTVFFQcUUzZURiWFR6Z3JaZEtiSlVuNDljYzY0WXJwY3RmQ1NBaHM3S00vZm9VTElpOE1tUWJBdnB0WUJGOHRPb2Z6bXZSWW8rZy9aY09nYjhUMUVVTmp5NVMzTWcrVzZLMGlMTmRaMlFhT3VzOEE4TGFYVXUxVys5MU80OS9PRTJUZlUyLzVHSzZWR2g2S3F5NTYrYUpSS09xYkRZZmUwN3Rxd1ZXNFlaQmhMTVNLQ29OUDBuRER4RlF2SVpVdm1iL1VBWnRJSUVvTGV2Tkhab2l2RjFZVlEya2E0TTRVdWkvSmxKZUhucE1Sa1NKdEtzdTA1SXJRWFlramZvV2VOZ0RzNVFNYlhOSmVHY0laUnFsMjd2Q3YxSGlvb0dIcFVFS0NTVHM1ZGVUUU1jRGlsSHNKVjVQVjk1Z1dLQmJxZkxlMmh6RG9KZE9ta0MybkVPVXVrdzVaNEF6VnlmUHlQZ1Z1c0hrdDJ6YVIwZ0oxeFIxRjBMMUZQYm56NmVNNDBVYTd6NU0vN2wiLCJtYWMiOiJkNDMxMWQzNDBhZTQwNWYyYjMyYmZhZDRjNGEyMTJmOGI4NGQyNzA3OTEwMjRiMmY0Yjc1NTBhZWY3YTc4ZmQ2IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236571820\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1164297073 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1897</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">option- &#1582;&#1610;&#1575;&#1585;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1897</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1896</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">zucchini-&#1603;&#1608;&#1587;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1896</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164297073\", {\"maxDepth\":0})</script>\n"}}