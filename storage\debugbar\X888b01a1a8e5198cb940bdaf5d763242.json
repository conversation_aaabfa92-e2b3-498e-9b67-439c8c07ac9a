{"__meta": {"id": "X888b01a1a8e5198cb940bdaf5d763242", "datetime": "2025-07-14 18:17:46", "utime": **********.79036, "method": "GET", "uri": "/invoice/eyJpdiI6IkM2cGlaZ2pTc3F0NTR5QXNsQkdERlE9PSIsInZhbHVlIjoidlhZcjVsSWNDYks1cElNeSt2K0Ftdz09IiwibWFjIjoiNjBkOTY5NzJiODI0MzdmNzBhNzhmM2Q0ZmE5YjQxM2JmYzE2Yzg4ZWQ5MWI4ZTI4YTRhYzQzZGM5ZTJlNjhiNyIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 259, "messages": [{"message": "[18:17:46] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.675882, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676073, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676152, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676225, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676293, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676359, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676429, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676495, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676562, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676637, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676711, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.676989, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.677478, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.67764, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.677716, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.677821, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.67791, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.677997, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.678082, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.678167, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.678322, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.678487, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.678669, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.678832, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679171, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679331, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679407, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679478, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679548, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679631, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679703, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.67979, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679865, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.679935, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680005, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.6801, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68022, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680319, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680423, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680555, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680672, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680743, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680809, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680876, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.680947, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681018, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681089, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681164, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681234, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681316, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681388, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68146, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68153, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 18.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681602, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681672, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 18.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681742, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 19.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681814, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681886, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 19.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.681955, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 20.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.682092, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68227, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 20.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.682391, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 21.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.682497, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.682602, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 21.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.682705, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 22.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68285, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.683074, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 22.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.683262, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 23.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.683414, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.683536, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 23.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.683647, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 24.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.683784, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.683922, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 24.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684046, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 25.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684163, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684323, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 25.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684586, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 26.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684673, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684743, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 26.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684843, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 27.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684919, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.684988, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 27.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685056, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 28.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685125, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685196, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 28.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685279, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 29.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685352, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685425, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 29.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685494, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 30.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685565, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685643, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 30.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685714, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 31.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685787, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685857, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 31.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.685929, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 32.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686012, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68612, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 32.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686198, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 33.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686272, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68634, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 33.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686405, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 34.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686475, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686543, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 34.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686608, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 35.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686679, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686746, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 35.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686813, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 36.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686882, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.686948, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 36.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687014, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 37.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687083, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687149, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 37.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687215, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 38.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687285, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687352, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 38.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687418, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 39.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687486, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687552, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 39.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687619, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 40.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687688, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687754, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 40.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68782, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 41.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687888, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.687954, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 41.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688033, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 42.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688103, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68817, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 42.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688287, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 43.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.68836, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688497, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 43.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688575, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 44.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688651, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688723, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 44.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688799, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 45.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.688968, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689053, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 45.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689125, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 46.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.6892, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689281, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 46.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689351, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 47.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689425, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689495, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 47.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689566, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 48.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689639, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689717, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 48.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689793, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 49.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689864, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689931, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 49.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.689999, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 50.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690075, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690143, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 50.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690212, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 51.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690285, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690359, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 51.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69043, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 52.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690503, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690572, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 52.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690644, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 53.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69072, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690788, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 53.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690861, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 54.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.690934, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691004, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 54.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691073, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 55.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691145, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691224, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 55.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691314, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 56.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691386, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691453, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 56.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691521, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 57.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691592, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69166, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 57.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691726, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 58.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691796, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691862, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 58.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.691942, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 59.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69202, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69209, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 59.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.692162, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 60.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.692235, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69243, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 60.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.692596, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 61.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.692686, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.692838, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 61.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.692987, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 62.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693141, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693236, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 62.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693308, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 63.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693382, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693454, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 63.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693525, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 64.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693596, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693663, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 64.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69373, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 65.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.6938, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693867, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 65.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.693933, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 66.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.694002, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.694087, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 66.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69416, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 67.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.694251, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.694333, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 67.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.694404, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 68.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.69448, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.694555, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.694627, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.694698, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.694773, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.694844, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.694921, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.694999, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695072, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695144, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695213, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.69528, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695346, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695415, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695483, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.69555, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695626, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695697, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695765, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695835, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695901, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.695968, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696043, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696114, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696181, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696251, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696325, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696395, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696468, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696537, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696603, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696693, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696765, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696833, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696902, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.696981, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697057, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697129, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697198, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697265, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697334, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697407, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697474, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697543, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.69761, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697683, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697757, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697826, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697898, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.697993, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.698068, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.698139, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.698209, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.698278, "xdebug_link": null, "collector": "log"}, {"message": "[18:17:46] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.698345, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.169133, "end": **********.790574, "duration": 0.621441125869751, "duration_str": "621ms", "measures": [{"label": "Booting", "start": **********.169133, "relative_start": 0, "end": **********.549977, "relative_end": **********.549977, "duration": 0.3808441162109375, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.549987, "relative_start": 0.38085412979125977, "end": **********.790576, "relative_end": 1.9073486328125e-06, "duration": 0.24058890342712402, "duration_str": "241ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55204920, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x invoice.view", "param_count": null, "params": [], "start": **********.663463, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.phpinvoice.view", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Finvoice%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "invoice.view"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.71374, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.718131, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.762559, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.774863, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.777219, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.777668, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET invoice/{invoice}", "middleware": "web, verified, auth, XSS, revalidate", "as": "invoice.show", "controller": "App\\Http\\Controllers\\InvoiceController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=408\" onclick=\"\">app/Http/Controllers/InvoiceController.php:408-444</a>"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.034809999999999994, "accumulated_duration_str": "34.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5794618, "duration": 0.02115, "duration_str": "21.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.758}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.609222, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.758, "width_percent": 0.977}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.624362, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 61.735, "width_percent": 1.465}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.626278, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 63.2, "width_percent": 1.063}, {"sql": "select * from `invoices` where `invoices`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 418}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6305392, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:418", "source": "app/Http/Controllers/InvoiceController.php:418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=418", "ajax": false, "filename": "InvoiceController.php", "line": "418"}, "connection": "kdmkjkqknb", "start_percent": 64.263, "width_percent": 1.293}, {"sql": "select * from `credit_notes` where `credit_notes`.`invoice` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 418}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.633987, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:418", "source": "app/Http/Controllers/InvoiceController.php:418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=418", "ajax": false, "filename": "InvoiceController.php", "line": "418"}, "connection": "kdmkjkqknb", "start_percent": 65.556, "width_percent": 1.149}, {"sql": "select * from `invoice_payments` where `invoice_payments`.`invoice_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 418}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6358142, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:418", "source": "app/Http/Controllers/InvoiceController.php:418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=418", "ajax": false, "filename": "InvoiceController.php", "line": "418"}, "connection": "kdmkjkqknb", "start_percent": 66.705, "width_percent": 0.833}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 418}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.637378, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:418", "source": "app/Http/Controllers/InvoiceController.php:418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=418", "ajax": false, "filename": "InvoiceController.php", "line": "418"}, "connection": "kdmkjkqknb", "start_percent": 67.538, "width_percent": 0.747}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 418}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6402621, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:418", "source": "app/Http/Controllers/InvoiceController.php:418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=418", "ajax": false, "filename": "InvoiceController.php", "line": "418"}, "connection": "kdmkjkqknb", "start_percent": 68.285, "width_percent": 0.776}, {"sql": "select * from `invoice_attachments` where `invoice_attachments`.`invoice_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 418}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.641851, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:418", "source": "app/Http/Controllers/InvoiceController.php:418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=418", "ajax": false, "filename": "InvoiceController.php", "line": "418"}, "connection": "kdmkjkqknb", "start_percent": 69.061, "width_percent": 1.034}, {"sql": "select * from `invoice_payments` where `invoice_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 421}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.643357, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:421", "source": "app/Http/Controllers/InvoiceController.php:421", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=421", "ajax": false, "filename": "InvoiceController.php", "line": "421"}, "connection": "kdmkjkqknb", "start_percent": 70.095, "width_percent": 1.149}, {"sql": "select * from `customers` where `customers`.`id` = 8 and `customers`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 423}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.645637, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:423", "source": "app/Http/Controllers/InvoiceController.php:423", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=423", "ajax": false, "filename": "InvoiceController.php", "line": "423"}, "connection": "kdmkjkqknb", "start_percent": 71.244, "width_percent": 1.609}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 428}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.647525, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:428", "source": "app/Http/Controllers/InvoiceController.php:428", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=428", "ajax": false, "filename": "InvoiceController.php", "line": "428"}, "connection": "kdmkjkqknb", "start_percent": 72.853, "width_percent": 0.919}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 429}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6496599, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 73.772, "width_percent": 1.235}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'invoice' and `record_id` = 5", "type": "query", "params": [], "bindings": ["invoice", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 432}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.651542, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 75.007, "width_percent": 1.149}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'invoice'", "type": "query", "params": [], "bindings": ["15", "invoice"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 433}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6536021, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:433", "source": "app/Http/Controllers/InvoiceController.php:433", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=433", "ajax": false, "filename": "InvoiceController.php", "line": "433"}, "connection": "kdmkjkqknb", "start_percent": 76.156, "width_percent": 0.804}, {"sql": "select * from `credit_notes` where `invoice` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 435}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6553369, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:435", "source": "app/Http/Controllers/InvoiceController.php:435", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=435", "ajax": false, "filename": "InvoiceController.php", "line": "435"}, "connection": "kdmkjkqknb", "start_percent": 76.961, "width_percent": 0.632}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 99}, {"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 136}, {"index": 22, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 30}], "start": **********.667661, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 77.593, "width_percent": 0.919}, {"sql": "select * from `invoice_bank_transfers` where `invoice_bank_transfers`.`invoice_id` = 5 and `invoice_bank_transfers`.`invoice_id` is not null and `status` != 'Approved'", "type": "query", "params": [], "bindings": ["5", "Approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 726}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.7080119, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "invoice.view:726", "source": "view::invoice.view:726", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Finvoice%2Fview.blade.php&line=726", "ajax": false, "filename": "view.blade.php", "line": "726"}, "connection": "kdmkjkqknb", "start_percent": 78.512, "width_percent": 1.58}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 728}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.709692, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 80.092, "width_percent": 1.551}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.714263, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 81.643, "width_percent": 1.063}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.716277, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 82.706, "width_percent": 1.149}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.720632, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 83.855, "width_percent": 0.862}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.722573, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 84.717, "width_percent": 0.919}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.763036, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 85.636, "width_percent": 1.293}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.764879, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 86.929, "width_percent": 10.428}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.770402, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 97.357, "width_percent": 0.862}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.772602, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 98.219, "width_percent": 0.948}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 6214}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.775265, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 99.167, "width_percent": 0.833}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\InvoiceProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=1", "ajax": false, "filename": "InvoiceProduct.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 78, "messages": [{"message": "[ability => show invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1744157080 data-indent-pad=\"  \"><span class=sf-dump-note>show invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">show invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744157080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6297, "xdebug_link": null}, {"message": "[ability => send invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1474200496 data-indent-pad=\"  \"><span class=sf-dump-note>send invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">send invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474200496\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.669708, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1699767215 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699767215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.670019, "xdebug_link": null}, {"message": "[\n  ability => create payment invoice,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1654626079 data-indent-pad=\"  \"><span class=sf-dump-note>create payment invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">create payment invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654626079\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.670382, "xdebug_link": null}, {"message": "[ability => show invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1213496621 data-indent-pad=\"  \"><span class=sf-dump-note>show invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">show invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213496621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.670781, "xdebug_link": null}, {"message": "[\n  ability => delete payment invoice,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-538239606 data-indent-pad=\"  \"><span class=sf-dump-note>delete payment invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">delete payment invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538239606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.707491, "xdebug_link": null}, {"message": "[ability => edit credit note, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-962968277 data-indent-pad=\"  \"><span class=sf-dump-note>edit credit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">edit credit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962968277\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711921, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2013802531 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013802531\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712182, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-406971920 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406971920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712772, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-491383608 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491383608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.72536, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.725972, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.72623, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726413, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726713, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1707363745 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707363745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727016, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-536631897 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536631897\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727304, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2121891551 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121891551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727648, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1752893904 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752893904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727916, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-875336434 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875336434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.728185, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1316480850 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316480850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.728454, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-944408359 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944408359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.728722, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-548381275 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548381275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.728997, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1552351272 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552351272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.729272, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1685957094 data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685957094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.729548, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1706824033 data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706824033\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.729752, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1793164354 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793164354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.730392, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-226335897 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226335897\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.730638, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1980562059 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980562059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.73169, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-367096728 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367096728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.732708, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1273959544 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273959544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.733364, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1140844263 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140844263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.734184, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1158682145 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158682145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.734964, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-219912761 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219912761\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.735638, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-768712799 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768712799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.736284, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1392798628 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392798628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.736894, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-199827241 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199827241\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.737389, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-560249174 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560249174\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.737903, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-667824057 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667824057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.738576, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2112061448 data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112061448\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.739256, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.739817, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.74062, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-834729137 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834729137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.742292, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1741850113 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741850113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.743842, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-289428110 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289428110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.745238, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1982331425 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982331425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.746317, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-474509463 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474509463\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.746978, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1993257261 data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993257261\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.747425, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2084424332 data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2084424332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.747917, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.748292, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.748668, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749237, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-328784308 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328784308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749971, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-92447895 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92447895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750355, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1195891993 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195891993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750814, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.751044, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.751421, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.751864, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752087, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1770158043 data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770158043\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752274, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-281442779 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281442779\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752478, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1739891390 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739891390\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752638, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1532383294 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532383294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752811, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-404597675 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404597675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.753144, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1637050057 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637050057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75339, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1875540022 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875540022\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.753607, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-924748211 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924748211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.753847, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1104682054 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104682054\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754592, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1640399656 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640399656\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.755343, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1349598214 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349598214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.756119, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1437830010 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437830010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757052, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2001291572 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001291572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757865, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1982286868 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982286868\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.758648, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1799555556 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799555556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.759535, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.760348, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76113, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1716350043 data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716350043\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761309, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761995, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.762286, "xdebug_link": null}]}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IkM2cGlaZ2pTc3F0NTR5QXNsQkdERlE9PSIsInZhbHVlIjoidlhZcjVsSWNDYks1cElNeSt2K0Ftdz09IiwibWFjIjoiNjBkOTY5NzJiODI0MzdmNzBhNzhmM2Q0ZmE5YjQxM2JmYzE2Yzg4ZWQ5MWI4ZTI4YTRhYzQzZGM5ZTJlNjhiNyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/invoice/eyJpdiI6IkM2cGlaZ2pTc3F0NTR5QXNsQkdERlE9PSIsInZhbHVlIjoidlhZcjVsSWNDYks1cElNeSt2K0Ftdz09IiwibWFjIjoiNjBkOTY5NzJiODI0MzdmNzBhNzhmM2Q0ZmE5YjQxM2JmYzE2Yzg4ZWQ5MWI4ZTI4YTRhYzQzZGM5ZTJlNjhiNyIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1082761634 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1082761634\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1102386529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1102386529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1150666444 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1150666444\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-484636317 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517065368%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ind5THZta0NuZndCL04vMUx2U3dxSEE9PSIsInZhbHVlIjoiQXBwMGdVaTNWU3Nmb2ZjS0tQT003Z0U0YTI4VzVsaE44YVFyMk1PVWdkdW1IZnVhQStlbk1TWnhBYmZyOExXYVFBVTRnR1pVYmVqUXZkT1Y5NDZhdzRNNm9TY3AwTW1YdS82WDh0Vkg0Njljclg5S0tNRVlkT253SkR2cWFFRkVzazdXTUJVWEdsaU1nRTMycXBZTEQ5dllYMlIxR1krSkRzZGlpeUlnazMwZnZRT25ubWRucXlkOVpNOFhldnNSZXFwRmtubkw2OGpWdmtpd3ZWR3grRTZjbnpjNlpWbUhwNXI4U2xJV0JjSVByN2JSSGwxdXZ4NmlUb3FFSE5VNDNlMDJxL2ZpQ0EwcHZLN2xCUzdrYWdRYU5yN3lsU1ZoelV3Ui9EUjh2T3JocnhpcXkzWnNDak8veXl1eEYwODBObHYrbzRrdElBQmJ4NXA0RDh4Zkt3bzNJWWpxYTM2QkdweVVmdGY5Sk1GSjdsbDhQMzczOTlXUUttOXY4ZlBpKy9tSHNSOHJwRmFpR202TFU5OFAyTWRReU5vamFpR3NYbVpZbE91WjE1cU9FSDRsQWtlVm1VRG5icjg1RlpJdktKeldQSElLS2hBZGpYb0cxSzhsMXpLWlF5aWJ3MlpMS2ZoS0MybHZhSHVLaFA3YzFnbWZQVHNuaUdXUzRFa2siLCJtYWMiOiI5YTE1NzJjYjYzNzhkMTUxNDRjZTVlMDQ0NjVmNmZlOTVmODg5ZTFlMTEwNWI1NDFkNTNkMzMzYTRjYTE4MzhlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNVbGJMTHpoSnJMTzZ0MEpUTk9Hamc9PSIsInZhbHVlIjoieE0rQlQvNytTSTIreUUyZitOZVJULzI2SUpyYUJpVXBWS0ZUTUY3Ti9UTmF1Rkgyb2xOdUFqN2xzMTdLTUttNksxYmM3ZVpabklGRWxvMk9aK1NSSmRjVkJhZUhvYTR4STNabFRtYTRna3E2alRJYzJhRzdoVjZiejNmZVZGemZRd1BSQ0Nab1h5blp5ZWNHM3J6U0I4MEVERkhSMGFWQTBvK1Y5cVB5R0FQM0Z4ZjZPeFJMYUlhelN6ajJxUmpwcEp0Ly8wNnRiZHViS2dGN012S0MxR0Y2TEpmc1R1UE16NkdRWDk3VnQ4UEtSS1V4aEhTWGJrQ1luYjQvcVpIcWdHNk5zZFltTVlmeDlEcEh3SXZZQnFvWVZHQlN2M3RncHMxRkZhYWkrV0lNd0p6QzhtMExTa1hKRU54QU9lbEYyVEZ1QmhJYUE3WXBKTkdqUldDSms0YWROTTFXa3Z4UEdxOWF4SHdzNVA2cGVYV3BIMXRUeE9DRGRCNTBJdjhjRUw2MW5mTHAwS1QrRllQbkhRQ2xiMGpkUURzTDc3QzEvcU9idU03WjFqYm0zZk9Hb0ZaYzlFZURyZlJhSHpjRHdmalRkSGtPckhKM2hMblFkbkoyUU9qNUMySEtYQ2J2WFVwT1NKS080b2FiT0ZhQ01jMmMwUGpzWFBWYWtOVjkiLCJtYWMiOiIwNDhjY2E0NjhkOThkZGUyOGQ4MWEzMTY3NTIyOGZjYzJkYzdkNjg5YjJiYzBlNThiYmVmZDM0NzE4ZTQ1MzdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484636317\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1652395877 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652395877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBPaXlCVzVoZ1hNT1FQR3lYTEYyd2c9PSIsInZhbHVlIjoiaFgySFZpc1FQQVpCMXhFZkgrV3QwU210YlZpaFczS254MmRGNmpFWktVZUpjcVVQajhpTzlVTzV3Tk8rMzN0ejRXYUlTcitGeklEc3BWbW9pTFBzN2RxUTFOc2EwRldNVXdMTlZkNnh3U1pkc0RGSk02SE1pT29vMHU3djdTRTBmejRTdk1JTzBoZTJ2b21WOFVYNGpBVXNYemladml2amxFMGRLVm9XU1hoZ3MxQUJrMjFqMmVEdDgvM1JOS09peVZqRCtlNDRBNzZML0VSeWJCTHM4a2d5aWpxUU1vMXpsN0lockJlUWdjMXY3TXh2T3VkRGFNTlZROUhtYzl2aGZBRzBDamRPOFN1TVdIeWE3N2lmUG5ZM2l4ZUxtKzhRMWpGM2NCSXdOWnF2Y2dKbXZzS2RMOUlJWjI4d2JQSHVMTjNaL1ZtVy9KaEF1Wm1NYkxrbjE2WDFTTlAzVEs5VlhWdGVmYk4yMkpMTXlSZFdZdHJmb3dKdTFqaWtjeU9rSnVSVDNDQjJKSTBSZ0JMWWJTalNWQldyTkxEZVZLODBYcVRacG1WSm5UbElSMVdPSHpudzNxT3c4TGxSaFlWakVTYzlhTFIrSWRjd0x6UUQyMmdYS2RVb0YvQ1FPeEhucHRLNEdDOVAzOWNTeGNTNG9ZcEhCV1JtcnZEMDRqcXYiLCJtYWMiOiJhYWNiNjc1MjU2MDQzMzU4OTQ0YjIwY2M0ZGE1OWRjN2NiNmY3ZmU3NzU1OGI0N2I2YmJlMTE4MDMyYjNiOTFkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkF1S0l6UE5RMUVpa3ZlMmE2ZUhuRXc9PSIsInZhbHVlIjoiOTNEV29UdHdPRWVoaG9jRGQ0cnh2YTRSWEI1eGtoa3FrcVoxODRWZFBRaW9WRDV1ZkZ5TkVhM255WXltVThQUURtak4xeUIwSFUwQ3FQSGZrempWa0VNVndhNHJXak15bDF3T0VUQ1VoRG93c2pqczNqUXpxTjM2dWQyTzl5V2FDUjVEbjFYVmFGdmc3MFNNa0pod1kzQkd1d1hXRGlwMlIwTmR6TmZRR3orSElGeVhNZ3gybkdZRWwrUTZxdzAxVGFVeG5MeGtobUg2cWk1Z1VnejlOQVAyOEJSV0pVK1RheGZnaGo4bW1pOFUyZWx4a0JkRk5BenBlcml2bzlTNFJEV2RaNkxRaHNTRTdDK1l4dDk0Zmc5bEZCM1RPRU5LUzc2M0l3RkV5YzlWcmYwNHkzQmpEcTVkb0NHcmZ5U2JOWndQbGUxM2xTQXdOUVh1YjEyQnVDNFpqYjRaaUdCbnJ0M0NKQk4zaS9TcmpIMjNsek1vaXBtNjlVUTFLZWNBZnQvUU5TbzlLbWF2OTJ3cjltaG9mQTZnelRWN2VtTDJ6ZTFEbDNsQUxlR0VGeXhOVHNGQ1ZkV1VGeldmTEs5cE1rY3NQSlhyTWlEYzJ3UktqeDVuVjNocmFqMEVQQ25JaVpLMy90TmFQRkw4Q0kvMUh5OTUyZS81RWlpVXVsa3kiLCJtYWMiOiIwMmE3NmEwMjIzNmFkMzAzOWFkMmE2NzExYWZlNGNjZjUxZmEyOGUxZDdkMjk2NTUxYmJjOTMxNWYxYThkZmZmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBPaXlCVzVoZ1hNT1FQR3lYTEYyd2c9PSIsInZhbHVlIjoiaFgySFZpc1FQQVpCMXhFZkgrV3QwU210YlZpaFczS254MmRGNmpFWktVZUpjcVVQajhpTzlVTzV3Tk8rMzN0ejRXYUlTcitGeklEc3BWbW9pTFBzN2RxUTFOc2EwRldNVXdMTlZkNnh3U1pkc0RGSk02SE1pT29vMHU3djdTRTBmejRTdk1JTzBoZTJ2b21WOFVYNGpBVXNYemladml2amxFMGRLVm9XU1hoZ3MxQUJrMjFqMmVEdDgvM1JOS09peVZqRCtlNDRBNzZML0VSeWJCTHM4a2d5aWpxUU1vMXpsN0lockJlUWdjMXY3TXh2T3VkRGFNTlZROUhtYzl2aGZBRzBDamRPOFN1TVdIeWE3N2lmUG5ZM2l4ZUxtKzhRMWpGM2NCSXdOWnF2Y2dKbXZzS2RMOUlJWjI4d2JQSHVMTjNaL1ZtVy9KaEF1Wm1NYkxrbjE2WDFTTlAzVEs5VlhWdGVmYk4yMkpMTXlSZFdZdHJmb3dKdTFqaWtjeU9rSnVSVDNDQjJKSTBSZ0JMWWJTalNWQldyTkxEZVZLODBYcVRacG1WSm5UbElSMVdPSHpudzNxT3c4TGxSaFlWakVTYzlhTFIrSWRjd0x6UUQyMmdYS2RVb0YvQ1FPeEhucHRLNEdDOVAzOWNTeGNTNG9ZcEhCV1JtcnZEMDRqcXYiLCJtYWMiOiJhYWNiNjc1MjU2MDQzMzU4OTQ0YjIwY2M0ZGE1OWRjN2NiNmY3ZmU3NzU1OGI0N2I2YmJlMTE4MDMyYjNiOTFkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkF1S0l6UE5RMUVpa3ZlMmE2ZUhuRXc9PSIsInZhbHVlIjoiOTNEV29UdHdPRWVoaG9jRGQ0cnh2YTRSWEI1eGtoa3FrcVoxODRWZFBRaW9WRDV1ZkZ5TkVhM255WXltVThQUURtak4xeUIwSFUwQ3FQSGZrempWa0VNVndhNHJXak15bDF3T0VUQ1VoRG93c2pqczNqUXpxTjM2dWQyTzl5V2FDUjVEbjFYVmFGdmc3MFNNa0pod1kzQkd1d1hXRGlwMlIwTmR6TmZRR3orSElGeVhNZ3gybkdZRWwrUTZxdzAxVGFVeG5MeGtobUg2cWk1Z1VnejlOQVAyOEJSV0pVK1RheGZnaGo4bW1pOFUyZWx4a0JkRk5BenBlcml2bzlTNFJEV2RaNkxRaHNTRTdDK1l4dDk0Zmc5bEZCM1RPRU5LUzc2M0l3RkV5YzlWcmYwNHkzQmpEcTVkb0NHcmZ5U2JOWndQbGUxM2xTQXdOUVh1YjEyQnVDNFpqYjRaaUdCbnJ0M0NKQk4zaS9TcmpIMjNsek1vaXBtNjlVUTFLZWNBZnQvUU5TbzlLbWF2OTJ3cjltaG9mQTZnelRWN2VtTDJ6ZTFEbDNsQUxlR0VGeXhOVHNGQ1ZkV1VGeldmTEs5cE1rY3NQSlhyTWlEYzJ3UktqeDVuVjNocmFqMEVQQ25JaVpLMy90TmFQRkw4Q0kvMUh5OTUyZS81RWlpVXVsa3kiLCJtYWMiOiIwMmE3NmEwMjIzNmFkMzAzOWFkMmE2NzExYWZlNGNjZjUxZmEyOGUxZDdkMjk2NTUxYmJjOTMxNWYxYThkZmZmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1947702565 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IkM2cGlaZ2pTc3F0NTR5QXNsQkdERlE9PSIsInZhbHVlIjoidlhZcjVsSWNDYks1cElNeSt2K0Ftdz09IiwibWFjIjoiNjBkOTY5NzJiODI0MzdmNzBhNzhmM2Q0ZmE5YjQxM2JmYzE2Yzg4ZWQ5MWI4ZTI4YTRhYzQzZGM5ZTJlNjhiNyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947702565\", {\"maxDepth\":0})</script>\n"}}