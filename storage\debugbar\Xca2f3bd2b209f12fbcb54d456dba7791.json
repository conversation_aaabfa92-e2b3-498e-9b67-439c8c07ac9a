{"__meta": {"id": "Xca2f3bd2b209f12fbcb54d456dba7791", "datetime": "2025-07-14 17:58:31", "utime": **********.403304, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515910.961657, "end": **********.403318, "duration": 0.44166088104248047, "duration_str": "442ms", "measures": [{"label": "Booting", "start": 1752515910.961657, "relative_start": 0, "end": **********.343756, "relative_end": **********.343756, "duration": 0.382098913192749, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.343767, "relative_start": 0.3821098804473877, "end": **********.403319, "relative_end": 9.5367431640625e-07, "duration": 0.05955195426940918, "duration_str": "59.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46017200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0035600000000000002, "accumulated_duration_str": "3.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3718, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 54.775}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.382149, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 54.775, "width_percent": 11.517}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3902252, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 66.292, "width_percent": 17.978}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3960888, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.27, "width_percent": 15.73}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1477578504 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1477578504\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-742702404 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-742702404\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-747213801 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747213801\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-612861550 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515906042%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikxlb3hSdFJpL0VkRUlvZ3dCc3hWOWc9PSIsInZhbHVlIjoic0pWNEF5V1hCSTdBTEVKS3QwemRqQWJlT25XTGN1QXovbXhWeDRSendnT3liVFM5N1I2R1U0QThxM3htdGtaTXBpand0Yk1XN2tmNlBhL1JvdlJOUUVMSEpoaGM0S2xjMm9LZFdqS0ltWlgyWTJZcnJjdlI4SjMvb2FNUERJaUdGWXlMb2NhL0dRZHBRanNEVVk1RkdpSFpMWS9pUW9zeTB5U1lZVDN3OXJ3SzlLN000eExuUUpXZnlRR01mbkxpSFJUcU0rRFR6NzZ2Y04vbWlObmMxd3lBVlZkMTZ2ZWNTWUJiVDZJc3ZMVlZXS3BKZ3RManVjazFLTkFpL0J4YnVEWUxFRHFuME9ZMmUzaElVTzlkVW1CU2ZNdHhZQXh0MXloWVF1TjZ5ZTQxV1h2SHcybnFtM3dISkUyVFJKbkpWaDlzdVVJNWhsUHB5cUsrQ0xaK2ZsaDM1ZTJtWFR6S05CZlltUmxSZDhnaEJjL1Brdzg4dlFUckthQ0xVQVZReExRYUhPamJPclJOOTN2aW1jTlY0Ny92UFV1UHZMRXUwV1BaVk5ZSzFNckJCVXBWUkZHNGNoMjJKMXl6WGkra0ZxczNqNTl2NVJQelVJaXptc0NoYlZNTjFjS0I1U2p3WVVmdkpWUFRGMXY3TFBpRG5JdUZvRVdBR3FqVHAvdkEiLCJtYWMiOiI0NWE0NTQ5OTMwNmY3NTRhYjY0NDkwMzUwNDkyM2M5MjljNmNlNGJlOTYwOWQxZGJlM2UzZGQ1ZjQ3MmUzZTE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVDUnJVYndOM2NiSFl5dG5CdWM2Tmc9PSIsInZhbHVlIjoid0dYd3ZnUGo5VGpNSFdHOWhhVExYLzY3dEFGLytqa3lMU0pHaTVwWUszRG1IZjVCaEFRMWFMQWFUT2gvMEZQSEltU3ptS1NxM2liUzZPN1pkY2NLdm83WURMWk9jTng4Yy84VzQrMzVPSVNObjNjRkNzUzdNRTlVU3lYcXhGVlJSVGFvbVlJNTBtL1BZTnczMU45ZUtVNkJCK01UeThyZlRFVVlUNWFxZmczYW42aGpJK2dhN3BKR3RhK2tnZEd3M1g4aStRTzRabDFCdUJjS25PanczSlpXbTlybUU3aG9jVmdrYkw1U3laRWJVQ0FGdXFqUFZGb2tQZUJBWE9UMGR0dyt2bGh3OTFsMTFjQytIK0xyMDlsQVlPVnlHQ0tob2dJMWhqRFl0aGpjTHUrZVl1aU1JdEJweWJVTEhKMWlpODhKeUxFNzlIT3lEOEZMeU04VUsrdnZTZm5qTjVRVmxtT3hwRTVma044ZDlPempsMURvTm1JWnkzSnZQN1JaOU9WZ0xQM2M5eDJjVW16SUJzd1N0d29FbndwSi9wSFg4MVZkaFdHZHVRV0w0Z0Y1MlV2a3FQSjZvRWo4MCtUMnBYOVFXcEdiYUNmWjlWT1R1RUxMSGtLNDgvcmd5ZlNtRnk0U3VnYWkrWDZGdXM5VXE5cU1iOVlISkJPSUZrZGwiLCJtYWMiOiJlZWQwODZiNzM5ZDE5YWY2NjMxZGMxN2YyZGI4ZGIzOTMxZDc3NDkwODE4MWZjNTZhYTEwYjE0M2U3MmMxZjU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612861550\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-767149089 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767149089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2008810201 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjE5R0FHK1doM2VYc0tBSTZqQmF4U3c9PSIsInZhbHVlIjoiTGhBTHNvbXpPaW1GQUliSXg0V0tmZHJIZ1k3cUU2dHI2V2hlOWpaYWpXOHdTTlZMWCtuZUttVkhMOHgzSWJoNW80ZHVqNE96bGNPL1FHQlREVGxONGJoWERKcFZzTGZaVE40V3ZQSklZa3Y1K0c1ZHUrakM4WXlydE9tdXFRUk9KSnhONllEbllNam5YbGlSZkh6WEJvU0VvSUtTU1E2NkRrcDcvTVVwazBRWHNzSm9QRUZ5eDRFSnR6cUpsQkZ5QzRUUExXdkI2bS9DYTUreDdKaFkrT0dwbVA3MDFDbVEyekFrcGpVOXlKWjRiVnB6ckZkM2pmVElLcGp2dlN5ei94bWJvZWg0R2xSY3h4dW84MitUUDFLWjRsVzBCRFhFSTByN0taNVFUMTlXV0hyYkFSQS9QZVFhWUhPbnJpc3oyOEZDQlR1WnRENllmRGhlOEpGK1Y2NWkwaHBwWFBFQWpJVFNxTlo2SS92V3VVdFVpUlRma0ErczI3RXFzYVFQNDI5bVZQN2FDcTMyUkpCWVk0b3A4Zm1NMDB3a2M4aUtVVXFBY3pSV3gvbStObWJRV29hNnZEWU5Xd01XSlArTEh4dnY3R1labEU2bHZEM2hnTXIwVEJVV1RmT1drSGpyTThrTmxTRkYwenA5VkVyMDZ1bVY3VC8xNkpNWUVURloiLCJtYWMiOiI0NDVhNjI5MzM0YWU1ZWM1ZTE4OTMwODJmZTY5NzUwNmQwNDNiMDdhNmE0MDAzNzA3NTM4MWVlNGE5MDRkYWY5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVscFBieHhEN1gzZEF2TFdiN3JOVEE9PSIsInZhbHVlIjoiRnJKbFc3U2lEY1RXb1NwSWh4c0tOMFlNZzJZTnNVU20vZGkxMFNRVUtJcllOaTdjcHVXcXZoNGMwanB5SGU4TnBNNUpyU21ENGEreFBaZzAyanFCVXRRZW52Q0lUTU9oSjJ4VXgwTnlsNnEwWnZFMVhRWmZtVmNmTm5LMjN1UEZsQmNldnRJcVMreFo3MnNnQ3EvVlpJOVFYNlcxaW01ZlhFb0QzNVhVOFEzOHQwMUp6ZmNTS3JxU1h1d3E2a2dxVTFFRTB0VmpGclBFSWhncmlRRElkUUJwaDdSTHdwaUplYUNRN2M4WTBOejBpcGhLKzFNQTZUYi9GK0ZFbEJzdElaMEV5TUJYejBjL2NhMUxHS1pFbzVFN0ZZTVB1Y003NUdMRFNRQ1M0V3IyUjhPQ1lyMHRycE5KbXBiSUZvVDcwbWhQSHN4Zk50MkFzWVA2OWdLYWF1Z1FhNmRlTjM5SDNSU2xZK0g1NGZpME1QZFVNN2RoME53VkZkMzhqRVFUbXlnbE1NV0paN1Z5MWF0blpvanhqM3VBWnEyODUySUlkZUtXM05zUHRoS2tGVUV5Qm9NYktaVll6SmpFditGR0JHNDZMcXBMcmlRM3lIYzRVS0NBVFRTV2p5ekpWY1FLbGlESThONjVuVVZ5M0ljYmhjUkcvWUM0eHcyNU5oZlIiLCJtYWMiOiI1ZGM4NGI0ZWNkMzZkZGRkZTAwMzNiOTJkYTc2MmJhMzQyNTUxYWUyZWRhMWQ0MmM5NzliZWEyNWYwZjYzZTQwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjE5R0FHK1doM2VYc0tBSTZqQmF4U3c9PSIsInZhbHVlIjoiTGhBTHNvbXpPaW1GQUliSXg0V0tmZHJIZ1k3cUU2dHI2V2hlOWpaYWpXOHdTTlZMWCtuZUttVkhMOHgzSWJoNW80ZHVqNE96bGNPL1FHQlREVGxONGJoWERKcFZzTGZaVE40V3ZQSklZa3Y1K0c1ZHUrakM4WXlydE9tdXFRUk9KSnhONllEbllNam5YbGlSZkh6WEJvU0VvSUtTU1E2NkRrcDcvTVVwazBRWHNzSm9QRUZ5eDRFSnR6cUpsQkZ5QzRUUExXdkI2bS9DYTUreDdKaFkrT0dwbVA3MDFDbVEyekFrcGpVOXlKWjRiVnB6ckZkM2pmVElLcGp2dlN5ei94bWJvZWg0R2xSY3h4dW84MitUUDFLWjRsVzBCRFhFSTByN0taNVFUMTlXV0hyYkFSQS9QZVFhWUhPbnJpc3oyOEZDQlR1WnRENllmRGhlOEpGK1Y2NWkwaHBwWFBFQWpJVFNxTlo2SS92V3VVdFVpUlRma0ErczI3RXFzYVFQNDI5bVZQN2FDcTMyUkpCWVk0b3A4Zm1NMDB3a2M4aUtVVXFBY3pSV3gvbStObWJRV29hNnZEWU5Xd01XSlArTEh4dnY3R1labEU2bHZEM2hnTXIwVEJVV1RmT1drSGpyTThrTmxTRkYwenA5VkVyMDZ1bVY3VC8xNkpNWUVURloiLCJtYWMiOiI0NDVhNjI5MzM0YWU1ZWM1ZTE4OTMwODJmZTY5NzUwNmQwNDNiMDdhNmE0MDAzNzA3NTM4MWVlNGE5MDRkYWY5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVscFBieHhEN1gzZEF2TFdiN3JOVEE9PSIsInZhbHVlIjoiRnJKbFc3U2lEY1RXb1NwSWh4c0tOMFlNZzJZTnNVU20vZGkxMFNRVUtJcllOaTdjcHVXcXZoNGMwanB5SGU4TnBNNUpyU21ENGEreFBaZzAyanFCVXRRZW52Q0lUTU9oSjJ4VXgwTnlsNnEwWnZFMVhRWmZtVmNmTm5LMjN1UEZsQmNldnRJcVMreFo3MnNnQ3EvVlpJOVFYNlcxaW01ZlhFb0QzNVhVOFEzOHQwMUp6ZmNTS3JxU1h1d3E2a2dxVTFFRTB0VmpGclBFSWhncmlRRElkUUJwaDdSTHdwaUplYUNRN2M4WTBOejBpcGhLKzFNQTZUYi9GK0ZFbEJzdElaMEV5TUJYejBjL2NhMUxHS1pFbzVFN0ZZTVB1Y003NUdMRFNRQ1M0V3IyUjhPQ1lyMHRycE5KbXBiSUZvVDcwbWhQSHN4Zk50MkFzWVA2OWdLYWF1Z1FhNmRlTjM5SDNSU2xZK0g1NGZpME1QZFVNN2RoME53VkZkMzhqRVFUbXlnbE1NV0paN1Z5MWF0blpvanhqM3VBWnEyODUySUlkZUtXM05zUHRoS2tGVUV5Qm9NYktaVll6SmpFditGR0JHNDZMcXBMcmlRM3lIYzRVS0NBVFRTV2p5ekpWY1FLbGlESThONjVuVVZ5M0ljYmhjUkcvWUM0eHcyNU5oZlIiLCJtYWMiOiI1ZGM4NGI0ZWNkMzZkZGRkZTAwMzNiOTJkYTc2MmJhMzQyNTUxYWUyZWRhMWQ0MmM5NzliZWEyNWYwZjYzZTQwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008810201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1257249853 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257249853\", {\"maxDepth\":0})</script>\n"}}