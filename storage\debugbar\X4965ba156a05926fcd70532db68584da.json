{"__meta": {"id": "X4965ba156a05926fcd70532db68584da", "datetime": "2025-07-21 02:07:58", "utime": **********.997215, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.578492, "end": **********.99723, "duration": 0.41873812675476074, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.578492, "relative_start": 0, "end": **********.942832, "relative_end": **********.942832, "duration": 0.36434006690979004, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.942841, "relative_start": 0.3643491268157959, "end": **********.997231, "relative_end": 9.5367431640625e-07, "duration": 0.05438995361328125, "duration_str": "54.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46008480, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00333, "accumulated_duration_str": "3.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9714649, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 54.354}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.98217, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 54.354, "width_percent": 34.535}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.989975, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.889, "width_percent": 11.111}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_search=Afrooz%20Alam&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1776667082 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1776667082\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-665843368 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-665843368\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-21806763 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21806763\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-546312146 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"164 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_search=Afrooz+Alam</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753063674095%7C31%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjcrS2UyM0hNOGN4Zi84aUhiYWxUckE9PSIsInZhbHVlIjoidXZ6TFhpUGJXQVl5VUdwQkJ3dTNzT2dwbFdsRFpya3lXbnBuT2NSbjkyWFpNYlh3TEtwTmwyUjh3QVRTS1RhbU9sd29zQ1dhaEhPUjRLeFFIZ05QVXh5U3BnandDTUxMZ0dGV00zUkpOelM0ekpsRDFhUjlMcHJYRG1vT2UralBrWE45YVhqYUJORGoyL20yRjdRZDZrRjU3alArSjNxY2YyNUlreU9adWNLQmNZeGk2cERia2dQa0ZoeVl3cVpIQzVNQTBzbnNkL1hpeldYS1NoM1JzNDVMY3kzRHVQY1J3WWc0c0pLWlBGVXZuczBhRjRCU2pEMkhhNE40VHFsWmNCUWZ6bGV0UStNdXhyQnpxU25NN3oxbU9wZC9jR3JuSTdlamNIbSsxclNOUFFVck9jNktPZFRRYkJ2TDM0bFlzZWlWYTlycWJjZE90UzA4MHpsRjRBa2RYN05BWE1xN2xONFNjejRrTUZpQlMxc1VrSVlqQ29PSWFneHBBMUNwUlJiems2V3VPZmVqN2lOL2dGQnMwcnNwelR0V2xtQjhJN29sbE1BOTVucFJ3NEJ2T2h6L1JGWTdsbk56R3hRMnVscitacjVoNWNtbHdEbEtPVllrS2d3M0YvczhKL04vY05LS2FOSHlNeVBDYk52YXhYcDQvUEg0bzFqWFZiazAiLCJtYWMiOiI1YmYwMDFmNDRkZTQ4ZGQ2NGU3NWE5MjU2Y2I2NWE0M2ZhMDBlODMzYjFhZDZlMjhiNzBmMGJiMDg2NzcwM2JkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZMb0QxT0JVRnNxNUtpUzZ2RTdIZHc9PSIsInZhbHVlIjoicUJoMEFXbm5uL1pRaXovZ21LUm9NbW1JYWk2RlFFS216OWg3Y0phZU5DSG9wNzNtWDJCS1k3UTdteUNveFJ3T0xuRzh3WWdlbDRQVmk0RElmMHIxaTNUWTVDWEEreTNqSlJtOEtOanVtcHRMUlZQdTBNa0Yza0Z1b2djR1gxQzRvamhnWDRUSkdqZnBnZjFMS3NyQUFjbXdMaHRacWNWb3VzSnR3US9lb1VxcWRMSGkwMUpFcmlyNE95WXd6Sit1S1RYNEpSMzZHTlVBK004ZkNpZTRoM1hWQi91NERGaDBnMHBMaXBJb05Id2JMaHhNZ3U4S0tUd1hPL2Z4M2dMNjRUZFQzcWNGdVpFNUREdUZieGFJSG5ucFRyd3AvekhsVTlHVUZMWHFuaEZkUWdSSGRoblpwRm0wY0VZT2Roa0FMVHdjdGZSeCtHMkFmai9kVWRldU5KQmRtSmNzMlFnektBS0ZBdWd1eU9FUnhBdnVJUHNDR2IwbWppQlM3bEQ3ZnkyUzB3TjdZTzBDRGRSa2RkYW1ndkYwU2NwUHBCLzE1Ym1uRjRqdUI3dDdSSUpEQUhsdW1QVkNadVJhUllBSVRCV3N3ZW1WYXhUMjhKeGd3V3dYczYvRjQ3WVdOUWh2MjJHeHhtVTFvSmVVQzZoT0NOOGIyam5GR3RNeVp3VG4iLCJtYWMiOiIwZTg0NzczNmRjNzk2NTg1YzViYjI2MzliYTA5YTRkNzQ5YzVhODA0MmE4YWU0MGRmMzhjZDdiNWMxNTcxNjlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546312146\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1996410345 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996410345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 02:07:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdXNXdtRUV6RUZZd1B5bFRYa0xxOWc9PSIsInZhbHVlIjoiUEF2aGtobldMNCtQREVCY0tzdkJaMWVrbmdSMktQVmwraUo1bjJPZnpKSVkzdk9JNGp4ekpmbmc2dzAwejgvVFQ0L2kyM2xJN2NTano1ZnAvTS9hTVRYNHpsUkxBcDRscjg2YWhRcUF2cWZFaDhua1NlK0RnM2FCaGFtZFlybEREcWlWaVBFWFlUZWdpZnZTYnp2T2oyRGpEZXFWWkVzTVpsUnlqRjlORGxINXdkRjk5U09ReDg2SUZSK1ZNN3RUclBJRFVrYTNFVHBNdUh2azhtSHZoL2ZmakR2TGJJQXp3UVpvMTJYVGhiU1c3aW5Xa0YvN3o3L3VZaXYzejZIdThEOUJGWGRFWk1sZEdFVWltLzlzRktLbStrTW1JMmZkbEtpbThXM1NkUGdGTTYwSUpyeWNnb01pbS9YRlZldDAxd2ZiZGZJUmVXd3JSM0o0REVlcFBWUS9OZGE4Z0lIZDFwSUVCTElHcUFMVlRpbFhtTDAzb1BueUJxVXA5dlAzZUk0SDNSRjBoeU9lYzd4cTVoOUZYaDl6Z3lrV2F6OHZWc3VCT0Vmb3c2K243dEdMem44RkpGcG42Y2JDTXVNV1h0MlptWU15Q1dJaG1tbnZkOG4rL09GcWtkUUoydnZzYnQ5VndEZEtVQmtVSmt1bEZES3hxWmhlU2M1VHQ0OUMiLCJtYWMiOiI4MTg4ZGRiMGJmMjI1Y2E2ZWI0MDllYWFjY2UyYjhmZWIyZjVjOWJmYmJiM2Y3NDc4ZGQwOWRmYzliODI4ZGZmIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 04:07:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVWWkROSVVsY3pGRkc0U25ZVy9mSXc9PSIsInZhbHVlIjoiSktPL1l2RlgrU0JpSTdzM2dUcU5IMDRtd0ppSG9SRVZJVTFZRTRxMW1scjVnZE9LaEJnZ2dpbGFmcm45VHlaaFlkbTBDWXQ1ZFBjTFNxbTFaV0dmYTNITGVhN0x5MiszYTZlTlhkRjh0OWxia3JpM01CZVdXYVFYVlZTOGZQRDRGS240WU5XZktONW5RZ01iSGViL3R3ZDlGNWJlTFpkU0hrQ0g4MGEzUG80WWhoaDRJQVB1RVNmcndkdlVwTmJSSWgzRi92QVdCUWNDeC9vRmwrcXlIcmlpbTRCbjUyeTlRVWpjcTQ3TDZnV1BIN2kxWXFrT1RFL2Y2TXhqcXJaZWpVUzAvOVlJZHJlWnAyYit4cENUbVRsemFVYWRqeFc2NW5nak9LU0tIZmxoUWU0bmlNQmZweHZnc2tFN0xjeE53bEFtNHFMMTU5T1N6TnFzV1NWbDJhVVBGMi83akFlRnY0dExRWWo4NHlVbmFBSjh2akxtZjZmSjNVaEU3TVJTQmlOQ3FpVUdsREZNRnJac0FyTDYrSUhyOFMyYmZVWU9td2h5WnExdnA4MVdCWGc1YnpUa0g2OFJPbWxmMUFrMG92MUFDckxXVVo3Qy9hRURSWU82ZjE4Z2lKOEdsLzlTRlNRY2Nhbk13dDQxQ3JMRVI4dVZINEJscENpRGtsbVMiLCJtYWMiOiJkMzMwYTQzYzRhOGY1ZDQ3MzA0OThkYTIxZWFhZmJjY2I0YjU1ZDI2ZGYyZGJkM2JmMzYyNWI3NDE1ZjhiMzRlIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 04:07:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdXNXdtRUV6RUZZd1B5bFRYa0xxOWc9PSIsInZhbHVlIjoiUEF2aGtobldMNCtQREVCY0tzdkJaMWVrbmdSMktQVmwraUo1bjJPZnpKSVkzdk9JNGp4ekpmbmc2dzAwejgvVFQ0L2kyM2xJN2NTano1ZnAvTS9hTVRYNHpsUkxBcDRscjg2YWhRcUF2cWZFaDhua1NlK0RnM2FCaGFtZFlybEREcWlWaVBFWFlUZWdpZnZTYnp2T2oyRGpEZXFWWkVzTVpsUnlqRjlORGxINXdkRjk5U09ReDg2SUZSK1ZNN3RUclBJRFVrYTNFVHBNdUh2azhtSHZoL2ZmakR2TGJJQXp3UVpvMTJYVGhiU1c3aW5Xa0YvN3o3L3VZaXYzejZIdThEOUJGWGRFWk1sZEdFVWltLzlzRktLbStrTW1JMmZkbEtpbThXM1NkUGdGTTYwSUpyeWNnb01pbS9YRlZldDAxd2ZiZGZJUmVXd3JSM0o0REVlcFBWUS9OZGE4Z0lIZDFwSUVCTElHcUFMVlRpbFhtTDAzb1BueUJxVXA5dlAzZUk0SDNSRjBoeU9lYzd4cTVoOUZYaDl6Z3lrV2F6OHZWc3VCT0Vmb3c2K243dEdMem44RkpGcG42Y2JDTXVNV1h0MlptWU15Q1dJaG1tbnZkOG4rL09GcWtkUUoydnZzYnQ5VndEZEtVQmtVSmt1bEZES3hxWmhlU2M1VHQ0OUMiLCJtYWMiOiI4MTg4ZGRiMGJmMjI1Y2E2ZWI0MDllYWFjY2UyYjhmZWIyZjVjOWJmYmJiM2Y3NDc4ZGQwOWRmYzliODI4ZGZmIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 04:07:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVWWkROSVVsY3pGRkc0U25ZVy9mSXc9PSIsInZhbHVlIjoiSktPL1l2RlgrU0JpSTdzM2dUcU5IMDRtd0ppSG9SRVZJVTFZRTRxMW1scjVnZE9LaEJnZ2dpbGFmcm45VHlaaFlkbTBDWXQ1ZFBjTFNxbTFaV0dmYTNITGVhN0x5MiszYTZlTlhkRjh0OWxia3JpM01CZVdXYVFYVlZTOGZQRDRGS240WU5XZktONW5RZ01iSGViL3R3ZDlGNWJlTFpkU0hrQ0g4MGEzUG80WWhoaDRJQVB1RVNmcndkdlVwTmJSSWgzRi92QVdCUWNDeC9vRmwrcXlIcmlpbTRCbjUyeTlRVWpjcTQ3TDZnV1BIN2kxWXFrT1RFL2Y2TXhqcXJaZWpVUzAvOVlJZHJlWnAyYit4cENUbVRsemFVYWRqeFc2NW5nak9LU0tIZmxoUWU0bmlNQmZweHZnc2tFN0xjeE53bEFtNHFMMTU5T1N6TnFzV1NWbDJhVVBGMi83akFlRnY0dExRWWo4NHlVbmFBSjh2akxtZjZmSjNVaEU3TVJTQmlOQ3FpVUdsREZNRnJac0FyTDYrSUhyOFMyYmZVWU9td2h5WnExdnA4MVdCWGc1YnpUa0g2OFJPbWxmMUFrMG92MUFDckxXVVo3Qy9hRURSWU82ZjE4Z2lKOEdsLzlTRlNRY2Nhbk13dDQxQ3JMRVI4dVZINEJscENpRGtsbVMiLCJtYWMiOiJkMzMwYTQzYzRhOGY1ZDQ3MzA0OThkYTIxZWFhZmJjY2I0YjU1ZDI2ZGYyZGJkM2JmMzYyNWI3NDE1ZjhiMzRlIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 04:07:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"166 characters\">http://localhost/invoice/processing/invoice-processor?creator_search=Afrooz%20Alam&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}