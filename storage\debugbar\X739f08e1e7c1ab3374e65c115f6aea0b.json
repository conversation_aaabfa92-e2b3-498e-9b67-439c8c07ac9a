{"__meta": {"id": "X739f08e1e7c1ab3374e65c115f6aea0b", "datetime": "2025-07-21 01:19:35", "utime": **********.134152, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060774.676641, "end": **********.134173, "duration": 0.4575319290161133, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1753060774.676641, "relative_start": 0, "end": **********.063683, "relative_end": **********.063683, "duration": 0.3870420455932617, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.063691, "relative_start": 0.38704991340637207, "end": **********.134175, "relative_end": 2.1457672119140625e-06, "duration": 0.07048416137695312, "duration_str": "70.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46034168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00434, "accumulated_duration_str": "4.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.091289, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 41.244}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.102135, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 41.244, "width_percent": 14.055}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1161332, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 55.3, "width_percent": 26.498}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.12462, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.797, "width_percent": 18.203}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1496722977 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1496722977\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-888626704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-888626704\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-45838851 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45838851\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-591891590 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753060773188%7C3%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5qUXNPbDNJZk9DTFdlOWlCMGd1cUE9PSIsInZhbHVlIjoiVGRmSzFXL3ZVWGpXZHkzT2xiY1IrNEJVMlMzcVlnSGhDMFlqTktNdXFHdWxjZmFVcWRLVTA5RnBjQVo3Zzd0K3d0N3pwa08xbFAvNHcwYzdXMmhGU0JzTzhJZXNqcmQwUHhpSGlmTW1aYldYVkFZOFUrTzdTam1XWUl6cFh5dnVFU2dmY2s0ajF1RVBIWFB4ZHdYMVJGODlkVTZEMzVFYTR2bm1CSVF6c1Q4NjBQTHFUdXk4Q2ZKcUliQTl1VzdhdS9pM0pzUlBla2p3QVRXQ1VmRmZubldiNkNaQitEUkJCRi8yZG50RGlkMDJLZmlqR1ZuZE52N2FTQ3U5SDZnK2RsRWc2K1NFbjAxaXN3OU1WN1dJMEpJbEZUY1FkbFg1Sk1uY0UyTnF3WEFBU0ZFbTFxZEhPdk1CUjJrUk9GeGsrMDM3UHpycjFjRkx1U242QzcrK05VUjVJSDBuYWYxVmRNV08zR3YzbU9LTkNETGwwNFVrVHJCVWJKZVRRWVhVWmxYRWdqejlWTGVQUmVOczFta1hNYThxSDJncmhnMXJXZ2grRzBkWFgrMzRPVFQ2YTk2eFpILzMyejkrdFpFVC9hMEVvQnlpTWllaXNwUjdQTk53MFpjcUY3UnV6SFVGbWZxOStxMkxBL2NkeE1tWmtRSDAyd2JvaHBwQXhJS1EiLCJtYWMiOiJjMTA1ODVjYjZmMTJmM2Y3MzFmY2UxMGNlYTVhYmRhY2Y4MjFmMTA4ZDJhNTdlMmQ3OTQyMDk4MWFhNDcxMmZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkU2RSswd0tyVWhFOHdPbyswM2ZkMnc9PSIsInZhbHVlIjoiNDdEOWV3RmZCS1FvZmQ4NXNOWTRROWpCV0IvMnQ4NVRqVWJmUUNBOXppN3h0NThDTlA2NmJyZW9WOGRXOEgrNzAyekEwQ1ZsQkRDcGtjUkZybWxkRUtPdzJETmlMZ2dlZkNtZGtpMDdlOXAyblhiTWtvRkEvVDNOREoxcy83ZlBPZ0hBZHl1OGhkUytSYVBtV29DS3o2V2taU0hvcWozSDFndVpReWtQMW9SUTFpNFF3dzJ2WXdLOWVmZkZyZ2dzc0NOTjZtaU9CY1dQQ3BYczNQWlRuUFhWRlBKQzRINFBCa2x2K05aNmdodndGNVVmajFMSUd1bSttVm51alh1UnExV3hCNlQ2bVQwSEN2ZXNrUXJvcW1Ld0E2RHlLMmZNMG43OXBTTmNzcGl6WDNXWTNrakcxT2ZweEpPeDdnRCtWb2VtVlloanVPbHZOcmxlVTdSbkZmWklLOXlZT1IyeTF3YVFiK2ZJMVFDcFhmS0tiMUtVT0RtZWM2WmVCLzUxNWZUdVQwV05ubk1vdGFKdm9pTDQrdy9qSUVseE45R25Qamt0SEIxVWo2ODlpWEgyeDNDU2RsdDh5VkdBMGE0VG91TWlNMjlueVQrM1VyeGFrZTJsV1BuRVhQYlJhWmx3TmEwL2RDbE9yOGYyNFhSejhuNlFEWHVialN3bEJSMDMiLCJtYWMiOiIzNDllMGUxZDk5ZThiMTEzNjQ2NzFiMTI3ZmU0MzM2NzFhMjU1MjRiMzg5NmJlZjE0NWNkYzg4MDc1NmY1MDQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591891590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1242358180 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1242358180\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1504668084 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:19:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJoN3ZabWxjMEI4eHBsU3AzQkovUHc9PSIsInZhbHVlIjoiUFV1ZXV1aDhNNDhMbFhhT0MzVUZ3ZjdRb3dIWUN1cDdVZS9sbTdJbkh6c0NndXV0WkZOZWFxMXU1M1RvUmRJWEVHcXNIV3dOZGoxaWQ5WFlZcnloOURhVkszMGkyZWlyUS84QU9QVFhhSUx1Y295c0hHMWF5YThjdUdUbUxLNFZtZEtYV21jWVZXY0pIcCtxdGNtOEEzRmE2Q0xtK3JXcDhwelo0TEtMRWh3dDJqRjFSTmNtQ21QeWJKbTJoN2F6MFhOelhKd3pFYWcvSEwzS0drVEJoS2lqSEs3NlhCeTk4bFdYMndVeDJrNkxxdHJ2citUd2hDT1gyb3NGMENqL2l6UngwWHhtYVByenl2SFdIdGFMSit4UG1uSlRZZzNrVnBJSnhHZDU5empqSlo5MElhRFY0aDFDb0szWlhrZExhaUd3eGx2UkxHbDF3NWpyVmFCNGNobUJqYmZEc2puZkFEeTFTM1FISFdxclhjajgramVQZTZPN3UveW9peFIrSUhyZWJvQndXQVFmQ1hndjJ0dFdwOE9YZ1Z4NGNxZzh5ai8yU2lEejQ0eVlzZG9BL1htUVlDTmVkenovMFpKbkQxcXdnQ2lzK1BQWFR6R2h0ZTVnNFFCak93dDNwaHVmSENKd1hlWmM2SzJaRmJLeGNPZytoOTVyOVN1NG80cnQiLCJtYWMiOiIzYjI1YTZjZjdhODkwNjYxNGM2OGZmMDM5ZTM5NWUwZDI0YzRlYTQ3ZGUxZjJhMjE1ZmQ2ZDU4MGJmOTVmNmFkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpBRVBQS2JIZkpkdGZ0RmJNMDA0QUE9PSIsInZhbHVlIjoibGVmMGNXZFBGQUpuOWdQaTFTbTBJUWRQTjFyTXhHVGJqU2tXQmozQTFBbFRQbEJDSjVaZjZzOXZrckdsRUlzRFpJWktXWFI2Vk9NSWN1QkJKazJMOGlpY0lKSWVrUzZDdGZFSkFYUS9WcVVZdld3Y3FYUUhtZ3BTcGxySzhsY0dUcHdHelRmZUpUR2cyR2w3bmdxS2JndkEvMk5GeGhKb3JwWkRoTkRhcnNYRDA4alh2Z01HQkR5UW92NUxPQnNPOGNuRWd2RGJveEpzY0oxdnNjZjhDZWtmVXlWaExZMmd4RHpVaVV5UlAzZy9SUlBjcXUyZFpvUHA1YkVwR2VTRUdVbWdIelY5L2l0dS9TVnFvNFZXdytZK2ZIb0VydUlvcm4yZ1k2MGJaczkwbDN6Z2VuUjJxKzNWeVVBSWJzQzUyMC9xOGpLcDFOYUpFcUFwUDUwbUhtc0JXMkFxaE1VcjV6K0JTMEZEMHhneFFHVGo0M0NDKzRNYVI1aFhWTTBtZVlxYnlrMkw4K2U2akdIM0Q0Mm5sWld6RU5vSDd6ajBGb2hzY3dlTTUrRktDbGorZ0IrMlErbEtERGdhUERVbnNkamsrMWk5V3VnRmhaQ0pVSGtyOFlQemYzc3FKbWtqVHhTKzhqb2FFREc3VHdIcncrMll4cXBDTnZjTlhrTzQiLCJtYWMiOiI0YzYyOGIyMWM3ZDljZDNmNDE1NGViMTJhY2UwYzMwOTNjN2FmNzQyYWEwNGQ4NzkxMDg1ZjRlZTEyZmY0MjczIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJoN3ZabWxjMEI4eHBsU3AzQkovUHc9PSIsInZhbHVlIjoiUFV1ZXV1aDhNNDhMbFhhT0MzVUZ3ZjdRb3dIWUN1cDdVZS9sbTdJbkh6c0NndXV0WkZOZWFxMXU1M1RvUmRJWEVHcXNIV3dOZGoxaWQ5WFlZcnloOURhVkszMGkyZWlyUS84QU9QVFhhSUx1Y295c0hHMWF5YThjdUdUbUxLNFZtZEtYV21jWVZXY0pIcCtxdGNtOEEzRmE2Q0xtK3JXcDhwelo0TEtMRWh3dDJqRjFSTmNtQ21QeWJKbTJoN2F6MFhOelhKd3pFYWcvSEwzS0drVEJoS2lqSEs3NlhCeTk4bFdYMndVeDJrNkxxdHJ2citUd2hDT1gyb3NGMENqL2l6UngwWHhtYVByenl2SFdIdGFMSit4UG1uSlRZZzNrVnBJSnhHZDU5empqSlo5MElhRFY0aDFDb0szWlhrZExhaUd3eGx2UkxHbDF3NWpyVmFCNGNobUJqYmZEc2puZkFEeTFTM1FISFdxclhjajgramVQZTZPN3UveW9peFIrSUhyZWJvQndXQVFmQ1hndjJ0dFdwOE9YZ1Z4NGNxZzh5ai8yU2lEejQ0eVlzZG9BL1htUVlDTmVkenovMFpKbkQxcXdnQ2lzK1BQWFR6R2h0ZTVnNFFCak93dDNwaHVmSENKd1hlWmM2SzJaRmJLeGNPZytoOTVyOVN1NG80cnQiLCJtYWMiOiIzYjI1YTZjZjdhODkwNjYxNGM2OGZmMDM5ZTM5NWUwZDI0YzRlYTQ3ZGUxZjJhMjE1ZmQ2ZDU4MGJmOTVmNmFkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpBRVBQS2JIZkpkdGZ0RmJNMDA0QUE9PSIsInZhbHVlIjoibGVmMGNXZFBGQUpuOWdQaTFTbTBJUWRQTjFyTXhHVGJqU2tXQmozQTFBbFRQbEJDSjVaZjZzOXZrckdsRUlzRFpJWktXWFI2Vk9NSWN1QkJKazJMOGlpY0lKSWVrUzZDdGZFSkFYUS9WcVVZdld3Y3FYUUhtZ3BTcGxySzhsY0dUcHdHelRmZUpUR2cyR2w3bmdxS2JndkEvMk5GeGhKb3JwWkRoTkRhcnNYRDA4alh2Z01HQkR5UW92NUxPQnNPOGNuRWd2RGJveEpzY0oxdnNjZjhDZWtmVXlWaExZMmd4RHpVaVV5UlAzZy9SUlBjcXUyZFpvUHA1YkVwR2VTRUdVbWdIelY5L2l0dS9TVnFvNFZXdytZK2ZIb0VydUlvcm4yZ1k2MGJaczkwbDN6Z2VuUjJxKzNWeVVBSWJzQzUyMC9xOGpLcDFOYUpFcUFwUDUwbUhtc0JXMkFxaE1VcjV6K0JTMEZEMHhneFFHVGo0M0NDKzRNYVI1aFhWTTBtZVlxYnlrMkw4K2U2akdIM0Q0Mm5sWld6RU5vSDd6ajBGb2hzY3dlTTUrRktDbGorZ0IrMlErbEtERGdhUERVbnNkamsrMWk5V3VnRmhaQ0pVSGtyOFlQemYzc3FKbWtqVHhTKzhqb2FFREc3VHdIcncrMll4cXBDTnZjTlhrTzQiLCJtYWMiOiI0YzYyOGIyMWM3ZDljZDNmNDE1NGViMTJhY2UwYzMwOTNjN2FmNzQyYWEwNGQ4NzkxMDg1ZjRlZTEyZmY0MjczIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504668084\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1749934369 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749934369\", {\"maxDepth\":0})</script>\n"}}