# 📋 نظام الفئات في Journal Entry

## 🎯 نظرة عامة
تم تطوير نظام الفئات لربط قيود اليومية بفئات الدخل والمصروف، مما يسمح بتتبع أفضل للعمليات المالية وتصنيفها حسب طبيعتها.

## ✨ الميزات الجديدة

### 1. **ربط الفئات بالقيود**
- إمكانية ربط كل قيد يومية بفئة دخل أو مصروف
- عرض نوع الفئة (دخل/مصروف) مع اسم الفئة
- حساب المبلغ الإجمالي تلقائياً من إجمالي المدين أو الدائن

### 2. **واجهة مستخدم محسنة**
- حقول جديدة في نماذج الإنشاء والتعديل
- عرض معلومات الفئة في صفحة التفاصيل
- عمود جديد في جدول القيود لعرض الفئات

### 3. **حساب تلقائي للمبالغ**
- تحديث المبلغ الإجمالي تلقائياً عند تغيير المدين/الدائن
- عرض المبلغ بالريال السعودي
- التحقق من توازن القيد قبل الحفظ

## 🗄️ التغييرات في قاعدة البيانات

### جدول `journal_entries`
تم إضافة الحقول التالية:
```sql
category_id INT NULL          -- معرف الفئة
category_type VARCHAR NULL    -- نوع الفئة (income/expense)
total_amount DECIMAL(15,2)    -- المبلغ الإجمالي
```

## 📁 الملفات المحدثة

### 1. **Models**
- `app/Models/JournalEntry.php` - إضافة العلاقات والدوال الجديدة
- العلاقة مع `ProductServiceCategory`
- دوال حساب الإجماليات

### 2. **Controllers**
- `app/Http/Controllers/JournalEntryController.php`
- إضافة معالجة الفئات في `create()`, `store()`, `edit()`, `update()`
- تمرير بيانات الفئات للعروض

### 3. **Views**
- `resources/views/journalEntry/create.blade.php` - نموذج الإنشاء
- `resources/views/journalEntry/edit.blade.php` - نموذج التعديل  
- `resources/views/journalEntry/index.blade.php` - قائمة القيود
- `resources/views/journalEntry/view.blade.php` - عرض التفاصيل

### 4. **Migrations**
- `database/migrations/2024_07_14_000000_add_category_fields_to_journal_entries_table.php`

## 🎮 كيفية الاستخدام

### 1. **إنشاء قيد جديد مع فئة**
1. اذهب إلى صفحة إنشاء قيد جديد
2. املأ البيانات الأساسية (التاريخ، المرجع، الوصف)
3. في قسم "تخصيص الفئة":
   - اختر نوع الفئة (دخل أو مصروف)
   - اختر الفئة المناسبة
   - سيتم حساب المبلغ الإجمالي تلقائياً
4. أضف بنود القيد كالمعتاد
5. احفظ القيد

### 2. **عرض القيود مع الفئات**
- في صفحة قائمة القيود، ستظهر الفئات كشارات ملونة
- الدخل: شارة خضراء
- المصروف: شارة حمراء
- عرض المبلغ الإجمالي أسفل اسم الفئة

### 3. **تعديل فئة قيد موجود**
- اذهب إلى صفحة تعديل القيد
- غير نوع الفئة أو الفئة المحددة
- سيتم تحديث المبلغ تلقائياً

## 🔧 الدوال الجديدة

### في `JournalEntry` Model:
```php
// العلاقة مع الفئة
public function category()

// حساب إجمالي المدين
public function getTotalDebitAttribute()

// حساب إجمالي الدائن  
public function getTotalCreditAttribute()

// تحديث المبلغ الإجمالي
public function updateTotalAmount()
```

## 📊 JavaScript المضاف

### معالجة تغيير نوع الفئة:
- إظهار/إخفاء قوائم الفئات حسب النوع المختار
- تحديث المبلغ الإجمالي عند تغيير المدين/الدائن
- التحقق من صحة البيانات

## 🧪 الاختبار

تم إنشاء ملف اختبار `test_journal_categories.php` للتحقق من:
- وجود الفئات المطلوبة
- إنشاء قيد مع فئة
- صحة العلاقات
- دقة الحسابات

## 🚀 التشغيل

### 1. **تشغيل الهجرة**
```bash
php artisan migrate
```

### 2. **إنشاء فئات الدخل والمصروف**
اذهب إلى إدارة الفئات وأنشئ:
- فئات من نوع "Income" للدخل
- فئات من نوع "Expense" للمصروف

### 3. **اختبار النظام**
قم بتشغيل ملف الاختبار للتأكد من عمل النظام:
```bash
php test_journal_categories.php
```

## 📈 الفوائد

### 1. **تتبع أفضل للمالية**
- تصنيف العمليات حسب طبيعتها
- سهولة إعداد التقارير المالية
- تحليل أفضل للدخل والمصروفات

### 2. **واجهة محسنة**
- عرض واضح للفئات
- ألوان مميزة لكل نوع
- حساب تلقائي للمبالغ

### 3. **مرونة في الاستخدام**
- الفئات اختيارية (يمكن ترك القيد بدون فئة)
- إمكانية تعديل الفئة لاحقاً
- ربط مع النظام الموجود بسلاسة

## 🔒 الصلاحيات

النظام يستخدم نفس صلاحيات Journal Entry الموجودة:
- `manage journal entry` - عرض القيود
- `create journal entry` - إنشاء قيود جديدة
- `edit journal entry` - تعديل القيود
- `delete journal entry` - حذف القيود

## 📞 الدعم

في حالة وجود أي مشاكل أو استفسارات، يرجى مراجعة:
1. ملف الاختبار للتأكد من صحة التثبيت
2. سجلات الأخطاء في Laravel
3. التأكد من وجود الفئات المطلوبة

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2024-07-14  
**الإصدار:** 1.0
