{"__meta": {"id": "X5a94bc449194751ad6cc397dfb984dbe", "datetime": "2025-07-14 18:50:33", "utime": **********.676364, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752519032.977232, "end": **********.676384, "duration": 0.6991519927978516, "duration_str": "699ms", "measures": [{"label": "Booting", "start": 1752519032.977232, "relative_start": 0, "end": **********.374228, "relative_end": **********.374228, "duration": 0.39699602127075195, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.374238, "relative_start": 0.3970060348510742, "end": **********.676386, "relative_end": 2.1457672119140625e-06, "duration": 0.30214810371398926, "duration_str": "302ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46019672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00422, "accumulated_duration_str": "4.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.621383, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.242}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6341362, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.242, "width_percent": 18.957}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6418579, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.199, "width_percent": 21.801}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-303105462 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-303105462\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-571710701 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-571710701\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1081748179 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081748179\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2137939696 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752518924393%7C51%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJTS1VaaERSRnU2MVV2bWpOK010Z3c9PSIsInZhbHVlIjoiNEMzNzN4UmFmNGpFSmxFTEVVcGlMTVpuSWQ2NnR1SVFuTXNmM3dYS0tIdmJiYTRPNk1aUUxGVnpoMW1waVRWK1drKzFaL2d5QjVMb1Y1Z081U1ZaTGtXNTl1ckQ4MWpXeHFqL2ljeUI3MW8xVEZwZUM2SHhTbEp0TDB4ai9kaThmRW15akFUdkxDQzhOOEhuc0FSWjZSeWZtMTBpdmE2eUJwOXJRSDF2R09RL01Hem9RUUFiUE04Z2F5QldrbDVvOHFON20vdXNQK3h5cFU5SnpPZDhEZ3pmOFZyS05XRGpid3IrTldsWDBFVnlhOFR6dFlkaDJjeWxBYU5KWGowQU8zaWpHdENWeEVQcjhWemxuYXgwbEdnVlpTZUZJc2ptZUJ0R1FqVlJ4azJhWGg2b3orUUhRV2Z0Q0hmU2xRcjNOWGVNSjYvZ2JwTHZJeXlObEJlZStPa0xxQ0hXVjRCalhLTE9RaHpoZUhTWFN6ZHRnbyszbGZ6aDFLWTdYOWt1M2FVbjdnNno3N1dxSklHUjE0UE9meDFjSUtSN1kxYlZkMHpBalprSWk0UGczWkg4STN2VGtCdlBGRjhvYTk1MFk0WkxRNFNKbEo0S3VkMXYvQWRFU000UFZqNGpma0Iya0M5bWM1ZmEwNzNwYzFKVEFmMkxvTGtYUUhJNFhoUHIiLCJtYWMiOiJkNWRlMDE4MTc0NzRmZDA3N2FkZjBkZGE5YWNkYTBjNzhlODk2NzNmZDFjNWUzNmIwOWIyZDZhZmYwOTJhM2E2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVaZkJjbUNUTXZrakFOTTRiNVNrVUE9PSIsInZhbHVlIjoiazdoZStEWFJjaUp5Zlg1Z0R5bW02NDJoK1JpVUo3eFljSGQrSVNaU2l4SDBZYnc1ejd2VkwzWlZQcGhXWkt2Z3hqL2pqVmd5TmhUYVpwR2FscEZia1BIZ3psQTk4WlZsSHJjUThZMThHQzlCM0RTNWYxTnVpVy9wV1oxVUhSZzN5TUp6YnIrK2JxRG44ek43R0tFTGF3LzAvRDFJWVdNZk14QmtXQThZbGZ5YTBRdmd0MjR6MTVxWXpPRzdVYVNsNlRIbTVHcFBackRENFFsS255UUY5K2N6TnhrakxVU2I1ZDl1TzNPbk1tRGJtaW1BVjFkdkg0ZS9LdVdZdzh2S2t2MXJTeEk0VkdPNEMycWJHVXIyYWs5VmlIRjc4Zy9mTlN6WkJXR1BFNEFWMDN3bUVwTms1dmdjLzBFNDNwc1Z6N1ArT2Y4cGtXbzZyc21odUU3bHNDRFh1ZlE1cy9hVzdjaUJkOWRiUzdPby9ITmxubzBlVHo1MmxCblcxcElEcWxTQlMyVmNCdTA1UENockFwN3NhWko4NDNUMllmZFgzeVk4QUwvWEl6ekxJN05sdk5uWElJaEgzTmRHeXVTK1B0cE1CdXBqSytPZzNwRHdHd21CYnJVcGlEVEl4cnYzL2V5VmtURGloUSsyNEVnWXlIOWZuanhJUWtwYTdYWDYiLCJtYWMiOiJiYzhjNzMwNzUwZWJkYjY1YzFhNmI0YzQ3MzE2NGFhNDlkMzk3ZWYxMWYzMDdiZGJmYjNlYzdlMGU5MzIyNjEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137939696\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2027524406 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027524406\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1495416486 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:50:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZwNW1MMkxlUXNLVTFOeGpVbk92THc9PSIsInZhbHVlIjoiQVVDUHJyZ3Y2Vk90a2Y2SzB3RDIxalhRY0I1K1lUN2dMMW5nR0RtZnUzZ3lFSG5BeGtSSW9TelJ3Mng0eDFXQkRzWGQrYTJkeUgzSHFLQXRXcVRPTXRnTFdyS3VkZkRDUERzK3c1YW9SZ21JOUJZdVZJbUFWWlpNeXpBN3Rka2tGMTBIT1J5aHAwWi8xRWN5THZNdm90L05NSmhGWlVQK1Y3WEQ3YnJZekpRazVmMXdpWHpsMHNLYzNJOEM0dS9ibXhKeXpjYzlSOG5jaHo4THNteStMNE56eEQzU3JGRitkbkRObmNzV09xbWl1VndwaVlJaG8zS2xCSHlWOERpVWZacjdlYUVRaTNScU9JanNrc2xpcXA5eTYzNHRSakh4N1RQazJCUHJwZUlqTmFsVWdjb1I5RnhRRGdqVnJxdEo1b0YwVkhYVUNYVFJGcExXUTdDRHlmRUNUUUdZWExsRzl3R1VpSkY0QlVRdWc5VzA3OCtOcW96SHVJbit5WFpxSGRkTkViQ1RabUJNa1ZvT0JrU0t1b1Zlc0I4cG5wMzVCWXRNSlZIZjBleVJ0WElrNUlOU2J1M2xSMTRPZEhpTUUyU2lkd3FTemlNZy82WGZ0Vld1bHB3MzZVTCszSjk3dmQ3c1pwTlA0Q3FyY1dMVTcrMllBSTF4MHAvUTVQbUoiLCJtYWMiOiIyZTc0OTIzZGYxYjhlNWQ4NjdkZjY1NzYxOGFjMmQ3NjU4ODkxMWJiNTBjZDI5YWRkZGY3NWFhMTAxZTZiYzNkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:50:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitNeldhT085VVhBNXJvWkp6ak1hcUE9PSIsInZhbHVlIjoiM0k5aTA3SnpLR0FlYUVVMEpCUFp2SGNubWlpUHhKVGdOY1VKcElkT2dQckxVclphMEsvYlE0TG04QURFSXZIWm01ekN4S0lFUFNPN3ZVUzEvNkpBSS9jdjJYaDZEV21yeVBLUi9mbzkvOVhScklWVVRWZXd6MHpSbStScTM4cVFiU1JkRXFFY3l0cFpTZ1J6eU9VblVXRGh2eE1pRzhXVnpaQlkyRVJwVEJtLzRGSFI2OGhrWlhuWmF2bHB1clM1b2VxcjBzWkZmZUlzbVorbHVBM29ReGRVb2ZJOExCdFVCYmpkcjRmTW1aWk5RS0xGRGhibHczdDdVd3ZzNkRSbmkzVUNZMThEVXRieC9OOTUzRlc4QWZkM05UMFBSTndGTlBaRFRsR2VKUTc1TmRqbWtzdng1VExOTnV6eWlET1BlclRMUW92SWJhV2xTWHN5aW5HclFQZkFlWVAvc1VpTXhaRWVNMFh1S0dQdGZMaEpsc1A2WWhrQ3ZpRmhtdU1jYS9VWUU4KzdHSVlBUVVhNmY5UCs4dTdnZlAwamQ4dEtLRGorWEt2c1dUL05XTURud0lhT2RkNm4yM2F0OU9JM2EyYkpMTk1CTDlGdEZ1UUJoK3lld0NJRlllMk55Q3ZZQ3ZHRkNOR1p5SzRzaDhQUWt2UFdIV3ZxMmpmV1dsSnEiLCJtYWMiOiIwZmVkMDAwMDY0MDE4OGFlMDU1NDZhODYxNmI2OWY1NDhjODI5MWVlY2JjNjU3MmRjNjFkYjA1N2FjZjRjZGZlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:50:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZwNW1MMkxlUXNLVTFOeGpVbk92THc9PSIsInZhbHVlIjoiQVVDUHJyZ3Y2Vk90a2Y2SzB3RDIxalhRY0I1K1lUN2dMMW5nR0RtZnUzZ3lFSG5BeGtSSW9TelJ3Mng0eDFXQkRzWGQrYTJkeUgzSHFLQXRXcVRPTXRnTFdyS3VkZkRDUERzK3c1YW9SZ21JOUJZdVZJbUFWWlpNeXpBN3Rka2tGMTBIT1J5aHAwWi8xRWN5THZNdm90L05NSmhGWlVQK1Y3WEQ3YnJZekpRazVmMXdpWHpsMHNLYzNJOEM0dS9ibXhKeXpjYzlSOG5jaHo4THNteStMNE56eEQzU3JGRitkbkRObmNzV09xbWl1VndwaVlJaG8zS2xCSHlWOERpVWZacjdlYUVRaTNScU9JanNrc2xpcXA5eTYzNHRSakh4N1RQazJCUHJwZUlqTmFsVWdjb1I5RnhRRGdqVnJxdEo1b0YwVkhYVUNYVFJGcExXUTdDRHlmRUNUUUdZWExsRzl3R1VpSkY0QlVRdWc5VzA3OCtOcW96SHVJbit5WFpxSGRkTkViQ1RabUJNa1ZvT0JrU0t1b1Zlc0I4cG5wMzVCWXRNSlZIZjBleVJ0WElrNUlOU2J1M2xSMTRPZEhpTUUyU2lkd3FTemlNZy82WGZ0Vld1bHB3MzZVTCszSjk3dmQ3c1pwTlA0Q3FyY1dMVTcrMllBSTF4MHAvUTVQbUoiLCJtYWMiOiIyZTc0OTIzZGYxYjhlNWQ4NjdkZjY1NzYxOGFjMmQ3NjU4ODkxMWJiNTBjZDI5YWRkZGY3NWFhMTAxZTZiYzNkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:50:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitNeldhT085VVhBNXJvWkp6ak1hcUE9PSIsInZhbHVlIjoiM0k5aTA3SnpLR0FlYUVVMEpCUFp2SGNubWlpUHhKVGdOY1VKcElkT2dQckxVclphMEsvYlE0TG04QURFSXZIWm01ekN4S0lFUFNPN3ZVUzEvNkpBSS9jdjJYaDZEV21yeVBLUi9mbzkvOVhScklWVVRWZXd6MHpSbStScTM4cVFiU1JkRXFFY3l0cFpTZ1J6eU9VblVXRGh2eE1pRzhXVnpaQlkyRVJwVEJtLzRGSFI2OGhrWlhuWmF2bHB1clM1b2VxcjBzWkZmZUlzbVorbHVBM29ReGRVb2ZJOExCdFVCYmpkcjRmTW1aWk5RS0xGRGhibHczdDdVd3ZzNkRSbmkzVUNZMThEVXRieC9OOTUzRlc4QWZkM05UMFBSTndGTlBaRFRsR2VKUTc1TmRqbWtzdng1VExOTnV6eWlET1BlclRMUW92SWJhV2xTWHN5aW5HclFQZkFlWVAvc1VpTXhaRWVNMFh1S0dQdGZMaEpsc1A2WWhrQ3ZpRmhtdU1jYS9VWUU4KzdHSVlBUVVhNmY5UCs4dTdnZlAwamQ4dEtLRGorWEt2c1dUL05XTURud0lhT2RkNm4yM2F0OU9JM2EyYkpMTk1CTDlGdEZ1UUJoK3lld0NJRlllMk55Q3ZZQ3ZHRkNOR1p5SzRzaDhQUWt2UFdIV3ZxMmpmV1dsSnEiLCJtYWMiOiIwZmVkMDAwMDY0MDE4OGFlMDU1NDZhODYxNmI2OWY1NDhjODI5MWVlY2JjNjU3MmRjNjFkYjA1N2FjZjRjZGZlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:50:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495416486\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1838875302 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838875302\", {\"maxDepth\":0})</script>\n"}}