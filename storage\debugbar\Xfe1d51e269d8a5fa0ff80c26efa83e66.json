{"__meta": {"id": "Xfe1d51e269d8a5fa0ff80c26efa83e66", "datetime": "2025-07-21 01:34:24", "utime": **********.645732, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.188597, "end": **********.645748, "duration": 0.457150936126709, "duration_str": "457ms", "measures": [{"label": "Booting", "start": **********.188597, "relative_start": 0, "end": **********.571227, "relative_end": **********.571227, "duration": 0.3826301097869873, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.571235, "relative_start": 0.38263797760009766, "end": **********.645749, "relative_end": 1.1920928955078125e-06, "duration": 0.07451415061950684, "duration_str": "74.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020399999999999998, "accumulated_duration_str": "20.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.60011, "duration": 0.01924, "duration_str": "19.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.314}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6307979, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.314, "width_percent": 2.99}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.637403, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.304, "width_percent": 2.696}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=8&date_from=2025-07-01&date_to=2025-07-01&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-193058585 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-193058585\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2047283056 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2047283056\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1846094010 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846094010\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-797502734 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-01&amp;date_to=2025-07-01&amp;warehouse_id=&amp;payment_method=&amp;customer_id=8&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061661312%7C12%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFFYldmYzNRckk1czRtNkwzZ3Nycnc9PSIsInZhbHVlIjoiOXZkU0lCYTFsbEQxaTB1WGg5WnRFWm9lK05aaWYwTG4yWFdJSjVFT1VVMVBWSVVvZUpKSWpQaUdOaVV6V1FtQVNMalI1K1pWTGU2NTBieW9VeEp4eXFNVlorcVVmSUQ3dlE4UnNHbVkxUll3eExwVmQzM21HbzRHNUkrbHh2SHRQajRUaVg1K2kxZzh3VGZLNDN2Z1EyVEgydVlWY3FPQWhqbk1jNmdzZVlJK2NWMzNvUFd3N2NRWjBGeTltVHRiV3ZTSGhPRTg2YnV5ZWxzWmlpZFk4b3pzdlVEc2lYL0VQQ3UrcGNNQndLNjZybnlrK2RLdXYxM0tKOS9iRm05VDlUcXo0eUlXYUlqSDlCcmlvc2hwcVEyN1R3SXVZa3IvOFNZL1JsQllESGJKcm56M2g1VTcvVEtIaXRiTklDYzZtNTFHekJHR1ZqR21xQ3JTcnZOajNxdEo4bElJVjZacnVJOGxSR0wxcnZqM0xXUmpLRHRHWG9aL25XS0ZQV0hwdWRhMmhHOUlCTmlhYjd0dk9RMUc3ajRCS2dZdWw5eHpwK0lGTWhmVUNxenFJL0dySHNEc3IvemNORFVqT2RsdkozbFFyeWY1cG5JSjNYRFhnaWJudm9OTllNTkYvQVF0WGFuZ1JqRUVVOHFlTm9OV3Ria1VNaHNTcGI2ZzBXS0kiLCJtYWMiOiIyOTlkZDQ5NWE3NWZmOTQ0YTczYzc2ZDg4NGQzMzQwNDQ1ZWI3ZmQ0MTYzMTI0ZGMyMDcwNDJlZGM4MjY5MGRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inljc0ZzcUEyd29UTFJkTWJ0MldNakE9PSIsInZhbHVlIjoiWjhMdk1wdHpHRXZtVFhMdW1nZ0FFR3lKaTRKbHlWUzh3VGpzWVdOeEo0TWNQYTlkWElzU3BhQlZMWEpCbm9namxjMkZyaW0wTFdxdnhleFlVR2V4QjRhVStsQzBic0MyUTA4MGlTQ01Ic0NSWjM5VUpSeG41NzVFS3JVT3AyTVpEUVBTKzY3SGtBRkM1eHlWRVQyYTNya0NIc09YWXpPdm9iRmN2VEg1Tk0xSGJjWStxZFRwMDN1NmU1MTlNZzBwUms3ZldVMWlIOFRNV1N5T1doNzQ2RDd3YnlCWGVJRTc4b2UyK2N3Y1RrcVlvYmpyQ1NkM1NLZFYwOG5lclJKSEpUcmgvYWFtNVExVm9UNzFzTE1BSldNNUhLdlRodVhEQngvYUhncjBKVGdPRy80WUl3OHcwdUtmOGtVN1lRKzFtellVZHd6RXhJaEJVWG8xTkR2Qzl5NFNWNGtKR3JBRk4yVVhqNWtkeEhZc2ExeGltc3d4cnQrTDNYMlJJVzdSS1N3WUxuOTlyd05yMk04czRsWWhxdnVWdktxd1dqeFNLRUoreE1JdmZXSHEyTVpuNzMzbERWdS9ZcFlYL1ByQ0pBS29FMkFZemFOclIrVU0wT2dNVXVsakNacVp1c05zQVF5TTRCd1FmWVdDY2d1aDU1OEk2WUZ0NElUbjJYTGYiLCJtYWMiOiJlYzQ5YWM1MzE5YWM4OGFhZmI0ODY0ODc0Mzc1N2RjNjVhZTNmZGY5NGRkMjY3MDU3ZTY4MGVmYTNjNDA5MTBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797502734\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1136576102 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136576102\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-514722064 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdXYSthcXczZ1hpbUx0MUtyV3NOeGc9PSIsInZhbHVlIjoidWN6dXdFZ1Y3WVFDU090RzRzbDFrMXpDY0pwak4wbC9GaER4UWdPVDQzZENUNmJVcGdxTnpvbGpaV1V5SDloNEFIekx0UTlLdDV6eUNEcVlDeEJwQXRqeTlES1R0bGxjY05EcWlJb2NwRXF1elFDK3M3SEFhaGJIZzFEWWpBUzVxUUNUOVRVQ0thQ3UwRWpES3V2ay9LendFU0wrRkxXZklQMUtmb1E1aDZKVFl4ZnMzY0pXWUpRbmd6ZCtRYlYvV3ZTNHY3ejYrSHRZU0YrUjE5UldZT2JXT2ZVU0FjV0lHb2hxSXNmM3JmZkZTOUdYOGt6bjFIb0dJQzlxN0xGZVBOQUpiYitvZ2M2RWlFVkdNaUZRTlhsbjd4dUpkWG9iSitLQlM1MDJ2Tmw5SkN2N2F0eS81L21pYmZqUmMvdjJlQ1ZBQllVMWI3ZGkzQk5iYktsOUVOdm1HYTg2ZjFPSkh0MDVRZDMxY3dRdlBWSGlJaE5yR25wVEhDMmlBbFIzeXNSZmNETGRTdjVEUnJJTlhUOW1ieFMwN2syRlFTOVFDSzlsZHJzcUdCUklKUjJWWVVoSUpFak1qaDd6M0R5NlRNQ1J4d0hIN25OSDNJWjFaVlg3Tk5xK2djR0RWY2ZrYk9RUEV2VGtIQlVxWU1Hb0JvN3h5Um9NVGxuTVRyRGoiLCJtYWMiOiI0ZWMyNTJjNWJkNDZmMzM4NzE4NWIwNWNlYjcwMGQwMjk2MWVlZDgyZjJlNzI4MTFlNzczYzBmN2RjMWM2NTQyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBlOEVHM1lRSWsrQjd2bWtLZC9BTEE9PSIsInZhbHVlIjoibDMxOHhjWWhXL0FyOGw0MEpQcU1pTmpjcnM2NGIvdEVQSVpVcldUOW05OC9lV1h0Mksva3RrbVRnTHZBOXJycTVEOFB0MlhLYWVZNDA4bXBCeUtUaCthbnpDR1RSL0JXb24rd05wcDlaQ0JSeUdDYnJwR0pxcUZ4OWkraVJaZnhIR0RlQ0paUWdMVXZXbG9LTFRQVzMzaktJOW0zNjFYTWFHQUNjUk0yWkpwTWt0WDdyU1l4YnAxWStpWFJCRnJCTmt2cm5qZUhSSmw3T0hSRkFBVGc5QU1CdUh3NWcxYnQ2My9MRXVqRkxmWDlxWExQVXlydVdqRnZndmRpV05XR2htMEdBYk9QZi9qY1NsMWFPemNxalpHNDhmbjUyNC9FVDNBd3F3L0JPWE0zVktQR1lLK2EzeVJxaG8zRFc5eXNJSEFqanU2MjZZdUg5cUlMUVVMTm1LM3gwTDE3SjV3NWZhK0hCaDRPcjRGT2xQU3E1Q2lsYXBiVGg0aGdWOUx2NnBzSjF3NTNPcmJxd29aVUhzQ0M2cDlGaDVJK2pvTkNZS20vU2J4aHZZZGFIcEtlcjVxdmRzWWdoSHE0TUtyOUZyL1VGR1dmaEROV011dVdLQmRDWk5JdzdNMUVsQ2tseXphNm95SUIwV1puUXhmUGVDMGF1azA0VFIyc2JyV3MiLCJtYWMiOiJiZGVhN2ZjNjAzMWNhZDQ5MDk4OWRlYzkyN2FhYWZlOTVjMDEyMDkyYzg1NTY1MGE1MzdhYTgwMTQ3ZDMwOThlIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdXYSthcXczZ1hpbUx0MUtyV3NOeGc9PSIsInZhbHVlIjoidWN6dXdFZ1Y3WVFDU090RzRzbDFrMXpDY0pwak4wbC9GaER4UWdPVDQzZENUNmJVcGdxTnpvbGpaV1V5SDloNEFIekx0UTlLdDV6eUNEcVlDeEJwQXRqeTlES1R0bGxjY05EcWlJb2NwRXF1elFDK3M3SEFhaGJIZzFEWWpBUzVxUUNUOVRVQ0thQ3UwRWpES3V2ay9LendFU0wrRkxXZklQMUtmb1E1aDZKVFl4ZnMzY0pXWUpRbmd6ZCtRYlYvV3ZTNHY3ejYrSHRZU0YrUjE5UldZT2JXT2ZVU0FjV0lHb2hxSXNmM3JmZkZTOUdYOGt6bjFIb0dJQzlxN0xGZVBOQUpiYitvZ2M2RWlFVkdNaUZRTlhsbjd4dUpkWG9iSitLQlM1MDJ2Tmw5SkN2N2F0eS81L21pYmZqUmMvdjJlQ1ZBQllVMWI3ZGkzQk5iYktsOUVOdm1HYTg2ZjFPSkh0MDVRZDMxY3dRdlBWSGlJaE5yR25wVEhDMmlBbFIzeXNSZmNETGRTdjVEUnJJTlhUOW1ieFMwN2syRlFTOVFDSzlsZHJzcUdCUklKUjJWWVVoSUpFak1qaDd6M0R5NlRNQ1J4d0hIN25OSDNJWjFaVlg3Tk5xK2djR0RWY2ZrYk9RUEV2VGtIQlVxWU1Hb0JvN3h5Um9NVGxuTVRyRGoiLCJtYWMiOiI0ZWMyNTJjNWJkNDZmMzM4NzE4NWIwNWNlYjcwMGQwMjk2MWVlZDgyZjJlNzI4MTFlNzczYzBmN2RjMWM2NTQyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBlOEVHM1lRSWsrQjd2bWtLZC9BTEE9PSIsInZhbHVlIjoibDMxOHhjWWhXL0FyOGw0MEpQcU1pTmpjcnM2NGIvdEVQSVpVcldUOW05OC9lV1h0Mksva3RrbVRnTHZBOXJycTVEOFB0MlhLYWVZNDA4bXBCeUtUaCthbnpDR1RSL0JXb24rd05wcDlaQ0JSeUdDYnJwR0pxcUZ4OWkraVJaZnhIR0RlQ0paUWdMVXZXbG9LTFRQVzMzaktJOW0zNjFYTWFHQUNjUk0yWkpwTWt0WDdyU1l4YnAxWStpWFJCRnJCTmt2cm5qZUhSSmw3T0hSRkFBVGc5QU1CdUh3NWcxYnQ2My9MRXVqRkxmWDlxWExQVXlydVdqRnZndmRpV05XR2htMEdBYk9QZi9qY1NsMWFPemNxalpHNDhmbjUyNC9FVDNBd3F3L0JPWE0zVktQR1lLK2EzeVJxaG8zRFc5eXNJSEFqanU2MjZZdUg5cUlMUVVMTm1LM3gwTDE3SjV3NWZhK0hCaDRPcjRGT2xQU3E1Q2lsYXBiVGg0aGdWOUx2NnBzSjF3NTNPcmJxd29aVUhzQ0M2cDlGaDVJK2pvTkNZS20vU2J4aHZZZGFIcEtlcjVxdmRzWWdoSHE0TUtyOUZyL1VGR1dmaEROV011dVdLQmRDWk5JdzdNMUVsQ2tseXphNm95SUIwV1puUXhmUGVDMGF1azA0VFIyc2JyV3MiLCJtYWMiOiJiZGVhN2ZjNjAzMWNhZDQ5MDk4OWRlYzkyN2FhYWZlOTVjMDEyMDkyYzg1NTY1MGE1MzdhYTgwMTQ3ZDMwOThlIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514722064\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1266815024 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=8&amp;date_from=2025-07-01&amp;date_to=2025-07-01&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266815024\", {\"maxDepth\":0})</script>\n"}}