{"__meta": {"id": "X8d8818a53a375b28684870f559386804", "datetime": "2025-07-14 18:30:19", "utime": **********.775044, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.345654, "end": **********.775057, "duration": 0.42940306663513184, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.345654, "relative_start": 0, "end": **********.720382, "relative_end": **********.720382, "duration": 0.3747279644012451, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.720391, "relative_start": 0.374737024307251, "end": **********.775059, "relative_end": 1.9073486328125e-06, "duration": 0.05466794967651367, "duration_str": "54.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45995712, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00312, "accumulated_duration_str": "3.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.748555, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.949}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.76073, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.949, "width_percent": 19.872}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7670588, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.821, "width_percent": 12.179}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1051159293 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1051159293\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1101776224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1101776224\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-581228998 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581228998\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1947304680 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517814721%7C41%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IngvanZ2R28wL1g0UCtrVndhYk12VXc9PSIsInZhbHVlIjoiS3Y3N0djTzdnWmNvNGE4dWgzdzhmQ0hZOHl6RjRYZndLQUc2ajBqWnVBRnNwZnp3RXRwSVFEMDRwTVZ1ZVZYK2RsWDMyOGNzVjlDVEVVb2o4UytRTVI2VXlNa1Q0UG9ZV2F4RjI3ejFiU0VUY1Y2N2FBeDYwN2dsOXYvV3kvc0Zuc084a1lmMERDc2tKL01EWWNaSWx1bVNTa3FzbG1Qay9yOHByYzlwak55U0xhcVFkTDhXN0RiZTNDZmF6anpaZVN4V2NWTWdBekN1RGEwYXVFdWVlZkRXbmRuWUtlL0RQUGo1Q0dwTUdxM1l1VlhTVlh1UVB0Nm9WK0lLSTlHazRFMzNTNDdaT2RMQWZPSjhTcFJLempETllNTXR2SFpwR0Y5SlAzcktGOHg5M21SZDBHQjlka0gzR2NFaWNCRHNsNTR3UWhxSm9FSW5GeUlDMG9IbWprMThTMGt1WWZhVnhkeFc4dnZDV3RQek1Ealc0QWVrcXJTNmV6d2o0Z1NpZ1pDSVoyYUpEWDE5cUVGN2gzM3VoSCszT2xFbmk1WHl3SVljSWtqRzhzdmR4eWd0RWxUdEV0TjcwcFlTbUplR2VML3ZNbE9vUEhVb3h3NHM3SGRwaFhJaEhQYUo0ZC9tNmkvcmV3UW5UUUh1b09Yd3pXSmhyd3hYVkZUU2hNTW8iLCJtYWMiOiJmODFhZTY4YWY5YzU1OWI3Yjg4ODc4NzU2YjcyYzRmNmZjZDE1MWQ3Y2RhY2E1MDA0ZjFmNzE5Mzg4ODRhNzc3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9NRDhUcFlFN3dPaG8vcXdBUnNPSmc9PSIsInZhbHVlIjoidzBXZUxRY0FBLzZNdTZwdEs3d1hUNjB0VUQzTkswc095TzVNblByUFZMSkVnRW1OUG1XZ3A1MzVZcWlQUFRMWVdKV1FKL2hycFF0WXkwVUNjSTZRTFlEY3BobUZWYTFyYnpnaU9mVGxVbmhEUUxxUDFjSDBiUy9QUjBBKzJpZ2NKcjNUcnkwU3pMZWpEQWdFY0l4OE96LytwQ2c5dzNHUThKSTJ5c3RXOFdRVXROcWxKK2tQdlBRTkQwbytiTStzK05lM05tM0J1UW5ha3AwaDFpMU9HT2J2MEdNd2p3THY4cDd3NWlZM2lnbzRDUm5xZk5PbWdRbzVQNysvQUhXMGUvN3BoOTdhTUloTW5NNjVnb0FMM1dIWnhBNE1OMXVEOEpsVlBKWkhnSGFuUmZaM0MvVEN2amJWUnZVRnpTdWlFMDExUTE4M25sTkg2WGpHK0x5OS9za3d1Ukl0MnVWWnRQdnFnYlZhdmdvNUtzN1dKQjJxWGVCNkJoWERPYXk5S3NxVVFoZDc2d0hncW5WS1ZiaUEzT2tSTFI2ZDJJNVZxSXZEbFJsQWRMVytxbnJ6MDNpYWdWTTdVRklOQlk1d09oVWkxSFdTbDBsUDk2UVhISXp3c2I5bGorcEFJT1d0cmpEUjlYVFRwbDVDR25sUVZhamZsZzZ1TTVLcEFWNkUiLCJtYWMiOiI0ZmNhNWNlNmFjMzI5ZmJjYjBmMDkyYzMwYmExYjhmZWRhNjlmM2U0YmM5NWQxOWNiYzE5YmNmMmY2MzY3ZjMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947304680\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1705973718 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705973718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-90055970 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:30:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InovMW0rMm1SNmVxaHduK3dsQW1zeFE9PSIsInZhbHVlIjoiWENXdXBVWERUaW8wMHgxREg0ZWNZV0o2N2ljOThtMzNJcnN2SlhrOGRXbDRuZTFtM1hjbVYvSGxiVzhjZHZFR0loaUF2YUMwWWlIZ1NmVTUwRlV2R2VVYVl1RWdWbFVuRThuN1VyU1pBK1ZoYjB1ei9sYVk4dXhNRTlIZmpwblFzVGxoNS9ZQjd6eklwM0VWbDJWZTg4c1dyRGVTcU03QWxlZzRkSXpaNHdUVm9xRWh4Y0lOeklnY0ttelVHc2RHNzliMGdHZVFKRGM2UUE5NVNQUGJhbUJ1VHhhRUMzTmVITldVRUV3dWs3TzQyMG1XZ2VMYXBYWU0rWmtWRjAvZ0J6YUZVVU0rVTJoaTZyUWZUODRaVUZwaUl5WmZoSGpZRVQ4UFcyTEgzTlRqWkFuK3J6WXNobzI1STh2YVNxQStFd1hPdVBXdGdaMmNUeUcwcklrTTF5c0Vqd0VXZ2oyaGtwZnd2SW9BUUYrY1BzWW43NGhJNFo2QWZMSTh1N2o5bGQrVnFDUm8vZlNMbkRaLzNoakp2UWxQZUN3dXZJNlpiN2NtZDFxQjFWSUR3ZFV2STJkWjJXRlVxK3JpU241bmdzVWNyT0NvYjdlbXZmY1ZTTDBIYXgreThCS2IyL25xYzYwaW5SbndaQkJPMXA4SFZub2lwWS9JMVJKZ29CUGQiLCJtYWMiOiIyMWNkNGFhNTJjY2I5MWE3MTQwZjlmNDI4NGM1Mzc0MjUyY2NjYmFhOGFmNzFhMGM1YmFmOWQyNjQ2ZGU1YjRjIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktLeTVJY1BwcDZMVmJvVXpPN3JwRXc9PSIsInZhbHVlIjoic2lLRmREZTdpUS9WZW8wLzd3ekVGRStqVjU2QmZxbmNjU1QwV3JsVTAyNWlWY05nd1ZNWEpOZjdobkJabWhhU0srVndZZW83TmNVVWRBU0Ridkk0RlNIZ09mRUVxUjlEUkxEaElydDJWYUtrVDZRMU1UdDVMcTYzVEJrb2p1T3o3dFdqeE9HSjRNTkM1Y2dEZFYxZ3AvWVg5d2VJOTY3MndsYndXZnBJWW9KbkdFeXUvUmJucHJYMTZlSitpQSs0YW00U1RnUzN1RS9YelZ1ZFNkaWRDalEvLzYrQ2FTZjRFK2lkMWVZVXcxSGdpMThpOGprVHJVSVhUZVNqV3ZSOEpNOUJpWmIrMGNzUmtzUFRMWW5JV2FmVStWckNUTHRiVEFGOXNTaWVVQitZM2w5MmxLcks1WHRkQW1pb0MyWXBFcFM1OGg0Qm9zTHdKeitOZzJMUVlDUStnUGh6aC9SQkgvWEVSeGlGYmlhdHBHa29xb09NWVFxdGlkcnpoOFpKalRacWc1M1lkc3VXZzdFT2h5ZmlobVNLWmNhQlJiMEZuWGU4MXlJUEtQYkd4YlRkdVlRMEdGYkl1K1VKS016K2ZNNy83c292Y3RaT2xROFV0UDhydlJWOXYzcWczRkF4VWZDMjRITUdjTzRpTG8zZS82eGN1RXhRUlEwbmd1aE0iLCJtYWMiOiI3NDVmYmI3NDE3OGM5ZTJiZmFmMjA1NDZkM2FmZjgzNmIxYjQ2MzQwY2QzYjNhNjhiMDIzNDVjYzNjM2Q0NzczIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InovMW0rMm1SNmVxaHduK3dsQW1zeFE9PSIsInZhbHVlIjoiWENXdXBVWERUaW8wMHgxREg0ZWNZV0o2N2ljOThtMzNJcnN2SlhrOGRXbDRuZTFtM1hjbVYvSGxiVzhjZHZFR0loaUF2YUMwWWlIZ1NmVTUwRlV2R2VVYVl1RWdWbFVuRThuN1VyU1pBK1ZoYjB1ei9sYVk4dXhNRTlIZmpwblFzVGxoNS9ZQjd6eklwM0VWbDJWZTg4c1dyRGVTcU03QWxlZzRkSXpaNHdUVm9xRWh4Y0lOeklnY0ttelVHc2RHNzliMGdHZVFKRGM2UUE5NVNQUGJhbUJ1VHhhRUMzTmVITldVRUV3dWs3TzQyMG1XZ2VMYXBYWU0rWmtWRjAvZ0J6YUZVVU0rVTJoaTZyUWZUODRaVUZwaUl5WmZoSGpZRVQ4UFcyTEgzTlRqWkFuK3J6WXNobzI1STh2YVNxQStFd1hPdVBXdGdaMmNUeUcwcklrTTF5c0Vqd0VXZ2oyaGtwZnd2SW9BUUYrY1BzWW43NGhJNFo2QWZMSTh1N2o5bGQrVnFDUm8vZlNMbkRaLzNoakp2UWxQZUN3dXZJNlpiN2NtZDFxQjFWSUR3ZFV2STJkWjJXRlVxK3JpU241bmdzVWNyT0NvYjdlbXZmY1ZTTDBIYXgreThCS2IyL25xYzYwaW5SbndaQkJPMXA4SFZub2lwWS9JMVJKZ29CUGQiLCJtYWMiOiIyMWNkNGFhNTJjY2I5MWE3MTQwZjlmNDI4NGM1Mzc0MjUyY2NjYmFhOGFmNzFhMGM1YmFmOWQyNjQ2ZGU1YjRjIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktLeTVJY1BwcDZMVmJvVXpPN3JwRXc9PSIsInZhbHVlIjoic2lLRmREZTdpUS9WZW8wLzd3ekVGRStqVjU2QmZxbmNjU1QwV3JsVTAyNWlWY05nd1ZNWEpOZjdobkJabWhhU0srVndZZW83TmNVVWRBU0Ridkk0RlNIZ09mRUVxUjlEUkxEaElydDJWYUtrVDZRMU1UdDVMcTYzVEJrb2p1T3o3dFdqeE9HSjRNTkM1Y2dEZFYxZ3AvWVg5d2VJOTY3MndsYndXZnBJWW9KbkdFeXUvUmJucHJYMTZlSitpQSs0YW00U1RnUzN1RS9YelZ1ZFNkaWRDalEvLzYrQ2FTZjRFK2lkMWVZVXcxSGdpMThpOGprVHJVSVhUZVNqV3ZSOEpNOUJpWmIrMGNzUmtzUFRMWW5JV2FmVStWckNUTHRiVEFGOXNTaWVVQitZM2w5MmxLcks1WHRkQW1pb0MyWXBFcFM1OGg0Qm9zTHdKeitOZzJMUVlDUStnUGh6aC9SQkgvWEVSeGlGYmlhdHBHa29xb09NWVFxdGlkcnpoOFpKalRacWc1M1lkc3VXZzdFT2h5ZmlobVNLWmNhQlJiMEZuWGU4MXlJUEtQYkd4YlRkdVlRMEdGYkl1K1VKS016K2ZNNy83c292Y3RaT2xROFV0UDhydlJWOXYzcWczRkF4VWZDMjRITUdjTzRpTG8zZS82eGN1RXhRUlEwbmd1aE0iLCJtYWMiOiI3NDVmYmI3NDE3OGM5ZTJiZmFmMjA1NDZkM2FmZjgzNmIxYjQ2MzQwY2QzYjNhNjhiMDIzNDVjYzNjM2Q0NzczIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90055970\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1663137243 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663137243\", {\"maxDepth\":0})</script>\n"}}