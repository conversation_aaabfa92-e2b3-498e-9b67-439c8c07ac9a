{"__meta": {"id": "Xd6b231625eb9a238a11f674fc1ac98fb", "datetime": "2025-07-21 01:33:56", "utime": **********.345813, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061635.913336, "end": **********.345827, "duration": 0.4324910640716553, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1753061635.913336, "relative_start": 0, "end": **********.291532, "relative_end": **********.291532, "duration": 0.37819600105285645, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.291541, "relative_start": 0.3782050609588623, "end": **********.345829, "relative_end": 1.9073486328125e-06, "duration": 0.05428791046142578, "duration_str": "54.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006224, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00295, "accumulated_duration_str": "2.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3210232, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.475}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.331798, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.475, "width_percent": 15.593}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.337364, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.068, "width_percent": 15.932}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1848587127 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1848587127\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-525795417 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-525795417\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-263664417 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263664417\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2029238960 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061249606%7C8%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlOUStYVEF4QnBnTDMvbnJ2R2F0b3c9PSIsInZhbHVlIjoid2VuRGUrODQzWFAzMmU3MFdHNFdIUGQyVC9ib0VaQ1U1MkI2YzNWbGRmSmlwRExnSzZsNVpOMllsK2JoVWlBRjRUYWcwUm5OSWh5VnpnYm1ZdWduMnJ3L2RkdDZsSm9WRWNMM1d3UC9wdTNsV1FXQWhjZTZrWFhmVEZ2VFhGVldvN1V5VkpEd2tzejkzUlo5U09sQ25FaVNGVkVxUDR2SDNWUHdqTVEvc1hwcXlnamwvK2lMR044OUk5WExZQWxpbkpIRDc0SXQ3RFFkNWtrZ1Jrd25kamZodmxoejZXMlZWM0lUaEVVMzN1WUsxL283ay9zdUVtWTV2U0FWNlcwZGZ0Y1RhOFh4UStGd2tRLy9xcmJNOXJLd2t0UHh1Nlcyd3djYlVlaVg1cUZBOHhENlljSjNwdWRmWFJiWmYzdzBsT0ErTUcwbmZ6OXdYMldDR0F4WmNzcWg5YWRteEh0b2k0VjZnTmg4cVQ4KzJrdWRKcVBUYXBQVStSUmtUbFN0MWtMakxyMzE2WHh2MC9hS20xdmtyOU9kQWpHeW43MFJaSDQxZ2U5K2Q1Z1VISFhUMGREM1Bua2c5UnNRNG9IdFpFeFVOMmh3UmZuOVoybG1MK2QrZ240dXk5bTljTGxyNXoyZlNkaVpFUHlqNGhyLzczeHlOOW1zdmZQcTRDa2UiLCJtYWMiOiIxMWQxZjc1MzMzMGJmMjYzMjM0MDI2NTk3ZjFkOTE0YTJhYjNmODZkMjI1ODJkZWFiZTFkMGUzM2NlNjU0MDJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktYNnJUMnlxb1RoWFlFZi9wZjNPYVE9PSIsInZhbHVlIjoiNi91WDY4UGlnSFJzL3BWaThURXdWN3BlSDlpTFA0RW9pZlQ2K2F1Sjczc3VoWFdaRU1PU2Ixdk1LVExGTFpSTlllc2I1M0RaejhaUk84WjZUUGVCRVl0NWxvenEzSVVCMEZuR3pteGtwZ0lWTEhRNkFaS3ZNSUZhRUI1aVFqNDNVcm5sZEJFZTMydFVnTGNnRlE1RGpWbW12RHBpWnJEYWcwbk9IRW1CaTZKUktnZzhFZzQ2T2hLd201T2w1NjAxWU1mVmtxNjIyZzNIZmtTS0dWWGwxbU9uWGxqd2hwYTNBUVRCbnRkR21oY2syNGM0RDRrbExlMGtkcUFkbHlmK01KR3F6bGUzTnFSWlJiKzhpT0ZpOEpid3B3VlBidkQ2M1FjL1lGNFBWSWEzUmM2OUZ5cUMzeFhBRDNsZVNjL0hyZHB5alJOa0E5YWxsWi80VzVCRXR6TzJMa1k2VFNWMHFrcE8xOWZXTGpXZUIyZm5PR1MyL2xjL2h5Z1gzb0xtZ2s1UThEYlFPeitocVVkRDhXMmlncHQyMTBuRk9MSHFSWXF0T2p1SldOU3k5eUUzSUNzMnpkYUhPY1FmeE5hZjF1eE5qa3RNWmRZbkpwd1BVYmM4Tk5pY1hmWmhTSm8yV2hHVFZBMGd1cytYUVo5R1dKNlROV3hqN1owZXJwNXQiLCJtYWMiOiJiM2I0OTI4NWE5NThmZGFkYTZlMDY3ZGU0N2I1NTA2OTc3NmQxMGMyNzRmNmQ1NTY4YjVmYjdiYmQ3NzQ0YWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029238960\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-998464507 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998464507\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1043166640 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:33:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFxeWc0djg2enlYZUlmay8rMkt2QWc9PSIsInZhbHVlIjoiV2xOUmVHc2ozTzFmV0p1OWJtZ0ZibW0xRU9KRE9IcmFJdU9wb0tKdzVHYkdick02QXMvbVVlakhBVXUzbHBkMllFNFh5c1o1bGFibHRqOWk5UWl4QWgxalFPS0FMY0xlNDdsSVM2TGhJc2NDQ25ub2NOTnVSclkvVmM5MDVUS0duS0pvV0VUWnp2OHRUWit0NjQ0U0RSSTZBMGJ3MktWMkhyT3NSS2dRODQrTGNVZXI3eUU1dlUwM01wVExqUFA3THRsME82NzRWTDhqWEJYUjBVNUJaRDZTOEFLcG1Kb242NHJNZGlIL2EwNit2a3FsOU14QUxtL3NEZWk1YWhqNFN6bDVPOWk4NStBLzl3ajRyKzBUTzNoai9ac1h2VUVINjlYdk1PT3ZndC9HL3JJaExHM0VJNEN3T3B3RUVhckR3bWVNaG9ZNmRUQWhkVml6RXdvbmhRZ0ZXOWJpQ3R1RlVrZmhNRStiekxIbWtJYklHVzI5RHVTL080WXF5SnlvenJBNkk1c05QR05tT3JjdzRJdHR0aHVBZnlvd2ZtNVhzMmNyakxncFQ0YjRFVmRxTGRMTHlaZU5oMVJCNERuRjVnK25Gb3NLU3NKTnY4UGJSdHhGNjdPeWYzL0dRMWFZeGYzUTNUMXdoTW4rNWVlcUc3eVRWRjZuZi9yRUxDQUMiLCJtYWMiOiI2NDkzODZhYWIwMjdjYTEwYmRiZjM2YjkwNjE3NzM5ZmM2M2IzMDg1MTg5YmRlYmQ5ZTg4YmI5NWE3OTU0ZWU0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:33:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxRU2ZOWHN2Zk5iLzc1MWFLUzF5V0E9PSIsInZhbHVlIjoiOW5ENVVJK3VNN3M1SnNhYyttMmN1ZDAyQTFMSlhXWGRQRWZiVjl3UWdtZ3FYYlBkbzBtNHhHU1JXc3RLaFAzVXFnemkweCtIRG82d2xGLzVkQ2lnKzdlczlGR0dNR0xsZGNYS1dMMSt5ZFlVcDFHYUJiRE1uSDBiQ3JFMlBEcnNKSUtiVmNNYmhhQUpoWG5xWkpCM3NVQXBGR205QzVQTzNPMEpva1dhQng5Z3dhbVpkUXFIdUxtZlVUMFBZZ1phQmUyUEg3bytEeEFEY3ZaSWZ6cXZrRVlscVF0eERuRlgxQkoxcDhtZ3hVblhVQ0s5d2lrSEpQeHFkc1B5QzNZRHMwVVpkMHVVZXVaWHNLbVZSeEtQOVpVYmc2d0RuSVBtRDNWRXgzN2pwSU53Z0pxcFBFbjBnQjkybEJEckxPdDVwbzhPbzVBWDAzOHE5MEI3a25yZTNTOXZhZld0RFhoOHZ2R1VBTWdXSmxJVy95TUU2ZGI0ZHZGVTEyTGJQK1ZoSGMzV0RiTW92d0ljc1F6MnNBVnlGeU12ZU1LNldoU2lBSUZ3Y3FtVUhFY1NkeVlQRWlLajJUU2lkQk43ZzhoNkVaVFRNZXYwWmlIQWZwMFp4TGpvaC96WWpyYlNwWHlqdHJTSGw1QWl5Nk1ScFBJUmxRUmVNVFA2VWtJOHpaa2UiLCJtYWMiOiI3ODA5NmM1ZmQ1NWQ4ZWI5MzhjZmNmYTljYzU1OTU1MGZlYzFiYmM4MjJiZTM0YmY2MDBjN2VmOGVkNjJjNjYwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:33:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFxeWc0djg2enlYZUlmay8rMkt2QWc9PSIsInZhbHVlIjoiV2xOUmVHc2ozTzFmV0p1OWJtZ0ZibW0xRU9KRE9IcmFJdU9wb0tKdzVHYkdick02QXMvbVVlakhBVXUzbHBkMllFNFh5c1o1bGFibHRqOWk5UWl4QWgxalFPS0FMY0xlNDdsSVM2TGhJc2NDQ25ub2NOTnVSclkvVmM5MDVUS0duS0pvV0VUWnp2OHRUWit0NjQ0U0RSSTZBMGJ3MktWMkhyT3NSS2dRODQrTGNVZXI3eUU1dlUwM01wVExqUFA3THRsME82NzRWTDhqWEJYUjBVNUJaRDZTOEFLcG1Kb242NHJNZGlIL2EwNit2a3FsOU14QUxtL3NEZWk1YWhqNFN6bDVPOWk4NStBLzl3ajRyKzBUTzNoai9ac1h2VUVINjlYdk1PT3ZndC9HL3JJaExHM0VJNEN3T3B3RUVhckR3bWVNaG9ZNmRUQWhkVml6RXdvbmhRZ0ZXOWJpQ3R1RlVrZmhNRStiekxIbWtJYklHVzI5RHVTL080WXF5SnlvenJBNkk1c05QR05tT3JjdzRJdHR0aHVBZnlvd2ZtNVhzMmNyakxncFQ0YjRFVmRxTGRMTHlaZU5oMVJCNERuRjVnK25Gb3NLU3NKTnY4UGJSdHhGNjdPeWYzL0dRMWFZeGYzUTNUMXdoTW4rNWVlcUc3eVRWRjZuZi9yRUxDQUMiLCJtYWMiOiI2NDkzODZhYWIwMjdjYTEwYmRiZjM2YjkwNjE3NzM5ZmM2M2IzMDg1MTg5YmRlYmQ5ZTg4YmI5NWE3OTU0ZWU0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:33:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxRU2ZOWHN2Zk5iLzc1MWFLUzF5V0E9PSIsInZhbHVlIjoiOW5ENVVJK3VNN3M1SnNhYyttMmN1ZDAyQTFMSlhXWGRQRWZiVjl3UWdtZ3FYYlBkbzBtNHhHU1JXc3RLaFAzVXFnemkweCtIRG82d2xGLzVkQ2lnKzdlczlGR0dNR0xsZGNYS1dMMSt5ZFlVcDFHYUJiRE1uSDBiQ3JFMlBEcnNKSUtiVmNNYmhhQUpoWG5xWkpCM3NVQXBGR205QzVQTzNPMEpva1dhQng5Z3dhbVpkUXFIdUxtZlVUMFBZZ1phQmUyUEg3bytEeEFEY3ZaSWZ6cXZrRVlscVF0eERuRlgxQkoxcDhtZ3hVblhVQ0s5d2lrSEpQeHFkc1B5QzNZRHMwVVpkMHVVZXVaWHNLbVZSeEtQOVpVYmc2d0RuSVBtRDNWRXgzN2pwSU53Z0pxcFBFbjBnQjkybEJEckxPdDVwbzhPbzVBWDAzOHE5MEI3a25yZTNTOXZhZld0RFhoOHZ2R1VBTWdXSmxJVy95TUU2ZGI0ZHZGVTEyTGJQK1ZoSGMzV0RiTW92d0ljc1F6MnNBVnlGeU12ZU1LNldoU2lBSUZ3Y3FtVUhFY1NkeVlQRWlLajJUU2lkQk43ZzhoNkVaVFRNZXYwWmlIQWZwMFp4TGpvaC96WWpyYlNwWHlqdHJTSGw1QWl5Nk1ScFBJUmxRUmVNVFA2VWtJOHpaa2UiLCJtYWMiOiI3ODA5NmM1ZmQ1NWQ4ZWI5MzhjZmNmYTljYzU1OTU1MGZlYzFiYmM4MjJiZTM0YmY2MDBjN2VmOGVkNjJjNjYwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:33:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043166640\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1021194200 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021194200\", {\"maxDepth\":0})</script>\n"}}