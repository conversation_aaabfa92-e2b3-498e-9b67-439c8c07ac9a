{"__meta": {"id": "X64854a7372cbb4361a89143cc7b7e57b", "datetime": "2025-07-14 18:28:51", "utime": **********.918755, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.462232, "end": **********.918769, "duration": 0.45653676986694336, "duration_str": "457ms", "measures": [{"label": "Booting", "start": **********.462232, "relative_start": 0, "end": **********.866786, "relative_end": **********.866786, "duration": 0.4045538902282715, "duration_str": "405ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.866795, "relative_start": 0.40456295013427734, "end": **********.91877, "relative_end": 1.1920928955078125e-06, "duration": 0.05197501182556152, "duration_str": "51.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991104, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00276, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.894884, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.87}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.904497, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.87, "width_percent": 21.377}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.911459, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.246, "width_percent": 17.754}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1374804721 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1374804721\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1782682443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1782682443\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-139362355 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139362355\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-249858854 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517724713%7C26%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9jcTV4NUN3TFRVUVFvTDVoa2xLUUE9PSIsInZhbHVlIjoiM3EvWlF2bUYvWEIzeEQvM1BINkFkMkVqSzJBSkpNb0tTK3B0MWRYMkR6ZGJSRU5mYWFKVEZlMjdENyswQkZYTmFobXhMOWphSEV2RkRxUGlOVkFvVXVLUFNUVThDWGhodStPYkUxQm1DN0dKL2VoMnRYMTI0dUh4eHA0OHN1WFg5WUNTOTZiVXh1L3IvbkN6a0R2L0RQU3ZBVFlMMjJwOEtsVGZQUW1LNm9kcnBBWE53SFB4dGxvbnZOL0ZaT0tZTDMxRklvdFRSMEFlbEJacVVmSlJzRzhpbVpjVk1KQStzeTZDUS9zZG5FSTJmUDlhREhOclEvaDd3Q1lpakM4djREUnNQTWUxUG0rZXY3QVJtK3dSN3cxWlhaM3ZkSFRzWXEveTl5QjhoTUgrb3ovcWIwSVdxeXFNR0FuZ05CN3VpdWpJYStvRU5FRXp1MW1YQVFab0VhTy9obnNIUUJDeTUxTXBORFY5ZWEyMjZpMDlKUjlmTlVjVno2ekNSbklScklSSExZRjlaU1V0WFNaN0tsOW14c1luUk1MM0RKQVZEUDZkSWxxS0lTU3RjSVlCRHNkYW9YSGZwd1lkM2FTdlU0OGlFNytZVmw0MlVUeEFPZEh5TWR6RHhqN2oyREZiVFllL1Awd3d2aUlsTEFwVm9GMXovak9rY082Tm82U08iLCJtYWMiOiJiOWI3YjZiZDM3MDZkZmM1YmY5MmNjOTg2NjJmZDQ4MmQyZGM2MTE4MGUwMGFhNzRlMWY0MGE5Y2E2YTdjMWZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJSN0h4NmNjVGw0L0hzOS9ocEIyM1E9PSIsInZhbHVlIjoiWkFQY2dLb0p5RDFsOHhkR0xZQ2g4R08vckRrZ1NycEw4MXN4VFRZMDRGN0N4bHBESURnYjN3UnFCTFkxdDJHbFdrVEkwVmRLclhxM0UvL2V6Nks5VEc5Q3NwNG4yZnZWUTNJcWM5YkhRdk5DSWNVamRBOWNXK3RZQlJBZmhnNmJudVMrOVhIM3ZxUiszaGFwKzRuV3ZuSDZxOHBTYUFVSG5xaTcyY0Q3T05wWlRQWDlOZzNqKzd0U09IUWxubnNlb0ZONUVCWVFjS0J2cGlFeTNSUVoxT0ZmR1lTdVMrZGN4VjdqWURsNTlzMzZXVm5zZEt4TFdyQVlHUzlYdGdSNWtUbjFVUWR1alcwMWExQktsblhMUVFXTFhrcE9RQ2ZBMi9GOXMxaTRlaGJSTTJOc3JUQ2JxK294OWVqOXBTcnoyU0FBOWtpK3JpSzJYMGU5OUd1emhGNm5HdmgvZlducytUcDZReVVqOVZKbEdYS2djNndCUHBMS29pTmtHQWRSSldGV295WERVRzE4NlhDNlVCUzBQWVFPK1JjZVhYQVVtaGwxbi9qUDl6bE8yRE9KVktBTlpyNnhIQzNSYnhXZlprcER3VnRjZDR6dGRYelhpbnlhakVPb1Q4OW1HQWxlZDNBS1V3dFZqanVLZVFJVGxWSUYxTjVweGhMV3puZUwiLCJtYWMiOiIyMGIxY2UxMjM3NDU5ZjBjM2VlN2M2ZmUyYjZmODBjM2U4NGIyYzQ2ZDBlNWNhYWNiZmMyOTFjNDhmNzg4M2EwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249858854\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1395587310 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395587310\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1383012825 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:28:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhyTktkMkdIcGgzUUpPRmpmcmJIamc9PSIsInZhbHVlIjoiM0ZUU2YrUzlOeCt4cEhWcTM2YVdrYVpNTVVTOCsxd1EyeVM5amdjVkFSTEt6UmpsKzd6TlZQN0xJZ2REM3krMGQ1OEEyV01OTGhOZDl6ZXlRMTRNUG5jUklFS0VPa0VYV2h4Y0dpYUlNem02MnZiRTRmTXFLeG84TVN0bWNTUzlZNUlDRUFTRVFzVm9rVGdkKzhvSnFNdUpuSkVwMUF1dEdvaTBvZlVKN1dMamx3TUY5ZnpSWVFmOXIyUVBtNlVEWHVxZnhYSmxlNlJONWRtcVhNUnlSeU9BczZsaSthS2FnQXV6M2c4QUVCcVpRT0xyZlZQd3ZJOERQTllvZEg1MzJCakpJbkN0b1M5Q1FCMk5uTUdYWmVMMkFTSUdxSnhwVnFCaDNRcEl3VmJ2Y3E1R1pGbGVLS1NTSzRnR3k2NG9kdDJTRGxtU3k2NG9XNUpMSk16YUVDNU5zSXVxMEVkWE04VldCYmFlL01jN0x6b1I4VSs1cEhsN0llOUtoemwwZDlBZmVRQ3VycjdvelU5NldzL2t4WUp4eHgxVlNpc21wdENBbjFQQW5QOVY0OG52MmY3ZHJoY0t0N2NCRitHV2ZWSjFYb1lndU50WHhXZ25LVEZjVFhPV0tqa2ZHMjNDVXNnTlVjQUdLakdoRTNocVkwOWx1SWNFUTNYWndoR00iLCJtYWMiOiIwN2UzNDk0MGNhOGUyZTQwMmQ4N2RkNjQ5ZDUwMjhlZmQyNjc3OGQzNTJmZWVhYWFjNWYyZGY0YTdlZjEwMWQxIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNzSWRFK0dFUUN2cUNvYldjTXdyYUE9PSIsInZhbHVlIjoiemh3bnBiTy9URGUwQ0Q5bGpheENlUEJpNmRXTWdhb202cEU5QndGT3V3bWxESS9qeGFrdzA2OEgvMzlEYlFSTlByNTdpT2RBU29LdlhBUElzMis5OVNNZU1MTTI2Rk9iV0NVTXFRcHJaSU9FUGk2MmEybGhaQnJFU1NFbzdwWkhtcmRncFZ3M04zcGhJS3U1T2VRRE9oUkhTWEVrcmQ0Y0FoYStrejBFQTZiSjJvelhEcWV6cit0SVQwWk4rSWR0bnFKM3VHd2RnUWxsZFFRcUxwMU9xODRMclh5SUYxZEt5Qkc1S2dia1Z4cEpmbkpPdmFjSHBqOEc1RHZCaCtPb09pTVczWnJ0ci9SZEU3ZVZMY1RXWjZpUHpVcFBiYllkeSt5U2UxT3hhZktVWmE4QnNxYVFPNncyK2p1YUFmQ0t5aDhHa2J2WE5TSnVqcVNtSW9Md0ptanNjczNyOU9iUmFTSmRQZDNScmlKVzJOWDM5MUFjUzMrVXlJTkRETCtZVmVSdlJxemppdHd2eEpYYmtkTFoyREZhcmNXU0YvODByN1lXYTk3cnN5RUFxMnRvekhWb1RkRDA5STM5REx3eHFaNG5BZFB4VldscmxZTUtSTTZ1dEpOWkd4YTJ2U1BXWWE1cHoyTXBGM21HY2swclZMZnltS3ZkTjNQUHZQN2ciLCJtYWMiOiI3NGU1YTA1MDhjN2U4ZmRkM2RlOTM0MjU5NDRmOTNmZDQ4NzU1MjRhZGQzNjE2YjNiMWQxMWM5YzRkNGI4ZTMxIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhyTktkMkdIcGgzUUpPRmpmcmJIamc9PSIsInZhbHVlIjoiM0ZUU2YrUzlOeCt4cEhWcTM2YVdrYVpNTVVTOCsxd1EyeVM5amdjVkFSTEt6UmpsKzd6TlZQN0xJZ2REM3krMGQ1OEEyV01OTGhOZDl6ZXlRMTRNUG5jUklFS0VPa0VYV2h4Y0dpYUlNem02MnZiRTRmTXFLeG84TVN0bWNTUzlZNUlDRUFTRVFzVm9rVGdkKzhvSnFNdUpuSkVwMUF1dEdvaTBvZlVKN1dMamx3TUY5ZnpSWVFmOXIyUVBtNlVEWHVxZnhYSmxlNlJONWRtcVhNUnlSeU9BczZsaSthS2FnQXV6M2c4QUVCcVpRT0xyZlZQd3ZJOERQTllvZEg1MzJCakpJbkN0b1M5Q1FCMk5uTUdYWmVMMkFTSUdxSnhwVnFCaDNRcEl3VmJ2Y3E1R1pGbGVLS1NTSzRnR3k2NG9kdDJTRGxtU3k2NG9XNUpMSk16YUVDNU5zSXVxMEVkWE04VldCYmFlL01jN0x6b1I4VSs1cEhsN0llOUtoemwwZDlBZmVRQ3VycjdvelU5NldzL2t4WUp4eHgxVlNpc21wdENBbjFQQW5QOVY0OG52MmY3ZHJoY0t0N2NCRitHV2ZWSjFYb1lndU50WHhXZ25LVEZjVFhPV0tqa2ZHMjNDVXNnTlVjQUdLakdoRTNocVkwOWx1SWNFUTNYWndoR00iLCJtYWMiOiIwN2UzNDk0MGNhOGUyZTQwMmQ4N2RkNjQ5ZDUwMjhlZmQyNjc3OGQzNTJmZWVhYWFjNWYyZGY0YTdlZjEwMWQxIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNzSWRFK0dFUUN2cUNvYldjTXdyYUE9PSIsInZhbHVlIjoiemh3bnBiTy9URGUwQ0Q5bGpheENlUEJpNmRXTWdhb202cEU5QndGT3V3bWxESS9qeGFrdzA2OEgvMzlEYlFSTlByNTdpT2RBU29LdlhBUElzMis5OVNNZU1MTTI2Rk9iV0NVTXFRcHJaSU9FUGk2MmEybGhaQnJFU1NFbzdwWkhtcmRncFZ3M04zcGhJS3U1T2VRRE9oUkhTWEVrcmQ0Y0FoYStrejBFQTZiSjJvelhEcWV6cit0SVQwWk4rSWR0bnFKM3VHd2RnUWxsZFFRcUxwMU9xODRMclh5SUYxZEt5Qkc1S2dia1Z4cEpmbkpPdmFjSHBqOEc1RHZCaCtPb09pTVczWnJ0ci9SZEU3ZVZMY1RXWjZpUHpVcFBiYllkeSt5U2UxT3hhZktVWmE4QnNxYVFPNncyK2p1YUFmQ0t5aDhHa2J2WE5TSnVqcVNtSW9Md0ptanNjczNyOU9iUmFTSmRQZDNScmlKVzJOWDM5MUFjUzMrVXlJTkRETCtZVmVSdlJxemppdHd2eEpYYmtkTFoyREZhcmNXU0YvODByN1lXYTk3cnN5RUFxMnRvekhWb1RkRDA5STM5REx3eHFaNG5BZFB4VldscmxZTUtSTTZ1dEpOWkd4YTJ2U1BXWWE1cHoyTXBGM21HY2swclZMZnltS3ZkTjNQUHZQN2ciLCJtYWMiOiI3NGU1YTA1MDhjN2U4ZmRkM2RlOTM0MjU5NDRmOTNmZDQ4NzU1MjRhZGQzNjE2YjNiMWQxMWM5YzRkNGI4ZTMxIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383012825\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-700839868 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700839868\", {\"maxDepth\":0})</script>\n"}}