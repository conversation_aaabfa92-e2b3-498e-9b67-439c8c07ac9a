{"__meta": {"id": "X0f53b122bd66825924258b978be98d52", "datetime": "2025-07-21 01:17:57", "utime": **********.672399, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[01:17:57] LOG.info: POS Search Request {\n    \"search\": null,\n    \"type\": \"sku\",\n    \"cat_id\": \"0\",\n    \"warehouse_id\": \"8\",\n    \"session_key\": \"pos\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.666796, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.249764, "end": **********.672415, "duration": 0.4226510524749756, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.249764, "relative_start": 0, "end": **********.60002, "relative_end": **********.60002, "duration": 0.35025596618652344, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.600029, "relative_start": 0.3502650260925293, "end": **********.672416, "relative_end": 9.5367431640625e-07, "duration": 0.0723869800567627, "duration_str": "72.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49168072, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1271</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0032300000000000002, "accumulated_duration_str": "3.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.63465, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.56}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.645055, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.56, "width_percent": 14.551}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.660443, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 68.111, "width_percent": 17.028}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.662506, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.139, "width_percent": 14.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-726383608 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726383608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.666153, "xdebug_link": null}]}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-695893343 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-695893343\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-976061718 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976061718\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-837597158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-837597158\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-75868229 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060674456%7C6%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkgrUE5HQmJudWVmOS9zbHNOdDhCZXc9PSIsInZhbHVlIjoiRm1hNWpCcE5rSkpBbWxndm9QamdUVFdFYzdZOC9MKzJUZUJXcVFJSmFMbHRybXUyTEJDT2JnR3hLak5HbnA5ZTg0QzM1RkJTdGhUUGFhZTBucGNtcTlpNWVuRVpiRXJNa0tIL05iaWxHNUFjS2dtVkVmU1VWbEluTTVKNFpEaHh1Z0hrYmUwSjJubSsvQmo4dXQzMHVRNDVDZmF5R2UzUEROV3FrMXN2K2RsWWVQTXB0NUFIMSt0dkkwZkl5ZU9DQ2hpc0Q3NVU1L1VGYmtlczZaR1ppV3RNUkZZUzNLZnp0VEhPK2NScWM3aXNlM0M4Vlo3T3FvOUtlR3JyY0IxL3QxeExNdkJJb1lWanE5bW5WOEpHTkNoYytmS2hnSWhaUUJRSlRiUFVjbGNJeGt5NjVuU2syOFIvbmlubnl1bmVSY0xodFVjVDFEY2pUUytBdlQ0MUt4NXZSemxZUjZHSXJ4cFByeFNjOTR5QnJCa2JoYkRNdEdFTjhKUUhDZDdvd1daWXJ3dTdqanZzZFh3RHdpeCtwbE5DNm05dXBXTy9QdmJxbDdkOTF5MFBwM1ZTMVF6NmxXUkI4cURVWi9aeUFESjRTV01kMkxkd3IvRW5hNFo2U3lLd2xVVldhbG52VXFBMlZMNXFVMHFOZW03QStWWklSTEtHU2g3SWczY20iLCJtYWMiOiJkNDI2NmRjNjE3M2RiZDI2Nzg5NDJhYTE3NGZkZWMyZTliNDE4ODg1MGRhMTk4NjM3MzE1ZWQzZDIwYTU3MTQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRYdmYwZFpKUXM4RlBiYlNvQkM0eEE9PSIsInZhbHVlIjoialprRHp6VlZXRDluM3dobXNJOFlJbDJhbm5xajR6UWs2d2FwdWtOcVdLS2MrV1llTVZ3bWhMbzY4aFZOWU5SVWVQSnNZb1phQXlYMU9zcUxQQmc3Ynk2QXpMNVY4T3hPWjhuc3RlN0hrdldoYWVHYysxTzI1T284bW5xTlNpMWM1eTR2WGloc29JbEZ5RG9pNUdSN3R4TXlOL1h1MnZoUFNTd3B4UEtWcVRLM3Y5dlRnVG5Ybk5XTlpZTUhvK2gyb25aZnFsMVM1Y1JPdkN3cHpmSy9IbjEvUUNka05CTUl1ZkVOaHkrS09OcnFHaFhiempNcmpTa05pUlpSYzNoQUpaemtFL1cxQ1d0YzdRSkxsMkZVeHRsZkVTYTF4WXd0bXN4ZzI5KytZU0QxWHhDV1AzeEJhdzhrU28reUE1bGJMbXZubldCOVAweENoSHE1RXIyc3czRUUvclRWbmdzUXc4cWtiWTF4Z0dHS0NvczN1azNUbGVxbzMzS1pRYys5Q0dOdzZNWlp5d2xNeG0reWI4a2I0M2p3QzRsMHRPOEM0ZFhldk5QRUp6c2dmS0M5N0puL1g5K2M0T2lhbDdpWm52ZnNVZlRZcVdWRDEzYTY0VkVlcGUvWEZSSWJaWVVrT2ZCWFBuNlA4MVdVVkJhK0s3bjlGTlpBUVlBbUpLcFIiLCJtYWMiOiI4N2Q2ZDM5MGM5MmRmNzI0MjZjMWFiYmVkN2FiZTM5OGNmNjA3NTkwMDFlOWEzYjQ0MDg5NTc5MTgyYmEwMDljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75868229\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-72976771 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72976771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1997819566 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1NT0RscmhvWVA5b0JteDZZSDJMRlE9PSIsInZhbHVlIjoiSW9Ub0FTbDRxbXFMdnk1KzlSdXFpbWtZOWpxSUNyZjJkVzVKWFN2c0NSc2paaWxmZW9IZUpZSUlrREFEbG13Z1pkL0hSdHVieGZiSW5PTFhuWXVFZCtXTHc2TXB2UE1neXBnNE1nUVFuRjB6ckF5UVdmK0lIY01qanZMdVhIUmFwdHJNTS9DSlo1Zlh3a0ZVTDFjREpvNTZRT2cxbnFqQWJzSE1kV0hLZ3lmdjM5by8vZENiU2VRb0RDd3pnQWdBNlZISDBna3JjWkxnMTEyNUl2aVpRZW5Vc3FhZ2k1WnRVa3hTdnB2L1Fxb3JXT1RMdk55SVh0M1VWWXg4bExtbmJPZHQyUEQ5VUN1RHFRb0oyUUFHNW5YeG5SK0VHcVljb2JjRXl3dmhwUmxYQXJCQ3Q2UktUSmtjdHBZTzBoWWFXKy9KVEZ0RHh0cHNjc0RkcVpieVhTOE52MUxBVHIxaVZtYXNwZGtScVN1eDEvT2MyVmFqMnN6bGc2WlRDOC9kQUxBTkQ1eFZDT01GSnJPZmZXNzFKMG8xcjRIbkNpTVc3aHQ0NHpBMU0xYzlnRW03TTl2c2h3b2FrekpMdk8vSXM4OFN6eTY0SVhTRVdBTDh5YTd3REJPemhoaUx1WFIwUENpNVcwaGc2SS90a0JpdE1vYVRBaWNJRVRaOUtacEwiLCJtYWMiOiJmYThjZWZkNzczOGViMWUyYzgyMTlhOWZhMTYyZDVhODA4ZjhmZTliMjFjY2ExMTVhZTM4MzM2YmEyZmZkOGYwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhoVEk0VHZteGptNnVJazFDaUhkSGc9PSIsInZhbHVlIjoiVmdNUnpXelRPeDVQSTk4dTdWM1l5cG1zVkc1Wkk0SC8wQ1RrRXlPcVhYVlVvalpsOUZvU3g2VzltRCtLZnA0empZZXBMUzE5QXNiajNvaHN1M056SDdHcTVDTjQ3WGpYeXExSlp4RVZSNS9xOW1QT1hqR3dLK3VMS0dXcmtqNVljY0Y0VDhoNXhkZ09zS0oxOWl1YVBqa2t0VE5WaE0vT29qVkF6dTJSeHhSNkhIendjaUp2M2RGZ3IraWFpK3FIZ1V1T2lpQ0dWam0zdFppRlU4STRPb3JYb2wvNlM1QjQwVVE5UEh1N0dRMmtVNWhSMmVvSXZBN1RTUkZlcVo2L0RzODBpLzd6REhqZ3Q2RUtkOFpEZjhjTndVSC81R3l2bTlCbzJMb2grOVFudzRHVUM5RUNzQVVzMmpXNnY0Rzd4YXlCUFVYZW5PSWg3dE8xRU5DRnUyN3FmY2JqYmd3TEdoSGVhWHJHdWRkMXMwVWRzQVZvWHZmY0tDWlNXWXIwN0ZsV3lTYkpZSVVuTzJMYWc1UWFHWWdXU3JMcnFVVFRpdVZBemRiaXdlajROcWt3TnJVRU5vblBPZ1BnWXZONjVGRURmMFF4b0VsWW9TeXFvRWJhb1d2ZXhiODZvd0pqS1JpeVZKWWp3dEl1ZHBuaHdnTTZReUJvNWxpMStYU2wiLCJtYWMiOiJiNmZhMzViMjYyNDdhOWY3YzRjZDdkMDg1ZWVmM2JkNDhhYTE1NGM4NDFlZTEwNmVmNzAzYjFmYTdlMTEyNWQzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1NT0RscmhvWVA5b0JteDZZSDJMRlE9PSIsInZhbHVlIjoiSW9Ub0FTbDRxbXFMdnk1KzlSdXFpbWtZOWpxSUNyZjJkVzVKWFN2c0NSc2paaWxmZW9IZUpZSUlrREFEbG13Z1pkL0hSdHVieGZiSW5PTFhuWXVFZCtXTHc2TXB2UE1neXBnNE1nUVFuRjB6ckF5UVdmK0lIY01qanZMdVhIUmFwdHJNTS9DSlo1Zlh3a0ZVTDFjREpvNTZRT2cxbnFqQWJzSE1kV0hLZ3lmdjM5by8vZENiU2VRb0RDd3pnQWdBNlZISDBna3JjWkxnMTEyNUl2aVpRZW5Vc3FhZ2k1WnRVa3hTdnB2L1Fxb3JXT1RMdk55SVh0M1VWWXg4bExtbmJPZHQyUEQ5VUN1RHFRb0oyUUFHNW5YeG5SK0VHcVljb2JjRXl3dmhwUmxYQXJCQ3Q2UktUSmtjdHBZTzBoWWFXKy9KVEZ0RHh0cHNjc0RkcVpieVhTOE52MUxBVHIxaVZtYXNwZGtScVN1eDEvT2MyVmFqMnN6bGc2WlRDOC9kQUxBTkQ1eFZDT01GSnJPZmZXNzFKMG8xcjRIbkNpTVc3aHQ0NHpBMU0xYzlnRW03TTl2c2h3b2FrekpMdk8vSXM4OFN6eTY0SVhTRVdBTDh5YTd3REJPemhoaUx1WFIwUENpNVcwaGc2SS90a0JpdE1vYVRBaWNJRVRaOUtacEwiLCJtYWMiOiJmYThjZWZkNzczOGViMWUyYzgyMTlhOWZhMTYyZDVhODA4ZjhmZTliMjFjY2ExMTVhZTM4MzM2YmEyZmZkOGYwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhoVEk0VHZteGptNnVJazFDaUhkSGc9PSIsInZhbHVlIjoiVmdNUnpXelRPeDVQSTk4dTdWM1l5cG1zVkc1Wkk0SC8wQ1RrRXlPcVhYVlVvalpsOUZvU3g2VzltRCtLZnA0empZZXBMUzE5QXNiajNvaHN1M056SDdHcTVDTjQ3WGpYeXExSlp4RVZSNS9xOW1QT1hqR3dLK3VMS0dXcmtqNVljY0Y0VDhoNXhkZ09zS0oxOWl1YVBqa2t0VE5WaE0vT29qVkF6dTJSeHhSNkhIendjaUp2M2RGZ3IraWFpK3FIZ1V1T2lpQ0dWam0zdFppRlU4STRPb3JYb2wvNlM1QjQwVVE5UEh1N0dRMmtVNWhSMmVvSXZBN1RTUkZlcVo2L0RzODBpLzd6REhqZ3Q2RUtkOFpEZjhjTndVSC81R3l2bTlCbzJMb2grOVFudzRHVUM5RUNzQVVzMmpXNnY0Rzd4YXlCUFVYZW5PSWg3dE8xRU5DRnUyN3FmY2JqYmd3TEdoSGVhWHJHdWRkMXMwVWRzQVZvWHZmY0tDWlNXWXIwN0ZsV3lTYkpZSVVuTzJMYWc1UWFHWWdXU3JMcnFVVFRpdVZBemRiaXdlajROcWt3TnJVRU5vblBPZ1BnWXZONjVGRURmMFF4b0VsWW9TeXFvRWJhb1d2ZXhiODZvd0pqS1JpeVZKWWp3dEl1ZHBuaHdnTTZReUJvNWxpMStYU2wiLCJtYWMiOiJiNmZhMzViMjYyNDdhOWY3YzRjZDdkMDg1ZWVmM2JkNDhhYTE1NGM4NDFlZTEwNmVmNzAzYjFmYTdlMTEyNWQzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997819566\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-415618883 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415618883\", {\"maxDepth\":0})</script>\n"}}