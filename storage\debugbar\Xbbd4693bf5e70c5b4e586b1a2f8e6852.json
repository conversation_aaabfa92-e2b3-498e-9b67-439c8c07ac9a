{"__meta": {"id": "Xbbd4693bf5e70c5b4e586b1a2f8e6852", "datetime": "2025-07-14 14:39:57", "utime": **********.467714, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752503996.988667, "end": **********.467728, "duration": 0.4790608882904053, "duration_str": "479ms", "measures": [{"label": "Booting", "start": 1752503996.988667, "relative_start": 0, "end": **********.409699, "relative_end": **********.409699, "duration": 0.4210319519042969, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409707, "relative_start": 0.42104005813598633, "end": **********.467729, "relative_end": 1.1920928955078125e-06, "duration": 0.05802202224731445, "duration_str": "58.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991504, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.003, "accumulated_duration_str": "3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.441797, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.667}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.453741, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.667, "width_percent": 16.333}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4603798, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80, "width_percent": 20}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer/eyJpdiI6Ik5RSndNZWIwZm1YVHN3UlpLcWZtY1E9PSIsInZhbHVlIjoieG9wQnQ0N01rbU0wUWhRREFEK2NrUT09IiwibWFjIjoiZjQxZDQ5NjhiZTAzZmVlYTFmNWM4Zjc0Y2Q5YTY3ZDMwOTAyNmNjNzQ4MGU0YTQzZTY3YTdlNWViMDY0NmRjMiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-360786590 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-360786590\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1932099349 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1932099349\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1273649683 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273649683\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-32127551 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6Ik5RSndNZWIwZm1YVHN3UlpLcWZtY1E9PSIsInZhbHVlIjoieG9wQnQ0N01rbU0wUWhRREFEK2NrUT09IiwibWFjIjoiZjQxZDQ5NjhiZTAzZmVlYTFmNWM4Zjc0Y2Q5YTY3ZDMwOTAyNmNjNzQ4MGU0YTQzZTY3YTdlNWViMDY0NmRjMiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503992835%7C19%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhTcWZKTktPZ3JQbUJVaHlzSDV3ZkE9PSIsInZhbHVlIjoiODR4WllNa3ZsYmIzSm1FSEZJTERzQkd4ZUlVb0N3Z2hOaURhSlpGTE1BZGpuMmNsM1BNVWNEcWJnZDh4Y0l6NEVZSnhRLzBwVk5SbG9pdTh0OGFpb290ZGcwZXdOclhaYWx1SFdNUVpzNENnbk04N1hwckM2dGtYdmJTNW5pTnBaR01meWErbkRYQjVlWEtvMzhJRWliRmRSWWZoY2pKNFNZdk9IRU5nNHY2MkJZR3BpN3VhVG80Ny85RDdOSWZHWmRGcm1sM2pIaWNVRTdTeldvKzBiSTRpcGtPdVNDWXVJaXVkMzM3OFJxaXBveVJJcjVLdlNFQTFkTlhqSUxDTzRiMGw5RnNseWx2NWsxMjRCOHMyQ3VCWWYxUHZCSVo4L3VZR0JqSzhHNTBzakNMWUF5TlVsYUNGMm9YQTg2WlREUjRUZjF1S3hNc2Z3dENrK2tTNXhaMVFKTi9rKzVvbHBBaktvWjVBOTVTTVFSZEN3NHlUVXUzbGNBMWZzakV5NHJDOEFIdGowY3RLL0ZRRXBvVWZhY09wb2k5VVV2WHZPT1lFVmh0LzhQc3dQbEw3Znl0NkdVSWJqOFhmOVdKcVpkOThCMUhmYzlUVHBqUENPZGdIZUxLaDZYRTFwVStKZFI0bE1FandmZC8vYkR4R2pwaTQ5S3NKQ3E3Znd1NnMiLCJtYWMiOiIxOWZkNTE5OTExNGM1MGIxZmQ4NDFiNGI0NmQwYTgzOThkZjNlMDY1NDZlNjQ2ODNmZTcwNGQyYzgzOGRlZWU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlovRFU5bThTazUvalU2YzN0NjBENEE9PSIsInZhbHVlIjoic1psUHgxOFRvaElaNUlOQkRaK0d1UmZTbmhNYmlqWjJZUUxZaTF6NExqNWlENmNhaHU2aWc0YTgrNWVYdEVpQXlZaGJ2Tjh1TjQ5Y3QrbXFYSFdhUk1lNkcxQ1U2N1RvL2o5dXVqaWhsMVp0SHVpVEZrbUs4dCt2Y3V2RXVxVEV6eHFtZ29mR2VVaStINHBkZVpqdk9KMEdPZ1dpZ0dHVWtlUW9aUkpWbFJHUW9HanJLSlJBbnZsY1BmNnRxQUVmMzdTQTJ1dm9McFNiVVZmTlNEQjRDUXJ0dFpNdVFxZ2VQdlh5aXh5d0RiU25SdGg5dENOZDBFRHZSRXBaYzN3Tm9PMS9BS1ZuamdoNVEyRjVKa2tBZ1VzUGRsZHdOSm5Kb3VlNGUwRi9uODZpRTNKaGRoMHZFRSt5V1dqWTJQWFM0bUh6TnViZWMrOUxPdXBXcGtXQlF5dUlHSzZkc3RtOEw5RXFBa2Y4aDRhQ3pPT1lMN3dVUVc0VW5CaFBQTGF1T3p4U2NXVENLQzdoU01ieEVDV3FaVExxZnZvamczTFRaR211M3pQaVFZUitBc0lEeHdIb3dyUmdoWVBBa0xkazZSVzRXc3ZOQkI5QmNIdERwWjN4RmtFdmdTVUU1b1NkbVRCQ1Q5c0s1UW5ZeFBWUTk5dnErcUNUbFNMSStJSjgiLCJtYWMiOiIyZDliMjY5NDllNWIyMjczM2MxMmQxOWMyZDk2OWYxYTJkM2I4NmI5M2VjYWFhMjFkODQ5OGFlMzEzNjk2ZTNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32127551\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1863415385 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863415385\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2049769205 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:39:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5LaG5zazduVUt3SGdzVUlIczZMdUE9PSIsInZhbHVlIjoiUFNxc1B5Wmg3QmNjT0Y1a2dNOG1ZQys2VW00RGgrVjJGY1p5ZEtaOUNPYmg0Y2VGckRYWEM4WFJocFRTNVRaUDlDa3BXWUlJaXBWT0E1ODFvNnNnT3FNdUhmdU5VVi8zdDFaU0dxdDg4Yy9GbzM5UWV0K2tKY3QvZHNiY2IzbjlMNVd4VzlBZGROU29jSWlwNWUwVjVtQkUxYlRkQnM2Rk1RVnh4QzZ4aEdHUzZmQWNpZWVKKzFNV3VtazZ5Sm1QZFVnTk4rekFSMVhnQ09wSk90Y0l2SC96VnhoVjVlay9WM05kWGpzWFkyZHcwQ1h5UU9ra3ZFQ2RpeFVFd2F5ODkvUGN5U0JZcEFEOHhaWHY1dFhTS0ppbURZbnd5T0hFdnY2dHRjRkdTMjRjdFNRbFQ2YjR3amlKdGtBV1ZzNUpTY2NyTFc3K0dCYnhoZUdIWEllTmxoZUdGUU9YQzhzZXVPRTZ4NUdpVVU1cURTRCtDUW13SVlQVzd1d2JOTnJyRFY5TG96STBaUlM3K1FURmNTQXhaZmN6bHFYMUdYK1FjNFpacE5TS2piVEFIMzNBVkNGYUdZQ2dJQmxJRm9BVFoyME9Qeks0aVEyWTE5L1lyMmZIMWd5WUIyNlluWFhnOTBVdDlPbEdoV0FUSHl1SFZkbnpDZ3RTbDJmTlRiaHciLCJtYWMiOiJjZTEyZDVmNTg3OTlhNmY0ZTQxZmY2ZDRlZGY0NDE5OWY4ZDRiM2QxMDZiNTE0N2M1NTcwOWQ0MDZlNzA4Yzg3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpDK3NMaHkzelJyYnFXTHA2M1p0UFE9PSIsInZhbHVlIjoiOStVSitCVjFRcHgwN2ZWRGwrYk5uWTVleCtrSHNyeGx5bVJxa0NXS21QcHE0SVAwaGF4TVo2aWI4REhibEViWUk2UHRKNU5SRXNhaS9tekZRYzY0aVBGbTVQR3JsV0hyMzN6N2VlTDA3STdrYTVISXhXM0VDTWYwRUxJYlRPM2NGaS92NDkxMEVKUlM3NlNKcDVUNkdHVXRXMzhtdlJlV3NPN2ZBQVdRM3U2dXRUSnFVSW5TMlpiUmdJNmovK3Rja0h6TDJDYVM4V1RPc01iNGNETnJVNGxYZXY0N3N6ODFBRU8rVDNUei82d1BKd3hSS1hhMzlLMXB3MHQreWdqQ0svMDlkTDZlQW1DVmhVNmtmeXN3T0dPVDEyL25pbWxFWFQvUGozNkY5WmJDeTJCSTdJYmw4c2JJUWxQL3U5dCtxd3BRK1VVTHZEWHY1UGVKUmdJK3dheU16QTYvVlVkMHZ0YnBzSTZQd2ZVcEo2UzNvNmdnTU81RmYySVl1R1M5OGNlTi91YkxERVNDd3FtSXRtd213WGdpVHJiWkc4MlBZa3hZejRYNVFJbWVLQ05tM29BK25KT3hTRkNIZU5zbXJBNG4ybXVCZ3AvM0VKaitFSzhCQ0E4TWtqQ01HVnJ0ZDZ0Z2RJV0JhTlNTR3VhZkpobjhsa3hIVG5yZVJZUVciLCJtYWMiOiI2MjgzOTAwZmQ3M2MyNGYzZDRiNjVkZTQ1MWNhZjJhMzQzMDU5M2YzNzM0ZWFkMzUzNDMwYjEzNjc4NWQ5YzJkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5LaG5zazduVUt3SGdzVUlIczZMdUE9PSIsInZhbHVlIjoiUFNxc1B5Wmg3QmNjT0Y1a2dNOG1ZQys2VW00RGgrVjJGY1p5ZEtaOUNPYmg0Y2VGckRYWEM4WFJocFRTNVRaUDlDa3BXWUlJaXBWT0E1ODFvNnNnT3FNdUhmdU5VVi8zdDFaU0dxdDg4Yy9GbzM5UWV0K2tKY3QvZHNiY2IzbjlMNVd4VzlBZGROU29jSWlwNWUwVjVtQkUxYlRkQnM2Rk1RVnh4QzZ4aEdHUzZmQWNpZWVKKzFNV3VtazZ5Sm1QZFVnTk4rekFSMVhnQ09wSk90Y0l2SC96VnhoVjVlay9WM05kWGpzWFkyZHcwQ1h5UU9ra3ZFQ2RpeFVFd2F5ODkvUGN5U0JZcEFEOHhaWHY1dFhTS0ppbURZbnd5T0hFdnY2dHRjRkdTMjRjdFNRbFQ2YjR3amlKdGtBV1ZzNUpTY2NyTFc3K0dCYnhoZUdIWEllTmxoZUdGUU9YQzhzZXVPRTZ4NUdpVVU1cURTRCtDUW13SVlQVzd1d2JOTnJyRFY5TG96STBaUlM3K1FURmNTQXhaZmN6bHFYMUdYK1FjNFpacE5TS2piVEFIMzNBVkNGYUdZQ2dJQmxJRm9BVFoyME9Qeks0aVEyWTE5L1lyMmZIMWd5WUIyNlluWFhnOTBVdDlPbEdoV0FUSHl1SFZkbnpDZ3RTbDJmTlRiaHciLCJtYWMiOiJjZTEyZDVmNTg3OTlhNmY0ZTQxZmY2ZDRlZGY0NDE5OWY4ZDRiM2QxMDZiNTE0N2M1NTcwOWQ0MDZlNzA4Yzg3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpDK3NMaHkzelJyYnFXTHA2M1p0UFE9PSIsInZhbHVlIjoiOStVSitCVjFRcHgwN2ZWRGwrYk5uWTVleCtrSHNyeGx5bVJxa0NXS21QcHE0SVAwaGF4TVo2aWI4REhibEViWUk2UHRKNU5SRXNhaS9tekZRYzY0aVBGbTVQR3JsV0hyMzN6N2VlTDA3STdrYTVISXhXM0VDTWYwRUxJYlRPM2NGaS92NDkxMEVKUlM3NlNKcDVUNkdHVXRXMzhtdlJlV3NPN2ZBQVdRM3U2dXRUSnFVSW5TMlpiUmdJNmovK3Rja0h6TDJDYVM4V1RPc01iNGNETnJVNGxYZXY0N3N6ODFBRU8rVDNUei82d1BKd3hSS1hhMzlLMXB3MHQreWdqQ0svMDlkTDZlQW1DVmhVNmtmeXN3T0dPVDEyL25pbWxFWFQvUGozNkY5WmJDeTJCSTdJYmw4c2JJUWxQL3U5dCtxd3BRK1VVTHZEWHY1UGVKUmdJK3dheU16QTYvVlVkMHZ0YnBzSTZQd2ZVcEo2UzNvNmdnTU81RmYySVl1R1M5OGNlTi91YkxERVNDd3FtSXRtd213WGdpVHJiWkc4MlBZa3hZejRYNVFJbWVLQ05tM29BK25KT3hTRkNIZU5zbXJBNG4ybXVCZ3AvM0VKaitFSzhCQ0E4TWtqQ01HVnJ0ZDZ0Z2RJV0JhTlNTR3VhZkpobjhsa3hIVG5yZVJZUVciLCJtYWMiOiI2MjgzOTAwZmQ3M2MyNGYzZDRiNjVkZTQ1MWNhZjJhMzQzMDU5M2YzNzM0ZWFkMzUzNDMwYjEzNjc4NWQ5YzJkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049769205\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-986594869 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6Ik5RSndNZWIwZm1YVHN3UlpLcWZtY1E9PSIsInZhbHVlIjoieG9wQnQ0N01rbU0wUWhRREFEK2NrUT09IiwibWFjIjoiZjQxZDQ5NjhiZTAzZmVlYTFmNWM4Zjc0Y2Q5YTY3ZDMwOTAyNmNjNzQ4MGU0YTQzZTY3YTdlNWViMDY0NmRjMiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986594869\", {\"maxDepth\":0})</script>\n"}}