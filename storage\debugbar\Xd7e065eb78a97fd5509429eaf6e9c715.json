{"__meta": {"id": "Xd7e065eb78a97fd5509429eaf6e9c715", "datetime": "2025-07-21 01:57:25", "utime": **********.813406, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.363507, "end": **********.813421, "duration": 0.44991397857666016, "duration_str": "450ms", "measures": [{"label": "Booting", "start": **********.363507, "relative_start": 0, "end": **********.757086, "relative_end": **********.757086, "duration": 0.39357900619506836, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.757094, "relative_start": 0.3935868740081787, "end": **********.813424, "relative_end": 3.0994415283203125e-06, "duration": 0.056330204010009766, "duration_str": "56.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45996032, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027500000000000003, "accumulated_duration_str": "2.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.788656, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.273}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.799009, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.273, "width_percent": 16.364}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8054368, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.636, "width_percent": 20.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_search=Afrooz%20Ala&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1443363486 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1443363486\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-163103002 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-163103002\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2040087740 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040087740\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1708645441 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"163 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_search=Afrooz+Ala</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753063042128%7C27%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRGalpkamZTSzFFWU5ONmlIVEdXTUE9PSIsInZhbHVlIjoiQ2FHbjg1MWV0U2VRb1pwY0QrMW5tWjdZRHB2WlNNbkE3ZExhaGdGN29PcVlNeXlKaXEyckRMYkN4YTdrNlZ5dWpNY3JlOXVwVkxiQWx2UmtkSFNJKzRBMEg1MExRSnRzV2R3aTdjcDZPaEJvZE1sdlB3MHIwYmZEb0NrZkRXcXhaN1FadmhRN3NPdkNHcEVLMlB0S2F3Y2hFWHdVRGpndmUwRDlYOXFHeVJKenNYUjZYMGJ6VzRZdDdiUlRtUjNmUi9UWC9CMGRPVkN3U2NCMjVPN1AwNklMRy94eWZQTWk4VTB1dktiV25LSFBNMjhONGlpT1J4dEZlVDIvRGdqSERaSC95Y1NnS011RjBRNG5kL3U2Qy9TRHRkOGxJSy9xK2o1NDFsd21TUlVwSjNBZmtyUmV4Ykc1clk0QXRPYmRyK251V0dBNGZPUEN1aEk3RUhTKzN5SkszRHN4UmRFeEpMOFIxY05LVDRFOVFIN0QyTnhvWHRKSlhLYlozaHlkVm5NdWowWEZFYkJ2czFBajZJUXRQa2RYK1M3bUx0RW02WjhvNFdTZHdJMzBvUzk3UWZ4M3IyZ0NLc29wN2tpR2pqVWxtRjkrTFVFTzVVYjhsSzJ6dWZxNUF0eExIaDVyMmU0SFk5Q2tLR0o5WjFkMU1QNG5yR2xrYmpvaS9zMG0iLCJtYWMiOiI3ZTkxNGJiZGVmYjAzNDMyMWNhMzdiMWQxYmJjZjI5NmQwMjNlNTY3Njc0OWE4NWUwZDBjZjg0N2U3ZTFmY2MxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImIxckZ5Y0JWMGJwWFhqajlEcm85M0E9PSIsInZhbHVlIjoiUGZCbTUzY0F0d3NlVU1lQ2Z2dTBlSmJoRXVpWWhFcHRrOCtzN2d3ZWxJNmlYZlRBV1JnOUk1RkZRQUFrM1JpVDM5TjE4NE13YVVZbGFsMjVGSG5TaGZmb2taWmY3NXhwRG8wZi8weTk1UFdoL05GTzZsTnpZMndrczZlZWp5anFTWVVKbC9iNitzaWswT0ZBSG8vRHVrcWZHNDFNMGs0QWZXM2g4NkxDZ0VPaWE2VW5sWDRqcDhCRmJXc2g2U3pjY1ZWR0hRT1R3bytoRmlVZVdES2p1eWVqWmZIcVJ3ODZuOHkyRE9rOFA0L3c0SUl4Nlo5MHBkb2ROYUt1NVhGeGF0RlN5cmFZdHE4Zm41Z2dGaDNjTFBLWitScTFEYk8wcHA0dDArOXpqOGpCVHFiRVVjSTlVRFRZalpYVHRkYjc3RHVSbWxLTlU5OWQySEw0cC9URW51b3RGMmxkRTJQU2trUUc5emZrVklzejVNcUVhSmY0WmZxMlYvQXpnbGVxYmJDeXk4Y0NpTnQ2K2JWMXk2STFPdnptay94NDVaUmlsN1JBNTU4UzExSkpmTVR3cDFRR3ZvSHhDcFpPTlhmV3pmeWFaa29uYkkyQ3pTR1pMSDhaWTV6a01sTHFDeHV6ZlZ0R0ZUZWdrSzRSaHlzVDRNMzNnYzI5b0lpcytEVlgiLCJtYWMiOiIxODBjZWI2NzJjNjgzODg1ZTc0ODI0MDNjMGZiZGQ2MDg4OWIxZDM0MDBhNzE0NjQzNTg0NzM0ZWNhYzljYjhjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708645441\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1926451949 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926451949\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:57:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNvZzZpTWJKM3JCTXovcVpEN1NvZmc9PSIsInZhbHVlIjoicGF4UnloSE5xdHEwYkMySHhOcmdpOG9OclJBRUNjQ1VMcVJmNTJzNVR5TlpkQk14TE9pMXpwMGFQc21lN0crZlJOVzZ2TnI4V2ZPWTFhMkVRMWF6WThFNjZ3ZUpmZW1hc05ZNERaWHNYTGdGdXg5MTFYRjRtbEZaeVpnSFlybXRHd1BIWFlibzlUd1d2dDh0Yzh2UGJlNVMwN3VpSDlmVm9makZkV1VrajhJazYwMWVGZS9PbGtqVmlUQ05tcjRFK3Vwd3RScWVTdHpFTUx1ZWhWb2xFTmdMUzhMTEZxQnVLeDlKRUsydTJOV3BxblhHdlIyaWZkdTJiVE9SRjM5UWo0UDRWejBOYkJydzdlZTMwTFlleUJNVU5kdnQwOU80Z0FidldDWDc5czV4UXp3dCtFRnZjcnpwMllzUkRVbmtTSzVrMndFeFQxb2EzVnVUb0lqT1lTNHkyL3pCVTdEQmE1eWl2M3NOdm9EWTB0a0pDOHBvc3Jwb3ZBUlQxL0diVDZQQitDc01DQnp0TnhmU21kaUpyK1ZzeWlpZVRYWWgzNVhteENrWXNtQXF4SVpmNnV6ZjRBdlJaQ3gralFvQUg5R1ZLS3N6QXlTUWVDOW1hbEl0b2h3Z1BpZGxRTmRmREI2eDdDekxhbUQ3aXZ3ZDh0YmpUcWhsT0lYYUdFZlUiLCJtYWMiOiI1OTJhYTMxOWY2ZGI1ZDZkYTBiNzEzY2EwNmVjZDMyNDFlOTFhMzE4ZDJkODU2ZDY3NmI0ZWJmZDJmNWEyYzg3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijh3aUpFemE1WEY0WXk0bnoveCt2K1E9PSIsInZhbHVlIjoib2R0NGtnQ0NJUjRRbU9Lekl5WTdRZ28xblp2RW55VThhT2hRRCtoSWduODZCWVhPdXFFeGhYSXUrU3J5Q2dYZ3QwZ2dwZGVUY2tpRGErK0w2bTNlVWxiL1BGQ1dEL1BDWDdYYVRMelY5ZXBqMkRzNHhvR0tuc2NXY3RjZDBSbUtIM3pYRERwZXhRSDY5S1pFRXFwaGU3OElJYjRLUmlLdXR0UzE0NDhvTTVjaXpKNUUyeXN3L016VGdNS1JnV3Q5MUxxM1MrYXBNM2hmRHNMSmJjN3VuZWtDZTc1VzZOY0FrWU1MdmpkaG9UZmZqeS9qT2NjVCtiRkJNRDQzdGEyWGd4WDVscFFtTEFuek5rQmNMRkU2QUltU1M0MWVFaUtpNmVKZ0U1eHVXM2l4RHdaOFpqYkhEN1lVN2R3TE5EbTd5L2VDVzNpaVVGd3RZQm1zL3NlaHJoL2ZwZkx0YmFyaGxKeTVaREZWK3U2WmxFcDhKdisvSnhSSHdXMWtadjV3Q1JYVlpWbHdEKzRsZE53TzZPbFh0T0VYNVBlQjhkRnhjc2ZaWUJQMGhLaDZQZSt4cm83Si8zUDhXMHZ1SEUrNGc1aXBUMGlyR0JsRXlHeEl3TWpINllkVW9HczdCdTNZdU5jd0cwSE1tUEljM2UxTmlpZStCT0hQNkJnUkUxbWUiLCJtYWMiOiI5ZTM5YWY5YWMxZTkzMmJjZmI4MGFiYWJjZWFjMGFiOWNjNDMzMDAyNDViMjJkMzc2MmFkYzUwMGE4ZjRiNTA0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNvZzZpTWJKM3JCTXovcVpEN1NvZmc9PSIsInZhbHVlIjoicGF4UnloSE5xdHEwYkMySHhOcmdpOG9OclJBRUNjQ1VMcVJmNTJzNVR5TlpkQk14TE9pMXpwMGFQc21lN0crZlJOVzZ2TnI4V2ZPWTFhMkVRMWF6WThFNjZ3ZUpmZW1hc05ZNERaWHNYTGdGdXg5MTFYRjRtbEZaeVpnSFlybXRHd1BIWFlibzlUd1d2dDh0Yzh2UGJlNVMwN3VpSDlmVm9makZkV1VrajhJazYwMWVGZS9PbGtqVmlUQ05tcjRFK3Vwd3RScWVTdHpFTUx1ZWhWb2xFTmdMUzhMTEZxQnVLeDlKRUsydTJOV3BxblhHdlIyaWZkdTJiVE9SRjM5UWo0UDRWejBOYkJydzdlZTMwTFlleUJNVU5kdnQwOU80Z0FidldDWDc5czV4UXp3dCtFRnZjcnpwMllzUkRVbmtTSzVrMndFeFQxb2EzVnVUb0lqT1lTNHkyL3pCVTdEQmE1eWl2M3NOdm9EWTB0a0pDOHBvc3Jwb3ZBUlQxL0diVDZQQitDc01DQnp0TnhmU21kaUpyK1ZzeWlpZVRYWWgzNVhteENrWXNtQXF4SVpmNnV6ZjRBdlJaQ3gralFvQUg5R1ZLS3N6QXlTUWVDOW1hbEl0b2h3Z1BpZGxRTmRmREI2eDdDekxhbUQ3aXZ3ZDh0YmpUcWhsT0lYYUdFZlUiLCJtYWMiOiI1OTJhYTMxOWY2ZGI1ZDZkYTBiNzEzY2EwNmVjZDMyNDFlOTFhMzE4ZDJkODU2ZDY3NmI0ZWJmZDJmNWEyYzg3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijh3aUpFemE1WEY0WXk0bnoveCt2K1E9PSIsInZhbHVlIjoib2R0NGtnQ0NJUjRRbU9Lekl5WTdRZ28xblp2RW55VThhT2hRRCtoSWduODZCWVhPdXFFeGhYSXUrU3J5Q2dYZ3QwZ2dwZGVUY2tpRGErK0w2bTNlVWxiL1BGQ1dEL1BDWDdYYVRMelY5ZXBqMkRzNHhvR0tuc2NXY3RjZDBSbUtIM3pYRERwZXhRSDY5S1pFRXFwaGU3OElJYjRLUmlLdXR0UzE0NDhvTTVjaXpKNUUyeXN3L016VGdNS1JnV3Q5MUxxM1MrYXBNM2hmRHNMSmJjN3VuZWtDZTc1VzZOY0FrWU1MdmpkaG9UZmZqeS9qT2NjVCtiRkJNRDQzdGEyWGd4WDVscFFtTEFuek5rQmNMRkU2QUltU1M0MWVFaUtpNmVKZ0U1eHVXM2l4RHdaOFpqYkhEN1lVN2R3TE5EbTd5L2VDVzNpaVVGd3RZQm1zL3NlaHJoL2ZwZkx0YmFyaGxKeTVaREZWK3U2WmxFcDhKdisvSnhSSHdXMWtadjV3Q1JYVlpWbHdEKzRsZE53TzZPbFh0T0VYNVBlQjhkRnhjc2ZaWUJQMGhLaDZQZSt4cm83Si8zUDhXMHZ1SEUrNGc1aXBUMGlyR0JsRXlHeEl3TWpINllkVW9HczdCdTNZdU5jd0cwSE1tUEljM2UxTmlpZStCT0hQNkJnUkUxbWUiLCJtYWMiOiI5ZTM5YWY5YWMxZTkzMmJjZmI4MGFiYWJjZWFjMGFiOWNjNDMzMDAyNDViMjJkMzc2MmFkYzUwMGE4ZjRiNTA0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"165 characters\">http://localhost/invoice/processing/invoice-processor?creator_search=Afrooz%20Ala&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}