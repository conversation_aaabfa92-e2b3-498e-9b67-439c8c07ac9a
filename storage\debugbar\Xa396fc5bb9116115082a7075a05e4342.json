{"__meta": {"id": "Xa396fc5bb9116115082a7075a05e4342", "datetime": "2025-07-23 18:18:16", "utime": **********.625809, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.118612, "end": **********.625833, "duration": 0.507220983505249, "duration_str": "507ms", "measures": [{"label": "Booting", "start": **********.118612, "relative_start": 0, "end": **********.546578, "relative_end": **********.546578, "duration": 0.4279658794403076, "duration_str": "428ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.546593, "relative_start": 0.427980899810791, "end": **********.625836, "relative_end": 2.86102294921875e-06, "duration": 0.07924294471740723, "duration_str": "79.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46533576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016059999999999998, "accumulated_duration_str": "16.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5785701, "duration": 0.01487, "duration_str": "14.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.59}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6020548, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.59, "width_percent": 3.549}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.60834, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.139, "width_percent": 3.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-609909518 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-609909518\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-162149436 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-162149436\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1358741984 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358741984\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-970399832 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; XSRF-TOKEN=eyJpdiI6ImNoQ0lQQjNWTmdIb0V2RkFsUFQ2K3c9PSIsInZhbHVlIjoiTmQzMlNQbmhNdzlsakVHdHJMK01vZCtRcU94SEJPVkxPRTlITUlLbXcxOWF5aUFiMndORWpodmhlUG0ycXNLVGxNMG0xY0J5ZklyOWlackN0cXh5VHd4QUxLTEVZc2RpWS9yYnQ4Y1pYMnc4RStLYkZmRWlhS1RYSE9neFhWR3BCQ1hHS2tma05Nd3FXdjZlUWMwYm9sbFVCS1M5Z0JCd3loZHp1YktzTjBiS3FJeDRSRFgrdlZjVUNFbHZCVDEySDFNc0tMM3Bkc3djd2wxR3l4K3AzSFozWmczejhGRXhUQzdjYUZXUGdoaWJNcm11RDlDeGZIVndYeEwvejlERExhbFdybmFzZGc1Nmd6cjBZOFB3S01SaXBWZnM2RmRUWUVyZ3JmSlpaUVpIWFNSLzlmWGlSREpDaDJpY2YxaTE5WHdsVXhIdlV2Ty9aRE5qb1hPU0wxakd1NkFMd29EVlJ1R0hKVURWYTlGMm1pS1gxU0QvdXF6S0JLY3pObEQ0YWJ2ZnEyUWdOS2RxQThBV1hEVklPTjA1Y2N2bmZuK1hQcnFudEJxMUoycFVYM0xHOGl4UXl1ZVFrNkRsYjNWV3AySGVvdjYrTnZ2MnhaV3lRSWw4WGZqeTFYa2ZvT08vZyt5UStDcEZrd3MwMXowZ1piQ1h4dDA4RFBlTVBwUHoiLCJtYWMiOiI1MGJjNzg2ZjAzNzNmMjIyNTMxNjgxOTEwNjRjZWViOGExNGZiYzZlMTIyNDBmNjY3ZTVjZWM5OGViNmU4ZWYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpFQk1TRTJ3dXRzR2VQY3pKNk12TGc9PSIsInZhbHVlIjoiaWZxOFFjRTEzQkxnQzFOU1EvUzRRSWZJanIwdVRlbDBuQUJML0dCaWRDd3hDSTNvTk1meW0wSGxTVmNMZEJWbWZObmRsRDROYjV1VHBKYklQS1poL25sTkpOa1AwMnVnTEEzVksvL1ZiTlFFSVFXYytVYVIwb2N1R2QzNnlkN0FoZ0trTE9tMXhhUDZaL2dXY1FqUkVycCtwRSs1aDV1TUVjQmhCLzl1YktpQkVQYkNoRFBMbmxHSU9PODNicklJSnNoVWRKMUpuMCtqRzVydEFoUXhjSTI3U1FjVjB3WG9HdzFhaThzcG8wWUkyb2R2QzZZUDQwSjFTS0c1b3VBOHFhWG8vOUU1ZFFsTXoveGZKck1maXl5VEQvYzVNeUJFNDk0RWQ5dlE3L09CTXlBSzJMclY5UytueGhjYWFiaVVkOVZrc3NNRjl3SVJhNnBmVUVLM3ExTzVIcVlQTE5rRzdCamlLbkd3bTZoNk1CdTVMWHVPZk01NGtrbGFhVFp3RDZKVzh0YkdVVS9GVkdSZzNXanZaMWFrOFk4UWpvemZqSklEQ1A2Y1VjenNmdFQ3MXh5MG80MUNndXpWckFZVXhTVXh1UHFwWmpBNk5DY0sxUWxMaGFuSVR3U1FCTXYrdUNvdjZLRmFldVUxUERwbUpzbUFteW1uWmQyVi9KanEiLCJtYWMiOiI0YTFiYWE5MmJjYmZmMmZiMzg0MTRmYjU4ODEyMGM3ZTdlNzE0M2RkMjM0ODUzMzc4NWRmNzgwNWY2MzdkODUxIiwidGFnIjoiIn0%3D; _clsk=c6ed4t%7C1753294695171%7C3%7C1%7Ca.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-970399832\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1630215089 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630215089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1725670204 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:18:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5IeWRId3pibUVIOE9meDd2NXI4SVE9PSIsInZhbHVlIjoiQytiM2U2WXpRbG43NXV3U0tFNDhPekVBSUFSbjZ0MWdrOHc2UGV4RTdUdjdqeEVmeHQ3ZHRWY2pxWkUxMVgrZ29tbjVwOW1waitBejZYOVNXemdQZlBRRFd2dXg3a3NiUUkyaDdxZmgrWlhlbzF2cXdrdlBYbi9BeDJ6T3ZVUGVoY0o2ZTc4RkdBeVVQQlJPTGNkR2VDYW04RHR0amZ1RlVMRHFOTUoyN3VrZ0RKeGVLMG50U0gxaGo0U00vWUE0MzBrZlpmTlgyTC9aeGQyOStZM1hFZDVsbDkyOTJOU00zWk9sNzRpODFFdUJOTGxBK092N2paSGNLMnhOZS9xTm54eE1Ua09oNWxwczUvdjdSODJEWVdrVk4vYmpZZ0d3Q09mRUZSWVFmWFRURFdlSnRXYmhQaC9QcngyQk9Ec3NJNFM0RXowT2RjRVkraFAzdU9EMlhjQ0pPQTJuN2IyZGYwb2pYRnBNeXhGNzNpZm9rOGhrVTFYNG9LYi95OUY5NmlOYndNYjdQcCs4YkVqT0NwbUdReHVIRnVJM24zNTZMY0dVMGJnemxHZkhQUmZUNFY3Wm4rT2d4WEhtOExGbnhsR2V0Kzd2V2RKYXRZQ09QYnl6QzZNTmxkVkR3TjFid0VKemlxTDVLTlhZUUcwK0d2WjRpQTZSVXN3M3RDOGsiLCJtYWMiOiJmMjI3YjEzYzNkODJjOTJmMmY5MmI3YzNmMjgyYzdiYzYzZmQ3YjRjMTMxYmY1NDkyNDYxYTRhMTJiY2Q5YzRjIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:18:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitTd2E0N3VRT2wvVVRGY1RrSHRIc0E9PSIsInZhbHVlIjoianAvVnZNL2xSOUtURnZFaVEwblMxbVZwMUdEUlBvNjc5T05NZzM5c2xqM0ZiSXArRFhqRFhvRmN0ci9OV2lqNFJxNlRyb0tWTlp6SHhkM1JyWC9EUDVVbWMybGtFcTB6eFNtaUEzUkIzNWZBQndxKzRWdFRoS1htUWUvRlhqQ1d3R0tRTWhtYU16TGxRSHJyWUpnbk9Ga2VURmp2eDZhMDZndUh0RXE4bW50VlFhc213aTQzcU9xbkYwYU1HY3VmVk10WDlNVlhHTFVDMlZFQWVXazVRRkdrOG1HYUE0dWJ6UlI1ZUlTM2lTcWNEaVVqeFQ0bzcvYUVFcjVBT3RoTUc2cXp2enhubXRnSnQzQURkdVRwY0dsMWhmVUx1OWs0YVNGU1puRXNCNVZXek9uQjV4YzBhaU1teU56UUMra2ZWU2NJY0ZDVElJQWI3VXBjcW05UE1ucHlhczM3NVI5dnVyNEFZUEdXUHQ5ZkhCRlgwS2dMVHVCenNhRENvNG15ZkhTOXdsNlhVTmJlNlZ4NEgyRTQxc01HSS8rVFBVSkhSUU90RmZSWmhLcnpJcXk4bDFFb1p4V2I2M1pFRkhTQTFwbzczMUlkNDBJdGpteVRoVjZGbW41VkJ5WlQwQ0xXQy9qNDJNcElkQXZmMVpqM0lQSE5GVldjbmpjSjliVjUiLCJtYWMiOiIyMzY2MDQwNTczYmM5MmRkZGQwOThiNGExMDg1ZjYyNjE4MzEyYjBmZGY4MWU0OGM3NzE1ZDhmODAzYWUwYmNhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:18:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5IeWRId3pibUVIOE9meDd2NXI4SVE9PSIsInZhbHVlIjoiQytiM2U2WXpRbG43NXV3U0tFNDhPekVBSUFSbjZ0MWdrOHc2UGV4RTdUdjdqeEVmeHQ3ZHRWY2pxWkUxMVgrZ29tbjVwOW1waitBejZYOVNXemdQZlBRRFd2dXg3a3NiUUkyaDdxZmgrWlhlbzF2cXdrdlBYbi9BeDJ6T3ZVUGVoY0o2ZTc4RkdBeVVQQlJPTGNkR2VDYW04RHR0amZ1RlVMRHFOTUoyN3VrZ0RKeGVLMG50U0gxaGo0U00vWUE0MzBrZlpmTlgyTC9aeGQyOStZM1hFZDVsbDkyOTJOU00zWk9sNzRpODFFdUJOTGxBK092N2paSGNLMnhOZS9xTm54eE1Ua09oNWxwczUvdjdSODJEWVdrVk4vYmpZZ0d3Q09mRUZSWVFmWFRURFdlSnRXYmhQaC9QcngyQk9Ec3NJNFM0RXowT2RjRVkraFAzdU9EMlhjQ0pPQTJuN2IyZGYwb2pYRnBNeXhGNzNpZm9rOGhrVTFYNG9LYi95OUY5NmlOYndNYjdQcCs4YkVqT0NwbUdReHVIRnVJM24zNTZMY0dVMGJnemxHZkhQUmZUNFY3Wm4rT2d4WEhtOExGbnhsR2V0Kzd2V2RKYXRZQ09QYnl6QzZNTmxkVkR3TjFid0VKemlxTDVLTlhZUUcwK0d2WjRpQTZSVXN3M3RDOGsiLCJtYWMiOiJmMjI3YjEzYzNkODJjOTJmMmY5MmI3YzNmMjgyYzdiYzYzZmQ3YjRjMTMxYmY1NDkyNDYxYTRhMTJiY2Q5YzRjIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:18:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitTd2E0N3VRT2wvVVRGY1RrSHRIc0E9PSIsInZhbHVlIjoianAvVnZNL2xSOUtURnZFaVEwblMxbVZwMUdEUlBvNjc5T05NZzM5c2xqM0ZiSXArRFhqRFhvRmN0ci9OV2lqNFJxNlRyb0tWTlp6SHhkM1JyWC9EUDVVbWMybGtFcTB6eFNtaUEzUkIzNWZBQndxKzRWdFRoS1htUWUvRlhqQ1d3R0tRTWhtYU16TGxRSHJyWUpnbk9Ga2VURmp2eDZhMDZndUh0RXE4bW50VlFhc213aTQzcU9xbkYwYU1HY3VmVk10WDlNVlhHTFVDMlZFQWVXazVRRkdrOG1HYUE0dWJ6UlI1ZUlTM2lTcWNEaVVqeFQ0bzcvYUVFcjVBT3RoTUc2cXp2enhubXRnSnQzQURkdVRwY0dsMWhmVUx1OWs0YVNGU1puRXNCNVZXek9uQjV4YzBhaU1teU56UUMra2ZWU2NJY0ZDVElJQWI3VXBjcW05UE1ucHlhczM3NVI5dnVyNEFZUEdXUHQ5ZkhCRlgwS2dMVHVCenNhRENvNG15ZkhTOXdsNlhVTmJlNlZ4NEgyRTQxc01HSS8rVFBVSkhSUU90RmZSWmhLcnpJcXk4bDFFb1p4V2I2M1pFRkhTQTFwbzczMUlkNDBJdGpteVRoVjZGbW41VkJ5WlQwQ0xXQy9qNDJNcElkQXZmMVpqM0lQSE5GVldjbmpjSjliVjUiLCJtYWMiOiIyMzY2MDQwNTczYmM5MmRkZGQwOThiNGExMDg1ZjYyNjE4MzEyYjBmZGY4MWU0OGM3NzE1ZDhmODAzYWUwYmNhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:18:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725670204\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-462523875 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462523875\", {\"maxDepth\":0})</script>\n"}}