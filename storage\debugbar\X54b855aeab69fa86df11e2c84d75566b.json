{"__meta": {"id": "X54b855aeab69fa86df11e2c84d75566b", "datetime": "2025-07-23 18:20:43", "utime": **********.305031, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294842.846682, "end": **********.305058, "duration": 0.4583759307861328, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1753294842.846682, "relative_start": 0, "end": **********.231718, "relative_end": **********.231718, "duration": 0.38503599166870117, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.23174, "relative_start": 0.3850579261779785, "end": **********.30506, "relative_end": 1.9073486328125e-06, "duration": 0.07331991195678711, "duration_str": "73.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01718, "accumulated_duration_str": "17.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.262763, "duration": 0.016149999999999998, "duration_str": "16.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.005}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.287904, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.005, "width_percent": 2.736}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.293887, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.74, "width_percent": 3.26}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1344300185 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1344300185\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1122777920 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1122777920\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-819624414 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819624414\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1982084686 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294839750%7C7%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNTUmg0Q1M3SUd4TlYya2NvWFc5bUE9PSIsInZhbHVlIjoiZ2lYZEJ3U00vQ2prOTFCR2xYMC9tUDdzU1NXR0NibUcvdU5vOUR4L0pNZWVQbzY5WkhjZGFVRmRxQWsrSERsT0x0Qmg0eUdpTmI0blp1NkpLVHJiVFR5UFBmWHJiWm1wakhGbkloYk02KzRXcTZsN0xJaXNYTkJFcTExZTg4anU4OWtBRHJTcU1hSHI4di9IQzE2SmhGYjRSczdYTHhXVUZ5TnlRQ000dkVkU0xqYzJFSnptYmVsdXBEVk0xV1ZqaUMvRWVMNElVWXVpcGtOekpHc0FwbmYrZXhyOTRDbTlUNXk5YnhmRnhWaWxiS1ZDTEZ3R0lqY1g3R3hGeTZ2OVpSYktWWU81NTJpcDczcDR2RGYwb0M4Q2lBK0FnVzN2SnAxWXpyV1E1WjgzMG56cnZOY2gybnhLSW1pZ0Q3S2JLblpZVVdiVmR1L3hRcGZQSSt5N3lhbi9rNFFKbGV0T2lUanVvUzBXbVdMblZrU0hXbURnVVNPSkw1QjRiczNQQlowOW5EaVM1NFFlUFMxdlZmYTZxRld4bXdJSHNHb3c1SWZsSlhSa1NrNkRFZ3BScmxSWHpFWGp0emFkT3g3U24yTGxLVlNGMGtQV2tPbWQ0dVkrNnVOZk5nN1Zqam9qWlE0OTcyckdzT3JaU1F0NG5DazhHUlpLclpmdTU0bXEiLCJtYWMiOiJiZWExNzE4NGZkNDIzNTg4MGM4ZjE2ZWQ0MzM3OTkyNjExNmMzZTRjMzM4OTc4MWY0NzViM2YzYjUzMTM0ZDQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitrUFRPZUFNMVczbVg3bmFEeTBXcGc9PSIsInZhbHVlIjoiVFAraEcreXY5NHBWNVpTRDFBZFl4eVhyVWJ4dUJuU1RBMFkwckUyYjNNMndvd3pCWUpHM24zZmY0eHZuRmcvT1RtTUF4djFSMjJzQmJBRFNyZWNFZlJDSm1wN3UydWxDZEVBSTBic2VFdEYrMVo2RXRaV2VSZDVXdnQ5aXA1VE9uNTRFRUU3RkFRU01CcEdQSWEwc1pXN0ptVStSTXFuRnVwUzNQNUsyTVRNazJxMnQ2dklRNVpKL2V5elM3dWZnbTR6a0psYWVIWTBXbUhIZHBqSUFMYUlVenI5T0V2SlZQblhtaEdxOWU0WSs5bTlyNUIvam0xRHRrOUxvRkoyRHBVd3JoOERSSHFZVC9oSHIzM0dUYXlmV2ZGTHRSOXZjZkNaS2RKVjlkSjB2b3d4bGkva2JQTGgyM1BydmZOeEQvVDE2QlBaQ0MwTDVTd2lQTmUzeVRBRFMxc3BFVW1qRTJManRXbjFDWmZ0Wk9UMkdMR09ZNWZWOGl5SmpHVjNiRUtkUlpXN2hKRjQ1dHNqalhvNGUxVHhPRklKN0Q5dlZ0UzBCK2p1UVYxUjRZdEV2Y2NSS1Vyd3ZoUm00Zk9yUmRSbGhWZFV1SWhYVFhjUmhGT0NnU2FGZjBmRG9JWGZQYXlZQXJJT3FYSGdFdmd6eWlOT25rRTRKV1pHMkozUXAiLCJtYWMiOiIyYmNhZWZiMjQyZTJlODQ4NmYwZjRhYTE2NzgwZDQwZjRkM2UzZmM0MmM0NWM5ZGZkZjFlYTkxNTZhYTRiYmVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982084686\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1951220913 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951220913\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-580516789 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:20:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJOZEVTb3FhaVJtZ2dRUXhDdEdCQ3c9PSIsInZhbHVlIjoiYm5JWFVlZHhTVEIvWWVWK2hTTXZLSFZUMTh1bVJtQUcrRzRrbXJkeUtBWVdQSXpNWmRjN1ZIZ25WR0FKUFVhcmtESWdYWElyNkc5b3lSM2huQXQ3RFNGQjAyVGRNaldoU3B1YlpYM2xKVVNMTlNUa0M1RTFrbmExMUc5NTNIaDR3Y2VHajloVW9Mb1pkYWU4SWlNczk3cDM4TTRnTElzY3o5SnZXSFdhNGVHMVovVmhna2dUREdmdTVzemdpVW1jVjVYbm96RE5CZm9OOTM2aXE0Y25GUCtPd1ZWbUJKWStRdHBjY0JGTHJDdkhaeTFnT1c2ZDZHeVgzcGcwdFdFWmlqOFgvdTBibVc0QkFjbDkzVW10c3lOM2duVkkyeXBkdVdXdEdTWTlQNWRyV0V2eGc5WnNTZjBnTjNOamwwQ0lYY0hzeUdkWDBuREpSMVN4OHVJejFMM3lRcXZQNjBHUDJYdDJSbmFjd0VJSUFwYUYxZVN4VGIzUVZFcUZzNnNFKzRIakRpRFZxcUxmSFUvbVVNL2I2bmdNaUFqeHFqTzd6eTFyN2FXb1pZQXNvc2VGbmt5RGwyT3l3cERrRVlLelRzcHVOL3F5bE1kYmVuZlh4MHR2ZjlNQWtSdDB3SlgwQU81NlNVZk1UTnMydSt6TTd3eHQ1NitmWXAyWHhPSVUiLCJtYWMiOiI5OWNjM2FiZTgyM2FhMmUyMDViNTYwNDg3Zjc3MWFlNjEyYTM3MDUxYzMwYTZjOWU5YjkxZTRlOTRmZGQyNDAzIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBCdTBiVUJhUzlCUWxkMHAvOHF3Nnc9PSIsInZhbHVlIjoiTWxQRGdDekhwK2FwNGxvQzJpSG1KSHhyUVUwZmFFY2UyU3E0SGowVzVPajZEcmF0ZllVQ3VLbDdZZFJVcXAxZUg2ZVpTQlc5Zk9SZVc1ZFpqWUQybDN0YUF5MFJJeU41ajFQbHZTV0tkbXVoRk56S3k5b3JweHIyWHJibFZkeXVCY2xMclYxRGJsbkcyMjBmRHlZQVlWRUZhbDZwdkN2cVN1OEtDTWFtQjkwcmNQeTNvaHZIdktrYUpwT1BaVDJoV1V5dHgrWFUvclBUMmtKbGlqVmRtMmhXY1Q0d0FHd1Y1VGRYajVmczN2d0FOTUp0KzNINWcrZko3R0ZNdVpSRTN1K1JFbWpLdjdaNzV5TWxUU0V6NHVoQjhXNmlSbVRONm1FODZRVjRqeUEwV3Y4SndyWFlZSWt5V0lTK3FMTGNhNnFhWlVxUUU4VUpFZjlxenFTU1JmVEt6WWx5dXhac2tNekJPZVdIZlNldnpIQjM4VkFyL2JPT28xcERsc2RJczlMRGg2OWZsTEhBRkVOb1ZITnNXbmkreitsWDJQbVNTVVF3NFhnd3Mxc1BzV1NCcmpGcGp0ZER4TVEwbUllcWduT2JRemlYeTZ5ODFiZkhza3o0MzFydlZ1aDRESmRVSzJ6ZU9UR1hpZ241UkxQYTIxS1ZuMlROMWxBWW1QK0MiLCJtYWMiOiIxZTBkNWQ5NjA5ZjMzZmJmODUxMGE2ZDU4YjQ5NDdmNzBjMGQ1N2Q3N2IyM2IzMjZkMmQ0M2QzZTA5NTMyNGJmIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJOZEVTb3FhaVJtZ2dRUXhDdEdCQ3c9PSIsInZhbHVlIjoiYm5JWFVlZHhTVEIvWWVWK2hTTXZLSFZUMTh1bVJtQUcrRzRrbXJkeUtBWVdQSXpNWmRjN1ZIZ25WR0FKUFVhcmtESWdYWElyNkc5b3lSM2huQXQ3RFNGQjAyVGRNaldoU3B1YlpYM2xKVVNMTlNUa0M1RTFrbmExMUc5NTNIaDR3Y2VHajloVW9Mb1pkYWU4SWlNczk3cDM4TTRnTElzY3o5SnZXSFdhNGVHMVovVmhna2dUREdmdTVzemdpVW1jVjVYbm96RE5CZm9OOTM2aXE0Y25GUCtPd1ZWbUJKWStRdHBjY0JGTHJDdkhaeTFnT1c2ZDZHeVgzcGcwdFdFWmlqOFgvdTBibVc0QkFjbDkzVW10c3lOM2duVkkyeXBkdVdXdEdTWTlQNWRyV0V2eGc5WnNTZjBnTjNOamwwQ0lYY0hzeUdkWDBuREpSMVN4OHVJejFMM3lRcXZQNjBHUDJYdDJSbmFjd0VJSUFwYUYxZVN4VGIzUVZFcUZzNnNFKzRIakRpRFZxcUxmSFUvbVVNL2I2bmdNaUFqeHFqTzd6eTFyN2FXb1pZQXNvc2VGbmt5RGwyT3l3cERrRVlLelRzcHVOL3F5bE1kYmVuZlh4MHR2ZjlNQWtSdDB3SlgwQU81NlNVZk1UTnMydSt6TTd3eHQ1NitmWXAyWHhPSVUiLCJtYWMiOiI5OWNjM2FiZTgyM2FhMmUyMDViNTYwNDg3Zjc3MWFlNjEyYTM3MDUxYzMwYTZjOWU5YjkxZTRlOTRmZGQyNDAzIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBCdTBiVUJhUzlCUWxkMHAvOHF3Nnc9PSIsInZhbHVlIjoiTWxQRGdDekhwK2FwNGxvQzJpSG1KSHhyUVUwZmFFY2UyU3E0SGowVzVPajZEcmF0ZllVQ3VLbDdZZFJVcXAxZUg2ZVpTQlc5Zk9SZVc1ZFpqWUQybDN0YUF5MFJJeU41ajFQbHZTV0tkbXVoRk56S3k5b3JweHIyWHJibFZkeXVCY2xMclYxRGJsbkcyMjBmRHlZQVlWRUZhbDZwdkN2cVN1OEtDTWFtQjkwcmNQeTNvaHZIdktrYUpwT1BaVDJoV1V5dHgrWFUvclBUMmtKbGlqVmRtMmhXY1Q0d0FHd1Y1VGRYajVmczN2d0FOTUp0KzNINWcrZko3R0ZNdVpSRTN1K1JFbWpLdjdaNzV5TWxUU0V6NHVoQjhXNmlSbVRONm1FODZRVjRqeUEwV3Y4SndyWFlZSWt5V0lTK3FMTGNhNnFhWlVxUUU4VUpFZjlxenFTU1JmVEt6WWx5dXhac2tNekJPZVdIZlNldnpIQjM4VkFyL2JPT28xcERsc2RJczlMRGg2OWZsTEhBRkVOb1ZITnNXbmkreitsWDJQbVNTVVF3NFhnd3Mxc1BzV1NCcmpGcGp0ZER4TVEwbUllcWduT2JRemlYeTZ5ODFiZkhza3o0MzFydlZ1aDRESmRVSzJ6ZU9UR1hpZ241UkxQYTIxS1ZuMlROMWxBWW1QK0MiLCJtYWMiOiIxZTBkNWQ5NjA5ZjMzZmJmODUxMGE2ZDU4YjQ5NDdmNzBjMGQ1N2Q3N2IyM2IzMjZkMmQ0M2QzZTA5NTMyNGJmIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580516789\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}