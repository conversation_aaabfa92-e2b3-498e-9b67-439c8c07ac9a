{"__meta": {"id": "X4f6ab9f2b3f3a95e02cdac03b9e62e40", "datetime": "2025-07-21 01:19:39", "utime": **********.924373, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.462302, "end": **********.924394, "duration": 0.46209192276000977, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.462302, "relative_start": 0, "end": **********.847687, "relative_end": **********.847687, "duration": 0.38538503646850586, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.847698, "relative_start": 0.38539600372314453, "end": **********.924396, "relative_end": 2.1457672119140625e-06, "duration": 0.07669806480407715, "duration_str": "76.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46005936, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02105, "accumulated_duration_str": "21.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.877115, "duration": 0.019739999999999997, "duration_str": "19.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.777}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.906554, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.777, "width_percent": 3.088}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9146922, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.865, "width_percent": 3.135}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1209181484 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753060775053%7C4%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpBbFhualBJSVRLS0UxQkxuTm1YSWc9PSIsInZhbHVlIjoiN3NRNHdyUU5ZbTlsa2hJVjFQc0VCQVkrZmVMUVpqSEFYcDJHM3lkcWhhc0VtWkVraDhEdVZZMlJkNVZRKzBnYmVnOURWOHdhMDRxRUppays2ZzFrSXVpdnpmS1Y1OXZST3BDRnh1citQbWpKeGtZNUliTFFueEJ5bDhkbnVIR2RCYWkwNzkxZVFCWFd5TEx0dWlrNnUxNS9tZzVyTEEzVGRIOUlabXl6NCsxSTlJMmtBY3lSRWN6YWxIUnBSZ1I0MDlqY3pPRlc3ZTcydk9CdnRQb2J1ZEU0aEdKb1JQZ2JkdEVIUGNRVm9iSmYyeDNMMk9ZRkpqakQ3aVQvemtoY0xXbVE1c0t4MTZYZW1mTVJscUhtRjhVamRlUTh5OTF6NkduSUx2UENmdFMvUklHVzFBb3cyVnBsdTJELzJBOXRGOTU2RTRCb2gxaWNKa1N2VEdEQjVEcmdDLzdORkFnMndlOEZaZEdsNENONlYzNHAwc1FZa3lkRG9pdHFHeFhHQkNqRnJmcWFHSmVYMjU3WFdKVWNMQThMSnM5Vis2OWhTaHpET3RmWGVkd0wyWTA4RHk3SGxXcWxlN29zQXYwNFdCS0hSKzhHalFqSjhzVzFRNkFMYjdzWkdFOXh1UCtSR2U5a0pPMUIweEpCSlVnemR6cVBCK3lrSCtDMGVQVzYiLCJtYWMiOiI1ZDA4NTA4NjhmOTMwNGQ4Y2YyMmE2NDFmNGUzZTA5YmI3YzY0NTAzYzA4YWU0NDEwNDUzM2VhMDUxZWJjYmFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVPMjdMVTVObjVjdmpoMXFRWWMrRkE9PSIsInZhbHVlIjoicDlFb3Z4OXNLUzFZS1g1OHlsM0EyUStFdHNpbnJoaWEvQmVCaUUvVTFkdi9RQW1zRnNtMkZFTUl0N0I1aU1GMTJZUzViODhCQ21TalNERzVaaW9MMEZVOC9UUm5aanQxcXJUd0tQUHZ3VUsvSFI1R0JNTnNqOVhXUVdvdDhZdWJOZC9zZER1eTNPeGg5ck1oWC85clBjenZIZEE4all0VWN0dlVmVzNicEdPaXYxMUQ1VXdTVmFJVnNXemVnVXdWVVVQUFVVL3pTSnJFbW50UFBwb1M0cUZGRjd5YlNLbnp6THM5aHdUSEZHN1AvOHhtQkNpUnd1WGtHQmlac2s5Tk0vbm9yZmxHc05IK3k2VytBYVNCSWc0eXptWEE3RzlZM01ObUFQUHNTUjVvc29hbE1sOWpzWmdCaHdvSmY3cnlzNllTS0x2ZGVKUUc4WlpvUHRhcTlYYk9nMlBzWEp0TGQrQVNCWlJPcnEvbmk4aUhKejRQSEZaS2kvVjFOdW5yeGlEZmdVWG9XZXViVmdMZXdRbmJadzdYa2ZmRkQ1VFVJSVFWd3lxUnM5WnFySnp1bzhHMStvTTk5Y3VLS1NjT1doejJSUDQyTWp6UU9UQzZlRld2alF4MlJER3ZId1VZUEN5ZmI4M0RweVE4RHlVVC9Ha3IrWVBHQ0FYeTNaSGkiLCJtYWMiOiI4OTczYzY5ZDA3YTQyYmI5YjdlZjE4YTU3YTA2ODE4ZDBjNjRmNTAzZDQyZTMyNjBiZjdiZTVhZDRkOGI5YjNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209181484\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-152175390 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152175390\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1472459420 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:19:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBEcjlHM09hK2N2eUJmd3R3OC9VNkE9PSIsInZhbHVlIjoiRytMYXZ0MmhJN1VDZjlBOVd6eHF2VVR3SlZCYUVOOFpEWElEcFFXak91MVVFelhJOWx3R2JyaXZXb2lNQ29YcGh5V2JhZ0xhNkFrYTFuaHVmSkJ4RDBKVVdRVzlBRUtvTWEza0pGLytlQy84aEhKVWp0K2NvV1RqTlJkeTFhZVh4YWVTR3kvM0M5NmxJdTNpY1gyWThDZkR3a3FHaW1MZ0p1UnRpejNtRnNIcEdtNmRzN0U4dHpaT2lLSklqd3lEb0ovdGJmbWVRWURocy8rYVBxVEZuZW9KUmdNNXJiYzJLVTFmNEdzOEwwNmNxTjRUVDdNQVhBZkhaKzVkdllnN1VQUEpjaStieXVndTVWVDV4R0hvUGVCODhucDB6NC94UzFvbG9yam5mS1hFSUpyd3BWRUhIQ0ZhTVJDT0VBWHdwWFZZa2ZtZHNIc1dZVStkUkcrOU5GaXRjT0dBNjhUY3h1OHNBY3hiY1lpZEh5ZFhYckhBZDh1L0p6cjEwUVo2SDJEaDVDa1FLSHRYcEgvTmEyeVpjNGtZZGt5NFVhODlrb25HcDMrOHNyNkI4VG12aEoxZklYSDNwMVRHTU5wTXdBc3ZhcUh1ZVB3Y2FESjBWL2FRb3dmSHdUVkNWakxoK0JUcVZOMDhWM0FoaEdkMTFTOG9SblZzdllCd050UnIiLCJtYWMiOiI0ZGNhZDJlM2YwYjc3NjhiZjZlMGFjMmM2ZDg4MDY2NzAzMTMyMGI2NmZkNmFlZjFlNTVkZjM0MzFhMTY0ODE0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJjVkFuTWFmMit6WE5CNjR0cTAwZXc9PSIsInZhbHVlIjoiV0wyRzRBbHhVUDI1S2F5Q1Bialc5aUhuTTlGODNvRG5LTW5ybFhHemx2ZjRNUFlDVmZpQTU2K3B1T3BZcUtHWVQrd210WnR1MW5vZmJEc1VNSCtvZGJhU1AzOC9YazcyMUhTUjFmSE41NmlOeE1neTd6dzhzaEM0bXpEOGdJWU84STdQcS9LQWQvTHZLd3E5RHpIVUVLZGpzd0psb0ZDRzB6MzY0Q3JLZThnMUJzNFlKdmY3eTduUS9uNnVXc2RXbnc3RjQzVDR4bnFYclRQU1VCZHhvV0ZNMzNZMGtuUE5HN1djVzMyd3IxTGQ4MXVwbXZic1BMVWJwaWR0SGo4a2RGQmlPU3M2WnJaUHN6RnU5MndZN2NkM3BjTTU0MTFta0pzTEVKVFRab05lM0p2N0xRRWc5V1ArTTN2N2VOUzJ2TjJybHRtUEVxZlRCS3RBY1ltSU9hQmYvWEI4a1l0bDRnY2YxcFpLWWFPdHl2cEFOR2M5RUpFeFdrcWw2eDB3M0kvYlFDM2dGa3Z1RjdBeVU1QWpUb3NyNExjU09yMGZNMVFRNXFRQzVLOEV6MzlSaEsvK2t1TU10MGE4dE13SzluUHhJbDVkRUlHVDA3T3NoeStQL2xPVEFpSUVDS2xiVWNCcC85QnA1YU16R1ZwN0RhQjlrS3pxY3lLazdueGgiLCJtYWMiOiI0MDZkNzNiOTYyNTAxZGIxZWNhYTk4NzY1N2E0ZmMyNGYwMTllY2E3MDQ0OWEyYmVmMDRhMWE4NmRjZjQzZWNiIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBEcjlHM09hK2N2eUJmd3R3OC9VNkE9PSIsInZhbHVlIjoiRytMYXZ0MmhJN1VDZjlBOVd6eHF2VVR3SlZCYUVOOFpEWElEcFFXak91MVVFelhJOWx3R2JyaXZXb2lNQ29YcGh5V2JhZ0xhNkFrYTFuaHVmSkJ4RDBKVVdRVzlBRUtvTWEza0pGLytlQy84aEhKVWp0K2NvV1RqTlJkeTFhZVh4YWVTR3kvM0M5NmxJdTNpY1gyWThDZkR3a3FHaW1MZ0p1UnRpejNtRnNIcEdtNmRzN0U4dHpaT2lLSklqd3lEb0ovdGJmbWVRWURocy8rYVBxVEZuZW9KUmdNNXJiYzJLVTFmNEdzOEwwNmNxTjRUVDdNQVhBZkhaKzVkdllnN1VQUEpjaStieXVndTVWVDV4R0hvUGVCODhucDB6NC94UzFvbG9yam5mS1hFSUpyd3BWRUhIQ0ZhTVJDT0VBWHdwWFZZa2ZtZHNIc1dZVStkUkcrOU5GaXRjT0dBNjhUY3h1OHNBY3hiY1lpZEh5ZFhYckhBZDh1L0p6cjEwUVo2SDJEaDVDa1FLSHRYcEgvTmEyeVpjNGtZZGt5NFVhODlrb25HcDMrOHNyNkI4VG12aEoxZklYSDNwMVRHTU5wTXdBc3ZhcUh1ZVB3Y2FESjBWL2FRb3dmSHdUVkNWakxoK0JUcVZOMDhWM0FoaEdkMTFTOG9SblZzdllCd050UnIiLCJtYWMiOiI0ZGNhZDJlM2YwYjc3NjhiZjZlMGFjMmM2ZDg4MDY2NzAzMTMyMGI2NmZkNmFlZjFlNTVkZjM0MzFhMTY0ODE0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJjVkFuTWFmMit6WE5CNjR0cTAwZXc9PSIsInZhbHVlIjoiV0wyRzRBbHhVUDI1S2F5Q1Bialc5aUhuTTlGODNvRG5LTW5ybFhHemx2ZjRNUFlDVmZpQTU2K3B1T3BZcUtHWVQrd210WnR1MW5vZmJEc1VNSCtvZGJhU1AzOC9YazcyMUhTUjFmSE41NmlOeE1neTd6dzhzaEM0bXpEOGdJWU84STdQcS9LQWQvTHZLd3E5RHpIVUVLZGpzd0psb0ZDRzB6MzY0Q3JLZThnMUJzNFlKdmY3eTduUS9uNnVXc2RXbnc3RjQzVDR4bnFYclRQU1VCZHhvV0ZNMzNZMGtuUE5HN1djVzMyd3IxTGQ4MXVwbXZic1BMVWJwaWR0SGo4a2RGQmlPU3M2WnJaUHN6RnU5MndZN2NkM3BjTTU0MTFta0pzTEVKVFRab05lM0p2N0xRRWc5V1ArTTN2N2VOUzJ2TjJybHRtUEVxZlRCS3RBY1ltSU9hQmYvWEI4a1l0bDRnY2YxcFpLWWFPdHl2cEFOR2M5RUpFeFdrcWw2eDB3M0kvYlFDM2dGa3Z1RjdBeVU1QWpUb3NyNExjU09yMGZNMVFRNXFRQzVLOEV6MzlSaEsvK2t1TU10MGE4dE13SzluUHhJbDVkRUlHVDA3T3NoeStQL2xPVEFpSUVDS2xiVWNCcC85QnA1YU16R1ZwN0RhQjlrS3pxY3lLazdueGgiLCJtYWMiOiI0MDZkNzNiOTYyNTAxZGIxZWNhYTk4NzY1N2E0ZmMyNGYwMTllY2E3MDQ0OWEyYmVmMDRhMWE4NmRjZjQzZWNiIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472459420\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}