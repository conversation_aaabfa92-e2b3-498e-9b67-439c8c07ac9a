{"__meta": {"id": "X51dacd117a753c47c8814de181a52950", "datetime": "2025-07-14 18:50:33", "utime": **********.676486, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752519032.941586, "end": **********.676503, "duration": 0.7349169254302979, "duration_str": "735ms", "measures": [{"label": "Booting", "start": 1752519032.941586, "relative_start": 0, "end": **********.403245, "relative_end": **********.403245, "duration": 0.46165895462036133, "duration_str": "462ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.403255, "relative_start": 0.4616689682006836, "end": **********.676505, "relative_end": 2.1457672119140625e-06, "duration": 0.27325010299682617, "duration_str": "273ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021889999999999996, "accumulated_duration_str": "21.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.55635, "duration": 0.020329999999999997, "duration_str": "20.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.873}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.591414, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.873, "width_percent": 3.883}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.601191, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.757, "width_percent": 3.243}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1478319892 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1478319892\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1910820366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1910820366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1018762607 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018762607\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1827988129 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752518924393%7C51%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJTS1VaaERSRnU2MVV2bWpOK010Z3c9PSIsInZhbHVlIjoiNEMzNzN4UmFmNGpFSmxFTEVVcGlMTVpuSWQ2NnR1SVFuTXNmM3dYS0tIdmJiYTRPNk1aUUxGVnpoMW1waVRWK1drKzFaL2d5QjVMb1Y1Z081U1ZaTGtXNTl1ckQ4MWpXeHFqL2ljeUI3MW8xVEZwZUM2SHhTbEp0TDB4ai9kaThmRW15akFUdkxDQzhOOEhuc0FSWjZSeWZtMTBpdmE2eUJwOXJRSDF2R09RL01Hem9RUUFiUE04Z2F5QldrbDVvOHFON20vdXNQK3h5cFU5SnpPZDhEZ3pmOFZyS05XRGpid3IrTldsWDBFVnlhOFR6dFlkaDJjeWxBYU5KWGowQU8zaWpHdENWeEVQcjhWemxuYXgwbEdnVlpTZUZJc2ptZUJ0R1FqVlJ4azJhWGg2b3orUUhRV2Z0Q0hmU2xRcjNOWGVNSjYvZ2JwTHZJeXlObEJlZStPa0xxQ0hXVjRCalhLTE9RaHpoZUhTWFN6ZHRnbyszbGZ6aDFLWTdYOWt1M2FVbjdnNno3N1dxSklHUjE0UE9meDFjSUtSN1kxYlZkMHpBalprSWk0UGczWkg4STN2VGtCdlBGRjhvYTk1MFk0WkxRNFNKbEo0S3VkMXYvQWRFU000UFZqNGpma0Iya0M5bWM1ZmEwNzNwYzFKVEFmMkxvTGtYUUhJNFhoUHIiLCJtYWMiOiJkNWRlMDE4MTc0NzRmZDA3N2FkZjBkZGE5YWNkYTBjNzhlODk2NzNmZDFjNWUzNmIwOWIyZDZhZmYwOTJhM2E2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVaZkJjbUNUTXZrakFOTTRiNVNrVUE9PSIsInZhbHVlIjoiazdoZStEWFJjaUp5Zlg1Z0R5bW02NDJoK1JpVUo3eFljSGQrSVNaU2l4SDBZYnc1ejd2VkwzWlZQcGhXWkt2Z3hqL2pqVmd5TmhUYVpwR2FscEZia1BIZ3psQTk4WlZsSHJjUThZMThHQzlCM0RTNWYxTnVpVy9wV1oxVUhSZzN5TUp6YnIrK2JxRG44ek43R0tFTGF3LzAvRDFJWVdNZk14QmtXQThZbGZ5YTBRdmd0MjR6MTVxWXpPRzdVYVNsNlRIbTVHcFBackRENFFsS255UUY5K2N6TnhrakxVU2I1ZDl1TzNPbk1tRGJtaW1BVjFkdkg0ZS9LdVdZdzh2S2t2MXJTeEk0VkdPNEMycWJHVXIyYWs5VmlIRjc4Zy9mTlN6WkJXR1BFNEFWMDN3bUVwTms1dmdjLzBFNDNwc1Z6N1ArT2Y4cGtXbzZyc21odUU3bHNDRFh1ZlE1cy9hVzdjaUJkOWRiUzdPby9ITmxubzBlVHo1MmxCblcxcElEcWxTQlMyVmNCdTA1UENockFwN3NhWko4NDNUMllmZFgzeVk4QUwvWEl6ekxJN05sdk5uWElJaEgzTmRHeXVTK1B0cE1CdXBqSytPZzNwRHdHd21CYnJVcGlEVEl4cnYzL2V5VmtURGloUSsyNEVnWXlIOWZuanhJUWtwYTdYWDYiLCJtYWMiOiJiYzhjNzMwNzUwZWJkYjY1YzFhNmI0YzQ3MzE2NGFhNDlkMzk3ZWYxMWYzMDdiZGJmYjNlYzdlMGU5MzIyNjEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827988129\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-900090795 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900090795\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1935522251 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:50:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpKUGl6cDZOQ0ttT1EybElUZDkzbGc9PSIsInZhbHVlIjoiajJHdnF0WEtUQ2o5aGREb3BFbUJBbVUrdEx2QldtZnhlWlFFS29JKzFpMTVXaVI1STRpTEx2UzJtK2U1K0E4ekVubHFUa2YwT1o1ZTlVWUFxUmxtWEdyalE1VVdUOGFUVFo1ZFQ1VlBqU0JqN2RHUk5HS1FPSWFtUlhKeUM2TTdWR1h2dmdSTno3emFrT2tjWmFRTEkvQ2xSREdGYm5mbWFhejg1YVc4RzRiSWNzWU1DY09CMnVhL0JpL0J3dkw0cXBERHMxb0JzV3l4eE1lRkh3TU1WR0dFaUlESnNaa3M1L0hTWEQ3b3RwcnJ3ODdqWlJnc2d4eW9EaHdTbCtEcEdJUlRkY2Z1M1UrbTU2Q0dsQ1lzcEV1VTdXemFDL3hDZ3NpNlo4aUJJTFlGclNvNWhQaVd4NU8wNVVrZUZ3NzNZNWcycUt0RDVNQUNmbUloYzBmWG9uT1VXWmJDSnQvTHBlVGVteW9SNWZjcjFhZUVVUVduNDBHRnMwbThEN0hJMXNJZ2FUamc0d1pKVEVTb3hiWENrZ1NYYnRyb3JnK3BEamJLM21OMXlabURqc0FzYnFjZHNpWDVETGxiYVVpR245SE1DMlpqRityME94Z3FpTitrYWhqUUxnOXJFQ3h6WmdocURTdXZEc3drV29JYzk4NFpXK3pPcGxWbDkwZVoiLCJtYWMiOiI2NTUxODI4MjdlNTkzOTg4YmE2MGNmYzY4OTk1OTk5YzgwOGJiMWE2ODhmMGU0ZjM2YjEzMTM3YmY4ZTQ5YzlkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:50:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRxaDRSdkJYU1dXcXFCS1Q5TE9tZHc9PSIsInZhbHVlIjoiMFEvRVpRNkZML3VVV21HUlQzSWVscy9vN29ub1lhL1R4aEpGdmhnYzV6TDR1QnN0Q1dQYktTUmMxZ3BlcVJpZmkwNUpUYll4bE4vOHI2RzZFdnkrdytXK2ZZSHBwWmlVR3dQMmZXcjl6Lzd1Y2IzVjJhNlpmSVBJaDRIR1RGNmdsTmFOeVpqSHN6K3hEQmx1TUkwQk5zZE8yaUR6QklLV0NTTXNkd3ZrbWZ4aFpGeDF0MDFrWnBOK05kV0lPb2dDemFXdU1IUGhOWU9mV2JJNTVIYUJnTmxnSW96dFc0Q3JUcXdML2NWSUxVWFhMbjdJazVBdXF6RG9CdkFRdEFOTkV0eEdFcWMvY3FBOC9uTFVkZ1kzK01wOXRPUkgwZ2dDazZaSzd3VURUMnJaQ0lGOXhLUHZIWEZscVlzUTkrdlhPU1FYRFNVTUc0QW9vVTJ5ZTJ0VUtEUUk1V25EN2kwTmRLRW9RT2NEMktERHVXTVBtSllmYVZaL2N2TzBMT1dBOUpjRVlZK1VDbmVGV1ZiY1NMN2g4bkc2dW1HbFpjODFUR05pZTNJZ3JCUGlTNCt0d05SOHEvaVdNcEdSM2ZOUWRTOTRlU2pRS2tNVU03NGluQXppYm1SR3JVRTdnVTJ4UndsVStpRjk3QTZ5MVZuZWFNZXJJeVVqTUZ0M1lUbjAiLCJtYWMiOiJkYjI1NTBlNWYzY2RhYmU3M2JiYmIyMzZkZWNkZWI0NTFjYmVhN2NmMDVjYTBkYmM2NmNmMTQ2MzNlZTQ5ZmM5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:50:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpKUGl6cDZOQ0ttT1EybElUZDkzbGc9PSIsInZhbHVlIjoiajJHdnF0WEtUQ2o5aGREb3BFbUJBbVUrdEx2QldtZnhlWlFFS29JKzFpMTVXaVI1STRpTEx2UzJtK2U1K0E4ekVubHFUa2YwT1o1ZTlVWUFxUmxtWEdyalE1VVdUOGFUVFo1ZFQ1VlBqU0JqN2RHUk5HS1FPSWFtUlhKeUM2TTdWR1h2dmdSTno3emFrT2tjWmFRTEkvQ2xSREdGYm5mbWFhejg1YVc4RzRiSWNzWU1DY09CMnVhL0JpL0J3dkw0cXBERHMxb0JzV3l4eE1lRkh3TU1WR0dFaUlESnNaa3M1L0hTWEQ3b3RwcnJ3ODdqWlJnc2d4eW9EaHdTbCtEcEdJUlRkY2Z1M1UrbTU2Q0dsQ1lzcEV1VTdXemFDL3hDZ3NpNlo4aUJJTFlGclNvNWhQaVd4NU8wNVVrZUZ3NzNZNWcycUt0RDVNQUNmbUloYzBmWG9uT1VXWmJDSnQvTHBlVGVteW9SNWZjcjFhZUVVUVduNDBHRnMwbThEN0hJMXNJZ2FUamc0d1pKVEVTb3hiWENrZ1NYYnRyb3JnK3BEamJLM21OMXlabURqc0FzYnFjZHNpWDVETGxiYVVpR245SE1DMlpqRityME94Z3FpTitrYWhqUUxnOXJFQ3h6WmdocURTdXZEc3drV29JYzk4NFpXK3pPcGxWbDkwZVoiLCJtYWMiOiI2NTUxODI4MjdlNTkzOTg4YmE2MGNmYzY4OTk1OTk5YzgwOGJiMWE2ODhmMGU0ZjM2YjEzMTM3YmY4ZTQ5YzlkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:50:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRxaDRSdkJYU1dXcXFCS1Q5TE9tZHc9PSIsInZhbHVlIjoiMFEvRVpRNkZML3VVV21HUlQzSWVscy9vN29ub1lhL1R4aEpGdmhnYzV6TDR1QnN0Q1dQYktTUmMxZ3BlcVJpZmkwNUpUYll4bE4vOHI2RzZFdnkrdytXK2ZZSHBwWmlVR3dQMmZXcjl6Lzd1Y2IzVjJhNlpmSVBJaDRIR1RGNmdsTmFOeVpqSHN6K3hEQmx1TUkwQk5zZE8yaUR6QklLV0NTTXNkd3ZrbWZ4aFpGeDF0MDFrWnBOK05kV0lPb2dDemFXdU1IUGhOWU9mV2JJNTVIYUJnTmxnSW96dFc0Q3JUcXdML2NWSUxVWFhMbjdJazVBdXF6RG9CdkFRdEFOTkV0eEdFcWMvY3FBOC9uTFVkZ1kzK01wOXRPUkgwZ2dDazZaSzd3VURUMnJaQ0lGOXhLUHZIWEZscVlzUTkrdlhPU1FYRFNVTUc0QW9vVTJ5ZTJ0VUtEUUk1V25EN2kwTmRLRW9RT2NEMktERHVXTVBtSllmYVZaL2N2TzBMT1dBOUpjRVlZK1VDbmVGV1ZiY1NMN2g4bkc2dW1HbFpjODFUR05pZTNJZ3JCUGlTNCt0d05SOHEvaVdNcEdSM2ZOUWRTOTRlU2pRS2tNVU03NGluQXppYm1SR3JVRTdnVTJ4UndsVStpRjk3QTZ5MVZuZWFNZXJJeVVqTUZ0M1lUbjAiLCJtYWMiOiJkYjI1NTBlNWYzY2RhYmU3M2JiYmIyMzZkZWNkZWI0NTFjYmVhN2NmMDVjYTBkYmM2NmNmMTQ2MzNlZTQ5ZmM5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:50:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935522251\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1405126948 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405126948\", {\"maxDepth\":0})</script>\n"}}