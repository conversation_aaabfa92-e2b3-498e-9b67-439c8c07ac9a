{"__meta": {"id": "X3456e78d60796c1940d528e3816c7986", "datetime": "2025-07-14 18:30:40", "utime": 1752517840.01452, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.578263, "end": 1752517840.014535, "duration": 0.43627190589904785, "duration_str": "436ms", "measures": [{"label": "Booting", "start": **********.578263, "relative_start": 0, "end": **********.947886, "relative_end": **********.947886, "duration": 0.36962294578552246, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.947894, "relative_start": 0.3696310520172119, "end": 1752517840.014536, "relative_end": 9.5367431640625e-07, "duration": 0.06664180755615234, "duration_str": "66.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016380000000000002, "accumulated_duration_str": "16.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.975281, "duration": 0.01549, "duration_str": "15.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.567}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.999332, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.567, "width_percent": 2.503}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1752517840.006246, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.07, "width_percent": 2.93}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1268440051 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1268440051\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-268695270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-268695270\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-153161243 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153161243\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1865695126 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517825171%7C44%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9MdkhBck9pZnF5OXFkWk5WcHB3U1E9PSIsInZhbHVlIjoiVU5VSnlpVXYvUC9NNnBXZG1yS0dGTUVyRE1scklneTZuUHBjWWV0ZUYvZGxMZnIxNmZpUmhQWlJrV0RIUFNFeU90WXFJUkdFampvUitMRGxsRngwS3d4eVB4UW9nOWFNRGVZaDRtbmMwYkRkNmdOaFVOQkw5NlBnVkZPQmtQZnp6c3NwQ0VOQ2VvV1lpVXpTdnNDOGlxVTN6OVBMMTI1d1VPa0dVMlBvT2xMSnhjSkpnNURMYXA4L0FEakR4bFBCU0RRNzMzU1F6cnV4UXoxNkM1ODdaditDNitqNVJpSmFVWmNIZ0J5cEVjK0tXNWEyYm4yS3VZdnE5NUNkYWUwRGUzKzRZZVdoNDAzZTNlUkJ4eXBIazNrR2NxbmZCMGRVZEJpSFJZV3QzT1hIdnNwZmZlL2FnYzVpTDVKbW9qVWRVdFhDWTk5bkIvMFkvVjFBdFY5am5xejJaMVJqa3ZxQ3dWWFJjS2FFRVMvcEtRZXlMQUV5WG9SS0lxQVVTWUFaNXZ2cThBS3pFWUJEWUdyczlySmtSalRWc1VqR2hoQlV0SUdSM3pLNEMxZ1NxVHVKQmcvM0pTR3FncnVmYkc2aG5vNUttNDhVYW1ubEN4WXppQW8ySHAyUHRGSG43dmNadUFwR005ZzZEMTBvdUtmcXVFOHh3MURrVHQzVHZESE4iLCJtYWMiOiIxMzJjZjBiYjZlNGFiNDY5ZGU3ODU2MWM4MTZiYjc5MjU1MTA2ZjZkNWRiYTU5ZTFhMmNmZmVmY2U3MzVhMDU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZaQnVDcjB0c20zSEVIUCs0Z2w1Z3c9PSIsInZhbHVlIjoiNEdnai9TbkZodjFiWjc1UitGVFkyY3lRS0Q5VTVGK3FkaFcyQUdyLzkzUGE4MkZuR05NZzJSNEhVSWtpeVpqQTh6TjZ2akt6aTZKcDhuRkMxb2pvNStWRnRydTlSYk50YU1JWjNhUGZlUDR2OWJCTFNyaUsxU3prUlJieU1DMlo3L3lvbVgrdzc4UlhVR3RKbTlXVnp4RmdsaXZ4SnE0WlV3SitHWmhsRGxOeWJkQXdTK2pqdjNIeDEwK0lGbVdEdy84MVIra21uY3N1SVUwL3VnU0xyazl1Nm9SbytoS3BQVDVyUjZIdjRMby9HclZFVHE4R3pvYkorUUNqMGVBRWRsRklDSG0wOStDSTJjOUc5aGRZV0U1enB0b0V1M3k4Y3ZNdG5hQUtPT1NQQmJLbW8yYnJNWnBveEhWZTQyWHFYTjZMTUdSdzh3SWVGQ2srcFZwRW4xRDJxT3E3TWZ1alI5Y1E5Mzd0cmJXOUZrcXh0TVUzYnRqYXIyTitmWVFuRHc5T3ROWmFtZWczcGpFQ20xekFaaEllTlRzS3FiaExWUm1uNUNLR3ZDUzdKdGdYZUVaaE94Sy9EVkpyVWE4TEZuMDhKYmJybE83MDRYdlJYSzN3QWltTGhPc1NJUk5BbFR4d0UyaTc0Y3YyVllQcSt2MC9UdFFJRms3a0puWTgiLCJtYWMiOiJiOWIxYTcxMzNmNTMyZDc5ZjIwMzRhMGIyY2IwMTkxMjM1MmJlNWZjMjU4M2ZjODBjNjc4ZjI0NjUzOGEzMDAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865695126\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-753433758 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:30:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZtWjJUTHNSdkttaGtJTzVuaVVYRnc9PSIsInZhbHVlIjoib2FKNlUxaWhScW1USEZpbzZxVGNPOHlMOUdBUy9Cb0ZlVGxhaFlXR29zQnZuV255TDZGVllkSThoVk5PL0I5K3NQbzB0OTlRc1BaOEdvL2pJNUFabXVrdEF3UFRiaSthUTFTMWhZK2g1eG9pNGxWZmd6eU40T0RqR1ZjQkFKVWZnR3Mwdkl0RElRaVIyRFZVK0RXakV4T0tMdTdWTzk2WFIyZmlGOUdodTNMNldTSU1ObVUvVHpzZU5lSlF4WWtOd1c3NEZTM3JkOUsxS2lWRjVycU1VL1BjcTQ3cDJuczFrdDhybDEzRjJubWdZWDM4UE9hM3lrR2prVGdFSEZxQmdLTnpLM20zTjJvbXhyakgzM016YnJLeDVNcVJrWjlhNmhaWHNLWVR5NlZucVdubDdJUGphQUphb3I4eUhOTmY1eFhuOUZ5QUMzY3pqaVlhYk1DV21tSUpUd1Q1QyttSHNaV3RNQzNMRHY0L21vMy91cmt6S0xqODJXV0UvZDhiRnRWN0dJZW1NS2NaZlRFb1oxcXZBNUZRMnpDN3oyVEFrRW4zaHRPYzZDcis4dEdtV1E1UWRGeWVOQWtFRGhJRm9HemNBWDBKZFZmb1RtS04rb0pBV3hTSzZ6dkN0eENwcFo1dnp6cGlhQU9acVcwaDVSWk1adzhIYVBlOWdPaEEiLCJtYWMiOiJlYzNlY2NhNzc3M2Y4MTBiNzA1NjNlOTZjZmZmZGY4OGVlYjViMzMxZDJlOTAxNTNiOTk3OGNjNjI5MDMwYzg1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9VN00ybnI0OEZ6RFRXbFlsOTF1d3c9PSIsInZhbHVlIjoia2RKeFBVazUvMWo0SldZR3U0WXBwc1lVWGNmUElSalJpbGtSNEFKZW1jMDJFaDM2Wk5rMk1ELzNXYjdVYklxUWF0UHlkVlczajRmRzZoVEVVbGlKcXkvQmw3YUpsaXFrM3VCSGJZRXdXY2VIU3VZTnZBZ2pOSUVEbGZ0aTB6dElRTTd6dmxOVjYydTA4VDFmenJ4WTd6K0ptYkNyME5EbEVHaHZ6KzJnV1JzNVhhaWpuQkpEMlBnOWwyY0tTWlJZeFIxaHk4SjhKWk1lQytKK1pDOTFERkRacExOSUIvMXZFVmc0U1pXSGprOXFzWWhVUzVRVWNMOFR0WkRPblE5Q2NJL2NGTEV2NXI1bEQ3ZlNzRXlSTjdLSXdSSUkvNUdwWDFkRWhQaVUxUjI2azhXc0ltd01PRFp0aFdzRkxRVDJCZEF2aGZ4Zk1rQ0c1bWkzVm96TEpPZWFoZ2V0d2ZrdThzY0RtQTE5V2E2RzNuM0dha0FwK1NDeDFoa1BWdWswMTl1Vk9FYlhSays5U29GS0UzSUp0YklrN0dLTTdnUE8yVFZTalBhZURKd0FLbTJPMDY0K2NlWHhyZHcxdkVxcTdIQVlvcGNwTU5HY0F0dGFGMm5WQWxpZUp1RWxzUmtkVmt0T0o3djBjWXFrN0hmL2xBZTRUTEc0WnI4eVdrdE4iLCJtYWMiOiI5YTI3NjQxZTBkYzM1NjZjN2QwOWMwYjkzMzQ0MmVkNjBlYmFjY2UxODcxN2JiZGQ3YzRiZTdkY2NmMGNjMzYwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZtWjJUTHNSdkttaGtJTzVuaVVYRnc9PSIsInZhbHVlIjoib2FKNlUxaWhScW1USEZpbzZxVGNPOHlMOUdBUy9Cb0ZlVGxhaFlXR29zQnZuV255TDZGVllkSThoVk5PL0I5K3NQbzB0OTlRc1BaOEdvL2pJNUFabXVrdEF3UFRiaSthUTFTMWhZK2g1eG9pNGxWZmd6eU40T0RqR1ZjQkFKVWZnR3Mwdkl0RElRaVIyRFZVK0RXakV4T0tMdTdWTzk2WFIyZmlGOUdodTNMNldTSU1ObVUvVHpzZU5lSlF4WWtOd1c3NEZTM3JkOUsxS2lWRjVycU1VL1BjcTQ3cDJuczFrdDhybDEzRjJubWdZWDM4UE9hM3lrR2prVGdFSEZxQmdLTnpLM20zTjJvbXhyakgzM016YnJLeDVNcVJrWjlhNmhaWHNLWVR5NlZucVdubDdJUGphQUphb3I4eUhOTmY1eFhuOUZ5QUMzY3pqaVlhYk1DV21tSUpUd1Q1QyttSHNaV3RNQzNMRHY0L21vMy91cmt6S0xqODJXV0UvZDhiRnRWN0dJZW1NS2NaZlRFb1oxcXZBNUZRMnpDN3oyVEFrRW4zaHRPYzZDcis4dEdtV1E1UWRGeWVOQWtFRGhJRm9HemNBWDBKZFZmb1RtS04rb0pBV3hTSzZ6dkN0eENwcFo1dnp6cGlhQU9acVcwaDVSWk1adzhIYVBlOWdPaEEiLCJtYWMiOiJlYzNlY2NhNzc3M2Y4MTBiNzA1NjNlOTZjZmZmZGY4OGVlYjViMzMxZDJlOTAxNTNiOTk3OGNjNjI5MDMwYzg1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9VN00ybnI0OEZ6RFRXbFlsOTF1d3c9PSIsInZhbHVlIjoia2RKeFBVazUvMWo0SldZR3U0WXBwc1lVWGNmUElSalJpbGtSNEFKZW1jMDJFaDM2Wk5rMk1ELzNXYjdVYklxUWF0UHlkVlczajRmRzZoVEVVbGlKcXkvQmw3YUpsaXFrM3VCSGJZRXdXY2VIU3VZTnZBZ2pOSUVEbGZ0aTB6dElRTTd6dmxOVjYydTA4VDFmenJ4WTd6K0ptYkNyME5EbEVHaHZ6KzJnV1JzNVhhaWpuQkpEMlBnOWwyY0tTWlJZeFIxaHk4SjhKWk1lQytKK1pDOTFERkRacExOSUIvMXZFVmc0U1pXSGprOXFzWWhVUzVRVWNMOFR0WkRPblE5Q2NJL2NGTEV2NXI1bEQ3ZlNzRXlSTjdLSXdSSUkvNUdwWDFkRWhQaVUxUjI2azhXc0ltd01PRFp0aFdzRkxRVDJCZEF2aGZ4Zk1rQ0c1bWkzVm96TEpPZWFoZ2V0d2ZrdThzY0RtQTE5V2E2RzNuM0dha0FwK1NDeDFoa1BWdWswMTl1Vk9FYlhSays5U29GS0UzSUp0YklrN0dLTTdnUE8yVFZTalBhZURKd0FLbTJPMDY0K2NlWHhyZHcxdkVxcTdIQVlvcGNwTU5HY0F0dGFGMm5WQWxpZUp1RWxzUmtkVmt0T0o3djBjWXFrN0hmL2xBZTRUTEc0WnI4eVdrdE4iLCJtYWMiOiI5YTI3NjQxZTBkYzM1NjZjN2QwOWMwYjkzMzQ0MmVkNjBlYmFjY2UxODcxN2JiZGQ3YzRiZTdkY2NmMGNjMzYwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753433758\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1814112821 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814112821\", {\"maxDepth\":0})</script>\n"}}