{"__meta": {"id": "X5107b42b84cadb4f0098ebc7a7a1f00e", "datetime": "2025-07-14 18:17:51", "utime": **********.452778, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517070.981019, "end": **********.452793, "duration": 0.4717738628387451, "duration_str": "472ms", "measures": [{"label": "Booting", "start": 1752517070.981019, "relative_start": 0, "end": **********.374727, "relative_end": **********.374727, "duration": 0.3937079906463623, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.374743, "relative_start": 0.3937239646911621, "end": **********.452794, "relative_end": 1.1920928955078125e-06, "duration": 0.07805109024047852, "duration_str": "78.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989544, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02397, "accumulated_duration_str": "23.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.404007, "duration": 0.02323, "duration_str": "23.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.913}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.438331, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.913, "width_percent": 1.46}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.444454, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.373, "width_percent": 1.627}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1592874504 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1592874504\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-809783398 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-809783398\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1301259546 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301259546\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-416076950 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IkM2cGlaZ2pTc3F0NTR5QXNsQkdERlE9PSIsInZhbHVlIjoidlhZcjVsSWNDYks1cElNeSt2K0Ftdz09IiwibWFjIjoiNjBkOTY5NzJiODI0MzdmNzBhNzhmM2Q0ZmE5YjQxM2JmYzE2Yzg4ZWQ5MWI4ZTI4YTRhYzQzZGM5ZTJlNjhiNyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517069148%7C19%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVkUTM4MUdJWGdXaVQrd0NaOWZZL1E9PSIsInZhbHVlIjoiamVSd1JaWlJGZ0RZZDhnMmVlWnp2T1dBODAyaUdCQnB1bjYxdDFIZEp0bS9GWnNOZ3ZteEg0VGVBRXZlRXI5K2JVd1ZhV2FhanZVK05jOGVIWitiekpVaCtCVFQ2TFpVZWtNSlQ5VWNxR1czQ2t3VDNQUVV1a1FXUlBLYnk3TTl1a0JIL3FtNVY5Q25OUm5uK2xVRjBkVGNoVDhwV0NnN0lsTU5UOWFCL3FxYXVHc2xTaVk3Tjd6ZHp2MDhIeUVzRjd5Z2JiWHNOaWRZWEYrZnFUaStJT0lzWDI3TjRKYmFtWndvSjRCRG9MQTRDLzVHUXVNR1BXS0R2TUV0eUQvVmV2WW5EZWRFSWt1Nk54UVRMaHhaamswYnpHTlVqQWViOTZyZXlxVEorN0d1VFpaZEYvL2d1T29zTUlSckFmNnpEeENGbFp6OGZ2NHRXRXFiRER4Z3IyTUtTUGNoYlBuRUJZeUU5SDVFelAzMGhMek5US1gyWTE2bllZT283T0Y4SnZzTzdKRFRHTUhmaElQUno3d3VvTHdFb1IraTJnQTlQUUpnbnI3akhtdlE1VGpjOGFOdDN2R0Y3VTY2SFI1dlZrUXRsaUg5S3R6Uzg5eDNhOWd5b2NyUjFVTU9TMksyZmR1NlkzdU5Tb012MGh2MjJvSUxlZWpwbktUSHAyUjMiLCJtYWMiOiI3ZGI1Njc1MGIxNDE1MTBhOWM5N2JkNjNiNGM5NjUyNWQzZjczNjA1NDhlYzVmZGM2M2RmYmVlMzFiMDVlNjhlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFxUzNyOU1NdXI3L1o5T3N0TjBpcmc9PSIsInZhbHVlIjoiRzAxZ2NpM0JsMGVQdStQOGh5WVFhYjBqOTVocC9MMjN6SlhwUWROWjlIWTcvQ2VZTUc3T2JFbEJiQUtvbzlDMW9FUWdLTDEvakdaTnd2VFV0UXNXdWpOeERQUTJFRm5FVjVnQnYrazE4WlZHVFRxMUZzS2p2Rk9aZkw3NkdBK2xSTk1JLzhlU05FT1d4aW95eEdmQU1kRGc5U0xMQjlyaG1YZHlVbXRncXNMcEFJbEp6NXFENVJ4K2dVVXFBRWJHZXZwcFZTcE5iSUFueEU2aWRsWVFyZE9mcHFtYndRQ2JnNGUvWFRzM1BHQWQ2ZGlJRmdBTENKZmQzdHB6SFpGRkJGb3dNNXpUTzdsdUJQMG9MNjYwb0Q3OWJyWGFDN0ptMTNTaDd4bFdUOGNPUkNTb2sxbHVGKzZYM2NBZFB1Skl0cGFqWDV6NFB5SndZK0lDblB0blZPZFlLT09jdkxoRDNMOEdiVU96eG94S3VFZGl5ZENKK1RlaUdCVjJ5NnRxT2U0ZDczY3lyK1B1dGtLTkNZa2NiQVhMemhQcVc4L1dVQzNDRjBpWlo5UVl1UVkyV2RJUnpDY0JMZXdYU0xlODFFTHZ6d1ZaSk84ZmFzellDcjhJSldUeHVsdk1YbG9NUCtqTWQrSVBHRS9iZjJ5M0huWE1yZmg2Um9ZaE5WTWEiLCJtYWMiOiI0Nzk1OTE4MjJiZjg5NmE0MzRiZDAzOWVlZGZjYWRiZTkyNWU2NzdiMzgzYWIyNTczN2M4Nzc2NmYyODFiMTZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416076950\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-740605650 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740605650\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-848436913 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldLRThzTStIZCtINmRMQlUvWlRER3c9PSIsInZhbHVlIjoiNWZXZ2srRDRONmpvK0ppYmZ5NUFZMVNIR2hpNUVyUWMvVmhqUkVUUHh0T1d5TkRPajdkZVZTdEJoeHFBTzlTT2Jldi95REN2czF3cVhSazBrWU1vb3k3Q0I1SDJXcnNHRUVtekZRYTE3VEpMUEtUQ1hoNTNGS0J6Nm1QUkk0SHBkQXJxY1ZXSUFNcW1FT01mN3J1WkdJV0tPcEhteEFmVWFwcjFNTmpPMjdHTGtxUkZYZWJlTklmeEs0NXlXQm5DMG50TGZxeWdhQVZkZGNKMVE5WWszUmhaemxzRWh1SUx2RTd3SEphWldPdmluK0FrVW1tQVlGeUNlb1owcTBySkROWmhOZUtvWHpwWEc1VmgvYm51NnRsZFlzM2plMTJSYTNOUzEwY00rNkVlL1l1R2xDSyt1Q3JpU2FOM25xQmxMR3Ruc1BIQ2lUSjU1REFYOGxVNDFYR2FnN0dPZzFud0FFLzJobk1lY0hyS0pXWHlZNlZyc3hiemdjWm9OYmpla3R0Y1ozTk1hdkp4c01WcEVrclFNSllIWEo5aEpNQ3NtSDZsMlBSZmhPOWtBYUYyVW5LTEsxSTJSYVYyYi95NGloTkdwZmMwRFRvRkltaWR5RDZQc1JBbmcrc2YwQWllOFlZWFQrZDJ1QzAzVFBHMmJQWjM5OGZzcFFnaUxJZUoiLCJtYWMiOiI1YzVkMjM4NTRhZThmMDBlYjMyYTRkMTM2N2M3MmRiMGQ3ODIxZTIxNTJiN2VhMjM2ZTQ3ODRmZjc0MTMxMThhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InMwa2EvdldsUW1rd3ZVNDNBblNqMVE9PSIsInZhbHVlIjoiRDU0QmxYTjN4bDNOYzFqTTZIUGw2ZjZPcmxabDM1Q3RSNEt1L2Nsd29JTFowdjhGdU5NdGtuUHNabUdtVGhuREhDY0tIdmpFMFNpRzkyR0dXbzJmN3Z6YkxJYkR4SlJzQW1WejZNVURwWXBRbmNIbEZjSE8yRHM3eFQ1aXE4Z2FOVjVWZ3puN0lUaHFadXN5OXQxaUxVS2l4NmkwVFExdS9GTWNSbjFpYjRWN2VLNXJwSEJRMzV0ZFg4dHh3WkFWQjdOUVBtOVJzNWRuVm16LzQ5U3BZYk5jWWJ0bXFwcWMvcjBtek5LQzgrTTRzS1VoaEZyZ2JoYm8ycHphcDRQMGZUWExITG9seU8yWUs1VllURUVTV2N1Ylloa1RRalJoZkhZWUJGMmFZS3dQY0w2TmlsZHJxZDJWZjhucnFqeXp5OWpCMS9ub2hMYUdFMm9NYkJiZWxaNS9OWUlzZjc4MnN0cllxM0VPSDR5a05Hb3podzRDZ0JHWVlRTWdkWXZNdC9rekZYVGd2OVBLQmFaYkFsYWxPUGMwUXlsMVdJV0REMWJieHloRERKcWJmdTZUcHJoK0dQU2VDczltUktaYTYvSm9OSjlKOERVdEVkQmoyZlltNWZ2eXZGQ2pzNGFHUHUxRXpoZDNmT3BFZ1NCMG44Y1E1ZHh4Y2xPU1hBcWciLCJtYWMiOiJjYjgzY2M2MTNhOTMzNjczZmM0ZDAyN2FkYzRiMzU0ZTY0MDVkNjAwNDYxNmJkMzYwN2FjOTJkYjkyZDIxMmJhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldLRThzTStIZCtINmRMQlUvWlRER3c9PSIsInZhbHVlIjoiNWZXZ2srRDRONmpvK0ppYmZ5NUFZMVNIR2hpNUVyUWMvVmhqUkVUUHh0T1d5TkRPajdkZVZTdEJoeHFBTzlTT2Jldi95REN2czF3cVhSazBrWU1vb3k3Q0I1SDJXcnNHRUVtekZRYTE3VEpMUEtUQ1hoNTNGS0J6Nm1QUkk0SHBkQXJxY1ZXSUFNcW1FT01mN3J1WkdJV0tPcEhteEFmVWFwcjFNTmpPMjdHTGtxUkZYZWJlTklmeEs0NXlXQm5DMG50TGZxeWdhQVZkZGNKMVE5WWszUmhaemxzRWh1SUx2RTd3SEphWldPdmluK0FrVW1tQVlGeUNlb1owcTBySkROWmhOZUtvWHpwWEc1VmgvYm51NnRsZFlzM2plMTJSYTNOUzEwY00rNkVlL1l1R2xDSyt1Q3JpU2FOM25xQmxMR3Ruc1BIQ2lUSjU1REFYOGxVNDFYR2FnN0dPZzFud0FFLzJobk1lY0hyS0pXWHlZNlZyc3hiemdjWm9OYmpla3R0Y1ozTk1hdkp4c01WcEVrclFNSllIWEo5aEpNQ3NtSDZsMlBSZmhPOWtBYUYyVW5LTEsxSTJSYVYyYi95NGloTkdwZmMwRFRvRkltaWR5RDZQc1JBbmcrc2YwQWllOFlZWFQrZDJ1QzAzVFBHMmJQWjM5OGZzcFFnaUxJZUoiLCJtYWMiOiI1YzVkMjM4NTRhZThmMDBlYjMyYTRkMTM2N2M3MmRiMGQ3ODIxZTIxNTJiN2VhMjM2ZTQ3ODRmZjc0MTMxMThhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InMwa2EvdldsUW1rd3ZVNDNBblNqMVE9PSIsInZhbHVlIjoiRDU0QmxYTjN4bDNOYzFqTTZIUGw2ZjZPcmxabDM1Q3RSNEt1L2Nsd29JTFowdjhGdU5NdGtuUHNabUdtVGhuREhDY0tIdmpFMFNpRzkyR0dXbzJmN3Z6YkxJYkR4SlJzQW1WejZNVURwWXBRbmNIbEZjSE8yRHM3eFQ1aXE4Z2FOVjVWZ3puN0lUaHFadXN5OXQxaUxVS2l4NmkwVFExdS9GTWNSbjFpYjRWN2VLNXJwSEJRMzV0ZFg4dHh3WkFWQjdOUVBtOVJzNWRuVm16LzQ5U3BZYk5jWWJ0bXFwcWMvcjBtek5LQzgrTTRzS1VoaEZyZ2JoYm8ycHphcDRQMGZUWExITG9seU8yWUs1VllURUVTV2N1Ylloa1RRalJoZkhZWUJGMmFZS3dQY0w2TmlsZHJxZDJWZjhucnFqeXp5OWpCMS9ub2hMYUdFMm9NYkJiZWxaNS9OWUlzZjc4MnN0cllxM0VPSDR5a05Hb3podzRDZ0JHWVlRTWdkWXZNdC9rekZYVGd2OVBLQmFaYkFsYWxPUGMwUXlsMVdJV0REMWJieHloRERKcWJmdTZUcHJoK0dQU2VDczltUktaYTYvSm9OSjlKOERVdEVkQmoyZlltNWZ2eXZGQ2pzNGFHUHUxRXpoZDNmT3BFZ1NCMG44Y1E1ZHh4Y2xPU1hBcWciLCJtYWMiOiJjYjgzY2M2MTNhOTMzNjczZmM0ZDAyN2FkYzRiMzU0ZTY0MDVkNjAwNDYxNmJkMzYwN2FjOTJkYjkyZDIxMmJhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-848436913\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1409232118 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409232118\", {\"maxDepth\":0})</script>\n"}}