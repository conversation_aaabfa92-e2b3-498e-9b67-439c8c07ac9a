{"__meta": {"id": "X1b7ebb27895c37593ded34d40b27f65a", "datetime": "2025-07-23 18:21:17", "utime": **********.856871, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.41667, "end": **********.856886, "duration": 0.4402158260345459, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.41667, "relative_start": 0, "end": **********.787504, "relative_end": **********.787504, "duration": 0.3708338737487793, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.787513, "relative_start": 0.37084293365478516, "end": **********.856888, "relative_end": 2.1457672119140625e-06, "duration": 0.06937503814697266, "duration_str": "69.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01769, "accumulated_duration_str": "17.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.816311, "duration": 0.016739999999999998, "duration_str": "16.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.63}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.84162, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.63, "width_percent": 2.205}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.84724, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.834, "width_percent": 3.166}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-voucher/55\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-820846663 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-820846663\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1949953218 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1949953218\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-986333282 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986333282\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1921646048 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/receipt-voucher/55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294875191%7C13%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikpkc1dMWjMreUNRN1RLdjZmNC9nWmc9PSIsInZhbHVlIjoiOGpPUENIZ3hnRndGaS9QcjdEUld6djUyVVNRRjVYQXROcTJhVk1GSDAxNlFXZXZKdVQvK0RwWWIyWEtRQkdmU2orb0l3WmJtMzB6K0Mya21MQWhIenpTY2tSdUJJTnRHZ2NZelEvODgvdDJMcnFHVVpXWU9rZ1cxTlhYUVJVSEtnUnhtN1V0dmhkcHhOMktFMFR6VmhHbklUTjZNVlQwTFVXSTBqTkQ5em84LzB4cm13SjVMd3kwN0ROR1J5dkZpUHRocC92dCtsbTgyalNHdkJaZUd1STVIYkplTDQxbDd0bTkzSk5HekcrcWJZOE4zZjdtOHczb3QyOFhGcTRWV3BjR3BydFpvMVVZUjZtVlFmWXdOSEQrZ2NPalhOMjNnNkd5VU5vY0ZVZlA1Y01FN0g2SUxqc2tNelJrK3doeUxoK0pHV1phbkczMGZTa05yTWpzaXQvMnVGYXFmVmQ0Ykh6QjJjTTk1ZWpGaHpNOUMvVjcwNDI0dGRTbklwL0NSSmNkaEZhZzN0U3NEaVc2cWhHZFZXM1QySWFGTkJTWWFWVDByRERleERScjZzdjBJZVZTQ3NOcjI4WnBxRjJUT1FTNlpXdzRUTGZOYXByYURnRjBUSzRDZzVuRktocWNXRlk4a3BGaUcxa0JtZWxYNU9QM01saWN3aVJYN1FVSFAiLCJtYWMiOiJiZjhhM2ZkNGNkNTliOGNkYWZhYWE2ZjM5NzkzZTc4ODcyNzVhYjg1MDgyYWU5OWJjNTNkMDU1NTNjZjdmOWY0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im12OVpiYTY4TVloVUZYdUNVQ3RKSEE9PSIsInZhbHVlIjoiQnNBUkV4ZTAzbUI1SDh3cWtKY1NPTEY3N0MrZi92UlBvMmRZczlzRGVtYzJYVlA3MTRrQlE3eFZOdHNMa2NXY3JPY1BxbVVYa0dpZHcvWEhUbDhPRENvSUlGdm5WWGZrRHlQclh4UEc4UjBVcnFaWmZtT2hydTU5N2pFeUc0QjN4SEx4bUZVUnAyOGR4dEZ5UGxkNkdlc1ZiLzZGNER0MHVXbUEreWw3NnVsRUZmRCtwdVZYZDcxbDA5S3E2dElZZ3YyU1JSRVV4NU9FZy9WbkR5UkpJR0ZWTktJS3V2eWNkdGxpbUdEbWExdWY1ZHpNSlJna1k4endYUnh2Q2RseTBXKzFJYStUQysxV2pDdVRVeXJMSnFIWElZMXhKMzVmYndWbk5qUHlvd3dHVDAxb2xHWkhUV1N0Z2ZnVXBlUUNoQzlxakF0aDhFWnVRNy9vVElQTUprWmUyNVZ2VWxaak5hWEpMbWhpMGMySVNLRUxjWENlWGoybktzcGp6clpTL3UzaWhxVVovSEU4b1VGd29SeHJ0dVpRR2VnSCtiWG1XbEU3Q3V3eWVXZDM1d01HankyK1pOWXkzWDhXNDhKbkJUOU1XMUJkRDNMTUNHcCs2YUorWTlCK0Nrb0wxeXg1UDVUY0c3LzI0b0FRUlR4ZWNMSXcwS0VZdEZxWkt1UkUiLCJtYWMiOiI4ZGZhNDVjYTZjNWM5ZWUwZjZiNTk5N2I3ZGRhZjQxMjllYmY0NjIwNWQwNGYwZDMzMWVmYWQ2NDk5NDZjZDhhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921646048\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-162598011 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162598011\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1618936330 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9nVklJdnZzanErOG1ncHRqVDVFREE9PSIsInZhbHVlIjoidFZUYjRoVndPQWVPUEFDUG5zbUN3c1VLUEtMT1Q5dWFsbVhUWCtNb3dmRVZnSUx1dW9tOWZoTnptT0tRTmpxckEyVnROR0Q5VUhVUFFCcXI4VE41SFA5Y0l4RysvOXBrQjNtQWtwTGNrbGNvMFUrLzZnQ2ZjdFN5ZUVXT2dIdDRBRDE1NXg0Qmo2OU84VEEwTWcwQkoxeWkxUXlSSDN2anhNNUpiZWZjZE1qdjVOWFp2NXE0WG9BVkNQd2xxWVA1a2JrMjFxSWU1T0NlbzgydFZBcGxlaVVoOU5VZThZUi9kcWZCaHRNc2lSVVpjcVlBbTN1T2ZkSXVCeTRoY0s2VEtOQ1VoLzZlbFpNVVlNSDZTZk1hMzFIUHhsS2dtK0FUU3BPdG1USVRHYjNZOW1oaFR6QktSeEphWHNYM2IxZDNBOU5YVldHSTl6R1EyVkJXNVdjdk9lV0tKSVM5MXZibUhZVWdKWUlYY1ZzOUcyZFUzZTRIaVJ6U0dPdGtKNTh5RC9sR2dWeEd5QUlsbG9pODVPYk9sWXpOd3pkRVo5WnM0ZWtYaTI5eHZRUzl5Zi9JSWdhTG9yLy9CYnlaL0lDajBwMVlzZ0V3SCt2ZEpiSExiajlrSUhCY0RtdkNTeHBIY2RBbVJvYWxqdHJaYjY5c2MvdVMzblh6bzFQQWt4SUsiLCJtYWMiOiI2YWM0OWI0OWRlZTU4NjFmZDU1NWM3ZTY0ZWU1NjIxNjI4ZWMwOTk2ZDk4MGM3NDYyMzZjZjcxYzc3MThjMGE4IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpybndpTDBmeWQwbkRBMjlMMFE1SHc9PSIsInZhbHVlIjoiT3p3c3QzaXovVXg2K0Z2ckI5Y2JIcXhaY2FqUkdHcHhmeE9pVHpyQjVqZmhUdG5sQWtyTnhBODJubGpQTjRMZER3SThpRHBHaTVEWnNzUjZCYlZoeWFOa3lhV1ZYMkw0WmJpbmRCVFp1QzhBdWh4aVcxRERUa2dPWXFXOGR4RlFoaGx2Uk1zY1B3NVI4K3J6REJXOS9XRzExb2R0Qk01b21Pb0VJRUx1dU1mOHJ5Rzk0a3cxUDlSeCttZUtnN3N3alZ1am9yY3lZbjFOeUhuL3o2UUlneFlzNkNFUW1sQWFtcWtHdGdRSHphbnA3VWJ0eG00T2xSMkF3VE1vSURCVUZScnVZc2VrUWNpRkwyV1JDd2F0Z1hOdndwQTN5WkNRdDFkODVrREx6NTYzdGxDUHB1L2lvOUJJaHNhc2VsNkI5WDVzbmVXaHo0eWRQa0VFS3NWMVdMOEt4VTFRT2lQTXU4Tm8xSnlCbFlXelBRbE5NV3lSbnZBQ1VweUJvV0hZYVNaMk03NHVnYmxXSG5pMXcvNzc0Z1VTa0NPQjZqTEwvQWZaVUViUGlFWnpWZWZxYnNFa3V1L21mbW83K1YyMkRjTGcrSmZmTXlxaHZEYmZhQ3RKaGFtekthS3hBeTN0eE9rcWJOampMQll1em5nM21neVovbXZDaHJNMnRoU24iLCJtYWMiOiIzODVmMGJmNjk0ZjFiM2ZhZDk4N2U5ZDE4MjY0NTNlYjQ2OGJlOWUxOWE0NjU3ZGUzMjZjNTM0MDkxNmRiZmMyIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9nVklJdnZzanErOG1ncHRqVDVFREE9PSIsInZhbHVlIjoidFZUYjRoVndPQWVPUEFDUG5zbUN3c1VLUEtMT1Q5dWFsbVhUWCtNb3dmRVZnSUx1dW9tOWZoTnptT0tRTmpxckEyVnROR0Q5VUhVUFFCcXI4VE41SFA5Y0l4RysvOXBrQjNtQWtwTGNrbGNvMFUrLzZnQ2ZjdFN5ZUVXT2dIdDRBRDE1NXg0Qmo2OU84VEEwTWcwQkoxeWkxUXlSSDN2anhNNUpiZWZjZE1qdjVOWFp2NXE0WG9BVkNQd2xxWVA1a2JrMjFxSWU1T0NlbzgydFZBcGxlaVVoOU5VZThZUi9kcWZCaHRNc2lSVVpjcVlBbTN1T2ZkSXVCeTRoY0s2VEtOQ1VoLzZlbFpNVVlNSDZTZk1hMzFIUHhsS2dtK0FUU3BPdG1USVRHYjNZOW1oaFR6QktSeEphWHNYM2IxZDNBOU5YVldHSTl6R1EyVkJXNVdjdk9lV0tKSVM5MXZibUhZVWdKWUlYY1ZzOUcyZFUzZTRIaVJ6U0dPdGtKNTh5RC9sR2dWeEd5QUlsbG9pODVPYk9sWXpOd3pkRVo5WnM0ZWtYaTI5eHZRUzl5Zi9JSWdhTG9yLy9CYnlaL0lDajBwMVlzZ0V3SCt2ZEpiSExiajlrSUhCY0RtdkNTeHBIY2RBbVJvYWxqdHJaYjY5c2MvdVMzblh6bzFQQWt4SUsiLCJtYWMiOiI2YWM0OWI0OWRlZTU4NjFmZDU1NWM3ZTY0ZWU1NjIxNjI4ZWMwOTk2ZDk4MGM3NDYyMzZjZjcxYzc3MThjMGE4IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpybndpTDBmeWQwbkRBMjlMMFE1SHc9PSIsInZhbHVlIjoiT3p3c3QzaXovVXg2K0Z2ckI5Y2JIcXhaY2FqUkdHcHhmeE9pVHpyQjVqZmhUdG5sQWtyTnhBODJubGpQTjRMZER3SThpRHBHaTVEWnNzUjZCYlZoeWFOa3lhV1ZYMkw0WmJpbmRCVFp1QzhBdWh4aVcxRERUa2dPWXFXOGR4RlFoaGx2Uk1zY1B3NVI4K3J6REJXOS9XRzExb2R0Qk01b21Pb0VJRUx1dU1mOHJ5Rzk0a3cxUDlSeCttZUtnN3N3alZ1am9yY3lZbjFOeUhuL3o2UUlneFlzNkNFUW1sQWFtcWtHdGdRSHphbnA3VWJ0eG00T2xSMkF3VE1vSURCVUZScnVZc2VrUWNpRkwyV1JDd2F0Z1hOdndwQTN5WkNRdDFkODVrREx6NTYzdGxDUHB1L2lvOUJJaHNhc2VsNkI5WDVzbmVXaHo0eWRQa0VFS3NWMVdMOEt4VTFRT2lQTXU4Tm8xSnlCbFlXelBRbE5NV3lSbnZBQ1VweUJvV0hZYVNaMk03NHVnYmxXSG5pMXcvNzc0Z1VTa0NPQjZqTEwvQWZaVUViUGlFWnpWZWZxYnNFa3V1L21mbW83K1YyMkRjTGcrSmZmTXlxaHZEYmZhQ3RKaGFtekthS3hBeTN0eE9rcWJOampMQll1em5nM21neVovbXZDaHJNMnRoU24iLCJtYWMiOiIzODVmMGJmNjk0ZjFiM2ZhZDk4N2U5ZDE4MjY0NTNlYjQ2OGJlOWUxOWE0NjU3ZGUzMjZjNTM0MDkxNmRiZmMyIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618936330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1412148513 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/receipt-voucher/55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412148513\", {\"maxDepth\":0})</script>\n"}}