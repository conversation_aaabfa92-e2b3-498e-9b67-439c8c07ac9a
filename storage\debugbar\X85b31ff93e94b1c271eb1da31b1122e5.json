{"__meta": {"id": "X85b31ff93e94b1c271eb1da31b1122e5", "datetime": "2025-07-14 14:39:38", "utime": **********.831766, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.359733, "end": **********.831781, "duration": 0.4720478057861328, "duration_str": "472ms", "measures": [{"label": "Booting", "start": **********.359733, "relative_start": 0, "end": **********.771812, "relative_end": **********.771812, "duration": 0.412078857421875, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.771822, "relative_start": 0.41208887100219727, "end": **********.831782, "relative_end": 1.1920928955078125e-06, "duration": 0.059960126876831055, "duration_str": "59.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46068000, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0030099999999999997, "accumulated_duration_str": "3.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.811393, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.389}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8234951, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.389, "width_percent": 16.611}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/journal-entry/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-562238947 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-562238947\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1834339197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1834339197\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-734065982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-734065982\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-393231101 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503670543%7C15%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZtcjNkQk5sVWRPcVRDVTcxZGl6bWc9PSIsInZhbHVlIjoiR1VyeUllV3JFQ014c3M2MHcreXIwanBSbUphL3NYLy9uV1hDMnMyeDdyQ3FaTDZwN3drMnl1ODJoY3J0NTFLVTd1enNvZjN0L3FMVi93VERDU0FvMU1PSTdjSXBoSU5JbDZFcEs4Q1JmaVRWd3Z5eCtYOUx1d2RlMVdGOTQzMlVQYnZUcTRmWXphSXpObDRGb3NvRmxueUhzYlJYc1FMUklqNlNQQmRxT0ZlYmxlclExcFlVcTVBSVdFZGh2TDRVWlV4THEvb3NUSDBZaWsrOGlCTFBHYTArZVBabG8rT3dMbkhIU2ZCbkJNZ3ptQW4wbGorNllLbW1XSGJwVHFlMTVqQ095ZnA3Q2lsNFFLLzhUNURFdXgzRDRHUm1rampQbktOd1BZbTBBSWlzNnNLSHIzd2Z0TzZuZXhiMmw2WEFaVFY3RFRBTjRnK2owTHI2K2pnQTY4OEZQbDBacGxvd1dUcEk4SU95THpRMG4yRGZKazd0K2dsaE1ZVGhVRm43ajhHcWpDYjd1VTBqVVl5eFYvM085WDloVFZuNU1kMy9JVFhRY2hpZC91dktuT2krL25hNzVSaUkwYWlTUW1kZjhpeTZMOXM0a3JIa252WVdpYXpyTzlhdDRtaWZKeFBmbXV6QnhENmRFZnRESVV1SHhvdzFUTFdjWmtub096Z3QiLCJtYWMiOiJhNmU2YjNhMTUzYTBlOGFmNThjZjhjOTk1MmFkMWYwMzE2MWVhMjk3YmVkMDY5Nzg3NTA3M2EwODI5N2FkMjVjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjAyb05KVUVseUNBUDdxRDhmSWRMSGc9PSIsInZhbHVlIjoiTGU4Q3Z2SkphekcrMUVJMkNPeFdsMTY3OHdQdkJXN3JlRGtMc3hialF1bHpWZFpQQk9pTHJGVllRelVyd3hESjN2ckdRdC9XQ2FYbytLSjl1S0l6aGtmNng0UzM3Z0pIak9vRzFicC82R1Jya1czU2JTcy9DYXJiUzRHcnhMSjRqMzM1WGFjUGxVZG81aXNYOG1SZ0xhR2Z1cVV0SlFyS3hQOFFNaEJpMWxXMndFOS9WZ0wzcHMzZ25DRm5HendOd2VTWHp4dC84d1h1RDRnaHhGR1JrQ3VMbk5UL3BYcFdURU1RdHQ4eWlnc25HZzI3M3oxS3JtYlBmc2xFbUkzaUNUOXNVenZEV0VVaFlIZ2pnRTFOQUpMZmhnMlpWb3piV2RhTENvMTk2Y0tpQzVTR1ZMbHhrTTUxNEdpeCtOOUU1MnJROUgvZUdTeHh5eXJocjRBRzQwTHB4Qzd5M0d5RUVNUFhuVGFDWlZVS00wZ3pLb1g0VlR0cU5ueHJDazJMUGx2WkdCWGxNTjB0ME1EVjlVWUNvVnUzUmFMRVh5Z3Y3Vi9CVjRmRmVzS3k2MERMY01NSEQ0Y0hnaU80ZkM3UWNEQ2ZiSEJodWNqWUxuR2pQd0pEemw4TEIwbk1CRWpiWU9TVStlMG9jSUpxelJBQk5Na1hwUkpRYWRVQisvK2YiLCJtYWMiOiI2OGE1NjYyYzg1MDE5OTc3ZDkzMjZkZDZhNDQ0YWNjMTI2OGFkMTFkMTdjOTIwMTk4YjBkODZjNjdkZGZjNTg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393231101\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-998407586 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998407586\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-32263943 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:39:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9LNm1EaHZyajFxWUx3Y2V0YU8vRUE9PSIsInZhbHVlIjoiVUJFV2N6SkZiblQxMVBNZFN1U2RoYWxCcy9iMHRoM1pkclc4SGNFTlV5TE5YaVpjNFNhSzlqc0tmSGJnRHhhUkR5NEJoSldCSm9FbndHRGJQajk1UXloZFR0eXdBS3ZNbnBnMFVwVmdWSzJmaXBwV09FN0laQTNtVXhxRUFTWnIyQ3dMbHltVzBTTzcyaDdKTU9EN1p6M0ZZd0JJVjZnanZqRGhxZ25sMEdDSklKYktxZ2Jya0wyZFlndjcwTjk4dCtYcDExNnJ4YXMwREluNHVMLzBldmU0SlJwa0xmME9VY3JwRGxWYmNhem9ZSlZjUXJJNVQwQVZFRENMZDNiV3d0eHBQTVV3bDFhNzhNWUJ3UVZTQzNUY1RuNXZhMmx0UEYvbHZjY2kyaGdKNDNJbk9LNm9yOWk5OTBvWFJ3SW43Tm4raGxHc3diblNaVktJMldFczA0UlJTOVFIZnNLeHB2dWNtY3RhdlJYbFlyLzNkQUtzV3cwNjZxYUlLaXZYL0hock9YblFHRm5BVVlLa3RUQ0hQRVZQT0Y5Rmc4ZjN0OEkzc1hVeVNaODhtbDBXY3UyYlNIUXZOVkFWSlFXaStCL3dEMHJqamhab0lZWFJ2L1VaSzBKNDBkN1FveGNFT1hhemF5MHdvN0xjWFptTHNUbkRXTmtyWi93TUI5c0IiLCJtYWMiOiJkNjlhMWIwYmU2OWRiNTAzNjZjNzRjOWMwYzY2YTg5Y2Q1YjIyZjY3ODBmNzU5MjhlYTA3MDNmOWE5ZDI2NTdhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJwTTlrQi9oOTdxQ21BZEtMVm5RWGc9PSIsInZhbHVlIjoiZGxpdWd2RlNTeTVxREkwb2QzcWpWZWlUWmxkNjc5b2ZJY2FNN05MZ0Z6N2NHM0xYYXpvRlk4MUdSMmdaUklYaURURit5a2xGMThMVEpESHhObExyUjJkNTU0TzFnbk5HdUM3blJYa0paMkdBdHpJTzFPL3l0cnBkMkVOOXJIeUYzdFZjVlpJdWZYM0lPbDJzUC9IVjhMU0VpLzFXN3hnVHNwMTREcWxuRWFGQzMrdUJ0T200eEU1RUpjdE1FVk9PajM5amoreUM1WHNmaDU0NmQwQWhmQUpTdFBuT0F4TnRiOU02cVhpKzJZSVFTbllNL21NTTBlazQ5Rk9ia0hCWFpCMld3Z0RHMlU4M1Q0ZGFkeTJpZTgyVU5kOTExQnJRditQeldvbS9Cb1VKZklGeDVBWkd1OUlnNjhOMTZtWU9lMUtoYkpYVzlFc2l5N05xbnc2c2RNQThrNUM0TEVwVTJUeGVxUVJEdkVLZ1FCaVFaVlFFYi94K2J4MjZQQmVuK1NtT0FKSHU2NzhjRE5Yck1YcVF5WXNpM1lJNHlwVnZHOW9kWFBVcVY5TWt0MDlNKysvWEloZ3h5dG4rakJYRlNyZnNaVXZNN1lSajBHa2tLWEoyR1ZibE5jTERBNnFoSVN6U05uTSt4Z3JDczZWQUFpdU5ZeVQ0YTh4NzMxV3IiLCJtYWMiOiIxMzIwMTkzMDZlY2RmMGUwMzc2YWY1ZmM0ZTNiMDhlMGU4N2NmYmJmOTgzODNiMjdjNmM5Zjk2MDA3NzAzOWUwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9LNm1EaHZyajFxWUx3Y2V0YU8vRUE9PSIsInZhbHVlIjoiVUJFV2N6SkZiblQxMVBNZFN1U2RoYWxCcy9iMHRoM1pkclc4SGNFTlV5TE5YaVpjNFNhSzlqc0tmSGJnRHhhUkR5NEJoSldCSm9FbndHRGJQajk1UXloZFR0eXdBS3ZNbnBnMFVwVmdWSzJmaXBwV09FN0laQTNtVXhxRUFTWnIyQ3dMbHltVzBTTzcyaDdKTU9EN1p6M0ZZd0JJVjZnanZqRGhxZ25sMEdDSklKYktxZ2Jya0wyZFlndjcwTjk4dCtYcDExNnJ4YXMwREluNHVMLzBldmU0SlJwa0xmME9VY3JwRGxWYmNhem9ZSlZjUXJJNVQwQVZFRENMZDNiV3d0eHBQTVV3bDFhNzhNWUJ3UVZTQzNUY1RuNXZhMmx0UEYvbHZjY2kyaGdKNDNJbk9LNm9yOWk5OTBvWFJ3SW43Tm4raGxHc3diblNaVktJMldFczA0UlJTOVFIZnNLeHB2dWNtY3RhdlJYbFlyLzNkQUtzV3cwNjZxYUlLaXZYL0hock9YblFHRm5BVVlLa3RUQ0hQRVZQT0Y5Rmc4ZjN0OEkzc1hVeVNaODhtbDBXY3UyYlNIUXZOVkFWSlFXaStCL3dEMHJqamhab0lZWFJ2L1VaSzBKNDBkN1FveGNFT1hhemF5MHdvN0xjWFptTHNUbkRXTmtyWi93TUI5c0IiLCJtYWMiOiJkNjlhMWIwYmU2OWRiNTAzNjZjNzRjOWMwYzY2YTg5Y2Q1YjIyZjY3ODBmNzU5MjhlYTA3MDNmOWE5ZDI2NTdhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJwTTlrQi9oOTdxQ21BZEtMVm5RWGc9PSIsInZhbHVlIjoiZGxpdWd2RlNTeTVxREkwb2QzcWpWZWlUWmxkNjc5b2ZJY2FNN05MZ0Z6N2NHM0xYYXpvRlk4MUdSMmdaUklYaURURit5a2xGMThMVEpESHhObExyUjJkNTU0TzFnbk5HdUM3blJYa0paMkdBdHpJTzFPL3l0cnBkMkVOOXJIeUYzdFZjVlpJdWZYM0lPbDJzUC9IVjhMU0VpLzFXN3hnVHNwMTREcWxuRWFGQzMrdUJ0T200eEU1RUpjdE1FVk9PajM5amoreUM1WHNmaDU0NmQwQWhmQUpTdFBuT0F4TnRiOU02cVhpKzJZSVFTbllNL21NTTBlazQ5Rk9ia0hCWFpCMld3Z0RHMlU4M1Q0ZGFkeTJpZTgyVU5kOTExQnJRditQeldvbS9Cb1VKZklGeDVBWkd1OUlnNjhOMTZtWU9lMUtoYkpYVzlFc2l5N05xbnc2c2RNQThrNUM0TEVwVTJUeGVxUVJEdkVLZ1FCaVFaVlFFYi94K2J4MjZQQmVuK1NtT0FKSHU2NzhjRE5Yck1YcVF5WXNpM1lJNHlwVnZHOW9kWFBVcVY5TWt0MDlNKysvWEloZ3h5dG4rakJYRlNyZnNaVXZNN1lSajBHa2tLWEoyR1ZibE5jTERBNnFoSVN6U05uTSt4Z3JDczZWQUFpdU5ZeVQ0YTh4NzMxV3IiLCJtYWMiOiIxMzIwMTkzMDZlY2RmMGUwMzc2YWY1ZmM0ZTNiMDhlMGU4N2NmYmJmOTgzODNiMjdjNmM5Zjk2MDA3NzAzOWUwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32263943\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1979912322 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979912322\", {\"maxDepth\":0})</script>\n"}}