{"__meta": {"id": "X25565b180ac9d3e5c8a1cc6807a976d3", "datetime": "2025-07-21 01:18:28", "utime": 1753060708.017287, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.579004, "end": 1753060708.017307, "duration": 0.43830299377441406, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.579004, "relative_start": 0, "end": **********.944897, "relative_end": **********.944897, "duration": 0.3658928871154785, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.944905, "relative_start": 0.36590099334716797, "end": 1753060708.017309, "relative_end": 1.9073486328125e-06, "duration": 0.0724039077758789, "duration_str": "72.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46536168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01282, "accumulated_duration_str": "12.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9715428, "duration": 0.01219, "duration_str": "12.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.086}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.992347, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.086, "width_percent": 2.886}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.998704, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.972, "width_percent": 2.028}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IlBlcTI1M1F0WW1JL2lLS3dISDBiZEE9PSIsInZhbHVlIjoibHE0UURHQ3ZJeUpyWGVtbWRScm1TUT09IiwibWFjIjoiZjljMzQ5Y2YyYmQ3NTBjMDRjODBlNDU5MDMyYjgxNTc4YjBiZTE2MWMwNzk2NWVmYjQ0NGYxZjg1N2QzZTljMyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-88193872 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-88193872\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1310596916 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1310596916\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1012174395 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012174395\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1695066667 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlBlcTI1M1F0WW1JL2lLS3dISDBiZEE9PSIsInZhbHVlIjoibHE0UURHQ3ZJeUpyWGVtbWRScm1TUT09IiwibWFjIjoiZjljMzQ5Y2YyYmQ3NTBjMDRjODBlNDU5MDMyYjgxNTc4YjBiZTE2MWMwNzk2NWVmYjQ0NGYxZjg1N2QzZTljMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060692836%7C9%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpLVk9hczVyM0VFWW9VdHp0QWx3cEE9PSIsInZhbHVlIjoiSFRrRFUrYU80MHd3UTRNVVA3MEs3NG5SL2gxRzZLdW1COVBHT0tpdE9NMlBQaDY1MXgzOGRPSER4dHpIWGcxcHFvQTJkL0RHNTFmb29RVUMwQ2VRclNpUzJqSnJvRHJtckRqRnJXcS8vREJWSGxWc2o5MzRab2hla2xKVVVZTnZnc3kwZ1BIczc2U0x3K2RjMlk2OEJXSzdoODdjZ2xnQkZjUGt2dThFNU1HMTRKZ0RXVXlCVWRFTDBJdWpVNmNxc1BxOVI3TG5kVVQyUlo0OGVrTE84WWxkNG9IVnE4ZXhRNU5sb0sxV2orbkNtcElFVkU0dGFyblV2SHlxeUZiVmNGWXRqYmFxMUlpS0t2Mmw4VUluUXAvdC92dk1HWXgycEpPMzJTMnBHNzZWcmJ3ZzJkRnZRZUxqY1BEQW5xeERScjBzVi9uN29BTis0cHh0UHZzdDIyS29ENHUzdUpwZm9RNFV6VTRuWmRnMFRQWWJYS2duV1NyelUvN290YXRNNlZ5cEZZN0hKdDhCWGp0dUxsQXdVKzBLcjcyMFh1MndNVXprSHlhODFUekpQYXFiYytxRnlYTGlWZVltUVRCUG5ERmV0a2t5T2tiVkN5MnkvMFlYZVdJWXVBUFRkSy9WNjBkWGtJOEZ4cHdUS3B1eGdpOTdvL25SYXRJYzFVdWsiLCJtYWMiOiI0YTMyYzZlYTc3ZjcyMjlhZmQxODQ1OWMxOGU4MTRkMDVlMTEzZTAxMjUwZDA4MWI3ODcwNzkwNDE2YjY2MGQ4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVtY3Y2SmpUN0xIUjNiZi8rUVc4K2c9PSIsInZhbHVlIjoiQ2lpaXNNaVVwWDRtSkNYbW1NK3RBcHNzL2pMLzd4VHp2Y3JhTHdGRzVjcGZNcEcvMHFTM2oxeFMwZk5mR200cnFhSytQaS9yT0ZvVDJxbEVwcUo2dVFYd0hwU1F0QVdWVUEwS1NtNysvbTU0RDB3T200M1pySlNQQUZ4TE1QU2tydkM1Nm5GZFU1Vi9HR2g4Mnk4WHdkSmtDSEhCUXhrd2szcy9BSHFyaS9VbEsxTU9BZzYwUVB6NWxNbVVoTEx6aHRqWDZydVo2SUlMc2VHaVY1UnV6akJQSGVOeXpkZTJtaFhrSUdOTkJJY1N3TitKWUVDcTI2V2dJK3R0cUp3bEgrb21GSWJLWDV1azA3Smh0SXp3ZHJoZDBtbTR1UEFmQWlVQWN2ZnNua2VZUmM0TVdMTE9MbHowS3F1c29JUjR3dG9nQ1pqdndQc25KVDN1ekRVVlZaNWpIdVl5Njc4Z2hYdVVTWjRxL0szV2RoZ0ZrVzVCUjlzU040Y2k4UmJkU1llbklFQ0FyOEhnaXR0cWhidWdYaFJtUTFPbWZQNzJrN3loMmltRU1pb0w1MlkrbTh6NVpnWG9DM3FBd2dhMzZ3WGt4VTVwVmtrbE9NRHZFQVNSWEI4dnVhUWhoRzNUMVJwQnR1SVRCWGVKZVRZYWZQWTFsTm5tWEI5eVp5SG4iLCJtYWMiOiIyNGY1OTQ2YThiZDRjNTA1ZTAxZDBhNTc5Y2YwY2UwZmVkN2ZkMmFhZTIxZmUyY2NjOWYwZjMyMzgyODc3MDdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695066667\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1330997495 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1330997495\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-994685424 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:18:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFGODFaL2prZnJYMUJZYTYxZ2k2ZGc9PSIsInZhbHVlIjoieC9hUE1nekFiTWVqdTgrNTVjRklHQXl6Q2d5TU1taEY4NmxkOGI2clRMN0g3OW5ORmFyRy9HWVVGaW12a3ZaN1ZQeG53TDdERHp3SnBhUjhZK3NvQ2dLYndhN05HczV2Skg0UHZROHR5bVFyV2ZEUFdVblI2T2RQdjhEdnJUb1ZSTXlpVlRwcEg1bEU2NDNRZlVKeGhYNFB6NHY4aWN4c0ladTlKRk9TSFZtQU9mNkZ3L2g4V2hWSGM3bDVUM1praENBOUZSeFRXQzlHZjBBSlJKWkp6OGZRdUZwT3lSb1kzM1picjJFUUxoVml2dnh4QktpdlVvL3pYU3lacTRaUFhYc0l4RmVJTUkvSXhteXpnU3dYbmdSWlp3dVlIQXF6YUhFaXdycnJzNHFNeUVBK1F2MkcwWkR6OXpzM0ZMdEdVNHB0WGNSQVRwczN3Y1VHOE1PYzRtQkVEV1F1NDcvWmRMK2pPRFcrTmpVNFJEdkQ5TGlhL04rbWRiZjFmemlXWUZLR3F4eEJoRFFXNzljNkFjTDVGR1lIY09DNTg1TGJ4WDgyWHpkWHJTeEYvRlNnaHZ6MGMzQU9hNkRHTXhGcUlNVlhrakN6dGtQYUo3Wk1GZCtZN251ZjlTdEZHdGxkNFE5aVpTV3EyMjIzdGpZY3ZvS3F1WjVEdDIwa3RNS0ciLCJtYWMiOiI0YTNhZTJmZjg0ODE4YjZkYTViYTI0M2ZjZTMzNjFiMTQyNWQ0N2E0YmM4NTg4YTczNjUyZGU4NDM2M2JiOWNjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImQxcEVzMUtCR05ST1YydlhMakQ2cnc9PSIsInZhbHVlIjoibFZENlQrNzBZdlhTQnc2b01zWUJJOUNpTnY5dW5rSENSNUt5Kzh5U2ZYMmVJZ05WMzE1d2tNZXhRWnl6Ni9RNzFXbnp5aXNxallGRmFXam90cVlhcGRFZk4yVzdXVVFrcGVoTlI3U2hpeloxTGd5SjFpbWpjMjcyOGZibEJZTEgyNjMrQzE3eXlKTGJMMWx1RVBMTmY1dGl2Mm1vMzhLdDdzelkvM01ZWlh5MG9ZWjhrVVkzNG1pZlNwRlNyaFh0dGNRRHpSM1NsZnhxcHl2VjQ5OXBXdG1NY0hrQUMzNDF6WWtLbWcydDZXS1I1ZnJtc2FHRnI4TVQ5d3ZtY2lXa2t4RkRUU3FvNk45aHc4d1p4ZDZ1dW1MaVJKRUVEKy9hNlM3dGVCeW5IRHowbW80T0R5WWVKcE1aWnVzY3IxZm9hSjBhMUZwV05VbGM4SWlERXhNSjczaFBhRDFTVFBYNXIxcGhVdmhDOEw5dkNOSmVPSlAvTHNzb2haRCt3NHdGNkFxMThzTXR2N1UyRlZCdGlORWg4SHE3SGp3a0gyVVl1MHpMSGJXa01QWWpTZ2k5OS95K2tTMEJlUys2SkhMWW15UnpXQ1dwZ01BNkhLdjB3SlNvc2I0TkVYbnhjeEh3eFc5ODJBM1VZb0dNK0NmOXFETDYzT3dQOGFnbkdKY0oiLCJtYWMiOiI0MTA2MGQ4NjhkNWVkNzQ1NWI0NjU5YmFlZDE1NmU4NmE2ZDUyNDk1YWZmYmZlNjgxZmU0NDczMTkzYmU1ZmE4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFGODFaL2prZnJYMUJZYTYxZ2k2ZGc9PSIsInZhbHVlIjoieC9hUE1nekFiTWVqdTgrNTVjRklHQXl6Q2d5TU1taEY4NmxkOGI2clRMN0g3OW5ORmFyRy9HWVVGaW12a3ZaN1ZQeG53TDdERHp3SnBhUjhZK3NvQ2dLYndhN05HczV2Skg0UHZROHR5bVFyV2ZEUFdVblI2T2RQdjhEdnJUb1ZSTXlpVlRwcEg1bEU2NDNRZlVKeGhYNFB6NHY4aWN4c0ladTlKRk9TSFZtQU9mNkZ3L2g4V2hWSGM3bDVUM1praENBOUZSeFRXQzlHZjBBSlJKWkp6OGZRdUZwT3lSb1kzM1picjJFUUxoVml2dnh4QktpdlVvL3pYU3lacTRaUFhYc0l4RmVJTUkvSXhteXpnU3dYbmdSWlp3dVlIQXF6YUhFaXdycnJzNHFNeUVBK1F2MkcwWkR6OXpzM0ZMdEdVNHB0WGNSQVRwczN3Y1VHOE1PYzRtQkVEV1F1NDcvWmRMK2pPRFcrTmpVNFJEdkQ5TGlhL04rbWRiZjFmemlXWUZLR3F4eEJoRFFXNzljNkFjTDVGR1lIY09DNTg1TGJ4WDgyWHpkWHJTeEYvRlNnaHZ6MGMzQU9hNkRHTXhGcUlNVlhrakN6dGtQYUo3Wk1GZCtZN251ZjlTdEZHdGxkNFE5aVpTV3EyMjIzdGpZY3ZvS3F1WjVEdDIwa3RNS0ciLCJtYWMiOiI0YTNhZTJmZjg0ODE4YjZkYTViYTI0M2ZjZTMzNjFiMTQyNWQ0N2E0YmM4NTg4YTczNjUyZGU4NDM2M2JiOWNjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImQxcEVzMUtCR05ST1YydlhMakQ2cnc9PSIsInZhbHVlIjoibFZENlQrNzBZdlhTQnc2b01zWUJJOUNpTnY5dW5rSENSNUt5Kzh5U2ZYMmVJZ05WMzE1d2tNZXhRWnl6Ni9RNzFXbnp5aXNxallGRmFXam90cVlhcGRFZk4yVzdXVVFrcGVoTlI3U2hpeloxTGd5SjFpbWpjMjcyOGZibEJZTEgyNjMrQzE3eXlKTGJMMWx1RVBMTmY1dGl2Mm1vMzhLdDdzelkvM01ZWlh5MG9ZWjhrVVkzNG1pZlNwRlNyaFh0dGNRRHpSM1NsZnhxcHl2VjQ5OXBXdG1NY0hrQUMzNDF6WWtLbWcydDZXS1I1ZnJtc2FHRnI4TVQ5d3ZtY2lXa2t4RkRUU3FvNk45aHc4d1p4ZDZ1dW1MaVJKRUVEKy9hNlM3dGVCeW5IRHowbW80T0R5WWVKcE1aWnVzY3IxZm9hSjBhMUZwV05VbGM4SWlERXhNSjczaFBhRDFTVFBYNXIxcGhVdmhDOEw5dkNOSmVPSlAvTHNzb2haRCt3NHdGNkFxMThzTXR2N1UyRlZCdGlORWg4SHE3SGp3a0gyVVl1MHpMSGJXa01QWWpTZ2k5OS95K2tTMEJlUys2SkhMWW15UnpXQ1dwZ01BNkhLdjB3SlNvc2I0TkVYbnhjeEh3eFc5ODJBM1VZb0dNK0NmOXFETDYzT3dQOGFnbkdKY0oiLCJtYWMiOiI0MTA2MGQ4NjhkNWVkNzQ1NWI0NjU5YmFlZDE1NmU4NmE2ZDUyNDk1YWZmYmZlNjgxZmU0NDczMTkzYmU1ZmE4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994685424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-846988799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlBlcTI1M1F0WW1JL2lLS3dISDBiZEE9PSIsInZhbHVlIjoibHE0UURHQ3ZJeUpyWGVtbWRScm1TUT09IiwibWFjIjoiZjljMzQ5Y2YyYmQ3NTBjMDRjODBlNDU5MDMyYjgxNTc4YjBiZTE2MWMwNzk2NWVmYjQ0NGYxZjg1N2QzZTljMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846988799\", {\"maxDepth\":0})</script>\n"}}