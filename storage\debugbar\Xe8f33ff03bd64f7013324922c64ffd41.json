{"__meta": {"id": "Xe8f33ff03bd64f7013324922c64ffd41", "datetime": "2025-07-14 18:57:29", "utime": **********.609435, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.164638, "end": **********.609449, "duration": 0.4448108673095703, "duration_str": "445ms", "measures": [{"label": "Booting", "start": **********.164638, "relative_start": 0, "end": **********.551312, "relative_end": **********.551312, "duration": 0.3866739273071289, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.551321, "relative_start": 0.38668298721313477, "end": **********.609451, "relative_end": 2.1457672119140625e-06, "duration": 0.05813002586364746, "duration_str": "58.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46003728, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00321, "accumulated_duration_str": "3.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.585419, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.994}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.595334, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.994, "width_percent": 15.265}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6018, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.259, "width_percent": 22.741}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1114376757 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1114376757\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-643644077 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-643644077\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1612363722 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612363722\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1005135624 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752519037242%7C52%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFyRU5ieStrZFFGeUtqTkQ5Uk1MTVE9PSIsInZhbHVlIjoiRDRnZmRJVkNtN0x5Zit2SXRHN2lpaGZIZWNuK3AycDBSWDI2WFNRZVVGOHRVZUE3RXI2WlZuNkt4VHV4VjdySTJoWXp4elUrcVJTVkRRN1ZmY01vY1FNTWE0SStUYUtxTnQvd3ZJb3FRbGluaXQ5WHlLU25xR3lGQnNsOXNCaXozV3VQeGVrQUlpR2h0OG1oaVEyZ3FKRlA0OFl4dXZRUUQvK0lKaHlVcmJHTHBaMlgvYkxscDVtV1A4cko0T1FwU0tRYW1NRzJBNXhLS1FhS2syT3BGS0NNRU9NMlFCWEZWMEZMbzRySDNkVGhPUGNuK2NDUkdoSTZFV1kyeGU1YVBpWEFzODRpSkdpZnFrZENrUTJOU1I2TzNDZEFUMS93V09IREFUU3FYSVhGTFkvYXp5dnR4SEZ5VmVRVGRwV3l3Wmlpd0JnQldrblZISk96ZXFYY3o3NGxYd3MrYVhQQUkwOU1PNU9NM1dyeHNubTR5SWFndjVrTkZkS1hiU0s0RXZrRCttWHdUNi9XY1huQkpTWGhHemZ3TXh6NGdUV1g3eG9FaVJiTXVhT3hSWVREVkN6Uk14bzJDMU5jQnBKbHBML2E3VWFzNTYwV2VYVnNRMWFkczdKbS9zSlhGUW55bzJyL3pLc3B5V2hZWGlxbHJIbHFNeWxQSmtNazFVOEUiLCJtYWMiOiIzZWVkOGEzYTQyODA3Nzg4Nzk3NTg5Y2QxZjZiODRjZjMyMTNiODUzNWVmMGJhNDcwOTgzZTRmNWMzNDAyZGZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9KQ1lJSms5MFg1NDQxc0NMeFg2bXc9PSIsInZhbHVlIjoiVlV5b3JXQzYvWTQrQ3FrSVI5S0czR2xUMmR5VmxBRVJCQy9ENnBnL3h1UVZ0QnNXalBxZDVsWFdvaVkzdjd4RzVhb2gxLzhNNUQ2NkQ5MS9welkzNjIva2hpc2lEN0RyY2dJalVFRTh2VDJoUTNndmxXVnRlVnVSd3dHSUoxWFJJSjUvdTc2SXpPYkZIRGllWnN0UmdoTkNaa09MMCtybHBzYU9DVk4wd2kxN1RYUkd6TlhLcWRWY2dLRURMKzVOcnFneGwyWnc1Yk1waDRvWGh3N2ZNenRDM2R0eHVMTlJacVRRWnp2eU51YXNJd1pNYlRveXA1dXo3bkxEZC8vSWhwVlpGbjcrRzgxV3J0cnNUNHRRZnpmQ3pXRlFvTWR5Z21YVkEzU1JaMytKTjNqaHg0bU1mVXpCN1VheWlXQXNHUzZQRWxkNFdSZFViL0tDMXJYS2hib21YRXZWWE40Q1UxSWhQV21WcUR0cm9lT1ZJalVUUVlQREpFWEFUZ1pQSHkwakRlT0dsd0x3cHBpR1lING1aOUxLdXRwVWhPS2l3NzVPZUZaeXU4TjZWUlJLNXpYcVF6dlFCb01hRGJqVWRQYXhCSTAwOFRjZ0ZmTVdST1lEUDRMcWE4WkRWQWg3WWptZ0ZZcVRTS1dKUTc2Y3l6ZVNWRFhPblZxNUFxNDEiLCJtYWMiOiI0YTBmMTI4ZGNlYTVjZDBkODNkYmNhMDBhMTA1ZmNlODljY2E4NTY3NjE4Y2FhMDBmNTg5NDRiOWZmZjY4YzI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005135624\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1226100278 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226100278\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2107020073 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:57:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJyM3lPM3dvS2tMYmRtMHlzbE93WHc9PSIsInZhbHVlIjoiK3BRMFZmMDUxakZsamUrY0ZmQS91TUpXVmc3TC9FdkNzT0xpdlpFVi9OM0NaTnRqSjM0c01nYUs2bWQ5STZJdnB0R1NyMTZqbi9jdHR1a1JlbUkySzFPNWhzVks1djNxaXlQRkRzNW90bkJzcnRoVS9lR251V2xsV2k1ZEk2Q3VYT0tmSE1uUjF1ZU00RjF5K1VXT3NhUDFVSGhTNE1jd043ZW5UbWhqKzh4K2hLbHhPY1dhdmVlekcxNS9qQmVCNVZVVXQ3aVpOcVEzZ3dTYXhJV2lzR1FXLzRVbmxPV0NVSVhTeFJ4V0Z4MWpTUW50endCSW0vYUtrbkRSMnpiakhoWjZGM2NFcTVUYkFyRytuVXM2R2VKK0ptWUQxYndGQkVlYXAyQ3Z1Y1NvaEpIK1RkRVA4K3U3OXE3ZXFxOTV1cUxNUkVsbHhOVlRPbVoyeHVwSGNxU2wvVDdjQjc3RXY3RTV6d0p1djJGZjg5bHpwT2poOE9LLy83dmdMbFFwSWVldGkrTlJpMU1hUlVnUE1hYjhXOWhocENoenQ3NEp2K0kvRE1aTnR5cHFDNjZNNWFPQnFZSGZ5ZVo4L3JWaElpclpmeVZMTmo0bTRQUDJvNEhSVFBPUkNZTFk5YmYwaEQwcXFJanFpc25HdVYySm5HNmRFMnUvRmN6T1JhQTIiLCJtYWMiOiJjZTdlZDFjOWY3ZmRkOWUxZmE0MDA0MmFiOGJjOTUwMWFiM2YwNGM3YmEyMTVhYTRjZGY3ZTEyNWI2MDViMDQ0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:57:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNyWmVFUDI4ZTBQcXFuUlFiYXpCNFE9PSIsInZhbHVlIjoiV200UFdoMTNTeHc1cEEyQUd6Q3FwQncybkZ6U29zbmNxQ1IreTNMd3ZzaElGRkpXakk2S1BrMWttTVVhOHQ4c3VWL3Q5VURmOGI1U1NRa2Z3OWo5MFZFaHZ5SVVJMHgzV0NjcjB3aWxFOFZPYzhHdG9rcG5EM0IyTzlHaXZnVXVZUzlNZTFKSVR1cWVQNzQwTlJHOSsyTHkxL1Z0ZzdGa3lpQmkrUVBKSkErd0F1OUxXRUdNbDF0NTF1K2RZQVhxZU9Tcmt1QUM3Z2MzbXlQSkdBRmFYeDUwdkpZd3FVdmtNTHlWZzY0dzBkQUhUam1idmlBUU83cmFzcjBzT2JjL2N0T1YwdjdKMit2S2lSRGdEU2ttN2ZNQzZRMHlESStYZkxaa0tXY2ZXVENyaHBBMUU4bHBKTzhoakxtaE5lZEx6eWh1cW1MdU5NbmZQbUpGZmhNWVdGVjdnckViRTNkZFhWa25oRE9KaEYzME5mVFI2dDRTYjJKcmZOWG5VTllaQU5mUGJ5d0F4NUJGUFpBY3Rmams5dmswZWdrVkZ0Kzl2eWZBNFdvbFNpWCtKcUVzTE9kZEVIZW9CVkNzSklKZ3gwUUh4bzcyeFhMLzM5a3htRzJCb2FrVWJmY2xBN1JtVVp5V1poN1pJcjQ3aUFBNER0R0J6cGtDQ0NLbXRXeDMiLCJtYWMiOiJmYmRjZjdhODA0Y2FkYTdiYTY1ZjVjYjVlMmE2NmMzNjhmY2NiNjY3YTYwZTJlYzRjYjkyMzlkMDRlY2M2OTU1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:57:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJyM3lPM3dvS2tMYmRtMHlzbE93WHc9PSIsInZhbHVlIjoiK3BRMFZmMDUxakZsamUrY0ZmQS91TUpXVmc3TC9FdkNzT0xpdlpFVi9OM0NaTnRqSjM0c01nYUs2bWQ5STZJdnB0R1NyMTZqbi9jdHR1a1JlbUkySzFPNWhzVks1djNxaXlQRkRzNW90bkJzcnRoVS9lR251V2xsV2k1ZEk2Q3VYT0tmSE1uUjF1ZU00RjF5K1VXT3NhUDFVSGhTNE1jd043ZW5UbWhqKzh4K2hLbHhPY1dhdmVlekcxNS9qQmVCNVZVVXQ3aVpOcVEzZ3dTYXhJV2lzR1FXLzRVbmxPV0NVSVhTeFJ4V0Z4MWpTUW50endCSW0vYUtrbkRSMnpiakhoWjZGM2NFcTVUYkFyRytuVXM2R2VKK0ptWUQxYndGQkVlYXAyQ3Z1Y1NvaEpIK1RkRVA4K3U3OXE3ZXFxOTV1cUxNUkVsbHhOVlRPbVoyeHVwSGNxU2wvVDdjQjc3RXY3RTV6d0p1djJGZjg5bHpwT2poOE9LLy83dmdMbFFwSWVldGkrTlJpMU1hUlVnUE1hYjhXOWhocENoenQ3NEp2K0kvRE1aTnR5cHFDNjZNNWFPQnFZSGZ5ZVo4L3JWaElpclpmeVZMTmo0bTRQUDJvNEhSVFBPUkNZTFk5YmYwaEQwcXFJanFpc25HdVYySm5HNmRFMnUvRmN6T1JhQTIiLCJtYWMiOiJjZTdlZDFjOWY3ZmRkOWUxZmE0MDA0MmFiOGJjOTUwMWFiM2YwNGM3YmEyMTVhYTRjZGY3ZTEyNWI2MDViMDQ0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:57:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNyWmVFUDI4ZTBQcXFuUlFiYXpCNFE9PSIsInZhbHVlIjoiV200UFdoMTNTeHc1cEEyQUd6Q3FwQncybkZ6U29zbmNxQ1IreTNMd3ZzaElGRkpXakk2S1BrMWttTVVhOHQ4c3VWL3Q5VURmOGI1U1NRa2Z3OWo5MFZFaHZ5SVVJMHgzV0NjcjB3aWxFOFZPYzhHdG9rcG5EM0IyTzlHaXZnVXVZUzlNZTFKSVR1cWVQNzQwTlJHOSsyTHkxL1Z0ZzdGa3lpQmkrUVBKSkErd0F1OUxXRUdNbDF0NTF1K2RZQVhxZU9Tcmt1QUM3Z2MzbXlQSkdBRmFYeDUwdkpZd3FVdmtNTHlWZzY0dzBkQUhUam1idmlBUU83cmFzcjBzT2JjL2N0T1YwdjdKMit2S2lSRGdEU2ttN2ZNQzZRMHlESStYZkxaa0tXY2ZXVENyaHBBMUU4bHBKTzhoakxtaE5lZEx6eWh1cW1MdU5NbmZQbUpGZmhNWVdGVjdnckViRTNkZFhWa25oRE9KaEYzME5mVFI2dDRTYjJKcmZOWG5VTllaQU5mUGJ5d0F4NUJGUFpBY3Rmams5dmswZWdrVkZ0Kzl2eWZBNFdvbFNpWCtKcUVzTE9kZEVIZW9CVkNzSklKZ3gwUUh4bzcyeFhMLzM5a3htRzJCb2FrVWJmY2xBN1JtVVp5V1poN1pJcjQ3aUFBNER0R0J6cGtDQ0NLbXRXeDMiLCJtYWMiOiJmYmRjZjdhODA0Y2FkYTdiYTY1ZjVjYjVlMmE2NmMzNjhmY2NiNjY3YTYwZTJlYzRjYjkyMzlkMDRlY2M2OTU1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:57:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107020073\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1233802130 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233802130\", {\"maxDepth\":0})</script>\n"}}