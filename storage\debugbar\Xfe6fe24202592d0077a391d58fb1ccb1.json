{"__meta": {"id": "Xfe6fe24202592d0077a391d58fb1ccb1", "datetime": "2025-07-14 17:59:05", "utime": **********.327768, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515944.87134, "end": **********.327783, "duration": 0.45644307136535645, "duration_str": "456ms", "measures": [{"label": "Booting", "start": 1752515944.87134, "relative_start": 0, "end": **********.262927, "relative_end": **********.262927, "duration": 0.3915870189666748, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.262936, "relative_start": 0.39159607887268066, "end": **********.327785, "relative_end": 1.9073486328125e-06, "duration": 0.0648488998413086, "duration_str": "64.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01601, "accumulated_duration_str": "16.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.290731, "duration": 0.01498, "duration_str": "14.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.567}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.314243, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.567, "width_percent": 2.936}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.320293, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.502, "width_percent": 3.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjR1TDdESWFrMDlZNi9SSlg0Y2xKUHc9PSIsInZhbHVlIjoiNE5SR0pOS3hGREh4dGg0aUR6T1ZWUT09IiwibWFjIjoiMjViNGNhMTg3MWRhYjgzYTQyNTdjMGQwZjU4YjJiZGMzZWJmNTU3YmRjMjRkYjZmZWUzYmZkZGVmYTc3M2EzYSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1788164840 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1788164840\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1872629982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1872629982\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-482562978 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482562978\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1379255512 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjR1TDdESWFrMDlZNi9SSlg0Y2xKUHc9PSIsInZhbHVlIjoiNE5SR0pOS3hGREh4dGg0aUR6T1ZWUT09IiwibWFjIjoiMjViNGNhMTg3MWRhYjgzYTQyNTdjMGQwZjU4YjJiZGMzZWJmNTU3YmRjMjRkYjZmZWUzYmZkZGVmYTc3M2EzYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; XSRF-TOKEN=eyJpdiI6IkZVaXBNVkVaSUlYaHdXcjFSaGYzQlE9PSIsInZhbHVlIjoiOWljVThJY0docmRWYXlNbE13SHRJdUY3NEZvUHpXSXRDbEhKa0EzV296UTNYNWJRU0h6eTBYYmRHMEJ1eWwvQW05T2h6RlhoclNJSHdvNi9HTzZtdVRpemU0SldES05lWjNONEVqbW80UG52UnVNd1dheFl1MVBrVXBZREhEdk9wVGFnU0dYVG1tVzk3K24wMGlETzRvRmQ0ZEkvWjkxZ3pkeEtpa1crYVNqc1hJbklsdHdvMFdnVzF4bWlpRlhld0NuRDA3b0U5VkNYb3ZPZFRLeFJucXJjS0xNSThLaHVvbElaYXFWYXR5L1V2ZmVnYm10SGJRMi9LN0RYc3BxdUJqUTRVZitWV01oTDM1ek1qdVNheTNNWXZmNk5FZVNZKzY1MWw0VElUdmw5WElLMWpERUxxRldQMFNnSXZRRnlUeTA0T3liM3lIcnozalJJbW9YemduQVNIMUJIa3JoRlU3QVF6NG1jMWFGUmlrYVcrcFBCUy9IcThQemx1UjlDN0tWZVNneERxelE1ZGhRcWdJbXo3Z3BMQXZJN3N2RC8xSk9CU1lqSFVCK1V2U09TMWRCeG1Va3dLekJLYjRMQXRkWVh6SWdTeHRCUFZLZ05tckxpcmozanhaZnVZbFpyenlUb3ZQdkpnd0EzUi9ZcU9QYlRWZ2xkRFBVWFVidTciLCJtYWMiOiI1MGFiZTc2YmVhYTNlMTNkZTVmNjk1ZDgwMDc2YzVjMTc2NTQ1YjZlNjViMGU2NTJmYzlmYTY4ZDc4MDZlOTg5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVvZGNVODliT0toNFlnZ1pOSitkL0E9PSIsInZhbHVlIjoiNWhVZVByTFlqemF0VlNlTWJUczNnVGRWZlBmTnN6VlpNbHlTYTVzdzB6Wk1SZXJ1bVQwKzhBZk9KWnhnYXYyd3JBVW4rZjBvUmFrVEk3M1BhOGVkUTJoRnM3WVZqQzJIWnY3TzVCamM3LzA4bEt5emp5bytic3pHY003cFo1R2FWWStaeDdTMkZUa0VqcE1tbFR5V2hscUE3SDlVQ1ZIb1lFUzVQQUxDSXY0cEpxT245Z0R5a0lpcXdPVHNHU2xvaTc2TWRRckk3TDhLTCsxTzVlcGVkY2wxSnZHL3VDMTRocXdDczg3MnIxSFowVnZvTktESHN2QUhwaDJHVXJ3QVVTQXdUVGl0d05VVkU2bGFyQTRzbkk5MlY5T0wyd2NTY1VYOEthZnhacFVjUmVrSG1OOExhTVhDQUIyVjIrSlRQNUFpTlYwYWtwaDc3QUNNaHZveDZpeVNIUDVhTTF2VjI0QlplSmtvV0UzdWxwenJNWk9wVDI1S1BmQ0FyVTNKU25wTzBBdDBIaUNIK0NJbHNleEp5RDRlM3ZMdzRVYVNDcTlxUzlPM2VTamFYanNqQ3EyUUZTd2ZPSGlIWGtCNlpxbHZGL2plZmpRbk9iMnJCdmFuVVBYb1JacUxLSC9Sb2hFaWpWNGc3Rm4xczBhRGQyMlhJa3FNc0ZoWGlOS3IiLCJtYWMiOiJiM2EwMjc0ZGI3ZGI5M2VmNGIyMmQzN2Q2M2ViNDkwMjNjYzhlNjIwMDY3ZGY0ODljNDlhZDZlMzQ4NWIyOTgwIiwidGFnIjoiIn0%3D; _clsk=14bfr1r%7C1752515944651%7C12%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379255512\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-782261877 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782261877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1053672972 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:59:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklVRzJrZ1NMYVVnMUF4VUlSVksvUFE9PSIsInZhbHVlIjoiWkJnUzBwS3FQR3FVL21lbXdqMlphOHp2SmZHRWt6ODZnNUE0VlZMSW9OS3I4bWNkMFZrdUVYMmNVcjllaitJQ0pDZVZDUlBOVEVvaUJyWlhFTFBtZWhEend2amZReEtGME5DcnFiOUdvTU9oay9XNzFVYWlvRHpJSllGMFFFZVJ5Z0xNdGJ1MENkN3VVZHJXL0htMHpvSVlYOUg2eVNnUTlWVDJmSXd4YXpya2NRMVZFWm5zT1VNdkxzRXBlZkdsZzM2N1ViWDBNNndub0RrRFducnBUVUl1a0Q2OEx6TVZpYUpqRXMzeERRVloxOEo4ZGE3TlhvSVkwZjY5YkFER0dKalU5WXZ3SWI0UXU3TzAra3ZZdWJyVFJNQmhpdUpRSWhpSlhTWHFqY0lJSWJaWXZNdHU4SzREdVRPSHZGdXZyazRkSDc4dUh5QzlOSnh3Z0NHNllVL2Q5bDdUR3J1MTZGOGxkRTRBS3I3akh0ZWorYm1tVW1NSk0ySjBWbEora0ZFV3BpYk1TSnhNUEtBSGlyVzV3bkhsRENxQVdvcmxXNHVQemxYdEkrcnFtam02bUlPS2dNYkJ5aWR5OGRMNUJ3UUlsU3UvVDVDa0kyeEZON0ZBY0JBWlpZdjd4M0k1NVI5aVZhWjZFMUE3cFVLbDhqdFdrS2duTDEvN2dzdEYiLCJtYWMiOiJjNDZjYWI0NTIyNzIxOWE0YmE3ZTdiYTdjNmY5YjgyNjljOWM2NzU5ZTgxYWMxZGM3NTk4M2U2NjUyMGRjZTczIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:59:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJObGpWakJhNVBQczAvMHNKdzFweHc9PSIsInZhbHVlIjoiSGVwT2cySmhMcW1OZWtlSm5ZcGRnY3FZOW5leW5BZjVOZHE0MzBiaU9YOWM4NTlsVms4N0VWSEU4ZVRDNVp1Y0lKUzRYODhYdWQzaktFbVR5QSs5cTNuWTA2Q2p6ZDBMcXlwQVdqME9zR2hSMXFmWHhXMVVpRzAvaElHWmhsRzZ6L29DVndrRHExRzFQLzdLa1ZwL2JOLzY2MU5PdFBBZ3dDRkFGbElWdTJVZTFXdjd3TkViUkwxTjB0SGxLcXBsNTFPajRrdmt2WFRDNThxS04rVEVrT3RMTFNGcVhpaEJneWNEMEJFbWN5eUxEUmg1MXFETnk3NFprYlhab0xwdEJVWGVpWlh2ajJocGRHekRIV0dacnJHc3o3RXpGM3JOaWE4SlVnZEhlWFJrZTRQT3ZuYURIOGtrNkd6cTJkSktRSXdDSVY4MHU3bmxBL3NjS1d1ZGZJK0JzNHRhYVJQUFpwYy8rRi9Vb21lRm5mUFV5Q3Jua0J3MlR1V2xzVitzZGtOK05HN3lhT3ZGSWRtRFkrQXBscVBlOVJFdjVZZFlCMXVoU1FvM3dzWEJwV1ozeG0rN2dkSk9VdlFrZnB1c1Z1UmFFTXphcmUwYjVYK1hZbjg2ZE9DcmhLblZBQjRqY3BRQThnRnloN0tvWXMzK2haeGhsMHVDei9wL3MyMWoiLCJtYWMiOiI1Y2IwNTc3OWZkOTlhNTMzN2MzODAwZDVhZTZjNDZlYWYwOWYzMzc2N2RjNzA3MzVlNWMyOTA0ZmIzMTg4NWZhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:59:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklVRzJrZ1NMYVVnMUF4VUlSVksvUFE9PSIsInZhbHVlIjoiWkJnUzBwS3FQR3FVL21lbXdqMlphOHp2SmZHRWt6ODZnNUE0VlZMSW9OS3I4bWNkMFZrdUVYMmNVcjllaitJQ0pDZVZDUlBOVEVvaUJyWlhFTFBtZWhEend2amZReEtGME5DcnFiOUdvTU9oay9XNzFVYWlvRHpJSllGMFFFZVJ5Z0xNdGJ1MENkN3VVZHJXL0htMHpvSVlYOUg2eVNnUTlWVDJmSXd4YXpya2NRMVZFWm5zT1VNdkxzRXBlZkdsZzM2N1ViWDBNNndub0RrRFducnBUVUl1a0Q2OEx6TVZpYUpqRXMzeERRVloxOEo4ZGE3TlhvSVkwZjY5YkFER0dKalU5WXZ3SWI0UXU3TzAra3ZZdWJyVFJNQmhpdUpRSWhpSlhTWHFqY0lJSWJaWXZNdHU4SzREdVRPSHZGdXZyazRkSDc4dUh5QzlOSnh3Z0NHNllVL2Q5bDdUR3J1MTZGOGxkRTRBS3I3akh0ZWorYm1tVW1NSk0ySjBWbEora0ZFV3BpYk1TSnhNUEtBSGlyVzV3bkhsRENxQVdvcmxXNHVQemxYdEkrcnFtam02bUlPS2dNYkJ5aWR5OGRMNUJ3UUlsU3UvVDVDa0kyeEZON0ZBY0JBWlpZdjd4M0k1NVI5aVZhWjZFMUE3cFVLbDhqdFdrS2duTDEvN2dzdEYiLCJtYWMiOiJjNDZjYWI0NTIyNzIxOWE0YmE3ZTdiYTdjNmY5YjgyNjljOWM2NzU5ZTgxYWMxZGM3NTk4M2U2NjUyMGRjZTczIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:59:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJObGpWakJhNVBQczAvMHNKdzFweHc9PSIsInZhbHVlIjoiSGVwT2cySmhMcW1OZWtlSm5ZcGRnY3FZOW5leW5BZjVOZHE0MzBiaU9YOWM4NTlsVms4N0VWSEU4ZVRDNVp1Y0lKUzRYODhYdWQzaktFbVR5QSs5cTNuWTA2Q2p6ZDBMcXlwQVdqME9zR2hSMXFmWHhXMVVpRzAvaElHWmhsRzZ6L29DVndrRHExRzFQLzdLa1ZwL2JOLzY2MU5PdFBBZ3dDRkFGbElWdTJVZTFXdjd3TkViUkwxTjB0SGxLcXBsNTFPajRrdmt2WFRDNThxS04rVEVrT3RMTFNGcVhpaEJneWNEMEJFbWN5eUxEUmg1MXFETnk3NFprYlhab0xwdEJVWGVpWlh2ajJocGRHekRIV0dacnJHc3o3RXpGM3JOaWE4SlVnZEhlWFJrZTRQT3ZuYURIOGtrNkd6cTJkSktRSXdDSVY4MHU3bmxBL3NjS1d1ZGZJK0JzNHRhYVJQUFpwYy8rRi9Vb21lRm5mUFV5Q3Jua0J3MlR1V2xzVitzZGtOK05HN3lhT3ZGSWRtRFkrQXBscVBlOVJFdjVZZFlCMXVoU1FvM3dzWEJwV1ozeG0rN2dkSk9VdlFrZnB1c1Z1UmFFTXphcmUwYjVYK1hZbjg2ZE9DcmhLblZBQjRqY3BRQThnRnloN0tvWXMzK2haeGhsMHVDei9wL3MyMWoiLCJtYWMiOiI1Y2IwNTc3OWZkOTlhNTMzN2MzODAwZDVhZTZjNDZlYWYwOWYzMzc2N2RjNzA3MzVlNWMyOTA0ZmIzMTg4NWZhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:59:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053672972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1222960707 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjR1TDdESWFrMDlZNi9SSlg0Y2xKUHc9PSIsInZhbHVlIjoiNE5SR0pOS3hGREh4dGg0aUR6T1ZWUT09IiwibWFjIjoiMjViNGNhMTg3MWRhYjgzYTQyNTdjMGQwZjU4YjJiZGMzZWJmNTU3YmRjMjRkYjZmZWUzYmZkZGVmYTc3M2EzYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222960707\", {\"maxDepth\":0})</script>\n"}}