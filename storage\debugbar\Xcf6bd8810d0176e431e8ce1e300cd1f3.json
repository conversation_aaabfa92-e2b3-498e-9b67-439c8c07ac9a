{"__meta": {"id": "Xcf6bd8810d0176e431e8ce1e300cd1f3", "datetime": "2025-07-14 18:29:39", "utime": **********.077213, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517778.629424, "end": **********.077229, "duration": 0.44780492782592773, "duration_str": "448ms", "measures": [{"label": "Booting", "start": 1752517778.629424, "relative_start": 0, "end": **********.022963, "relative_end": **********.022963, "duration": 0.3935389518737793, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.022974, "relative_start": 0.39354991912841797, "end": **********.077231, "relative_end": 1.9073486328125e-06, "duration": 0.05425691604614258, "duration_str": "54.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00266, "accumulated_duration_str": "2.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.051578, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.805}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.063072, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.805, "width_percent": 13.534}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.069092, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.338, "width_percent": 14.662}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-220098008 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-220098008\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1888113678 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1888113678\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1280508936 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280508936\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-986047516 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517773156%7C35%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikx3QmRuUUdvTWQ2RC8wZEhnS0p3ZGc9PSIsInZhbHVlIjoiSzRCakVJbFRYQTNxdzRraUZzV2M2Mm44MVpGcG1ZZTMwUjFIZVd3VnVWdXNCRStNYkxLWUNMemRYbjV4V2JBa0JNazNCS1Y1T0JuaDNmWU81U0J1OXdFdzBCaGpGdVlZVHk5VWpkbDZkNlIwMHJTakc0UUtnNFFWeXpabmI2QmJpMWdlbzhHbFYvK2FOSjBIcGR1dVA4Zi9YTGgzV1k0MEYvSHltbHZLL1FMRlk1UnFTelpvejR0dUNaTnppeVYyZXlpbFVpaXFqOCtxT2NsRDVGaUhoU1FmZXRVMGYrZEQ0NXdSN2JxYnVvMnBKcUROR3R1UDltWWNDaE5JL3VVYTVydU9xa0Q2NjNLZEVxVDh6NU90akMvbTdvZUIrT3d4eUpkNWFkd3Y0ZFlhdDczb1AvaTBFZUJGQmpObnRCbk9HRFZKR0JQdVlVZHF6MVFwRk9YMGdiTUo2VnNXRy9veGxKejBCSkdSTVhzT04vNDNRL2tob3lkVytibk9rTkNuRktuL2Q5OThsS2ZHNmZTUWVidTQyQTlHNGJueGNETXg1ODR3bGlSQmFjNnlrMDBYNU53d3hicTQ3QzFvTnJCckdWdFd6T3l5cHlYM1BqYXJ1RTNPVEZLc3dSNllKWk5uNHFvbmdWelQ2MnEydXhJaHlYeUlYeDc3bk9EWCt4MVgiLCJtYWMiOiIyZWIwZTlhZjIzYWY4MDQ3YmMwOThmMzgwNmM0NTFlMjYwMGQxZDkwZTNjNzJkM2ZkMWEwNzliMmI2YzIyZDUyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlI1Ui9RM3RGcklHanFFbkc1UzVxa3c9PSIsInZhbHVlIjoiRUFKVFpSWWRHT1phNVd6QUxHUE94QTVtay9WOUJSVWFVd0Zlc2UyM1BJSzBaREFhNWY5bG1sa3hXVlloL01PRmhpTEdYRmhZV0ZDN256L0xsenhXeVZwUzNIVTk2NjcvSlVxMVNTcURuUTBQTGx5bEJYR0pUSjV5Q2trK3l6eXUxVHd5Yk1td2FGRkNEWG5qR094eDFjMnFobTdaZFNoeGpYZzFMdVZJWTBZdTlQNVF6MEZnYW9GRjF4d1Q1R1dheGg3c0F3TkFyQ01qYWhvUHFadytNWFM0ZFJWUHlKT05DczNGYWdRS1lrQU42Y1VkWHJZeCtlMnZ6WjN3ellaRmxLelNPVUxyZnZaejZsM0hKSTRySktFczZGK0VqSXgxOTVCY3FmNkM4QUIvcE9GR1grbXI4TXkrVFRqM2Z3RENscGtGY2dJSXA3RGNPZEI1eGQ0a0VvTDhqTjFvWExEWDNNMmxROHR6VXU5a25QUUhHNmVUVHlrcW5DKzFFS0d4dkZMVENvQUlnQ2dUbUpPeVBaemlXcnI4cGlkNFZvQ3F1cDZ3Mlk4QWs1U2VWY2o4d25EVHlRTnBUWHJMREJDUVl3UnRjVlNmS3BuclFTVjhHY0xBeVVteWI2S25WbmFHMHZPckt5VXJQaWlhNFV0dE5qN2ZDWmxwS04wVk5oUVUiLCJtYWMiOiI2YzA3NTQ3MjdlNGFkNTFjYjMzNTI5N2ExMzFiNzUwOTVlNjZiNWQxZWZkYWRlOGFhODUzMWM4NzUzYTk1NTk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986047516\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1055769370 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055769370\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVjWk14Vllaai9DYnVZQVIybzlQUXc9PSIsInZhbHVlIjoib2xLYWpNalN1NUkwS2lkaHBnRVBhNy9SSEl2RUVpeGErRmdTSFh4RUFyOU1kT3lIdzhkYitlVFR2anJNNHdwbWRjNUZvMTArRkZVTklCZWxYbTc3dk9XazBLMUVPL0xOV3FLUmd0bE1PY1ZpdXdSdW1ybjBoOUJnNDQ5bXRVaysrWWUrNlZncjBFUDJ1NHhCcjJzclBFUlhYcVFqd3N5TDB3Qk1MNDdRVWxBMlJudG9ZT2FsWjFlZlRwcWkwakZUTEhEeFUwb3A5ZmwxTmtWMmx4Q3o1dk95SG85cGxXbm9jWkM2R1dCRmhNbVA3WnJGSitIbk5oNkZtUEhmRG8rM2xOMzRNOTk3MW9Rc0NzVlRmTDlWSjJRaDMxUjRPd0lYV1V4UHlsWjBIVEtzdGtla0ErVHVRY0xTV1VZVzd5cGVvMUhaakxpS05odE9wRVZRR3dlWHBkZ2JYd0x2ZUZFKzkvcTJ4RllOSkhrd2ppcVFFd29YN2F5YUpVcU9GQXU5UEh0QWJUZjBmTUhlWldxUW44RzJKRW12NEJ2T0dkd1NheDg2ZEtDOXUvdExVN0xMdTNKakpQbUxzM21kUnlmNkxuMkhJaVN1TUw4ZGVPK3VDTGZaYUVPcld5eWIvWElGL0ZZZW5mVzU2Y0daVXFROG5MTEhNU3JvUk9nK05yNEgiLCJtYWMiOiJlYTRkNWY2OGMxZWVmODNiZTdkZDJhY2Y1MDllMTVlMzk0NGQ3M2Q0ZTgzNGJmNjQwZTQ4YjlhNGI4NWM2NGU0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJZRjJJQk5OSVpRWUh4M1RFOFY3VGc9PSIsInZhbHVlIjoiSCtOYUFmZnBNVXAxL3pWRW1pNXo2UTBPbjhVWHUyL3pHbDgrU3FBRUVKZVczSHRVbHdhbGlaMzRUSmg3Z1RzSjdLdGpqSnRCa1BNVGJZUTA5c0ZBUUhLYVB6NTkrWTZvbmMwTFBOVkJQclNQbmphWFE1WWNhd3czeFNGZW1QS0Q1cDdDZUJwaGVpcHY2d0xuQmgrd1BBVFdJSkFQT2FRKzNoVnducFRqRngyWXBtVFVTU3RuMWhMb2FOaitkS1pWeHB5Y2EwTndheWVqd2lOb0VGcURpNkJzcHpnMUpReHp0OHRDaWJQMDdtaFhsUTRNZVQrSTAzbGc2MDV1c3NlQ1BObjVvbHh5cmZhVXJyc092SDZ5cjgzVWIwRHR5eVhRNi9FQ0hSN1BFSUFPNWIyT1I4ZmhYNkVNSUJBSTB3cjhhVjZmSHBwbUNJWTZkMUpHVHdjQnhsWUdKalBDTk9FZTIxeDdESHhhNDRYNy9NU2pvNDU2VFBUeVlJSWZRSTlNWVdqWC9yL0tEL0F3REsyeE51RitGb0phWHRDTXFJb0tBM21iSlFuYTRUYjByVEdBdXF0Wlc0aUNRdU1qOC96bGVLSkxRZW9maEYydUd0ZWtGWk1YdytteDdQZFllSWZvL2ZLUVdtUFNvRkxYRERqQUIwbnVmVks2ZWFRY2xCN2IiLCJtYWMiOiI5MmUxNDNlNTUyYjhhNTNiYzI0NDBlYzkzZmI1NjRjOGQ0NGQ0MTRhNmI3YTk5MWM5ZmJiZGQ1ZGU0MWVjYjExIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVjWk14Vllaai9DYnVZQVIybzlQUXc9PSIsInZhbHVlIjoib2xLYWpNalN1NUkwS2lkaHBnRVBhNy9SSEl2RUVpeGErRmdTSFh4RUFyOU1kT3lIdzhkYitlVFR2anJNNHdwbWRjNUZvMTArRkZVTklCZWxYbTc3dk9XazBLMUVPL0xOV3FLUmd0bE1PY1ZpdXdSdW1ybjBoOUJnNDQ5bXRVaysrWWUrNlZncjBFUDJ1NHhCcjJzclBFUlhYcVFqd3N5TDB3Qk1MNDdRVWxBMlJudG9ZT2FsWjFlZlRwcWkwakZUTEhEeFUwb3A5ZmwxTmtWMmx4Q3o1dk95SG85cGxXbm9jWkM2R1dCRmhNbVA3WnJGSitIbk5oNkZtUEhmRG8rM2xOMzRNOTk3MW9Rc0NzVlRmTDlWSjJRaDMxUjRPd0lYV1V4UHlsWjBIVEtzdGtla0ErVHVRY0xTV1VZVzd5cGVvMUhaakxpS05odE9wRVZRR3dlWHBkZ2JYd0x2ZUZFKzkvcTJ4RllOSkhrd2ppcVFFd29YN2F5YUpVcU9GQXU5UEh0QWJUZjBmTUhlWldxUW44RzJKRW12NEJ2T0dkd1NheDg2ZEtDOXUvdExVN0xMdTNKakpQbUxzM21kUnlmNkxuMkhJaVN1TUw4ZGVPK3VDTGZaYUVPcld5eWIvWElGL0ZZZW5mVzU2Y0daVXFROG5MTEhNU3JvUk9nK05yNEgiLCJtYWMiOiJlYTRkNWY2OGMxZWVmODNiZTdkZDJhY2Y1MDllMTVlMzk0NGQ3M2Q0ZTgzNGJmNjQwZTQ4YjlhNGI4NWM2NGU0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJZRjJJQk5OSVpRWUh4M1RFOFY3VGc9PSIsInZhbHVlIjoiSCtOYUFmZnBNVXAxL3pWRW1pNXo2UTBPbjhVWHUyL3pHbDgrU3FBRUVKZVczSHRVbHdhbGlaMzRUSmg3Z1RzSjdLdGpqSnRCa1BNVGJZUTA5c0ZBUUhLYVB6NTkrWTZvbmMwTFBOVkJQclNQbmphWFE1WWNhd3czeFNGZW1QS0Q1cDdDZUJwaGVpcHY2d0xuQmgrd1BBVFdJSkFQT2FRKzNoVnducFRqRngyWXBtVFVTU3RuMWhMb2FOaitkS1pWeHB5Y2EwTndheWVqd2lOb0VGcURpNkJzcHpnMUpReHp0OHRDaWJQMDdtaFhsUTRNZVQrSTAzbGc2MDV1c3NlQ1BObjVvbHh5cmZhVXJyc092SDZ5cjgzVWIwRHR5eVhRNi9FQ0hSN1BFSUFPNWIyT1I4ZmhYNkVNSUJBSTB3cjhhVjZmSHBwbUNJWTZkMUpHVHdjQnhsWUdKalBDTk9FZTIxeDdESHhhNDRYNy9NU2pvNDU2VFBUeVlJSWZRSTlNWVdqWC9yL0tEL0F3REsyeE51RitGb0phWHRDTXFJb0tBM21iSlFuYTRUYjByVEdBdXF0Wlc0aUNRdU1qOC96bGVLSkxRZW9maEYydUd0ZWtGWk1YdytteDdQZFllSWZvL2ZLUVdtUFNvRkxYRERqQUIwbnVmVks2ZWFRY2xCN2IiLCJtYWMiOiI5MmUxNDNlNTUyYjhhNTNiYzI0NDBlYzkzZmI1NjRjOGQ0NGQ0MTRhNmI3YTk5MWM5ZmJiZGQ1ZGU0MWVjYjExIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}