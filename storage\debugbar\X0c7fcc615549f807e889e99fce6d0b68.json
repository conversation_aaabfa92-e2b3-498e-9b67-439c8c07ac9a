{"__meta": {"id": "X0c7fcc615549f807e889e99fce6d0b68", "datetime": "2025-07-21 01:19:39", "utime": **********.905054, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.463292, "end": **********.905073, "duration": 0.44178104400634766, "duration_str": "442ms", "measures": [{"label": "Booting", "start": **********.463292, "relative_start": 0, "end": **********.850662, "relative_end": **********.850662, "duration": 0.38737010955810547, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.850671, "relative_start": 0.38737916946411133, "end": **********.905075, "relative_end": 2.1457672119140625e-06, "duration": 0.05440402030944824, "duration_str": "54.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993384, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00252, "accumulated_duration_str": "2.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.879762, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.889}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.890516, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.889, "width_percent": 18.651}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.896723, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.54, "width_percent": 17.46}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1030315010 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753060775053%7C4%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpBbFhualBJSVRLS0UxQkxuTm1YSWc9PSIsInZhbHVlIjoiN3NRNHdyUU5ZbTlsa2hJVjFQc0VCQVkrZmVMUVpqSEFYcDJHM3lkcWhhc0VtWkVraDhEdVZZMlJkNVZRKzBnYmVnOURWOHdhMDRxRUppays2ZzFrSXVpdnpmS1Y1OXZST3BDRnh1citQbWpKeGtZNUliTFFueEJ5bDhkbnVIR2RCYWkwNzkxZVFCWFd5TEx0dWlrNnUxNS9tZzVyTEEzVGRIOUlabXl6NCsxSTlJMmtBY3lSRWN6YWxIUnBSZ1I0MDlqY3pPRlc3ZTcydk9CdnRQb2J1ZEU0aEdKb1JQZ2JkdEVIUGNRVm9iSmYyeDNMMk9ZRkpqakQ3aVQvemtoY0xXbVE1c0t4MTZYZW1mTVJscUhtRjhVamRlUTh5OTF6NkduSUx2UENmdFMvUklHVzFBb3cyVnBsdTJELzJBOXRGOTU2RTRCb2gxaWNKa1N2VEdEQjVEcmdDLzdORkFnMndlOEZaZEdsNENONlYzNHAwc1FZa3lkRG9pdHFHeFhHQkNqRnJmcWFHSmVYMjU3WFdKVWNMQThMSnM5Vis2OWhTaHpET3RmWGVkd0wyWTA4RHk3SGxXcWxlN29zQXYwNFdCS0hSKzhHalFqSjhzVzFRNkFMYjdzWkdFOXh1UCtSR2U5a0pPMUIweEpCSlVnemR6cVBCK3lrSCtDMGVQVzYiLCJtYWMiOiI1ZDA4NTA4NjhmOTMwNGQ4Y2YyMmE2NDFmNGUzZTA5YmI3YzY0NTAzYzA4YWU0NDEwNDUzM2VhMDUxZWJjYmFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVPMjdMVTVObjVjdmpoMXFRWWMrRkE9PSIsInZhbHVlIjoicDlFb3Z4OXNLUzFZS1g1OHlsM0EyUStFdHNpbnJoaWEvQmVCaUUvVTFkdi9RQW1zRnNtMkZFTUl0N0I1aU1GMTJZUzViODhCQ21TalNERzVaaW9MMEZVOC9UUm5aanQxcXJUd0tQUHZ3VUsvSFI1R0JNTnNqOVhXUVdvdDhZdWJOZC9zZER1eTNPeGg5ck1oWC85clBjenZIZEE4all0VWN0dlVmVzNicEdPaXYxMUQ1VXdTVmFJVnNXemVnVXdWVVVQUFVVL3pTSnJFbW50UFBwb1M0cUZGRjd5YlNLbnp6THM5aHdUSEZHN1AvOHhtQkNpUnd1WGtHQmlac2s5Tk0vbm9yZmxHc05IK3k2VytBYVNCSWc0eXptWEE3RzlZM01ObUFQUHNTUjVvc29hbE1sOWpzWmdCaHdvSmY3cnlzNllTS0x2ZGVKUUc4WlpvUHRhcTlYYk9nMlBzWEp0TGQrQVNCWlJPcnEvbmk4aUhKejRQSEZaS2kvVjFOdW5yeGlEZmdVWG9XZXViVmdMZXdRbmJadzdYa2ZmRkQ1VFVJSVFWd3lxUnM5WnFySnp1bzhHMStvTTk5Y3VLS1NjT1doejJSUDQyTWp6UU9UQzZlRld2alF4MlJER3ZId1VZUEN5ZmI4M0RweVE4RHlVVC9Ha3IrWVBHQ0FYeTNaSGkiLCJtYWMiOiI4OTczYzY5ZDA3YTQyYmI5YjdlZjE4YTU3YTA2ODE4ZDBjNjRmNTAzZDQyZTMyNjBiZjdiZTVhZDRkOGI5YjNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030315010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1018907128 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018907128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:19:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdWSXV0WEU1TW5VVXEvVkF0ck1Temc9PSIsInZhbHVlIjoiczlidDM4QndzSHZ1b2xTSUF6Yk40bHpGME1WQnJmNktyNFRpWWxrM3MxMTlhbTBCc1M2TXpCK3FmQTl5UVpXWGc1M3NGd2p5d2VQbm1mRGRpV1UvQnZwWGZXWlBydWNLRWg3T0FvRm9GUGhiTGw5a0lmZFlkYnU5T2hZcGw3MUxmVjdWcHV4NHpBOFVqSWQ0TTM5ZHorVHNKZEEvNFdibWVVRFlZUzgzVmNmNVlmeDMyeXZOZzI5NW1Sbk4vSkxMQm5MUnY5cE82RTh4a1VDdHdBN1VuMUx6Z3BkcUxFVjRZeGpTZEhhc1M1c2pOLzFMc3R0THFpMmt5MnJPOXZzV0lBUmFWZWxHdDk4TzJWa3d5SXliUTJiU1JSbzJiZDZCUFB3aG4wc3VkU1ZiY3BaWDRML283YlZnS1VMaGloWGZHRFI3NDhlU3BlTlhLcUNJV0xKT2xsZnhQTGhoZ1p0NUUxd2dvMDFsK3lWa012UUo0ZkhGWE5HVTVXeU5UelhKTEYxZ1FJVTF4NXVmWVZaTDhYbVBFR1ArK0tjVTB1TGF5UVMxSzNTTmw1ME5IVy9aTVlRT29MaHRRUWFqZWFNT1ZISGdTck1Tb0oxVXJsSjhsMkVrM3RBRFkxS2lKUGhvaGRTZHp3blBVeGprNi9vZy9zOENkeVdxaXExeTZSeHYiLCJtYWMiOiI1NTE0OWEyNDZjZjU3ZDg2ZTI2MTUyYzY5NTc3ZTkwMTZhNWU1MGJhY2IyOTY1NmM0ODA0Yzk2NGViZjNhNjU3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9oSzdEd3E2MFpCNjlzTXNwT2M2dWc9PSIsInZhbHVlIjoiNnQweTVrdHY5LzdCMTRPVnFLSWFBckV0L2JQSkh4SVI0bklDelhDeGs0NmVYaGdPeGlOWUZJQnVHamROSEV0a284Wk1HZTJFR2kwOWEvaWVuZUNkQ3Z5cnRWQ0FhdFdHL0cyTTE0TzBSTGZaL3NtbXJzb1JncTlzMnpDTVlBUmRSaGRGQTZEVGtFRnI2WGdQSVhXeDF2NVBOVS9ZcUJqeHIvRjBQWGZ0WFArcmJOV3VVWFZoa2dFTkhKYnE3TGdKTHRVZnYwcDc2YXV1YmtvZWs1SWZkN2NRNkxNN0VJd2Z1WklEZWQ4TFoyalhmOFBPTE42aHFLRzZMbEhsdWlVT01zZ0FVUFp1c3RMWE1PQVU4RitOVnhVNExlQjc2ei9SaWE4MzVpZU92QUV5L1dybkd0a0RBNC9wSngvL2lDYTJDMnNqOHo5NnNWcUJ1NWdNT0g5R2lIS3JxRExyaTJ3YTBGN1EvSVFCY1JteTZpUmcvQk01VUxsWkpRZlNrdVMzVXgybE1vck5ibUR1ZDhLMWRLVWV5aHZzWG5rdXhvTXNpV1J2UEQyeElPcHBqWEU5UU5jS25pNWxyNm1UK3I2SHVKQ3JKVlRwVGVnSjZKOGNnUkE0ZU15ekpvTDF1a3I5ZXZ4bHhRZy8zdGpYektjVWFqcGJ4MkpSNmFKVTd3MFEiLCJtYWMiOiI4YjMyNGY5YTEzNGU5MGQxNTAzNWU2M2E4OGZlNzRlZDlhZTAxYmNlOTIwOTdhMzMzMmFlZGUyZGVkNmY1NmQwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdWSXV0WEU1TW5VVXEvVkF0ck1Temc9PSIsInZhbHVlIjoiczlidDM4QndzSHZ1b2xTSUF6Yk40bHpGME1WQnJmNktyNFRpWWxrM3MxMTlhbTBCc1M2TXpCK3FmQTl5UVpXWGc1M3NGd2p5d2VQbm1mRGRpV1UvQnZwWGZXWlBydWNLRWg3T0FvRm9GUGhiTGw5a0lmZFlkYnU5T2hZcGw3MUxmVjdWcHV4NHpBOFVqSWQ0TTM5ZHorVHNKZEEvNFdibWVVRFlZUzgzVmNmNVlmeDMyeXZOZzI5NW1Sbk4vSkxMQm5MUnY5cE82RTh4a1VDdHdBN1VuMUx6Z3BkcUxFVjRZeGpTZEhhc1M1c2pOLzFMc3R0THFpMmt5MnJPOXZzV0lBUmFWZWxHdDk4TzJWa3d5SXliUTJiU1JSbzJiZDZCUFB3aG4wc3VkU1ZiY3BaWDRML283YlZnS1VMaGloWGZHRFI3NDhlU3BlTlhLcUNJV0xKT2xsZnhQTGhoZ1p0NUUxd2dvMDFsK3lWa012UUo0ZkhGWE5HVTVXeU5UelhKTEYxZ1FJVTF4NXVmWVZaTDhYbVBFR1ArK0tjVTB1TGF5UVMxSzNTTmw1ME5IVy9aTVlRT29MaHRRUWFqZWFNT1ZISGdTck1Tb0oxVXJsSjhsMkVrM3RBRFkxS2lKUGhvaGRTZHp3blBVeGprNi9vZy9zOENkeVdxaXExeTZSeHYiLCJtYWMiOiI1NTE0OWEyNDZjZjU3ZDg2ZTI2MTUyYzY5NTc3ZTkwMTZhNWU1MGJhY2IyOTY1NmM0ODA0Yzk2NGViZjNhNjU3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9oSzdEd3E2MFpCNjlzTXNwT2M2dWc9PSIsInZhbHVlIjoiNnQweTVrdHY5LzdCMTRPVnFLSWFBckV0L2JQSkh4SVI0bklDelhDeGs0NmVYaGdPeGlOWUZJQnVHamROSEV0a284Wk1HZTJFR2kwOWEvaWVuZUNkQ3Z5cnRWQ0FhdFdHL0cyTTE0TzBSTGZaL3NtbXJzb1JncTlzMnpDTVlBUmRSaGRGQTZEVGtFRnI2WGdQSVhXeDF2NVBOVS9ZcUJqeHIvRjBQWGZ0WFArcmJOV3VVWFZoa2dFTkhKYnE3TGdKTHRVZnYwcDc2YXV1YmtvZWs1SWZkN2NRNkxNN0VJd2Z1WklEZWQ4TFoyalhmOFBPTE42aHFLRzZMbEhsdWlVT01zZ0FVUFp1c3RMWE1PQVU4RitOVnhVNExlQjc2ei9SaWE4MzVpZU92QUV5L1dybkd0a0RBNC9wSngvL2lDYTJDMnNqOHo5NnNWcUJ1NWdNT0g5R2lIS3JxRExyaTJ3YTBGN1EvSVFCY1JteTZpUmcvQk01VUxsWkpRZlNrdVMzVXgybE1vck5ibUR1ZDhLMWRLVWV5aHZzWG5rdXhvTXNpV1J2UEQyeElPcHBqWEU5UU5jS25pNWxyNm1UK3I2SHVKQ3JKVlRwVGVnSjZKOGNnUkE0ZU15ekpvTDF1a3I5ZXZ4bHhRZy8zdGpYektjVWFqcGJ4MkpSNmFKVTd3MFEiLCJtYWMiOiI4YjMyNGY5YTEzNGU5MGQxNTAzNWU2M2E4OGZlNzRlZDlhZTAxYmNlOTIwOTdhMzMzMmFlZGUyZGVkNmY1NmQwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}}