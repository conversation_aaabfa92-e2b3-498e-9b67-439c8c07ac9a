{"__meta": {"id": "X075bd8b86439fb1bbea43161a0155a36", "datetime": "2025-07-14 18:17:42", "utime": **********.591037, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.067284, "end": **********.591052, "duration": 0.5237679481506348, "duration_str": "524ms", "measures": [{"label": "Booting", "start": **********.067284, "relative_start": 0, "end": **********.500186, "relative_end": **********.500186, "duration": 0.43290185928344727, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.500196, "relative_start": 0.43291187286376953, "end": **********.591053, "relative_end": 9.5367431640625e-07, "duration": 0.09085702896118164, "duration_str": "90.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028620000000000003, "accumulated_duration_str": "28.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.53502, "duration": 0.027710000000000002, "duration_str": "27.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.82}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.573782, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.82, "width_percent": 1.398}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.581626, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.218, "width_percent": 1.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-485788681 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-485788681\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-618763696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-618763696\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1278378083 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278378083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1642577667 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517057929%7C15%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IksxSWJDb0JRRXMyelVWa1dubW1hM3c9PSIsInZhbHVlIjoiVm5IODVGSC9kYW5mYTFhYndnbDU0cEYvMUtCWktUODRpNGRFWHB0Zy9yQWRDTUgrRjl0eHZaSDVpcEUxbVFkTWZhcXN3bnJqaTkyd21DZWhoZHkxQkhyMzYxSG5QSm11WVdaMnJ2Nk9wbzNVWEtFU1QxQ1BOS0tvU1pRUFA0cmI5a3NGU3hjbm5oNEFiRk42bU5rWXhQUmhlQng1R2ZXUEJJN3BrWG5CL1Y3UmlndGp6MDZVdjBhZ25rVnBxNDI1SnhjVXJHdlVadjg1ZmZacktRSWNVWUR2V0ZpbHlZZUVFV3NsSjg1M3VLc28rZlg3WFNTUFRpQ3pvMmR0SEVsWUxhdzRXcThNUUN5OWp1Mkp6QTJxQ09NeUgvMHo0d2xUZk8wSS9VSnczdGxlYWY4SzdHQ3J5RjV1YnBvT29YR1RnNFhZbFJUS0xCUjQ3cHRqUDh3ZXZlbmtLWityTHJKeHFhcmExekhJY1BmVzZiemwwM2JoejI3YUVSRWdQRHBYZTdRMng5dDR0b2psQ3FvS3E3NngyUk9PMmc1QUMxQ3M1bkttMm5veVZKdjQzb1p5UXNTVXMvOFZEOWJQMjZNUElrWTdzdUlrZlhJOXVXeFIxazVPV0RYRitOR3Jqanc2S0dpWkNGQ2JmYmNxVk9WLzVGTGZQSjdDdGV2djFvWngiLCJtYWMiOiIyYTM4NDliMjFkNWMyMmFmZjY4NjY0NjY4Nzc3ZGMzMWJkN2U3NjVhMTg4ZGI3ZWJjNWUyZTUzNDQxNTgyZjQ2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZVZTJadFNTYVZWM1I2WG5qbWdwSVE9PSIsInZhbHVlIjoidXY1NkRjdUF0alQrN1lRS2VuanJ5b1ZkbWhSVzZkaUhnWUdKWm0xMmRYZmRqWGR4eVRDRTBPYXJxc2kwL09sTUVoSDYyTzFDOUZZZDRoREhEVmpqcFFnWDUrVGVETzBKelZzd3lERTE5SzdkbWg3MnlRRlNONUlZeDhXR0djcytlOEFYSVA1RFloVXM4Nk1UMWRWRTl6clFjMGVidCt4NjJXTEJ2SnVZUjFhVEtOeVdaMjBPNUFqQVF4dDB1Z0V4VlNMbCtJU0w2ZUNCd0p3alNYcnYyK1pyMGVIMGl3TkYrZm03NUNWSHU4M1ZWSHJrbVVkWUljRWNxRXptM1VZVGRxaWl3am9jbEJEK1dmMkhHUTRPN0FpdWNTZ2ZnbjU0K1FGWEZxdWFSbHEwVy9lUjhrMFVTRnNLT0E1UnphTDVOR2c5ZVZ0SDdYc3NzeTlIS1RRUEhyZytEVDhacWR2TUVwYXc3ekFtMGFwZEkzVmViUjZJelhGS0JEQUhYSTFmVUh2ZElKcVhLOFJPcnZaNzhVTHBCbEhqcmpraW45czNUdWVmOG5zVU9HeStIOC9XM0RnQU81dEpQYXh4RHBlTCtBVHFtemRjZElOQTdJTFhXcFhwbWNkZC93TklmdFY2eU91TEE5Yk5UREhCbFFYZVN0bzRpTzl1ZGZURzdGNnoiLCJtYWMiOiI3YjRlY2E0ZTZkMzdiYTRmZjUyZjIxNTIyYThiMjE3NjllZWZlNmI5ZTJiNTg3ZTRjMzMzMjQzYTE5NjljYTMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642577667\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1303835138 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303835138\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1039937665 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR2SVhTaUx3UXU5QVlXRitKc2FEL2c9PSIsInZhbHVlIjoiamFnS2JVclZXWnpkWktmVHpSOWk0czNiUndZTTBEQzRyeGcwNGtTdStyVXk2ME9aUzVjS2NBeUFXYnFDZndyNWhoT3MrajJORVRhMmhoaVR0Ukd4RDloRjJDL0VVdngxTFo3OU1ILy83cGZPcnFTL3hsOXN4TndocEZhMTJodUlqMGFyK3p3QmZPRnhSUFZiUEVweXBNYjhsVzhOMmhRYnRnei92L1dCT0VGTWpBYnNkZERLOTcyRWlGcGtQLzgvYjdDUVA0QUpDM3RRajVvOUZMWW1IR0x4TDk5cS96NTFPWXN4WStmQXoyUkk0Y1dEM0dnaVQzN3luc1ZpV2JRbFlCUk9GaHZGSGp0Y21YVlNzUWFqUTZONHhpcG0zS3VRaEU4dGZ1U05iTHZkZmZvTTlON0J4ajNDdXpwbm9qdnBkZHpmVEpTdDZYN2d5YjA1dTlwVXN1MGdScDQvSjJJUGpWTVl6TE5UWThTNFdreVdIU1p4VzdCWE5KY1hqWXhUMlIybDhqY1lPelQzbzNRTWwreEE0Rk13cWtiR1RvUHMxRlJER3BYcDQwbzUyTmhQTzZ2VmhoVHZRQTUrbzJldDNzVk9ENGRGd1ZXOGRmdmNVMStMTlJack9ERWYvRDEyc1lnTFJkY0tyanpuTERWSEhVT0hheENVejFOM1A3UkciLCJtYWMiOiIyYzRkYTZjOTdjZDZhMmI2NTVhMjAyNzExYjk5MjRhZTU0M2M0MThhMzY4ZWQzYTllNjZjOTZkZmVmNjIzNWU4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjMxSC9zK0R4TE9XOUV1N3ZHM3ByTlE9PSIsInZhbHVlIjoiV1hibGhzTHZHVlJSUFVOQ0g2S0hqQmI3NEhOTFZvRi8weEhDMUNrbHpPWkt6MHllVEFSVjVOWFc5YWlCZHRoRGI0VGlzejBoNW1xeDR6bERpQzlvbHBINjBVZmEvaUhQaEZsZ1ZqbDhFcTR4NTdHWVdqdXN4cDlTb2ZNTENsNGRReFdscXZIemVsWWJiWDY5dlNHU0NBV256ekFtL1FLMVc1SVpnYjlSZWhMT051RDJlYXlyWGJodzBlSVpsN0krN0NRRGZ3cHd2dnpBeDQ5bTdjZnNOWjlRaW8zdFRtbGk5ZDA0bTJ6bldSTDJONlViSTR3dFpjOG5mMjgzbXVUcmRTTEtNbWFxNmEydUY1cE5id2ROTDJjbWtORi9sRWVkeVlQOVM2L05jUlFFckJPeExMRTZXVkxmRlpVWDBNdFBmOVIwSW1jRDMzak9qN3J4ZFE1dEk4YTJNQTNIYTA5U05aUVpoUHRqekZ2bHJVUVRDb3Z3bWtCWWRNNnQ4b0tOWmJxVUZObTlIOXZYQ1N0clpxMnpEOGhVaCtDaEFqT0hOS2FTZkRMRkZwMFdTVGJiUjFVN2VuVHQ3dkNzL0lkN1lQdFBsSTJiVVErNjRvTlhKUVdiOVFmWjEwRjN4UUh1QjhMaTB5eS8yeldHSFVReERRYlhKZE5NNXJNQys5TEciLCJtYWMiOiJjNjQxNmNiYjFiZDJlNDI1ZjhjM2RiNGIwZjQwZjI4NzlhMmExNzAyZmU2MjYxNzE2MmRkOTllMjU3ZjZjYmVjIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR2SVhTaUx3UXU5QVlXRitKc2FEL2c9PSIsInZhbHVlIjoiamFnS2JVclZXWnpkWktmVHpSOWk0czNiUndZTTBEQzRyeGcwNGtTdStyVXk2ME9aUzVjS2NBeUFXYnFDZndyNWhoT3MrajJORVRhMmhoaVR0Ukd4RDloRjJDL0VVdngxTFo3OU1ILy83cGZPcnFTL3hsOXN4TndocEZhMTJodUlqMGFyK3p3QmZPRnhSUFZiUEVweXBNYjhsVzhOMmhRYnRnei92L1dCT0VGTWpBYnNkZERLOTcyRWlGcGtQLzgvYjdDUVA0QUpDM3RRajVvOUZMWW1IR0x4TDk5cS96NTFPWXN4WStmQXoyUkk0Y1dEM0dnaVQzN3luc1ZpV2JRbFlCUk9GaHZGSGp0Y21YVlNzUWFqUTZONHhpcG0zS3VRaEU4dGZ1U05iTHZkZmZvTTlON0J4ajNDdXpwbm9qdnBkZHpmVEpTdDZYN2d5YjA1dTlwVXN1MGdScDQvSjJJUGpWTVl6TE5UWThTNFdreVdIU1p4VzdCWE5KY1hqWXhUMlIybDhqY1lPelQzbzNRTWwreEE0Rk13cWtiR1RvUHMxRlJER3BYcDQwbzUyTmhQTzZ2VmhoVHZRQTUrbzJldDNzVk9ENGRGd1ZXOGRmdmNVMStMTlJack9ERWYvRDEyc1lnTFJkY0tyanpuTERWSEhVT0hheENVejFOM1A3UkciLCJtYWMiOiIyYzRkYTZjOTdjZDZhMmI2NTVhMjAyNzExYjk5MjRhZTU0M2M0MThhMzY4ZWQzYTllNjZjOTZkZmVmNjIzNWU4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjMxSC9zK0R4TE9XOUV1N3ZHM3ByTlE9PSIsInZhbHVlIjoiV1hibGhzTHZHVlJSUFVOQ0g2S0hqQmI3NEhOTFZvRi8weEhDMUNrbHpPWkt6MHllVEFSVjVOWFc5YWlCZHRoRGI0VGlzejBoNW1xeDR6bERpQzlvbHBINjBVZmEvaUhQaEZsZ1ZqbDhFcTR4NTdHWVdqdXN4cDlTb2ZNTENsNGRReFdscXZIemVsWWJiWDY5dlNHU0NBV256ekFtL1FLMVc1SVpnYjlSZWhMT051RDJlYXlyWGJodzBlSVpsN0krN0NRRGZ3cHd2dnpBeDQ5bTdjZnNOWjlRaW8zdFRtbGk5ZDA0bTJ6bldSTDJONlViSTR3dFpjOG5mMjgzbXVUcmRTTEtNbWFxNmEydUY1cE5id2ROTDJjbWtORi9sRWVkeVlQOVM2L05jUlFFckJPeExMRTZXVkxmRlpVWDBNdFBmOVIwSW1jRDMzak9qN3J4ZFE1dEk4YTJNQTNIYTA5U05aUVpoUHRqekZ2bHJVUVRDb3Z3bWtCWWRNNnQ4b0tOWmJxVUZObTlIOXZYQ1N0clpxMnpEOGhVaCtDaEFqT0hOS2FTZkRMRkZwMFdTVGJiUjFVN2VuVHQ3dkNzL0lkN1lQdFBsSTJiVVErNjRvTlhKUVdiOVFmWjEwRjN4UUh1QjhMaTB5eS8yeldHSFVReERRYlhKZE5NNXJNQys5TEciLCJtYWMiOiJjNjQxNmNiYjFiZDJlNDI1ZjhjM2RiNGIwZjQwZjI4NzlhMmExNzAyZmU2MjYxNzE2MmRkOTllMjU3ZjZjYmVjIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039937665\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-516368004 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516368004\", {\"maxDepth\":0})</script>\n"}}