{"__meta": {"id": "Xe2b21da038823b88e51cf3fc5f7ea322", "datetime": "2025-07-23 18:22:52", "utime": **********.587041, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.00761, "end": **********.587062, "duration": 0.5794517993927002, "duration_str": "579ms", "measures": [{"label": "Booting", "start": **********.00761, "relative_start": 0, "end": **********.385595, "relative_end": **********.385595, "duration": 0.37798500061035156, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.385605, "relative_start": 0.37799501419067383, "end": **********.587065, "relative_end": 3.0994415283203125e-06, "duration": 0.2014598846435547, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44728160, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02555, "accumulated_duration_str": "25.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4156651, "duration": 0.02504, "duration_str": "25.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.004}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.447755, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 98.004, "width_percent": 1.996}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1717219921 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1717219921\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1110524673 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1110524673\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1607424044 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294969181%7C4%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpSZFFKSGUxQXd1NEdKMEFZZkpEa0E9PSIsInZhbHVlIjoiUXZwTVh4RDZQamxyeXFVVE9sTDZoaHJtb0dSbVNGU0V4OVYxckJxQkt5RXNiUnUvaXZjYU5Ddk9zWGhteEF1c0M2UEU3N25KSndkYWtSSkxIbk5Sb3c1Qytxd2h4bS9EZ3R4RVJPd1c5elp2aHpTQTh1bVFMTVpwWDhzTVZ2MHNPRGNDZnY3RTkxRW1OMjBaeDRKYUQ0VmJlVW9sb2w5d1cyRkFZaGV2LzRjVmZZSHk2QUhCdmdPLy8yWU1tTzZFa3hDakQyQ1NnTjd4V25OdkpzdG9na2JzVHlUbWRvd1ZGTVEwTnJPQUlBVnlqWFk0ZDRBQ3FVWG1kcXhHRnVuMS93V0JSMENvb1A2aXJBanJuTDhiZXdwdHd4KzhlV1RuOVdCWDJ5MFdPakRYdnNhc1MvOU14ZDFvRkc2TGtnWVo2QnZ2bXRaT29FNnZMLzJWWkk3TXpmQVZJcUU1cnF5UFpSNkhiSmVqNXNNNmpmSWFVL1NzVmZPMm1HamUvVHdrR21DVWdYa1ZkTzVVWm5wM0NrWWc4K1pFSzI3QVlVenFHalNCL3VCaG1sVEN2aVZGZmZrVDJxYUN2dmgyYXJFcEJBckZCYW1mN3F4enozakVaWlRqTUdOQ095OG5WUkFiU2loUGlaY3d3dEw1VWJrcmFjN0dlWlVsam81QUdINEkiLCJtYWMiOiIwNjhjNjg1YjA2OGJjMjFjYzg3Yjg4OTliZjVkOWMwZDhhZWNhZDQyMTA3NjIwNzZmNTFjNDgwMmZiNTMxNDA3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVZU1BFUlU3MUtUVmIwQUFpNEM4N1E9PSIsInZhbHVlIjoiaE1yRHNPa0Z5ZW93ZnBpTFNPMDVLQkthMEZFWEcxMUFEY2xldDNyMTYvZXJLd1FFVm5ad3BJUmk1K3gzeEZkNXpWNmRzb3FxYlJhcWE2MmFYYnhLbDB5Nm01YzNvNXhVaDVKY2JmcGxDSXlVSURTU0NPeHVWVjhtV1kybC9mS3Q4OUJQM2RBUVgxSVBFVHhFQXVrYStvQ3BHSWFmSnRJYjQ4YjVrYXhiVFpvWmRCM1I1cEpPU1VVRXlMQ1c1R2tFWG1aRkcwL3IyOHd0dTVSWDNRa2pFYnhLeUNLM3lDczFGTjhRRnFic2dWTjVMZWFhOGpUYkRjbjgxdEx5bllSbVpnNVc5bDI0aG1xYjF6b2t6b0tqZ25Uak5pMDVuSHhHd2xDT1FyRDhYQXNycU40SmxTWjNROTZyanN1OVdhelpBZzVIV3JDS2h0bDFaM04reVdKREZsUEV5dSsxZXNhWDF4SGpBQWkwVGo2TkU3dHNLaXMzc0h4elN4RTFoRkRpd0NUWHlXYnBHYUYxV2FWL0lJMHc1aXIrTXhCRndaSXJYWGM2SmU2b2x5eEtMNk1VcUxNdS9Ga2tkcCtrM3hsWGNkbzdWRThRd3BLRGcyaW1FUGswMEI4UkdKcGpHZGJ5NXRmVTNpSHlpUVo1UVFRMnFNRUZTY0tFMHZYaUxRZzMiLCJtYWMiOiJlZDJhNDY0NDAyMjBlOGYyNWE5MTE0YzNiZWMxMjQ3MmQzYzUyMjcxMDE4ODcwNmFkYmE0NjE5ZTg4ODI1ZmM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607424044\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6f86AL2UyJbEsnP8wClSGR79UCod8lW0WDs0fxKW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1333519316 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkE3Q0wwY3ovbUw3WHY5UUFzZVB5c3c9PSIsInZhbHVlIjoiVFhTenNqQi9DYnV0S0pRM29MYWdOSm1TK1lPZGdYTEo1SDFZT1pNUTZRQWw2bHJEQmhnOHl0c2d0T1NpbWtWYWRKNk5JSkFIbnRtVjhaWXFwR0haTkJhK2pvUlVncWo1cEZsNkJ0NlprM0VFWmFmSkFMa3NGSXhwemt2Y1FreTd4T0V5Uzg3d1lBc0FERlU0OVhUWVpFVGhSOWtpbG1nRUZrdGx1QUU3UXRjRTk1ckV4b2FDSlVTdjVKak45OHo3RDR0RzRwM3JHdjk3YVZwcEp3cisvUXRDbkhtSnNWZGM2TGQ3a1BIR0JLQWNtUkpObDJZWkM0ckZ2STRISklRU1lYb0VRV3JMSldOenNJc3JuSTdWcmMvSXhOSFVmbTdkZjMyeVA1UnJCTEZyZ3A1alIzdjVPaEtFenNKQlNCYlkxaEpLb3U1NmdVSkFXY3RTemJWd1VoSnRRb2xoYTdheDI0bDJVM0xGWWh2TkVSOE5iZ2VzOS9zdHlHTlVRazBXaWVnaEhieWx6ekhRQVFTOCtKUGhITUV3U0VxWTVTTU5LdkwraDF3dzJPME9ZY040Y3IwM0s0UEl2THpVWVBxNC9PS0cxc0k2RHptWURBeWU2ZGI0Tm1leWhTVXJoK0RNMHNvY3h0Q1R3bTBwVXNjV0NsVW1nMU5ZenF3V0Y0WWoiLCJtYWMiOiJhNWI0MzQ2Y2JlZDdkYTlkZGFlYTMxNjYwMzkyM2YwZDk0NmY0MTRjMGNiZTA3ODM1MWY3NDM3ZTZkNmRlYzIxIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImQ1WXBpWDVhUTVReDhuVW8ybDBVbkE9PSIsInZhbHVlIjoiTEZ3RkN2QWlTTzNHUzk0UGxvaFZvWW8rTlUvMkpNTE9UcDl5N3NlTlkycThua0pjNU0zWDNQVXFzRmY5YnBEa3FyTTVKWlh0TitJeHg2bVozWFBUT2FJRTNJMSt4OGZpUDFSbnQvWnlBa1BpNEtydUs2cWIyVFBBNVRWZ25zZ2gyQTFpakpWTW9oanNIVnU1RlVZaHlHdENSMlhaNEd2S0k3WVcxSE5Kby90WSt5SXlxdklWNFpFNCtLQko3dmxiZWtEYWNoQW1qTy9pSVkvN0IrYnZzZFNsZmhIK3NBUkxKTVVoL2FMdnY2cDZtc2RaRHpNR1NlMWhBSFhVTDNWc1BPcVRFTU1NZlVvald0MDMvRU5OM1V1NTMxNXZwbmNOck52cmV6YndhaVBLblVBK3NucUFMMmJvZnllYnNvTVBuaC94WW1TK202OHhrVFF5UXFlVjhuVVY1MDdBR3haYVFBNXYvQkpMTWpsUEVMV1hOdUdjOTdWcEdzaVQvanYzRjFFc2ZPWXpjMHNRM2FoTkJsbWJKZjhvNktRTlYwaTl1b01LbCtzc3d1RHdJOVdaKzAyZHZEeEJ4OWtXMEdVenRLSTZlVm1CdDkxMDNUcVZkc2p2cWZ1bUU4VmlqNXd0N1ZUUk5UQU9jcSsvbktSSFJoL1hXcjNybzdEcThHMTMiLCJtYWMiOiJkMTBjNDkzZmY2YjZmN2MyYjMzYWM3MGIwM2MxNTBmNGFmNDI3YzdmMGYyNjRkYzRiOGYzMzkyMjVjMzIxZDdmIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkE3Q0wwY3ovbUw3WHY5UUFzZVB5c3c9PSIsInZhbHVlIjoiVFhTenNqQi9DYnV0S0pRM29MYWdOSm1TK1lPZGdYTEo1SDFZT1pNUTZRQWw2bHJEQmhnOHl0c2d0T1NpbWtWYWRKNk5JSkFIbnRtVjhaWXFwR0haTkJhK2pvUlVncWo1cEZsNkJ0NlprM0VFWmFmSkFMa3NGSXhwemt2Y1FreTd4T0V5Uzg3d1lBc0FERlU0OVhUWVpFVGhSOWtpbG1nRUZrdGx1QUU3UXRjRTk1ckV4b2FDSlVTdjVKak45OHo3RDR0RzRwM3JHdjk3YVZwcEp3cisvUXRDbkhtSnNWZGM2TGQ3a1BIR0JLQWNtUkpObDJZWkM0ckZ2STRISklRU1lYb0VRV3JMSldOenNJc3JuSTdWcmMvSXhOSFVmbTdkZjMyeVA1UnJCTEZyZ3A1alIzdjVPaEtFenNKQlNCYlkxaEpLb3U1NmdVSkFXY3RTemJWd1VoSnRRb2xoYTdheDI0bDJVM0xGWWh2TkVSOE5iZ2VzOS9zdHlHTlVRazBXaWVnaEhieWx6ekhRQVFTOCtKUGhITUV3U0VxWTVTTU5LdkwraDF3dzJPME9ZY040Y3IwM0s0UEl2THpVWVBxNC9PS0cxc0k2RHptWURBeWU2ZGI0Tm1leWhTVXJoK0RNMHNvY3h0Q1R3bTBwVXNjV0NsVW1nMU5ZenF3V0Y0WWoiLCJtYWMiOiJhNWI0MzQ2Y2JlZDdkYTlkZGFlYTMxNjYwMzkyM2YwZDk0NmY0MTRjMGNiZTA3ODM1MWY3NDM3ZTZkNmRlYzIxIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImQ1WXBpWDVhUTVReDhuVW8ybDBVbkE9PSIsInZhbHVlIjoiTEZ3RkN2QWlTTzNHUzk0UGxvaFZvWW8rTlUvMkpNTE9UcDl5N3NlTlkycThua0pjNU0zWDNQVXFzRmY5YnBEa3FyTTVKWlh0TitJeHg2bVozWFBUT2FJRTNJMSt4OGZpUDFSbnQvWnlBa1BpNEtydUs2cWIyVFBBNVRWZ25zZ2gyQTFpakpWTW9oanNIVnU1RlVZaHlHdENSMlhaNEd2S0k3WVcxSE5Kby90WSt5SXlxdklWNFpFNCtLQko3dmxiZWtEYWNoQW1qTy9pSVkvN0IrYnZzZFNsZmhIK3NBUkxKTVVoL2FMdnY2cDZtc2RaRHpNR1NlMWhBSFhVTDNWc1BPcVRFTU1NZlVvald0MDMvRU5OM1V1NTMxNXZwbmNOck52cmV6YndhaVBLblVBK3NucUFMMmJvZnllYnNvTVBuaC94WW1TK202OHhrVFF5UXFlVjhuVVY1MDdBR3haYVFBNXYvQkpMTWpsUEVMV1hOdUdjOTdWcEdzaVQvanYzRjFFc2ZPWXpjMHNRM2FoTkJsbWJKZjhvNktRTlYwaTl1b01LbCtzc3d1RHdJOVdaKzAyZHZEeEJ4OWtXMEdVenRLSTZlVm1CdDkxMDNUcVZkc2p2cWZ1bUU4VmlqNXd0N1ZUUk5UQU9jcSsvbktSSFJoL1hXcjNybzdEcThHMTMiLCJtYWMiOiJkMTBjNDkzZmY2YjZmN2MyYjMzYWM3MGIwM2MxNTBmNGFmNDI3YzdmMGYyNjRkYzRiOGYzMzkyMjVjMzIxZDdmIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333519316\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}