{"__meta": {"id": "X647f7184d1b020fe5a12a9e2e222c7fd", "datetime": "2025-07-14 17:58:45", "utime": **********.417889, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515924.974989, "end": **********.417903, "duration": 0.4429140090942383, "duration_str": "443ms", "measures": [{"label": "Booting", "start": 1752515924.974989, "relative_start": 0, "end": **********.356958, "relative_end": **********.356958, "duration": 0.38196897506713867, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.356967, "relative_start": 0.38197803497314453, "end": **********.417905, "relative_end": 2.1457672119140625e-06, "duration": 0.060938119888305664, "duration_str": "60.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46002568, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030099999999999997, "accumulated_duration_str": "3.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.39115, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.093}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4025478, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.093, "width_percent": 13.621}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.409792, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.714, "width_percent": 14.286}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-452558416 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-452558416\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1990018576 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1990018576\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-855850679 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855850679\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1667681922 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515924198%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJLNTIyVzFLc0pzK3hJUU16L214NHc9PSIsInZhbHVlIjoiYXhEeEJFQ3B3TmFpSXgvUktMVzB3aU5CWmo4WkxoRk9SQnMyM2Q2a0lJUW9COHlnRjhpTlRDQXprRWp5TS9PYVdDWVNpODBLY08zdCsrcEo1WGwvRldFZXpNVzdINDljS1M5YTdBY0M4dmJtN1J3Tm1UZDdpRU96cS9neHpjVUROdDFHNmpGZzJhMFdWRGl1Wmsra0hLVjRGWkJNK3hha2lMLzU0aUNPOG9WaU5jRlZ1eWVBdWNITXFzWnRsUDI5ZXU5ekptMHZNTmJGNUREOW5IZVFnbzg5SjBwNVcycDlqQW5aenBMSG9leVB0OTE2T2VpY2pKU3JEMnhTTEFMbW5HMTZLMVYvV2JXeGMxNTR2OTgxeHlqVlFpUG1wM0NyVzh5b0ZlT2pnMVM0aDMyZnhIalEwOFJNWEV6b2hGVldqSk8yUjVDeEVwUS84dDgwMDFYeE1PZnZWeG95TGZXOUNTTTBqNlFSVk9CeXNUazRUaEc3N1lFamdrTmtaQkE5djJQRDl1OFRxcnBVd2hGcFljbUkwazhtdGREODg1NjRWVnZQVXQ2eUx2cmlNWUNaekZtTHB3eFdYdmJvZ1pTS3A5YmNVM0djSTkreWl0VnpIVTJYd1U4MmFWa3E5U0NqOTRjeUN2MUkzc3FiVzRncEVKRlNJRER0T2NhQjF5NEwiLCJtYWMiOiJkZDEwZWMyZTU1Y2QyZmFkNmQ5ODYyZmNmNDM2YjJjMTY3Mjg1MmE4OTFkNjQxM2M5NTIzMzgyNGZhMWNhYTQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5PalVkRC9WZVgwVWo1TVBNV2tJUXc9PSIsInZhbHVlIjoiQ0tERjVnc3lxdFBoSVBob29qNjRxSHZ0d3RQM0JQVWcxRlhONFpyMWMyc3VtWG9EaFhjcGZOY1pLbFBIUi90SHlBbno4d3VkUGxTYjUwMDhzTkphSWlPQS9HRnR2VXZOSDZqaFU3WGk0MnA0MnNobTJrTVBoUTdIU2xaV1grSlk0NGFmRHE5UU11YmRUamdtNEVWMkc4MERJbkcxM0pUVUhSQTNVZllyaDJ1d20vTUU0VjJpZFdHU3lCQVlNb0VZR0djMW5nVHlrdU9lTnNVS3AyY0JvTzRIU2FwcVNGaExRN2lzSUdUNkhvQ2JMeFVmWnltWFhhVWw3TmNZdVd4L2RLT2tWYUxZTmF1c2hZNmxybUxaWDNYSlc0aWJSWDU3bURZUXFiVTNvZ2pmMGRGQVZjb0h2Y0lEK3pQWndKb1NMSTZaQWRWdVlUanpWYXE5TGdQSTc1WmdVTHVnelhVSElZaVZVcis5d2VqSXFaMEovS1puTCtza2JNQjRyb2lqaGNvUzUybUF3QVdmU29BellZQnVlQmg3YmY1aEZ4dUI5QlQvWkowdGgrS1N3aTkvWWlTdktQTG9pMzB0Z3ZVVnVlVVdYdkQ3dXB1Nml0OEE3UkpJaFVOSFhKWG5uUTcvdituaDFHQ0hqY211cjJ3NURaanMxNG54V0pKY21pZWwiLCJtYWMiOiI1NzgzZTc2ZTIxNjk5MmJhNmI1NzI3NmE4NmM1OGU1OTY0YzA3NTgyNzZiZDgwYWI1MTY0NTM2YTgyNDM4MWY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1667681922\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-941932480 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941932480\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imk0UVhtTVgrNGlNNG1JYUxsWDJxcmc9PSIsInZhbHVlIjoiRW1LZHJpQkVPcVJvcHJVV25PVzJ2OXJMUS9DMHdiR2xrR2hyVEtER2F5V2dXMkt0NlJJRzRBNklWYlZYUmpKeC9HR2RvUFd4cUNYRUdiblUxL1ArWkNtcDBkeGxIU0NvZzkxa2VLakNONVEyMURBdEM5V3QwRkwyQW54eGdDS1ZpMC9XaUxoOXhmTWxJelU0SGg3TlB4d3FGeGk5elh5aEJydStPV3UrSUZSdGkrOGNMVGgraGpmNEF2dXJSSWcxNkN5a0hTaHdpMXRSNnZZOGxsUHpSemxtQk1iQU5qL2Q1NHQ1MStJSGhDUHVubVIvZXliYTZiNzJoNzlsTXl6M0o1MTFjVkJ1dE9MV01WUTNIUmhsbWM3akRJb0V1cVd5UFNVU2JLN3VIU0JKNUtFNnlxUEJtb2xBZDhTM0VIWFVqbVBiM3Q1d3ZrNGhKcjY1WmpMTUZHYzM4VG1YK09sNWRrMnVrOHcwbXJFQXcvVm90SjMrekdUMWdyeGhGMElMN2dUZ2FsTk5qVCt1eGhteEFucUE4c25qa2RsUTlVc2FmV1A2bmxSS3NlSTJsMWJ1anpyN1pBWnBxTDNKYTY4WXE2TUtCZmZQem1LeWp3aVgyd29oTHQvYWh4eExkTk12V1NVdm9TbG92TFIyMXFGem5OVW02ZnpCRHVmSHdnNEMiLCJtYWMiOiJhOWM4Njg0Y2FjODgyYjlkMGI5NTAyZmUzMjc0MTFiMTJmOGFkM2ZjNDE1NmUwYzJhYTVhYjU1MGNlNjM2YWZkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImUyanJwL0ljRnZvN3JUY3hROVVEV3c9PSIsInZhbHVlIjoiVzh2RjdJbzV6a0tBOE51Z2k5MDJFNEJWMU5yMmlTeXliY1IwYWpFMkRnZmgxZmQ2UFFVTWlHV2hJVFhvY2h6WnRaRUZQOWlCd2tYSUZWcFdGeStPTjk5TmN3dTdhamFsNGJ3elo0U1dLdkZWZGIvM2Z0ZTcwSWVieE9NUXNBS0lyMXJ2MUsvSkVjSklXdTRraFRpVEx2NExTT2F1SDFlRE5kMDZhKytmcmdxUE9YSUVmSytLZUlqSWgrMlFnZXVkc0J2Mm9uSUdkajNCQ3BFMDlXc1ZrNm9EYmRxRnhobUc1WXMvaHcyV2dBRUdQRThJREtkYnVDdDV3WEptaXR3L2hJdk1udGs0dUdnNE53dEJCTFhGZkFSeEpPUmg1SFE0eWcwd2FtVmVBc2xNS0J2ZUVLKzZaaWdvR0dkT2s3Z0JWTjNpS1huUmh2M2xFbmhGNUNyZE5uUXJFaFJWZmJlNjRDb1FvS0llcDkycFg4cm1ZZFRQTnJ0N212dzk4cXUrdnd4QUFlUElWSVpWRWVWU2h1SzJYT3J6Q3FQd3hOZUg2SUhiMGZrbVh0d0ZXYnNXMzdNWTk4VGx2b2hhb21kR29Lb3pQK1JCdEE0K0d2aHJXckl5djJUQmdEb0tMaVViUHVpanFDN0IzQ0FhTEVBUmQxTEFKa0tPRGVhV2d4NGoiLCJtYWMiOiJkZTA2YWQ4NWE2NGM2NTM5M2NjNjU2YTc3ZTBkMTdlYjlhNDQzMzc2Mzc1NTI3MzhmOTk1NjE2ODgwYTUwMDFhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imk0UVhtTVgrNGlNNG1JYUxsWDJxcmc9PSIsInZhbHVlIjoiRW1LZHJpQkVPcVJvcHJVV25PVzJ2OXJMUS9DMHdiR2xrR2hyVEtER2F5V2dXMkt0NlJJRzRBNklWYlZYUmpKeC9HR2RvUFd4cUNYRUdiblUxL1ArWkNtcDBkeGxIU0NvZzkxa2VLakNONVEyMURBdEM5V3QwRkwyQW54eGdDS1ZpMC9XaUxoOXhmTWxJelU0SGg3TlB4d3FGeGk5elh5aEJydStPV3UrSUZSdGkrOGNMVGgraGpmNEF2dXJSSWcxNkN5a0hTaHdpMXRSNnZZOGxsUHpSemxtQk1iQU5qL2Q1NHQ1MStJSGhDUHVubVIvZXliYTZiNzJoNzlsTXl6M0o1MTFjVkJ1dE9MV01WUTNIUmhsbWM3akRJb0V1cVd5UFNVU2JLN3VIU0JKNUtFNnlxUEJtb2xBZDhTM0VIWFVqbVBiM3Q1d3ZrNGhKcjY1WmpMTUZHYzM4VG1YK09sNWRrMnVrOHcwbXJFQXcvVm90SjMrekdUMWdyeGhGMElMN2dUZ2FsTk5qVCt1eGhteEFucUE4c25qa2RsUTlVc2FmV1A2bmxSS3NlSTJsMWJ1anpyN1pBWnBxTDNKYTY4WXE2TUtCZmZQem1LeWp3aVgyd29oTHQvYWh4eExkTk12V1NVdm9TbG92TFIyMXFGem5OVW02ZnpCRHVmSHdnNEMiLCJtYWMiOiJhOWM4Njg0Y2FjODgyYjlkMGI5NTAyZmUzMjc0MTFiMTJmOGFkM2ZjNDE1NmUwYzJhYTVhYjU1MGNlNjM2YWZkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImUyanJwL0ljRnZvN3JUY3hROVVEV3c9PSIsInZhbHVlIjoiVzh2RjdJbzV6a0tBOE51Z2k5MDJFNEJWMU5yMmlTeXliY1IwYWpFMkRnZmgxZmQ2UFFVTWlHV2hJVFhvY2h6WnRaRUZQOWlCd2tYSUZWcFdGeStPTjk5TmN3dTdhamFsNGJ3elo0U1dLdkZWZGIvM2Z0ZTcwSWVieE9NUXNBS0lyMXJ2MUsvSkVjSklXdTRraFRpVEx2NExTT2F1SDFlRE5kMDZhKytmcmdxUE9YSUVmSytLZUlqSWgrMlFnZXVkc0J2Mm9uSUdkajNCQ3BFMDlXc1ZrNm9EYmRxRnhobUc1WXMvaHcyV2dBRUdQRThJREtkYnVDdDV3WEptaXR3L2hJdk1udGs0dUdnNE53dEJCTFhGZkFSeEpPUmg1SFE0eWcwd2FtVmVBc2xNS0J2ZUVLKzZaaWdvR0dkT2s3Z0JWTjNpS1huUmh2M2xFbmhGNUNyZE5uUXJFaFJWZmJlNjRDb1FvS0llcDkycFg4cm1ZZFRQTnJ0N212dzk4cXUrdnd4QUFlUElWSVpWRWVWU2h1SzJYT3J6Q3FQd3hOZUg2SUhiMGZrbVh0d0ZXYnNXMzdNWTk4VGx2b2hhb21kR29Lb3pQK1JCdEE0K0d2aHJXckl5djJUQmdEb0tMaVViUHVpanFDN0IzQ0FhTEVBUmQxTEFKa0tPRGVhV2d4NGoiLCJtYWMiOiJkZTA2YWQ4NWE2NGM2NTM5M2NjNjU2YTc3ZTBkMTdlYjlhNDQzMzc2Mzc1NTI3MzhmOTk1NjE2ODgwYTUwMDFhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1497735640 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497735640\", {\"maxDepth\":0})</script>\n"}}