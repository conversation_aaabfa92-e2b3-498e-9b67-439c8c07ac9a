{"__meta": {"id": "X4dff240fd946e9f21397c77c1261192d", "datetime": "2025-07-23 18:20:47", "utime": **********.859558, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.412657, "end": **********.859581, "duration": 0.44692397117614746, "duration_str": "447ms", "measures": [{"label": "Booting", "start": **********.412657, "relative_start": 0, "end": **********.799101, "relative_end": **********.799101, "duration": 0.386444091796875, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.79911, "relative_start": 0.38645291328430176, "end": **********.859583, "relative_end": 1.9073486328125e-06, "duration": 0.060472965240478516, "duration_str": "60.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46533824, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029200000000000003, "accumulated_duration_str": "2.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.826934, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.219}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.837661, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.219, "width_percent": 23.288}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.843634, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.507, "width_percent": 18.493}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2086852739 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2086852739\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-703934601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-703934601\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-810922207 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810922207\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-709965062 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294843263%7C8%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpVWTNndWZBVDNRaEx4Y2hiSGFmeVE9PSIsInZhbHVlIjoiNzNwMUdpME41cEZpQk9tSWlxbDRDTEphWEhZWXVaMlAvNXpSUmNndFNaOHBjcDV4S1JuVGg5dEE1N2wycEFkZ3E2WjMzblJIUXNDWEVKamJlaTNkY1drTnBrMzJFUUJwcWIxSnZtM2xhQmhsRmZmcU5ubG9id09wSzNpTXYycnB0MjNCQlNCVTdETEY4dVJneXNDV0hpdTQ5YWE2YXJmdkRYUGFYektzUndSNUs5Z0haQjcySGhiM05LK3FvWnBnR3JjVEtiKzhkMm5aa3pGYVFkY1hvODV2REZmS1gyNDAwQUlMZ2FnTTAxRVZTSnNMSFRsclZ1TE1zb3pMRmZnNTY0aDh5OWYySmN3bW9mRkswZHRmZGVzeWgwb2M0cDZXVlFJL0pIWVY4MlRXOHVFOHR1ZkxSbzVrRTFtT1E5dWhZL0xScEJjNVI0OUE3eEFkbnhaSjBiNzJWN0dwVncwZFhiZ3VXL1BJWXEwWVRzZzVMZlZYVUVFaStDbGMwRHIwYU9MaVJwSUVOQUNQTWFIRFVhbnkvWGNjc002Tm5aK2tlSTVzaVA4L3pmRFhiZFplbVBiWEpPak5EU3dId1duV2ExeTIxMmsxa1BwTzBDaGxlQU0rUGJud1cxM0ZNZkJXNWVzWDN0ZEpseTd0T0lRUm9LTU81QnpFYm1uUGFzTUwiLCJtYWMiOiIyOWI1ZTUzOGUzZWViZDQ4ZTRiNTQ4NTNlNjEwMTFjOTUyMzA5ZmYyNTI3NTU1NTU4Y2YyNzVmMzRmM2VkYjVlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlQUkhlbWF6VHpUbXpwRlFnS3hjeFE9PSIsInZhbHVlIjoiTHhaeTNMczA5bkFmeVJKTTBDNFhXZHNlTGVUM1Q0N1ZKN1plRExyY3JuRDBSMlNycC9lak5KYUVsZmNRa2wySWxlNk96MDRFTkszbWZLZDVpd0YwU3hCaVY5QWJqZFBUT0o0clBha0dkRVZXOWhRVzhnazMremRaUGdBdjRJbktiWkljdW5hSFdKZHp2eDFUMngybFlWNU5iSStJbnNUZkVibDVHRDVFaXdsNU9LeFZGTDkxdGsrTkIxeU1uaXJDelhqWk9OeUswV3BzR1kxT1htc1dRNFpCK1M2cWZrTWhXZ3hrbFNaNE5UMk9YZGx3Y29XVUFlS2x5RTcwMTVCMW9SS29MQngrTVpWUDBpR3hFVU5jN2RVdFRqOHFaRzlQanNJRkx6b0Z6Uk56Z2Vzbm5pZUlxOUlab051NlpPaE5NdXg2Uzc3UHRyRjF4Si9WMWt4bXJjejNEYlQrVXVsNktqVTZrTzJjcG5za3F5RjdaWTZ6OXNCNkZzVGVIQUwxL21pSU80REZpRE5JbXN0a3JaZUVIMVZjbXJxWnUvbUlYYkhqcTU1bE9Ud01RaVg5VDZvRjBsYmV3aWo3V3owZnZGeWFQaVBXd21FTmxRRElMTUtmQzM1SlBrMy8wK3dMSTk1aEFPWkozUTFkeVZrWE0vY242M0RucmZ1Mk4rVjYiLCJtYWMiOiIxNGUyNDEwNTY3ZmQ0OTY3OWE5Y2FiOTIyY2EyYTc2OTE5NGFmMDg3ZjJmYzk5ZjNmNDQwZjQ3MjljNDZhNDViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709965062\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-467695052 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467695052\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1642183192 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:20:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFJV1B5NHNoTWtRS2RFY3plQmZhc0E9PSIsInZhbHVlIjoiS1puMXhmOHBKZFBza0d1b2ZKWGc1RG9XUi8xczB6cHNTMUFMMzVDR0R4OFNreFNaRzVSQ3NuUFA3S09lc3hqOWpkK05kV3IraVZyOGx5UllydDVvc0MrYWpYY1grNlJ3WFpLaXNRSW9sOGxBSkFnejQwNUNGWWVxaDRRamdCY2VZVW84L0JTaWttUGtnNGYzM2Q2NnQvUjY0Q21lZTRWZGhLZzl0WmpmcS9qYzVnaWJ1Ti9IWC9wdm9LUEUzL2lUanZyUzExYUZ6d3VnNGVSbi9ZaUtDZ2s1QTUrbHhwc2pza0Qvb0lPL0xXTmFXV3N5Rm9oVE5Rci9UWE9sSUdEc0NoMkJZdUdkL3dtenhUSUpxYm5lWFo5OU9HTUtKTkwyK3l2bjdsS1hwNUllWERHNHViT0NZVzUwck10K0JMOGhFQXhLdnNmQ2NiUE0rYTExUE52dGJRK3FrZlR1N3NKbDlnRURyWkllOUlPUlJ3eDZEbktMNGN5TENxZHJ5Wi9YZk13WWdHWlpJSXkrOVlwOUxITGJXaEszWnhyMGhUeFMvNm1OYkJ2djhpVVgvakJsa3Y1MmtoTmtXQ3BLbFYyRFJYb3lnd1ozaWVhbVNxeFl2Z3YyYm1VSlV5SUxYSkM5NktObHlOUlJPcVk2cHJkd2RKTEF6c2VYaExPRUJDR3ciLCJtYWMiOiJiY2ZmNTNlMDE3YzFjZDNiNzEyYjkyZmI1YzIwY2M1NzkwZmYzMDI4YThiZWYzMTlhOGM1ZjAzNTBiMTQ0M2QxIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZkZzM4UUdZQWx3T1lUeVVTN1RlS2c9PSIsInZhbHVlIjoieDdZaXJ1dVk3cktWSFRhQVVKQmpleUZTMGhHcW5aSm4yRFkxbmg1bUU5c2ludXBSc1FhSTB3bFNNNS85SDFWNmYreWw4V21FZG1qcW5mYWdEL2VaeVNXMTNvNXFUZ2wraTdtaE5PUUx2b2Vrb0FuVW04THhvNEJDRXBQM3YwU1hIOHMrZHlyTmxYamV0Q3NSLzRnR2lOWVdSSkN2WnZFOXo1ZGd1cWoxeHl3OTh3bkRxWXhIV1hYdjJMakg2d0hpcjJCZGhYdTlDMWhOODhCVlVqM1UvRitLd3k4Z2R1UVhicllUaUREbkN4TnNjTVkya2tSbWtPMGhkSGpaRE1hREl0VEsvODFCZDNuZlcwTWpkTWJYRXVLeGkzQnhrelgrSVBOc29zZFdVODVkSEhGcDJ2dGdkM0ZiNDNsYnNIQ2hKcnRxZHFTL2pOV1NaMUs4QmljRmJmZkhUWm92VmxWelJrYURNUEdxRHB3aGxoMUJ5Q01JYVlhcHpSaEUwYWtpdHRXUFoyYU5lT3NvMUZVdWJKek1YNzFrUVNkWWhTREtJbU5ManBWVXd4WG5TUFNLcW9paVMrdEFjTjQwSXEwdTJTbHhvSHF6NktFWThXVXlIMlFxeVNFVDUrMVBKYVlpWFdKeTJhODNLSlAzTGFoOFE0cVliT0F2em5NaTdybHgiLCJtYWMiOiJhOTE3ODljM2EyNjc0NmM0YzYwYjNjMTBmYjg0MTZjZDMyYmM4ZjQ4ZGVmNjgxZTQxY2VmMTM5N2I0N2EyYjY3IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFJV1B5NHNoTWtRS2RFY3plQmZhc0E9PSIsInZhbHVlIjoiS1puMXhmOHBKZFBza0d1b2ZKWGc1RG9XUi8xczB6cHNTMUFMMzVDR0R4OFNreFNaRzVSQ3NuUFA3S09lc3hqOWpkK05kV3IraVZyOGx5UllydDVvc0MrYWpYY1grNlJ3WFpLaXNRSW9sOGxBSkFnejQwNUNGWWVxaDRRamdCY2VZVW84L0JTaWttUGtnNGYzM2Q2NnQvUjY0Q21lZTRWZGhLZzl0WmpmcS9qYzVnaWJ1Ti9IWC9wdm9LUEUzL2lUanZyUzExYUZ6d3VnNGVSbi9ZaUtDZ2s1QTUrbHhwc2pza0Qvb0lPL0xXTmFXV3N5Rm9oVE5Rci9UWE9sSUdEc0NoMkJZdUdkL3dtenhUSUpxYm5lWFo5OU9HTUtKTkwyK3l2bjdsS1hwNUllWERHNHViT0NZVzUwck10K0JMOGhFQXhLdnNmQ2NiUE0rYTExUE52dGJRK3FrZlR1N3NKbDlnRURyWkllOUlPUlJ3eDZEbktMNGN5TENxZHJ5Wi9YZk13WWdHWlpJSXkrOVlwOUxITGJXaEszWnhyMGhUeFMvNm1OYkJ2djhpVVgvakJsa3Y1MmtoTmtXQ3BLbFYyRFJYb3lnd1ozaWVhbVNxeFl2Z3YyYm1VSlV5SUxYSkM5NktObHlOUlJPcVk2cHJkd2RKTEF6c2VYaExPRUJDR3ciLCJtYWMiOiJiY2ZmNTNlMDE3YzFjZDNiNzEyYjkyZmI1YzIwY2M1NzkwZmYzMDI4YThiZWYzMTlhOGM1ZjAzNTBiMTQ0M2QxIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZkZzM4UUdZQWx3T1lUeVVTN1RlS2c9PSIsInZhbHVlIjoieDdZaXJ1dVk3cktWSFRhQVVKQmpleUZTMGhHcW5aSm4yRFkxbmg1bUU5c2ludXBSc1FhSTB3bFNNNS85SDFWNmYreWw4V21FZG1qcW5mYWdEL2VaeVNXMTNvNXFUZ2wraTdtaE5PUUx2b2Vrb0FuVW04THhvNEJDRXBQM3YwU1hIOHMrZHlyTmxYamV0Q3NSLzRnR2lOWVdSSkN2WnZFOXo1ZGd1cWoxeHl3OTh3bkRxWXhIV1hYdjJMakg2d0hpcjJCZGhYdTlDMWhOODhCVlVqM1UvRitLd3k4Z2R1UVhicllUaUREbkN4TnNjTVkya2tSbWtPMGhkSGpaRE1hREl0VEsvODFCZDNuZlcwTWpkTWJYRXVLeGkzQnhrelgrSVBOc29zZFdVODVkSEhGcDJ2dGdkM0ZiNDNsYnNIQ2hKcnRxZHFTL2pOV1NaMUs4QmljRmJmZkhUWm92VmxWelJrYURNUEdxRHB3aGxoMUJ5Q01JYVlhcHpSaEUwYWtpdHRXUFoyYU5lT3NvMUZVdWJKek1YNzFrUVNkWWhTREtJbU5ManBWVXd4WG5TUFNLcW9paVMrdEFjTjQwSXEwdTJTbHhvSHF6NktFWThXVXlIMlFxeVNFVDUrMVBKYVlpWFdKeTJhODNLSlAzTGFoOFE0cVliT0F2em5NaTdybHgiLCJtYWMiOiJhOTE3ODljM2EyNjc0NmM0YzYwYjNjMTBmYjg0MTZjZDMyYmM4ZjQ4ZGVmNjgxZTQxY2VmMTM5N2I0N2EyYjY3IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642183192\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}