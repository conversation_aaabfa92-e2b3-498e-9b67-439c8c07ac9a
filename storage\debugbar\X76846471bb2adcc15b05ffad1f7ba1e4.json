{"__meta": {"id": "X76846471bb2adcc15b05ffad1f7ba1e4", "datetime": "2025-07-14 17:58:47", "utime": **********.641363, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.115663, "end": **********.641378, "duration": 0.5257148742675781, "duration_str": "526ms", "measures": [{"label": "Booting", "start": **********.115663, "relative_start": 0, "end": **********.556615, "relative_end": **********.556615, "duration": 0.4409520626068115, "duration_str": "441ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.556626, "relative_start": 0.4409630298614502, "end": **********.641379, "relative_end": 1.1920928955078125e-06, "duration": 0.08475303649902344, "duration_str": "84.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02789, "accumulated_duration_str": "27.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.59076, "duration": 0.02641, "duration_str": "26.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.693}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.626969, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.693, "width_percent": 2.295}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.633482, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.988, "width_percent": 3.012}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer/eyJpdiI6IjBab0hPaTJkODVjNlBNVzVHajRKUWc9PSIsInZhbHVlIjoiVjVzVmpCRkNkNDU0UkxRMUlTckxWZz09IiwibWFjIjoiNTc4NGUwMjdiZmE2ODA5ZjU3NGM3ODNhOTY5MGExNjA1ODcyMDUyNzA1Yjk4NjRlMDU1MzA3NTRjZjlhNWFmMyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-717239490 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-717239490\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1738992799 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1738992799\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1169911213 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169911213\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-977659186 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6IjBab0hPaTJkODVjNlBNVzVHajRKUWc9PSIsInZhbHVlIjoiVjVzVmpCRkNkNDU0UkxRMUlTckxWZz09IiwibWFjIjoiNTc4NGUwMjdiZmE2ODA5ZjU3NGM3ODNhOTY5MGExNjA1ODcyMDUyNzA1Yjk4NjRlMDU1MzA3NTRjZjlhNWFmMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515925285%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlGbG0wem1UbnJBUlNGUnlwb3NIL2c9PSIsInZhbHVlIjoiZlJpWXlxbFd3QnppR3RYNFAyKzdsN0cwWjlmYUY4SmFqMVZvS2Qza3lnMHYrTW1BNzUyaEJqRkpHZEpxek1UNGVrci82Yk1qZ0tFblRwaTg2aWtTSWJHblNQMXluMXp6endPYmk1TmZ2Z1owRWhIbUlNMmlxYU1JNEd5UjF0YVlXWGNkM1VCai9NdmltL0FhR3FQb2swcXdSenFzSVU1b0M2ZmJsbVVoaDMraXg3NmFoTHVJQUxmU2xiMTBNeFJ1WEVUS3M3V0VoVUludHNIS0EzODdJbkw5bDQ4Q2J1aXFrenErVE9uSnJMTERiV3BoY1dFcmxyVStENFIwOTF2cmR4bzdMR0RrQ2ZrV0w4bmVreHpaYWtCSGNsUm9IOHEzTzgvRG80ck1hVTF3cmFnaWdmbi84VzJJc0M1NHRUc04zZ0tZQ2RBb1JXZnJlcENPck5DdnVuU1QwUnhYYVZwY0k0b2NGVE9TR0tONGZhbHE5K203MG8rbTdZb21RYzNZMm03aTdaWHRGQmRiVlp3b2N4cWpvRnN0TFBFdGljNnMvZlhRQzlSYktjcmV5RWtZT0dYcDJVV3RBK3dBT1ZBWUVHc1RKSnFyM2Q0a0V4aWtNUkZZR2NYdTMya0JVUHFhNWJaNzA0cWdvV1NMckxXRGFWMGc5WnFwbCtNNjFtS2QiLCJtYWMiOiIwMDM5NDVmYWM1YmQ4MzM3NjhmMWRlOTU3NmFjM2YxNDNiMWRkMTNkNjcwNzAwZTUzNmVlOTg5NWViYWNjYjVjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdrYnlvM2xwSDdBVHFsaTdYTFVwRmc9PSIsInZhbHVlIjoiZ2k3ZzhWSklNUjA2a3RSR2pkRlhrZTAwVEsyZVI4WThTdG1GQXZ1WG1GYmZ6Qkt5cFVDdzIycnIxSVdCRnlnL1J2eW9neXZPaWR5b2lHYVFUT2lhTHlFVVBUdjN4ZmtVWGE0SmxUK1ZhamZXQXhhT0ZhYktnYlRhQmplTGRSUmlWR0dwZmdPeTVTUkljV3J4Y0p4MnQwdUFIWnRGMFlzeUgvT2MvczErSzAwVngzOE9BZkxISGdwN3VTZ2wzcDg4RU5IaE9kbk5yZHkwNU0yZGN1TVYzUGd4Ym1WTEYyTndZQ0RjbGVXcDR2Z1k3NGR0YzY5SVN4TGZSU05LOHJqaVdLRldDRlU3RzZacHl0a0dyT2VHU3ZhRzNua0duRW0wMG1FRXdzWDNjY2JSaWRpRmhYUjIrUlNZUUdoaHE5aWVkQXRDN2xVWVBRVngxbmhLTTMrVWxCT2JXcDRFV3JIMVdhUldtY1lGSTRENHA5R2w5RzAzSUhDTmNpZ3NNdjFtVzNaZllIZUpRdnY3THFCQWVaRVplTjY3Z213RDVyQnk3Y3k3VnpBWStYWW1reUl0QVJLeDlNdHMrNmxhOEJjZ3VPNnh6VjJWaDJOQTNoeS9leWRZYnZYUm5kMGt6aDBQMFVqM1pTamVvelhRdHMvNHd5b1VQQ1B0WTlnTGRQY24iLCJtYWMiOiIzZTdhNjI1MWQzNjQxY2RiM2I2NjczZmY1ZTI4ODVlNDdmZGI0ZmNkZmFjMDI2NzRmODJmNDhkMWEzNjAwYzk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977659186\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-851135705 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851135705\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1060189673 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InorMzlwazI1ZUZ1SUtsZXpLSTlxSHc9PSIsInZhbHVlIjoiRUhkbGt2Q0NaaktCS3YrSlNvOC9ab1ZPdUNqTTFPdlpnQkZXUjJ2YWdqWldTQ3Y4aFljZElPTXk1anl3MVVhQkFhMmQ3bjNkb3BCZ0ZxNEdxUzJKaFVLMVFwbHZtemVqY1dCSVE2Zkk4Z21kTVhHQmtwNmpkeTQrNjVlVzBPT2R3VWJaUWdidzEzaGU2cjczb2hCQXpoRmpxaTUvQ1ZsTURJLzdlWmJoaGNlWm1nRFhMYjQ4MlZvenFGMHJTdkZxTUJZcWo0bmw4andwY2hkZ1kyV1VWUHhwekxLWmFqVHg4Yk42SkxvS3l6QVhPWlE0RThIbGt6Ky84allOQVFCYkw3bXc3d25wVlN0aUJDWVVROWV1N2kwYWEveXdXVEpFN1JhRXRRWTBReFROdndyWXBrV0FSNGVPazFUeDNnejhkRGtqVEtiWDNFVVhhUjhtT0dqVHVlUXBZbEt4UnNNYWp1RXMyandveHAwNWRqamtjN0NRVkRWTHVFa0xzL2RmOWRFWWx5MERiTk0rQWVWU0NrcTIxV0JBNkw5MzlsQWtyeGtyTUlPdi9vWmhCZUgzaE1rNmRIaFcwZ0RUSFJqV2ZuMk02WS9PRS9NODdzVWFGbldNaUJzemtoZ3hya0d3bkY0bS9LUnBhVDBvajdmd1BweGJndU82VGFLYXZEeGUiLCJtYWMiOiI2ZDBjMjViZGJmNTg5MzE5NmJiZTQzOTI0MmVhY2Y3ZDFmNzY3YTJiMmU2YjBmODA1YjFjODY0YjUxMmMwZmQ5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZ1NlhXaUdPYlVpTCtuVU0xN2JaYmc9PSIsInZhbHVlIjoiRkNyVDc1ckNyd2J3Y0d3d2E4OXlzbktzSXA4Rm90NDRlVFJnZnRKOGl3L2VOdGFIMWxubHFYRko5bGdBWS9VVGh1TTZDTlpDRHQvL3RTQ2hUQjNJNVU2SXNxMzM4SVJmVkxRT3pQZDRQU3UzSXVVNFI2UFpIcnhZNHhBVHRXVTA0eFd4OG4vR1UwOTd1UDN5MWlURmt2Uk4rOUo0RlJPMXV4U0dXWjR4MzIzTHR2OXB3OStzTzZjZFVUVWkyUkUxdkxoSFBkbWlSeTRwWTBhcWN4WDRJWG9wRVhOUVpyS1psNEU4bVZTUWVXSk5SMmQ3V25jNDRpNDRsbUdTdE45SUNuMStiRjJnMk11TnZPcXpWV0YvSEJEdkdBd2hEQ3JEK2RJNmR5a3E3bHEvSGRaQ3RQVlVBN2RPeXBVTFhibE51LzBTbjVLK243WWRuc0VNT0hqeEpVN0Rhaml4dWYvMHFqcUZjMFBRNzlFREN4Q2V2dG1nck9XazlHWHQ5dXpBdDZHb0RXUTQveDlOZEYwQllYWHJNcEhDNnJIRG4yZTg1VkhOWTBtbXBhcDQ1RG5JYXBYcXJLeUZmMlZvQ0xyRUpJRFkxWlNSNjNJTXI0a3dCdDhkRDhMSXltZU5oMUgvWHFzTDVmak9Nc0VGdmtkUkp4bHE5Y0FMT0pLWjVoeG4iLCJtYWMiOiJkNTc3ZmYzNjFiMTUxNmEwMjgzNzkzNWRlMmE4MzUxZGNhNTJhYTY1NzVkMjFmMzJiOGVlMGQ4YTAxNDdhNzE0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InorMzlwazI1ZUZ1SUtsZXpLSTlxSHc9PSIsInZhbHVlIjoiRUhkbGt2Q0NaaktCS3YrSlNvOC9ab1ZPdUNqTTFPdlpnQkZXUjJ2YWdqWldTQ3Y4aFljZElPTXk1anl3MVVhQkFhMmQ3bjNkb3BCZ0ZxNEdxUzJKaFVLMVFwbHZtemVqY1dCSVE2Zkk4Z21kTVhHQmtwNmpkeTQrNjVlVzBPT2R3VWJaUWdidzEzaGU2cjczb2hCQXpoRmpxaTUvQ1ZsTURJLzdlWmJoaGNlWm1nRFhMYjQ4MlZvenFGMHJTdkZxTUJZcWo0bmw4andwY2hkZ1kyV1VWUHhwekxLWmFqVHg4Yk42SkxvS3l6QVhPWlE0RThIbGt6Ky84allOQVFCYkw3bXc3d25wVlN0aUJDWVVROWV1N2kwYWEveXdXVEpFN1JhRXRRWTBReFROdndyWXBrV0FSNGVPazFUeDNnejhkRGtqVEtiWDNFVVhhUjhtT0dqVHVlUXBZbEt4UnNNYWp1RXMyandveHAwNWRqamtjN0NRVkRWTHVFa0xzL2RmOWRFWWx5MERiTk0rQWVWU0NrcTIxV0JBNkw5MzlsQWtyeGtyTUlPdi9vWmhCZUgzaE1rNmRIaFcwZ0RUSFJqV2ZuMk02WS9PRS9NODdzVWFGbldNaUJzemtoZ3hya0d3bkY0bS9LUnBhVDBvajdmd1BweGJndU82VGFLYXZEeGUiLCJtYWMiOiI2ZDBjMjViZGJmNTg5MzE5NmJiZTQzOTI0MmVhY2Y3ZDFmNzY3YTJiMmU2YjBmODA1YjFjODY0YjUxMmMwZmQ5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZ1NlhXaUdPYlVpTCtuVU0xN2JaYmc9PSIsInZhbHVlIjoiRkNyVDc1ckNyd2J3Y0d3d2E4OXlzbktzSXA4Rm90NDRlVFJnZnRKOGl3L2VOdGFIMWxubHFYRko5bGdBWS9VVGh1TTZDTlpDRHQvL3RTQ2hUQjNJNVU2SXNxMzM4SVJmVkxRT3pQZDRQU3UzSXVVNFI2UFpIcnhZNHhBVHRXVTA0eFd4OG4vR1UwOTd1UDN5MWlURmt2Uk4rOUo0RlJPMXV4U0dXWjR4MzIzTHR2OXB3OStzTzZjZFVUVWkyUkUxdkxoSFBkbWlSeTRwWTBhcWN4WDRJWG9wRVhOUVpyS1psNEU4bVZTUWVXSk5SMmQ3V25jNDRpNDRsbUdTdE45SUNuMStiRjJnMk11TnZPcXpWV0YvSEJEdkdBd2hEQ3JEK2RJNmR5a3E3bHEvSGRaQ3RQVlVBN2RPeXBVTFhibE51LzBTbjVLK243WWRuc0VNT0hqeEpVN0Rhaml4dWYvMHFqcUZjMFBRNzlFREN4Q2V2dG1nck9XazlHWHQ5dXpBdDZHb0RXUTQveDlOZEYwQllYWHJNcEhDNnJIRG4yZTg1VkhOWTBtbXBhcDQ1RG5JYXBYcXJLeUZmMlZvQ0xyRUpJRFkxWlNSNjNJTXI0a3dCdDhkRDhMSXltZU5oMUgvWHFzTDVmak9Nc0VGdmtkUkp4bHE5Y0FMT0pLWjVoeG4iLCJtYWMiOiJkNTc3ZmYzNjFiMTUxNmEwMjgzNzkzNWRlMmE4MzUxZGNhNTJhYTY1NzVkMjFmMzJiOGVlMGQ4YTAxNDdhNzE0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060189673\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1282439513 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6IjBab0hPaTJkODVjNlBNVzVHajRKUWc9PSIsInZhbHVlIjoiVjVzVmpCRkNkNDU0UkxRMUlTckxWZz09IiwibWFjIjoiNTc4NGUwMjdiZmE2ODA5ZjU3NGM3ODNhOTY5MGExNjA1ODcyMDUyNzA1Yjk4NjRlMDU1MzA3NTRjZjlhNWFmMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282439513\", {\"maxDepth\":0})</script>\n"}}