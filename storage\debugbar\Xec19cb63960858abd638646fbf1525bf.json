{"__meta": {"id": "Xec19cb63960858abd638646fbf1525bf", "datetime": "2025-07-21 01:17:42", "utime": **********.811048, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.419468, "end": **********.811062, "duration": 0.39159417152404785, "duration_str": "392ms", "measures": [{"label": "Booting", "start": **********.419468, "relative_start": 0, "end": **********.761194, "relative_end": **********.761194, "duration": 0.34172606468200684, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.761203, "relative_start": 0.3417351245880127, "end": **********.811063, "relative_end": 9.5367431640625e-07, "duration": 0.04986000061035156, "duration_str": "49.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46103688, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00322, "accumulated_duration_str": "3.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.79068, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.839}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.801385, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.839, "width_percent": 18.012}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.804301, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 83.851, "width_percent": 16.149}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  1897 => array:9 [\n    \"name\" => \"option- خيار\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1897\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1896 => array:8 [\n    \"name\" => \"zucchini-كوسا\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"id\" => \"1896\"\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1225627476 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225627476\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVZUytYQkRYdml2ZTVwOFRTbjd3ZGc9PSIsInZhbHVlIjoicTljN0NuQmFPTmlvZUVpRzNTbFFJSWJHc0g5dVFsZTFOUGtQdUp2NTBWY1dGOUdZZjhWSXdocVpaWExmNHRzSVpTaGNEdmh3RmYrTHdIMXEwbmpPZGxiaWNkM2hRMWlJM1VQdEtBTlE2eVIvc0w4RTUybTc1Tko2ek1reWl3bE9ES2duSWFPV1BUMDJabWNhME1uM3Y0b2tqbTBTQlIzVHh0WlNLSTJGdkhPT3VLc3FwOXJrelJxU0x4Z3NhKzB2dE5mams4ZlVXeVcvMm54OEhBRVdGcUxXMnFiK3ZBSzJ3T2s1ODlsTTloS0lhTXpsT2hZK1ljUTJHMGNpaFd5TDFoR2ZIMmZ1THBNYmtnanIrT3lOeUpHRG1Ra1haeHlMeml3QjIyQUlXUDFDa3BCdW5lR0ZwVE5uMkJvZTJYcDJWMzhNVXViM3BET01yVjBxT1VsRDBnMm80Ym5jdWJjNjM0RUh5ZE05ZnlPN3hnK2ZTbTBPZnJXSXNLTXlCY2VOZzJ5bko1ZFVLZmdlT010bjBlaG9FS2NMV2w1bHl3RVhtVmdKbHZZVVdXaEdMak1hR0tEYUFQK2c5cnJWN05uVFlyd1FJMnJaZmpFTFVEMGFnRVlpYmJKNjRIVEpLYjd3WjRWbXl4cU5vckZLc01Qa3pmdXljSlg1RUw0NTUyem4iLCJtYWMiOiJmZTlhYzg1YTkyZTljYzFkNWM1OWM0MThkYTQyNjY5NTAxNGMzMWUxMWQ1OTU2MjM3MDY1YTc4OGIyNTlmNTk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFxa3oyTWE3YTk4ZmM3SG41cWdPTWc9PSIsInZhbHVlIjoicnFaRS95bmFVMzlIcFM3cVJTSEZvbGtpK1BMSFRLYmluYVRPdVpaNmI2TFc5RCt0UWZGSk5yNS9kM1pkU3pObzN2K25BcXJIbTdkbzhGUVNGeU1sQlJTQ2RhRTZSSlVjdHpOMERmRmRXV0pPZzVGcUllL0lGejZSWk1JdGJoKzNyY0ZpYmI4VjdrdUEvL09GUFN4bnk1WEY1em9ZdHhaZjVXTFJCRUpMVk81b0dBaGJMN1ZYVGpTNTQyUlp5cW1TWEM3cUJLVmkzSE5kVU5sREN4eFBxQXJwNGpKSDV6TVRvd3VWYm1mRE5zQWt5dzBnVlpKU1pQTmdlSktyZktDc3NadjY4Lzh1c0ZsSUhUcjJSSldRWDFmNHlZOGROVEpmSGFKNi9QLytzbHVhL09neVljTWpjeDJVc1dBeE1UOTdTaXl3aC9DOURwcXV0a0VOMkR1azlKdmVzeTIxNitnUmFURml5Yzl2QUVwZU1MSElPYWRiV0tweXNIalhMU1BacUIyRFJpQVpVWTJFdjZvZTlWTkhmVURLSWJCWW5BWmpGNWN4WHpyRGN0dVFjYW9LSUhCU2M4VC9FQm5HdmtJaE1tZk1VMnhvbElwNDAvQzJGcnlPQXRRekRqUkpkZ1RhMWx5U0hSL3JtQ241N1ZUSG1Sby9KRlRpOUpjZ3QzbEgiLCJtYWMiOiIyYzkzMWE2OTdmYWI2NDRiY2JhMjJkODc4ZGViMTU4Y2JmNDgzNGY0YjQ4MGMxNzQ5Y2NjZDViMDRkYWZlZDgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2037709087 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037709087\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhGVHBCeHRKWFR3bGpUVGdhK20zM3c9PSIsInZhbHVlIjoiZWZWdWgvTzcyL3dFSisxWHVQNC9ad1ZWcmZuaXF0V1REcHcwbk1kMDBuNVJOZnhMWnJWcWE5WkhTZGloeC9meDRIZkVkMTNpSjRuR2IyUGVuWmROcHlIbzY2cytOcGsyQlBjdC9wWDAwOWQzTW55Um5HcGlIcHNsQTBVMmNpdmdkMlRIdjhlcy9ZdVJmR2xkWjBUZnVtc1diU3lMbXlNU1BFVkNZNEpYVFdBNkFDWklrUUw1UlZuTWJoUmdSeFd2WHZjWjhnYmtSOVAzT2FXYURCNnBZS3BRVUlxT0duYzFIemtLdEc1dVNRSGxEcnVKS3p0N25RdVNEdnFKVUhUUUM5bjlQQkxxTXF1SldmM2JUU0IvcWhxbXg4d25tWGdsdkJRaWFQMStKek1KL1RmTE5aKy80RjdLOFk2YmtTVTRjZWRtQndjQUJQK3ZHcG9TeXdKM2FoWHRpeEtqaloyN0FRbDU0T2xFQTI4ZXlvVzRlcklnQ0tiaDhOd3dMQVVXY0hnT0hQWUxtZ2lLejVoQ1BQczB3UEdZWGU2WXFlZzArK3dMeUxodmNmcWVBVHRuMTFrNUEvbHZqUlZJQjBaSWlMeXk2T0xBSGc1ZjV5TzhPQkN1RTdwWGtKYTlWdkNZRWtPVUN5Tng4aXlJc2hIeGxPdFphbTVFM01ySEdKaGsiLCJtYWMiOiJiMmY1ZjYxMjhkZTM2MmVmN2Q2NTcxZDhkZDBlODE2ZDU1YzZhMTI3MmUwM2EwODkyMzBjNTU2ZjUyNjc2MWQ4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdNWkd3anY2NlNBQWI2RUJZV1JGQnc9PSIsInZhbHVlIjoiaU1HbkVDcE1zMDE0czBzL0p3dmE0QVFIMTVMdTk0blNxb1Y2SnF3TEg5R2owZEcrOUhuMXhtOGNaKzFMeUpOUGhXdjJMWGp0bW5IVVlDWHB5R0N6cmc1cHVIcGdCZDVONm1xSlVGTUNKTjkxZnNPbWt0V3ZmNGg3L3RLWFEzTHcvb0JROFJCdm0vT3VPU0RZZCt5SDVlTFhJd2JBejZjNlVNTVlSMi9maXJndFRmNzFIS3ZyU1lhL1Nkcm81bkxGZTFldHFFbWdXSFBnM0ROdGo1VWUzMHRxYi80cU96YmgycHo5SkhQcVV5b0oyTm1WVlA0OGc1WVlrUnRVUzlmYnU4SlpBOE5NYjZKRnVSTzNub3YrSVAvMFJvZkZaL3BZL1FKTEY2RHNJOW1wVUlmS2hKaTFvUGxHcjZhcGI0Q2JjMHhSQmFOai9LZ1dBWjgya0NsbmVTRmpYV0d2SUlkb2t2TjcwanRZdHRWb2VMbTRpOUVYTzVUUjZMa0JDNHI2TlBEaitVY0xRbzNuc2VtbmhzN2dKM1R3ZnZpN3BGelFxTmVGUDdFWjZ3SGE5RDhUTm96UU1uWUhla2Nmcm92aE0zckFhMmE0Mjl6bWZTWlZMdy9UT2t6d1JDRE4wVHByVkYvK2dXbW5FRlJSbnhCVHNTeExyMkdYckFpekJGSmYiLCJtYWMiOiIwMjc1Yjc0OWM1YTRiOWEyZmYzNzE0OGQzYTJhOTJlYTQwMzBjMWM5ZjYxYzViZGVkZTA2YTYxZDZhOTgyYWY4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhGVHBCeHRKWFR3bGpUVGdhK20zM3c9PSIsInZhbHVlIjoiZWZWdWgvTzcyL3dFSisxWHVQNC9ad1ZWcmZuaXF0V1REcHcwbk1kMDBuNVJOZnhMWnJWcWE5WkhTZGloeC9meDRIZkVkMTNpSjRuR2IyUGVuWmROcHlIbzY2cytOcGsyQlBjdC9wWDAwOWQzTW55Um5HcGlIcHNsQTBVMmNpdmdkMlRIdjhlcy9ZdVJmR2xkWjBUZnVtc1diU3lMbXlNU1BFVkNZNEpYVFdBNkFDWklrUUw1UlZuTWJoUmdSeFd2WHZjWjhnYmtSOVAzT2FXYURCNnBZS3BRVUlxT0duYzFIemtLdEc1dVNRSGxEcnVKS3p0N25RdVNEdnFKVUhUUUM5bjlQQkxxTXF1SldmM2JUU0IvcWhxbXg4d25tWGdsdkJRaWFQMStKek1KL1RmTE5aKy80RjdLOFk2YmtTVTRjZWRtQndjQUJQK3ZHcG9TeXdKM2FoWHRpeEtqaloyN0FRbDU0T2xFQTI4ZXlvVzRlcklnQ0tiaDhOd3dMQVVXY0hnT0hQWUxtZ2lLejVoQ1BQczB3UEdZWGU2WXFlZzArK3dMeUxodmNmcWVBVHRuMTFrNUEvbHZqUlZJQjBaSWlMeXk2T0xBSGc1ZjV5TzhPQkN1RTdwWGtKYTlWdkNZRWtPVUN5Tng4aXlJc2hIeGxPdFphbTVFM01ySEdKaGsiLCJtYWMiOiJiMmY1ZjYxMjhkZTM2MmVmN2Q2NTcxZDhkZDBlODE2ZDU1YzZhMTI3MmUwM2EwODkyMzBjNTU2ZjUyNjc2MWQ4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdNWkd3anY2NlNBQWI2RUJZV1JGQnc9PSIsInZhbHVlIjoiaU1HbkVDcE1zMDE0czBzL0p3dmE0QVFIMTVMdTk0blNxb1Y2SnF3TEg5R2owZEcrOUhuMXhtOGNaKzFMeUpOUGhXdjJMWGp0bW5IVVlDWHB5R0N6cmc1cHVIcGdCZDVONm1xSlVGTUNKTjkxZnNPbWt0V3ZmNGg3L3RLWFEzTHcvb0JROFJCdm0vT3VPU0RZZCt5SDVlTFhJd2JBejZjNlVNTVlSMi9maXJndFRmNzFIS3ZyU1lhL1Nkcm81bkxGZTFldHFFbWdXSFBnM0ROdGo1VWUzMHRxYi80cU96YmgycHo5SkhQcVV5b0oyTm1WVlA0OGc1WVlrUnRVUzlmYnU4SlpBOE5NYjZKRnVSTzNub3YrSVAvMFJvZkZaL3BZL1FKTEY2RHNJOW1wVUlmS2hKaTFvUGxHcjZhcGI0Q2JjMHhSQmFOai9LZ1dBWjgya0NsbmVTRmpYV0d2SUlkb2t2TjcwanRZdHRWb2VMbTRpOUVYTzVUUjZMa0JDNHI2TlBEaitVY0xRbzNuc2VtbmhzN2dKM1R3ZnZpN3BGelFxTmVGUDdFWjZ3SGE5RDhUTm96UU1uWUhla2Nmcm92aE0zckFhMmE0Mjl6bWZTWlZMdy9UT2t6d1JDRE4wVHByVkYvK2dXbW5FRlJSbnhCVHNTeExyMkdYckFpekJGSmYiLCJtYWMiOiIwMjc1Yjc0OWM1YTRiOWEyZmYzNzE0OGQzYTJhOTJlYTQwMzBjMWM5ZjYxYzViZGVkZTA2YTYxZDZhOTgyYWY4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1897</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">option- &#1582;&#1610;&#1575;&#1585;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1897</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1896</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">zucchini-&#1603;&#1608;&#1587;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1896</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}