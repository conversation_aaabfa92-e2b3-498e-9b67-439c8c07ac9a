{"__meta": {"id": "X273756c1b26eeec8b662c8a97097c273", "datetime": "2025-07-14 18:30:25", "utime": 1752517825.247163, "method": "GET", "uri": "/goal/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.356155, "end": 1752517825.247177, "duration": 0.8910219669342041, "duration_str": "891ms", "measures": [{"label": "Booting", "start": **********.356155, "relative_start": 0, "end": **********.729646, "relative_end": **********.729646, "duration": 0.3734910488128662, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.729656, "relative_start": 0.3735010623931885, "end": 1752517825.247179, "relative_end": 2.1457672119140625e-06, "duration": 0.5175230503082275, "duration_str": "518ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52426464, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "1x goal.create", "param_count": null, "params": [], "start": **********.817231, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/goal/create.blade.phpgoal.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fgoal%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "goal.create"}, {"name": "5x components.required", "param_count": null, "params": [], "start": 1752517825.243579, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 5, "name_original": "components.required"}]}, "route": {"uri": "GET goal/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "goal.create", "controller": "App\\Http\\Controllers\\GoalController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FGoalController.php&line=25\" onclick=\"\">app/Http/Controllers/GoalController.php:25-37</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.022529999999999998, "accumulated_duration_str": "22.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7591438, "duration": 0.02043, "duration_str": "20.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.679}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.787788, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.679, "width_percent": 3.817}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.802851, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 94.496, "width_percent": 3.063}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.805292, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.559, "width_percent": 2.441}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2100809188 data-indent-pad=\"  \"><span class=sf-dump-note>create goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100809188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.80973, "xdebug_link": null}]}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/goal/create", "status_code": "<pre class=sf-dump id=sf-dump-1975168499 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1975168499\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-883143384 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-883143384\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-733175666 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-733175666\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-99511035 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/goal</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; XSRF-TOKEN=eyJpdiI6ImdOQjNPZUdaVk5kZnFsK3d6c24vWWc9PSIsInZhbHVlIjoiNkxPWURVbGZiSlVLYzVxRVY0dEIwV0NabzhLTWszRGhmTTdCMjl0NG9uc2gzYTlhVDdzWnNCaEtkVFBBM2k3WE90VHEwQ0Zvd2ROcmJRbFdlOUtqSGFQbXphVjhLOUVMd3dHWFFLRmlrdk9ObXVJamp3dDRERkpCNFUyKzU0eDZIZmhaUTF0V2ZVb294SURScC85REFKOTN4aDJYWU9ndmxHV0VPUzQrN2liQ0taVTVoTnNSMDRUdkZSWEE2Y3BYZWpoQU5oOFRTdWdaSC9aTk9TQTJ2QXdkU1ZwY0tVWnIyYlcwTml1aWtDTWJXK3JTK1ZlSHJzdFdwTzlYeU1Genk3dGtqcE51YnhBMi9ZTjNNRCt2WU93d3FvYUdFN3lFbW1MeThWZjdqYUhjUjhZeUM4d0JTeFNwZnVLOWp6Z3JDNTlVZ0lGYjJXb3lWSjljVmxsOVhTNXJ5YnY3dUdudXkyMFV2Z1ZSOVhtNnE2THl6QUcvalRsb2xwZm9ldXVVa3hIdldORkovbDJuTG5JM1lXV1BEZkQrZ2dWT0FHMGpaWnVNSThMeFZyU1BwRkQ4UWlsQThiRCs4RWt1Y3Bja0VsSUlNZEkzczZBZUM1TnhvMWVaZDB4VWpRUjJFL0VyS2dCSUJEeiswMUFRUGhiM1p1MmdTREZLdVgwNm1JeWoiLCJtYWMiOiI5MjE4OWM1OTM2YzY4ZjZhYWMyM2JmMTM3OWVhOTc4NzY2MjE4OWQ3N2IxN2I0ZjU0ZmU0ZGU0Nzc0MDQ2MWI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkwyWGpKalBJd003QlovNDhaSU5VblE9PSIsInZhbHVlIjoiV2JzMmFSRE1JT2N3a2tvb0I3ajBabzFiOVhoa1FkbmxmUjNjWVp2UFN5UGtLZHRYTzVxUUxEOEVhOXBwNDRCcXRxNkdLNU9rb2JJUTJYajNJQjBxSHpzZS9hYWFiVVdNd1o5UjVnemJQNjlyN2JaSDcra1RxdURxVjRVVWxaRk5LUEJ0bXJGeXJHWnFzWlNVaStzQ3dERWdDY3R3QjY0S2x0Mmc3VzhGcTErTUhYS2hlc3BKV3FKYUphLzdnZmFVQitqQWM0b3FvZGx6YjhWdFJuUFgxZWxRbnMzRWN3MG11cHlZUHc0QTRWcVdoQkNieDc1K0tLYWo4aFBqT1hEdVhPOWllcFl2cm13ZDh3OENjNFA2UFM4T0wwakZUeGRlL1BDcFRLM0M4VytWZEhaNlJLTzVybTBDeFFnd3ViekE4U2p4aEhXSlV5dEpmY0Ryak1VdGgvYVZZakwyNXNpSjZqQkhEQ2hOK1lxd3ljeFRBQ2o4T1N0eERoeXlMT2NmOE4vL2c1RThMK21PYXhNZzA4eUlHb1VPZ1lpaFhGSVp6QWs5eVFqbjBtQlJ6NFN1a3pPM3kzRmdkMkQvN0RLd2dTelpramdKT0hKeEZoSTdJcG1GZ3lBN09MYXg3dlBJcTFud3JtYm1wYnppTkI2ZU5IQ2RUeEozdFV2T2pGTkYiLCJtYWMiOiI0MGZlMzMxMWM2Yzk2OTRjN2Q1MDQ1NTFlNGM1ZTVkYzg5YjFkMjRmODEyNTgzMDYzYWE5OWJlM2I3NjQwMzA1IiwidGFnIjoiIn0%3D; _clsk=14bfr1r%7C1752517823124%7C43%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99511035\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-820041317 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820041317\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-266153505 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:30:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNqdVJCc0t1Q2ovSUZTZXJKQTZyalE9PSIsInZhbHVlIjoiL2pycFhjRVZlNkxqRFZ6UDh0c2F5OTIrd1l5NUFZSlBNbUJ5TE54OVpYNmgwMWlUam9FelpuSllnQ09FRWE3cG10ZGVaNENZOGJ5VUllT2VXSHVhZ1FwV2lIWXZkYjlxOC9Nb3ZHdHUzOGx0dDRLeXY1V1p3emIydkdPVTNlelFOcVE5ejMzdi9TYkhVNWhRYWU4L1JXM2xhQU8wb2R4UDlaQXZIYkJHNWljQk1oNUczOWh5MXl5Nm9iRlhrWGZYb0dkZzVPV25SaThxTlhyYUFiVHlMNGcrN3FkQVVMMDlpYlFJL0tYei9NVnE1eUYwWW0zU1hZOTVwdnpYZHRIR0pqRzBBNmcxanNSV3luSzQyUnpHN3Y3a0t1RGRzcUIwQjE4R24rUG9wWTZ5RFVoK1VUdi9sTDBJd0FtRkxMVWJRZU5IdHV3TmRrWUtLYm53aEp1QndLTGhldW81aXNTTDNlbnNzSStCdnVlYnRvcGc1NXpMNzNmVVVsaXI0aTBnTTdwRFUzcHRhL1oyMWlHVUpiZ2xBanRBbE5OTkVmK3kzVC94SDBJWXdFL0I1ZWZBckVGOUJlZFdjQXp5Z3I1dmN4TkF4bEZkeUpFUnBUblBvYnE5MlorSXk4Y0dPT1pPOVRoTGN4cTl0aTR2RCtSYW1rc1pDeWN5WDJidk9tMWEiLCJtYWMiOiI1ZGY3MjhjMWI5MzAyYjQ0NWMxZDZmYTQ0MzFlZmE1M2ZkNGFhM2Q4ZTk0YTE5OWY0MjU2ZjM2NTg4ZjIwMDMwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNkclUxQ2preEdERWE2Qm5hWGx6aHc9PSIsInZhbHVlIjoiS3VuZ2FqNEdKZW1XV0hDRkFqMk5CZGh1UXM0SU5lWUZBQllCbmRIWEpmUXkxUGNBQWhaRFB2dmJHK0w4OHNwS3pQNHZkb1VDSXBSSWRIRC80dHpZZVdyeU5VYmRmQW9scE9HRTlLR08zejFzVm1RVmlmS1hpNGNqUmZUV1Biai84KzM0NnJhTjBwZzAvMkJ0TlhCSjlvMmg5QnBMVm5PalJ0bEJqNmFTMWZLOGhvU3l1cXB2ZFRyL0svd3VBdUVncmtVQ0h4WXREMXFCRktQdGduQTVTUTRUalVleFhuNlM5MTlXdWl6eTNud01WcGRGSlA3N0lNMWRWUWNXRGp5NzEzWnExVFZFZUR4S2FZRFJyeThMQ2FabFVUUWdacGhqZk1pT0xIL25KWFp5aGJ5SGwyZWEveEFOdlcvMTJ1dE1MMlNON3o1NVdZaFRGYnIzWmhBR2NuT0xHUTl3ZHljSFNlSEF1ZzZlcElCRjQ3M1ZJeTdyS05pKzQ3eUlqWWVERE56Zml3YTd1Y0FtTyt1U3d0alZCeEVsRGxDVk5rSmdZVjJtTTROSnJUbnBIcjJISmtIZzhxOFFWeXBJYjVJQzYvTGc0d2owanYwQU9TSUVLdWIrNGg3L3VBcU5QMTRkaVhTcFkyWFNleHJqSFlsQlduWWxVQkk1UGZWT3VXb1AiLCJtYWMiOiI3OGZhMGY3MWUxMTRhMzc0MzcyNzZhMjZiYzM5MzBlZWZiZGY4OWNiY2FjNDBlNWVhODk1NDg0ZTFjZGRkMjc2IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNqdVJCc0t1Q2ovSUZTZXJKQTZyalE9PSIsInZhbHVlIjoiL2pycFhjRVZlNkxqRFZ6UDh0c2F5OTIrd1l5NUFZSlBNbUJ5TE54OVpYNmgwMWlUam9FelpuSllnQ09FRWE3cG10ZGVaNENZOGJ5VUllT2VXSHVhZ1FwV2lIWXZkYjlxOC9Nb3ZHdHUzOGx0dDRLeXY1V1p3emIydkdPVTNlelFOcVE5ejMzdi9TYkhVNWhRYWU4L1JXM2xhQU8wb2R4UDlaQXZIYkJHNWljQk1oNUczOWh5MXl5Nm9iRlhrWGZYb0dkZzVPV25SaThxTlhyYUFiVHlMNGcrN3FkQVVMMDlpYlFJL0tYei9NVnE1eUYwWW0zU1hZOTVwdnpYZHRIR0pqRzBBNmcxanNSV3luSzQyUnpHN3Y3a0t1RGRzcUIwQjE4R24rUG9wWTZ5RFVoK1VUdi9sTDBJd0FtRkxMVWJRZU5IdHV3TmRrWUtLYm53aEp1QndLTGhldW81aXNTTDNlbnNzSStCdnVlYnRvcGc1NXpMNzNmVVVsaXI0aTBnTTdwRFUzcHRhL1oyMWlHVUpiZ2xBanRBbE5OTkVmK3kzVC94SDBJWXdFL0I1ZWZBckVGOUJlZFdjQXp5Z3I1dmN4TkF4bEZkeUpFUnBUblBvYnE5MlorSXk4Y0dPT1pPOVRoTGN4cTl0aTR2RCtSYW1rc1pDeWN5WDJidk9tMWEiLCJtYWMiOiI1ZGY3MjhjMWI5MzAyYjQ0NWMxZDZmYTQ0MzFlZmE1M2ZkNGFhM2Q4ZTk0YTE5OWY0MjU2ZjM2NTg4ZjIwMDMwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNkclUxQ2preEdERWE2Qm5hWGx6aHc9PSIsInZhbHVlIjoiS3VuZ2FqNEdKZW1XV0hDRkFqMk5CZGh1UXM0SU5lWUZBQllCbmRIWEpmUXkxUGNBQWhaRFB2dmJHK0w4OHNwS3pQNHZkb1VDSXBSSWRIRC80dHpZZVdyeU5VYmRmQW9scE9HRTlLR08zejFzVm1RVmlmS1hpNGNqUmZUV1Biai84KzM0NnJhTjBwZzAvMkJ0TlhCSjlvMmg5QnBMVm5PalJ0bEJqNmFTMWZLOGhvU3l1cXB2ZFRyL0svd3VBdUVncmtVQ0h4WXREMXFCRktQdGduQTVTUTRUalVleFhuNlM5MTlXdWl6eTNud01WcGRGSlA3N0lNMWRWUWNXRGp5NzEzWnExVFZFZUR4S2FZRFJyeThMQ2FabFVUUWdacGhqZk1pT0xIL25KWFp5aGJ5SGwyZWEveEFOdlcvMTJ1dE1MMlNON3o1NVdZaFRGYnIzWmhBR2NuT0xHUTl3ZHljSFNlSEF1ZzZlcElCRjQ3M1ZJeTdyS05pKzQ3eUlqWWVERE56Zml3YTd1Y0FtTyt1U3d0alZCeEVsRGxDVk5rSmdZVjJtTTROSnJUbnBIcjJISmtIZzhxOFFWeXBJYjVJQzYvTGc0d2owanYwQU9TSUVLdWIrNGg3L3VBcU5QMTRkaVhTcFkyWFNleHJqSFlsQlduWWxVQkk1UGZWT3VXb1AiLCJtYWMiOiI3OGZhMGY3MWUxMTRhMzc0MzcyNzZhMjZiYzM5MzBlZWZiZGY4OWNiY2FjNDBlNWVhODk1NDg0ZTFjZGRkMjc2IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266153505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1666129397 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666129397\", {\"maxDepth\":0})</script>\n"}}