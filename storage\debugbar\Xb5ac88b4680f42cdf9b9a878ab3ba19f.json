{"__meta": {"id": "Xb5ac88b4680f42cdf9b9a878ab3ba19f", "datetime": "2025-07-21 01:17:54", "utime": **********.095157, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060673.643263, "end": **********.095172, "duration": 0.45190882682800293, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1753060673.643263, "relative_start": 0, "end": **********.04216, "relative_end": **********.04216, "duration": 0.3988969326019287, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.042172, "relative_start": 0.3989088535308838, "end": **********.095173, "relative_end": 9.5367431640625e-07, "duration": 0.05300092697143555, "duration_str": "53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46327528, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00268, "accumulated_duration_str": "2.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0754309, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.507}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.086111, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.507, "width_percent": 13.06}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.088509, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 86.567, "width_percent": 13.433}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-802000325 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNLS1ZGV1o0MW1oVC9pbUhCdDJmdlE9PSIsInZhbHVlIjoiRTMzbWpRUVBSeWt0MWNCRjFTWUhDZTJCWDRhTk1xL0JWa0RubHdjUHZOVHl0UDJZVHBtNjZ5SGsvNzIxSlhTcklReTg3ZHRZQm1MT2svRjNaNng1KzdialZtOUNtZ1djaHBnYlJldnRNNUVEWGl0UUMwdEVmM2hZM1NqcnIvQm9kZSsrTTFJYTQrM0ZuR1gxWGFYbFN2bC9uT05yaFJ1TUZsK1U1a3p3VDNZUFFrQ2dMZXlQd2VzckxOUHR4RE9xcmtuSEVTWEFqR0tOSWJESFBKNGRCQXlKVW1OK3dwWGFubUxjSk14d3B0V2J0czNYampvV2tYYTZITWFNbm45Z0pTd3ZlMkpiTVJlZnBNeW1DZzJOcmYzM3hraERuUFF3VElzL3BRV05rZjVML1phM0RWcG5NaUowZjNoMmdRVlRUSm82UVlMZlBaSHJEMHZuUHMrZlcyeTJ3OVplYXJjcjAwQ2Y0b1pFY2RTamtZQWdLNS9SVStjTi9reVZQYlRqOUdDcElFaVRFNjV2eVhnZSsrUHRFOTlPZ2k3SmRZRlNocW9Bb0hYOVN5SXFncldMZEowZzRYdTgrckRUNmdyZjNydXgxSXpvMVljYmwrSURtZWlIdkwwMC81ZnZJRXBZRmJNWnViVGxOMk5DcFhDdm5kcTI5K3BEMjQwbURkdzAiLCJtYWMiOiI1ZWVlZGU2MDY5ZDdlOTVmNGRhZjczZTRhYzE0ODgzMmM4MTQxYWVjZjMxZDRmNjI5NDBjYmZiMzhkYjcxNjNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlFNGlKL0w4Wm8zK2JNWUNhVGtZSWc9PSIsInZhbHVlIjoicjc2S21GRE1wRUJ4dHFZME1Qb2hxR2pLZk5XbjBBenFOU0N1ZjNYZStaVHIyeVpVTXZvbDhJMWI3THBHMFNBdWpicHlRRzhTdEpkY2ZCb0dORDdERE1VMHBxbmdSMlJTQ1JWTWtSSTYxdmhrckpoYmQvZ1VaTkx6REtZcnBMU3RnMDk5bXFCSHVKdnlZYjhrV0xDdXd6eE02Z0dBeU1mc3NHRjZjcUZNL0FJYzhaakNjajMzRGl1QnQ3YUxORnY5dWErNXpDK0FlaUxFbG5GQXNNM0dpajQ1aFIzajZST0w1UzRqMUVPQXJjc0tZZHl4YktBZHhET0RuV2IwMG55VDZOSjFGcEMwaTZScTAzTGpLVEY5RXIxSlRmWDBiaFIrOHhUK3JCaC9RbU1BOENPSTVUOVFscThMUU1walFjNHFLcWp1YmtkT09zZ1FWRE4zOW5JS0dEMnRMWHRVNGdxb2J4cW1pbFU4ckZ4MEJ4NGl1TXQ4bVI1Ly9PRzdHZVorOWpVRWV5cXBZZTNQNGRYbW9OOXRRcDNGZHkxcHNSdHhUamk2RmtNUEJXbDFOdGZsczByK0dOMTgrZGtlNy80c1pIR3R3YndVWjc5ZUlkV1Z3VWt0OGRxOXpXMnZLY2Nyei9ZSWt0TlMwUTdGdElROENzL2ZzOHRnLzZ0dGJqNmgiLCJtYWMiOiI3MTIzMzM0MjU2NjVlOTRkYWNiYTczMDQ5OGFkMDc1YWFhNzUyMTA2YjhkODljZjE4NTE2ZWY4Mjk3NTk1YTEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802000325\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1291056587 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291056587\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1795567705 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhES3hjbGI4dk5QODFLb3RJVFVaZkE9PSIsInZhbHVlIjoidHJySjQ4WFVrTmJtL2hMZkpHYlRjOTR2VFJORHdCQ1pERlFMclozQTJhQTEraXRaN3d4ZjhFaU8vNWpRL3MxOE1LWlpJRzlLa05NWHY4Yng3QXVLVjlJS2wwUm42TS9HVkYvUnRJbjFwOVMvL1BjVlZMYnhEb3JmWGE0MDdLSUlsR2ZuSEpaODE1akdEMy80UU9rMXBnSzZkT3hNems3blpMTDh0TmlUMzVUSWEzYzhLVFFGb3FvSmpXTEFXTmhaaFo5UGRpZmFrQTJpZkgvS2gyMzBmMk5jRmxHaktIZThsNWxiVGdyemxDWkFSY1dFNE12aG5WV3hlaWRQbEpzcWpLNlVNVGJXV3RMTStDblpBT0pndk56SjVaZ0tkcmJIZWg3NUpUZ2psakdpaTBpK1dIQXhmdlV2NnduL3R4UkVnYWhnL0Fha1Z3SnlnS3llcVZWWWk3QmV6T1R5aUJnVUc5aXBFUEt0dlM5VkE5QXZRRXAzV3VXeXp4NllqQW5lM3JqalhpUXFDenJoVlRMbDFaLzNCTCtyWVFxMThqYnhFdDZSK3UxQ1ppVjEzZmJiL1c4dTZ6R2NoK0JqVVcyTkJvZ1Q2aWNyZVA5VHNDSmpzUjd1TXNUcGJ6ZXdLU3JXeFJrNENtMmEwYmpCRmFwZHB6ZmxQUGNYa1dOSXN5YXUiLCJtYWMiOiI3ZTlmZjhlYjg1YmIxOTk1NTM1OWYzOGIxZmUyN2NjYzliN2Y2ZmY2ZjdhZjE2MTg1MGMxNGE3ODA3OGVkZDY2IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilh3YWgvalZiekQrbWhDQS9tT3Y5amc9PSIsInZhbHVlIjoiWUZFcDZJVk9kMFJGaFQ4a1F3MzV1WEpweExqQytVQ3BKMno0cUhlRFFidjhUVnF3ZmU1SitTQ3IxYk9lNWRBM3RFTWllT1kvU0Z3elp5S2VVU0pMS2wvWGZaenJZMjYrKzRSb3R5NS92WkdsSTlRcFRxeWNoZ0M0OVVvQmFYM0N2MlZoRkYyVWJMUzV1eGxQTEZKT3dRdU9oZTl2Zm9jTXF6NUQ3Z2lRL2pDcEdPUGZvK29DR29HNFhKa0s2dHkvNmQ5bTJUMWJ5aWpWZ2FyaWo4Z25ZMS9TU0JRRGNFNU10d0szbng0aUFYUTFiaU42V1BnN1JocEhhbTdqZGpXUms0eUpPb1hKZkxQZVNvd3lQZjF4V1NqdXFORzNLWVluL0JqT2IwUytyK2FvbFBqQkkvN0hKK1hXYkFJVkJIQ3hlNjZMZzNrbW9sL3R6UFVJNFp1Vmt4UXZYWTlKMkk4N3NYMm5hWWJUblVBUXBoeTNoTm9rMS9iRnlBRVNzdHBycTZUNnpZTHk5N3FZeXZsUm1IUW1QL3N3L3NrQlc2SXNDS2RLS0ZQSlBIUzV2bVhtckNpYnEyV0RFZFJ6STlKVnhLdTc5ZTFjbGJvL0VXOTNydnlFZFpQUFJVUEFHak9yTmYrdUJxQVI1ZzdKT3k5S0FiT1JXMmk0eG50RjhMUjMiLCJtYWMiOiIxYTMwN2MyZTkyYmUxNzdhNzNlNDQ5OWMxMzVkMGY0OTlkOTFjNTkwNjM5NjY0YzEzNGE0MjI4NmViZTczMGFjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhES3hjbGI4dk5QODFLb3RJVFVaZkE9PSIsInZhbHVlIjoidHJySjQ4WFVrTmJtL2hMZkpHYlRjOTR2VFJORHdCQ1pERlFMclozQTJhQTEraXRaN3d4ZjhFaU8vNWpRL3MxOE1LWlpJRzlLa05NWHY4Yng3QXVLVjlJS2wwUm42TS9HVkYvUnRJbjFwOVMvL1BjVlZMYnhEb3JmWGE0MDdLSUlsR2ZuSEpaODE1akdEMy80UU9rMXBnSzZkT3hNems3blpMTDh0TmlUMzVUSWEzYzhLVFFGb3FvSmpXTEFXTmhaaFo5UGRpZmFrQTJpZkgvS2gyMzBmMk5jRmxHaktIZThsNWxiVGdyemxDWkFSY1dFNE12aG5WV3hlaWRQbEpzcWpLNlVNVGJXV3RMTStDblpBT0pndk56SjVaZ0tkcmJIZWg3NUpUZ2psakdpaTBpK1dIQXhmdlV2NnduL3R4UkVnYWhnL0Fha1Z3SnlnS3llcVZWWWk3QmV6T1R5aUJnVUc5aXBFUEt0dlM5VkE5QXZRRXAzV3VXeXp4NllqQW5lM3JqalhpUXFDenJoVlRMbDFaLzNCTCtyWVFxMThqYnhFdDZSK3UxQ1ppVjEzZmJiL1c4dTZ6R2NoK0JqVVcyTkJvZ1Q2aWNyZVA5VHNDSmpzUjd1TXNUcGJ6ZXdLU3JXeFJrNENtMmEwYmpCRmFwZHB6ZmxQUGNYa1dOSXN5YXUiLCJtYWMiOiI3ZTlmZjhlYjg1YmIxOTk1NTM1OWYzOGIxZmUyN2NjYzliN2Y2ZmY2ZjdhZjE2MTg1MGMxNGE3ODA3OGVkZDY2IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilh3YWgvalZiekQrbWhDQS9tT3Y5amc9PSIsInZhbHVlIjoiWUZFcDZJVk9kMFJGaFQ4a1F3MzV1WEpweExqQytVQ3BKMno0cUhlRFFidjhUVnF3ZmU1SitTQ3IxYk9lNWRBM3RFTWllT1kvU0Z3elp5S2VVU0pMS2wvWGZaenJZMjYrKzRSb3R5NS92WkdsSTlRcFRxeWNoZ0M0OVVvQmFYM0N2MlZoRkYyVWJMUzV1eGxQTEZKT3dRdU9oZTl2Zm9jTXF6NUQ3Z2lRL2pDcEdPUGZvK29DR29HNFhKa0s2dHkvNmQ5bTJUMWJ5aWpWZ2FyaWo4Z25ZMS9TU0JRRGNFNU10d0szbng0aUFYUTFiaU42V1BnN1JocEhhbTdqZGpXUms0eUpPb1hKZkxQZVNvd3lQZjF4V1NqdXFORzNLWVluL0JqT2IwUytyK2FvbFBqQkkvN0hKK1hXYkFJVkJIQ3hlNjZMZzNrbW9sL3R6UFVJNFp1Vmt4UXZYWTlKMkk4N3NYMm5hWWJUblVBUXBoeTNoTm9rMS9iRnlBRVNzdHBycTZUNnpZTHk5N3FZeXZsUm1IUW1QL3N3L3NrQlc2SXNDS2RLS0ZQSlBIUzV2bVhtckNpYnEyV0RFZFJ6STlKVnhLdTc5ZTFjbGJvL0VXOTNydnlFZFpQUFJVUEFHak9yTmYrdUJxQVI1ZzdKT3k5S0FiT1JXMmk0eG50RjhMUjMiLCJtYWMiOiIxYTMwN2MyZTkyYmUxNzdhNzNlNDQ5OWMxMzVkMGY0OTlkOTFjNTkwNjM5NjY0YzEzNGE0MjI4NmViZTczMGFjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795567705\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}