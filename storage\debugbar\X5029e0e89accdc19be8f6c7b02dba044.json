{"__meta": {"id": "X5029e0e89accdc19be8f6c7b02dba044", "datetime": "2025-07-23 18:21:34", "utime": **********.088271, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294893.585296, "end": **********.088289, "duration": 0.502993106842041, "duration_str": "503ms", "measures": [{"label": "Booting", "start": 1753294893.585296, "relative_start": 0, "end": **********.00734, "relative_end": **********.00734, "duration": 0.422044038772583, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.007348, "relative_start": 0.42205214500427246, "end": **********.088291, "relative_end": 1.9073486328125e-06, "duration": 0.08094286918640137, "duration_str": "80.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46534016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02745, "accumulated_duration_str": "27.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.034478, "duration": 0.02638, "duration_str": "26.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.102}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0715399, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.102, "width_percent": 2.004}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.077782, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.106, "width_percent": 1.894}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-744007272 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-744007272\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1656596676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1656596676\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2045918305 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045918305\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1681020471 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294890134%7C17%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVlQjF0cVhkMmp4WDVsS1FmaEg4WHc9PSIsInZhbHVlIjoiRjExWlFMWHdTeGVEWVFrcUQzR2pQQXdCZm0rVmEwQXE0cGNOR1NNcmlRMjRzQktBQTZIQ2kyUW10OTgwWkpZSUZPbTYxSGh0ZjdGQWRJYzdLSVNNMGtrUGNjQ2JwQzJVK0F2RldJajZSaWxSNUFSd3JtYU9SMk9jeVEyblJUbmZNMld1TnJuMVdPN1RXVWgva3EwYmpXa2IrMHp2YWk4RTZ5bDNDdFFMTFh1REkxbjZsdlQyQStQRTJSSC9DNTVNNGJUZExzNmF3Q0RaMHpSLzh6V1FKNjArNERHQVBBR1REd25VU3NPalhCNkVubVFVQ2RZdWNtbXBsYTlwUXNvaVQ5TkJ5WUlQUlBWN1JISVBSZXJmY0pXcjNKa1RVVWovMzdtR3VIcEpQNzVpdzJhVHM4U3JLNXJxN3RzRDQrcHViM0tYWng0VVhlTG4xUkdYQ0tXN09PUW5aWEpTMW1qNGRIcXNaL1l4aUFDZ0tMOUVsMUZNYzFmOWlTTHQ5L2lHWDh0bUsyN24rNEY3MUNCV3cyMU9FVitISkFaVDhiRlVhRTliTTZnVTM1c2loSnZxSXlqVDhmeVlXWGR4WkNqaVdIbUp2Tkg4TnA1Tzc3MERFc2R3cHRqQlRlSndIOC9BcnJ0S0F3WW1oQlYxaUxnVndYUC9UdmZQOVMzMC9ESkkiLCJtYWMiOiI4ZWU0MDFiODE1OThlNWJjZDY2Mjc5OTkxZTRiOTVmYmIwZmNjY2UzNjgyNGEyY2ExZDRlZTgxYjk0ZDdjODA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVnRElyOFljL3BKNkhsbnNnYXRjYnc9PSIsInZhbHVlIjoiUGtkRmVpZExPUzVYM0hkZENMeHJRbFdsTWtlUWxENFZQQ2E3Q1hTVTd5ZDhidGlMeVJjSDVBeXJIN0U3YXZoc2pFd2lXRmRvVmZ0eEhPL1BqdDNqVjdMd1FIYkV4dTlSdElBT0RpUUFZQ2dIRWNsbEMvQWlLMHljM1JjTnp2Tk85dUNqVEFnc2Q0L0xjaGx6QmpWdVgzK2pWZ0k2bHJ4SHhIbzRsNjhHSVhxZDFKMDNkSktWcW9ZUFJ4Y2l5OURPeXlGbXFqb1g2ZDNiSlhzam56OEwvYmRpcFo1YlUxOEdNb2J1M1djQk5WZ1ZYRkorWFJnQ2pjNWFDd0RCZU1QQUpsZjc5cXBKKzIvbnphdVFFeVBldlN0dlFZZ2dhVlc3M3ZMY0F3bS9heXBpSk1QMEtVSDVkL0FWTHRDOFF5UGZUc3RjVFI4Wm02R3NMZzdxRVJXVWNtNjhKV0dJY3Znbk1CSlhhMlhWam5COU4xTUJSTUxPQTRYZW5XVHdRRHY5RVB6M0lUcjdJc3lwVUhCTW9ZSWNMK2pGd3NPVW5IRnNVVEw5bEtDeERMdktmYzRlWXVtYU94NEhlU2IwYVlTTGxheERXeVowM01EWCtWNVBmK0V4WWJmRFpBOHQ3ZmEyRWNIdURRbWJtK3ZjUE1rbzZScU9uYjJIKzFXOWtIYUoiLCJtYWMiOiI0NTIzMGVjMzNiMDZmNDE0MTU1OTE4ZDY2NjdmOWMxZjc3YWZlYzlmOTVmYzMyOGYyM2Q4ZmYxOTgyM2YzZTY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681020471\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1472907445 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472907445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1624549646 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ5L1RnNjBWbkZNenVsYnJnMXhSeUE9PSIsInZhbHVlIjoiS2ozTEFJazhwWGxRUk00Z2s0NXZ4ZXd5RUp0K0tpSnVOeHZIQ0J5VTVpd0Vsc1ZJU3lyZCtMTjZDQlo1QlRjRHU5V1NQSFNKVExlZU0xaTdSNE1NR3RaWitDUTFrVnk2S1ZHL0Rza0Z0cWUzU3Zzcm1iNmIzQzdZaHJ5MDJXSk9GSEZTZTd0SnVLNjZHSzhoaE9FVUVFeFBKUkNuMmNKVVZ4aEplTVp3bnFiRXN2bm5XV2tEVnh6ZUk0RlhnYllJdmdMcWZHYU51b1I2RHlwMDIwTitFV3FmdHJGTDA2QlBaTkQvN0dsbWhMcml6VlE0K1hldmhOcjVzeW5pc2x6R3dtU0hvNmQ5cGRKamJ6Q1hUVUFJSnZEenlFYXkwZ2toWlczYVR5RTlOV3FpVi9QbS9zWTZLWjUyZlVZNlY1dFpoSWs5S0xIV05JeUVub2lpd09hVnlSMVhDUDFBUnhsYVF5b0VScHBBaHpzejZ6ekxpckVRQXNEVDRhVDRPcmVmUjBYd09rUE5vaUJBZENJU1REdGdQVEF0Y05EUlNkb0xIYmNzYjM0bDBNU3N5SzJRU3BpVC9OQzVwSDVVUzY4RXI2T2YyMHgvT0pUem1BZVM1WGxEbngvUlAvZ3B6ciszeU9nVXRjTG0vanNUVWFrd0FOWUdtMXlMVnRTR2RtUlQiLCJtYWMiOiJjMTBlOWE1ZWU0MTUyMjFkODJjN2QxYmM5OGIxNjFmMzY0ZmMwZTM3Njc2Njg3Zjg3YjgxZmRiNjgxY2U3ZTJhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFuRW1SeEN2Q1g5MHp4V0sxRnV4cFE9PSIsInZhbHVlIjoiSDIzU1NjM09kVUhEeU1sMklKLzdjUVA2KzRGWlJFOHB6MXI3MzFHaFZuTmEzamorbEFMaHc4TzZqV1NiQ2VvYnFXRlpRNjIxWXVQd2k4LzFJYktvTmhKamxCSUVUMU5IaWlHQ3JtbW9leUtzczlUcmhiU2h0dkMxVExVT1hvZm5PU2dRTFlqVHBuRHJHaTQrZ2hLSlZkbHl6dzRLcldjT0JCMWZIQkl6VXJpQ0pBT0pkelZCcVFhbjRqaEVSWUc0VGNkRzFsYTRyekpCazNqRTNmWldXNkVIeUdCdE1relZURHJoWmV4SmRQOXJYWU8rczFLMU1BeC9WVU9PbHFMZEdtNmxlWHp1YXNpcmZjQUxEYXVReTdKbWhZNWg1WlBoek5UM0RCRVN1QjdnZFZQMFdqdWJWRHU2emJPZHdXSlJBYVNlRWtmK3FUbjMwSHJCMDRaLytOVFZUV2Y3WEtGTTMvN0lRNXRYTXRMVVl1aGdRY2ZhLzZ5NFZWR1F0ck1OcFg2ZjNrYUx2Ujl0ZnNCa1hNNk9ENlVUdHNOZkwxUDBRdk1QSndXd0ZtQkdDWjBnRVd2R2xWWTA5SGU5bE94Q0t3Q3JWaHJWbFN0RUtWa2kveUUxNlBSUm1MTW1sTXB6SWJPcTdKbE5Pb3pBMHpJMUsyK0thWXd1am5ZRVFEcWQiLCJtYWMiOiI0MjJiZmQ1MGFmNzA5ODNiMDcxYjIxNGY0YmZiMjBlNGRiMTNlNDMxZTI5NTA2NzM0OGNmYzU2Njk5NWYyMzcwIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ5L1RnNjBWbkZNenVsYnJnMXhSeUE9PSIsInZhbHVlIjoiS2ozTEFJazhwWGxRUk00Z2s0NXZ4ZXd5RUp0K0tpSnVOeHZIQ0J5VTVpd0Vsc1ZJU3lyZCtMTjZDQlo1QlRjRHU5V1NQSFNKVExlZU0xaTdSNE1NR3RaWitDUTFrVnk2S1ZHL0Rza0Z0cWUzU3Zzcm1iNmIzQzdZaHJ5MDJXSk9GSEZTZTd0SnVLNjZHSzhoaE9FVUVFeFBKUkNuMmNKVVZ4aEplTVp3bnFiRXN2bm5XV2tEVnh6ZUk0RlhnYllJdmdMcWZHYU51b1I2RHlwMDIwTitFV3FmdHJGTDA2QlBaTkQvN0dsbWhMcml6VlE0K1hldmhOcjVzeW5pc2x6R3dtU0hvNmQ5cGRKamJ6Q1hUVUFJSnZEenlFYXkwZ2toWlczYVR5RTlOV3FpVi9QbS9zWTZLWjUyZlVZNlY1dFpoSWs5S0xIV05JeUVub2lpd09hVnlSMVhDUDFBUnhsYVF5b0VScHBBaHpzejZ6ekxpckVRQXNEVDRhVDRPcmVmUjBYd09rUE5vaUJBZENJU1REdGdQVEF0Y05EUlNkb0xIYmNzYjM0bDBNU3N5SzJRU3BpVC9OQzVwSDVVUzY4RXI2T2YyMHgvT0pUem1BZVM1WGxEbngvUlAvZ3B6ciszeU9nVXRjTG0vanNUVWFrd0FOWUdtMXlMVnRTR2RtUlQiLCJtYWMiOiJjMTBlOWE1ZWU0MTUyMjFkODJjN2QxYmM5OGIxNjFmMzY0ZmMwZTM3Njc2Njg3Zjg3YjgxZmRiNjgxY2U3ZTJhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFuRW1SeEN2Q1g5MHp4V0sxRnV4cFE9PSIsInZhbHVlIjoiSDIzU1NjM09kVUhEeU1sMklKLzdjUVA2KzRGWlJFOHB6MXI3MzFHaFZuTmEzamorbEFMaHc4TzZqV1NiQ2VvYnFXRlpRNjIxWXVQd2k4LzFJYktvTmhKamxCSUVUMU5IaWlHQ3JtbW9leUtzczlUcmhiU2h0dkMxVExVT1hvZm5PU2dRTFlqVHBuRHJHaTQrZ2hLSlZkbHl6dzRLcldjT0JCMWZIQkl6VXJpQ0pBT0pkelZCcVFhbjRqaEVSWUc0VGNkRzFsYTRyekpCazNqRTNmWldXNkVIeUdCdE1relZURHJoWmV4SmRQOXJYWU8rczFLMU1BeC9WVU9PbHFMZEdtNmxlWHp1YXNpcmZjQUxEYXVReTdKbWhZNWg1WlBoek5UM0RCRVN1QjdnZFZQMFdqdWJWRHU2emJPZHdXSlJBYVNlRWtmK3FUbjMwSHJCMDRaLytOVFZUV2Y3WEtGTTMvN0lRNXRYTXRMVVl1aGdRY2ZhLzZ5NFZWR1F0ck1OcFg2ZjNrYUx2Ujl0ZnNCa1hNNk9ENlVUdHNOZkwxUDBRdk1QSndXd0ZtQkdDWjBnRVd2R2xWWTA5SGU5bE94Q0t3Q3JWaHJWbFN0RUtWa2kveUUxNlBSUm1MTW1sTXB6SWJPcTdKbE5Pb3pBMHpJMUsyK0thWXd1am5ZRVFEcWQiLCJtYWMiOiI0MjJiZmQ1MGFmNzA5ODNiMDcxYjIxNGY0YmZiMjBlNGRiMTNlNDMxZTI5NTA2NzM0OGNmYzU2Njk5NWYyMzcwIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624549646\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1891112555 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891112555\", {\"maxDepth\":0})</script>\n"}}