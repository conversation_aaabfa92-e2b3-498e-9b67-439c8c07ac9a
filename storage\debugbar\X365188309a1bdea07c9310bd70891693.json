{"__meta": {"id": "X365188309a1bdea07c9310bd70891693", "datetime": "2025-07-21 02:07:54", "utime": **********.06929, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753063673.625654, "end": **********.069305, "duration": 0.4436509609222412, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1753063673.625654, "relative_start": 0, "end": **********.017487, "relative_end": **********.017487, "duration": 0.3918330669403076, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.017497, "relative_start": 0.3918430805206299, "end": **********.069306, "relative_end": 9.5367431640625e-07, "duration": 0.051808834075927734, "duration_str": "51.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00284, "accumulated_duration_str": "2.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0460591, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.141}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.056045, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.141, "width_percent": 15.493}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.061769, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.634, "width_percent": 19.366}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_search=&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1457902866 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1457902866\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-101890656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-101890656\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2053802762 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053802762\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-876381208 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"153 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_search=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753063057049%7C30%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InozYkV4YjZueUJraFNieUYrdzNHOGc9PSIsInZhbHVlIjoiTDRRRDZJRnBXeGJ6MVhqTUVwTGp0WlVKWXZKbXhMendDeU5ZMUFNT2hJOXJleFdoSE13Rk5nbUg0aFU3WXVMTFAvRHorYWsyTW9WbC81SHdIZFlFVUNwODZNRHBPbEdlWndmMWRVSFBmcG8vU1dhQ3M0TlRBNy81RkRJckFhZTJ3aDdNaDVvS0JCV3JGZ0dEK0ZlT0FNY1AwQWE4TkhaN3hFaEhOaFVLSXp0SGhibzJxdGpuZkFzTWRIeW9XMDY1dVYyWjFLMHlSVGszWFVuSUVuem9aUlVCN3JDUUQxdXN4S3dhZEdwZmJkUFlqRUJGVzV4MmNzRDdZSVQ1MCtRcG1mK1k3TVdpampmbUw5RnFHRXg4Qk8yenlBU1c2a05kSDgrVHJFam9qc3g0T2hXcEJCQ3UwV1l5bXRkaXFIdkhBaXR3UGw2M1UxYjdRK0xTWFVHZ0RGb1JHK3lQYjhFZVVmTzBLd1pHYzlBYjRrTDJjZ3o4bnpnUWtxcmRNNE1Wb0hOdHd2a05yejlKYitqV3VSamRJOGRpZlJkMWFOU2grMVZwajY1SGFKSVJ3TDY4d2t2WHRHTVY0WGU2aWltcnRHK3Jvd1pUQlJtbzBwN2ptclMxMExKUkZUTUU1c1dPR3M3bmhGNkRUY1dzREN0T29mdzhBTFdmSmg1blJrc24iLCJtYWMiOiJmMzdmNTFjMDA3MzQ0NzY0ZTZiZmFmMDYxNTM2ZGUxMDMzMzVjZWRhNGRmMDVjYjk5M2IzODZiMmMyN2FhNjk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBsdTdoRUIzdlp2M0Y4cXRTc2lWcmc9PSIsInZhbHVlIjoiYTFDdHAzS2UveFZuRC8wR29qdXNaajkvOUV6NUovcHVpZ3BkODZ5NTBYcGhYcHlTUDRHVXVoQlN0NytSZHgwTmwwVWpjTW1qbm5ydUtzcEttYmJ5NHUrcER6K1IrMTBkcW9YVExNVUJtZ3pZR25sekJMN3dqYndGWW5tamFqQUlycFJUUEtuc3ZrRU10elVUUk9KaWhPczJyR0ZhQmk5RlFBM3dSS2ltRE1ZZUVaVExGUDFBVHREVDdsSmRicUUwcjA5RldFaCt0WE5BdU1RT0tId2FZdlgvVk5YOU5vQmJHdU84eXFOQ2Y0a2IvWFRRY0Z0aXprSWZiQUJPNW9tbFJOa3YvSGRxaHlPZ2pFMHUrL2cxbkZMUU9kbHJvY29HQVk1V1ZLeWYzZ0tIWkNIcUZFZ2Z5b1M4QnZSK2UxV1BleHYvTm9OaGt6Z1Y1QWs2T1hVYkNqTXM0UkZMamduUU9WaktEdjJLY09GdzZTb0hjVS8wYm5PZU5DV214WXZUVWF6cXhqcGtta1IzajJSd29IdEJYNWx2NWxSTmJqQ1oyTnVGT2Vsc2l3TTl2MEJFUmpxeEFkMUxhc3RJTEVzTi8vWFEwWFpJQWZLUE55TllsMXVwUXFKd29KUGUvWmZlUlZqT0pERXRzSU90NmdYYjVQOFg2M2VNT1VPSHhjQ04iLCJtYWMiOiI3YjRiNTI0OWM0N2FmMTExOGQ3NzQwOTg0OWE0Njk4YTVkZGE5MWY5MzhjYmE3YzMwY2VhNGY4NmQ5OTM4NGI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876381208\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-725737343 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725737343\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1105270259 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 02:07:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVpLzNCVENidkVtb1BaV3pPalpvRXc9PSIsInZhbHVlIjoiOXRablBCSmpmVW1hSm1ucklKRjNBbHo4SHdqdFRZVC95dm0vbW1GSGFwMi9jZ0I1T2d5OEM2RFByUnkrR0N2YWtoUkxiYjFMMHNxenhSemo3RU5LaVE2VGcrZFcvMzZoWkxnSnNYcGlOL0xZV3Ntam5SblF5UEl2aURTZnFpVGFQQWhzZlcyQkRSb00yQVFDaTdHTjJqV2xReldPM2VIZGRxV0lUWUllcGJoR3JJUkZ3U0hRYU1MeEpxbUNRU0hrSWNaUE9CWWtHNU5PeFhlZTcwV3E1VWhPWThWZ1JZWitaWVJINW95bUVvamdjc0IreVpjMitHeU5tK2dnNndsUS8zQVF1cHk4bmROTGJnRXllVWdCMGJRUmxTbDgwOXU4L3U1UllaM0VpZ3h4Y3ArQ1F6RHZhR1U1a3VOb0tvdHJnMmRXRkYrVCtiZG5BMWMybWMyNENWa3VqdkFaTUpWcXdpc045V2RaSVFCM3FWNUZHd29FQmtnM0wrRUJDZTRmNWcwc2o3MTBUdWlsbU5sY2FFV2l4VFNUM0ZXaTIyZkY4dGI3eU1JSy8zSUNJTnNSS0U3ZVRSOXo1Ym9UVldPZ0IvSHBSR2VFM3laUmVqeElIMENHWWtLSzRxQnlSVmhSMThEMS8xMFR5SFJlVFJPWDF2NlVuWERDWUFwY2xxdEIiLCJtYWMiOiJjMjExMWZjMDJiOWYwZjZlMTY2MDUzM2VmYTQ3Yzk4MDU2YjJjZmUxZDY4NjEwYzVmYzYxOGE2NDMzYTQ0ZGQ3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 04:07:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBSc3c5ZDMrb0I5dU10dXNJa29aR0E9PSIsInZhbHVlIjoiNW1lMVFVRURqWTNyU2ZlNWNaMk9xdHlqbWxvaUZkTFJGaFpnQ2c4OW0vby9zQThJUVlRd0YxRUlrWUNtWlhVQ0V2RFQxYVdCL3BQS0RnSk1lVEVUVC94VlRDV1U2ZVZwWG1aL21DazU1QllwNmdQMUNod01zVkF5cjFERjVUVjE3WWFmanZKUU8vaU00a3lDcW1iNkcrc1ZtK3ZOM25sOENwKzJYanpjSlIzN0syaFlPSEVyWHFSZlZzN3lFYlRIc0RtejNEbHpKck1xMWlSbHJiU0NiV2ZYWFUya3pSOE5zelR3RjFGaTNFN2xoY0tickVMazF6a0RTbVRmaFI0a0NETTBDdWtTdDB4dkplNDZGTzgrRVlsUDlNdFBUcVpjcjMrOSs3d3MxMDBuczduaEt1OGZuWERHSk1PSW9iZU1mR0Q3RnF1azRtb1RDOGNsemRPVUs4bEVvNE1HbXdlcStmL3NPKzNQVkY3eVFRYUE3WXNJM3U5bitYZm5HQnRIallGaEVqMnBibm40QWIrK3QwNEU1eXM1QjM1V1BrNGZucGFlRWpyeElsbENNaXhwbXdwWkNWNkJ5OE1lTXo0VlFTWTc0U2FTT3dkQ295UEUvWXlDbXZtVE5UREtzTlFKV295Y29tVzVwYlh5Z1cwQlNVVVRGb1F6emxIV2QrSmgiLCJtYWMiOiI3MDEzZGExM2VmOTE3ZmMxNmNkZWNjYmU5YTNjNjNhMTM4Y2Y0MjFjMzE5YWE1ODdiNDI1ZGY0OGFjZTgwZjk3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 04:07:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVpLzNCVENidkVtb1BaV3pPalpvRXc9PSIsInZhbHVlIjoiOXRablBCSmpmVW1hSm1ucklKRjNBbHo4SHdqdFRZVC95dm0vbW1GSGFwMi9jZ0I1T2d5OEM2RFByUnkrR0N2YWtoUkxiYjFMMHNxenhSemo3RU5LaVE2VGcrZFcvMzZoWkxnSnNYcGlOL0xZV3Ntam5SblF5UEl2aURTZnFpVGFQQWhzZlcyQkRSb00yQVFDaTdHTjJqV2xReldPM2VIZGRxV0lUWUllcGJoR3JJUkZ3U0hRYU1MeEpxbUNRU0hrSWNaUE9CWWtHNU5PeFhlZTcwV3E1VWhPWThWZ1JZWitaWVJINW95bUVvamdjc0IreVpjMitHeU5tK2dnNndsUS8zQVF1cHk4bmROTGJnRXllVWdCMGJRUmxTbDgwOXU4L3U1UllaM0VpZ3h4Y3ArQ1F6RHZhR1U1a3VOb0tvdHJnMmRXRkYrVCtiZG5BMWMybWMyNENWa3VqdkFaTUpWcXdpc045V2RaSVFCM3FWNUZHd29FQmtnM0wrRUJDZTRmNWcwc2o3MTBUdWlsbU5sY2FFV2l4VFNUM0ZXaTIyZkY4dGI3eU1JSy8zSUNJTnNSS0U3ZVRSOXo1Ym9UVldPZ0IvSHBSR2VFM3laUmVqeElIMENHWWtLSzRxQnlSVmhSMThEMS8xMFR5SFJlVFJPWDF2NlVuWERDWUFwY2xxdEIiLCJtYWMiOiJjMjExMWZjMDJiOWYwZjZlMTY2MDUzM2VmYTQ3Yzk4MDU2YjJjZmUxZDY4NjEwYzVmYzYxOGE2NDMzYTQ0ZGQ3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 04:07:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBSc3c5ZDMrb0I5dU10dXNJa29aR0E9PSIsInZhbHVlIjoiNW1lMVFVRURqWTNyU2ZlNWNaMk9xdHlqbWxvaUZkTFJGaFpnQ2c4OW0vby9zQThJUVlRd0YxRUlrWUNtWlhVQ0V2RFQxYVdCL3BQS0RnSk1lVEVUVC94VlRDV1U2ZVZwWG1aL21DazU1QllwNmdQMUNod01zVkF5cjFERjVUVjE3WWFmanZKUU8vaU00a3lDcW1iNkcrc1ZtK3ZOM25sOENwKzJYanpjSlIzN0syaFlPSEVyWHFSZlZzN3lFYlRIc0RtejNEbHpKck1xMWlSbHJiU0NiV2ZYWFUya3pSOE5zelR3RjFGaTNFN2xoY0tickVMazF6a0RTbVRmaFI0a0NETTBDdWtTdDB4dkplNDZGTzgrRVlsUDlNdFBUcVpjcjMrOSs3d3MxMDBuczduaEt1OGZuWERHSk1PSW9iZU1mR0Q3RnF1azRtb1RDOGNsemRPVUs4bEVvNE1HbXdlcStmL3NPKzNQVkY3eVFRYUE3WXNJM3U5bitYZm5HQnRIallGaEVqMnBibm40QWIrK3QwNEU1eXM1QjM1V1BrNGZucGFlRWpyeElsbENNaXhwbXdwWkNWNkJ5OE1lTXo0VlFTWTc0U2FTT3dkQ295UEUvWXlDbXZtVE5UREtzTlFKV295Y29tVzVwYlh5Z1cwQlNVVVRGb1F6emxIV2QrSmgiLCJtYWMiOiI3MDEzZGExM2VmOTE3ZmMxNmNkZWNjYmU5YTNjNjNhMTM4Y2Y0MjFjMzE5YWE1ODdiNDI1ZGY0OGFjZTgwZjk3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 04:07:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105270259\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1871023098 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"153 characters\">http://localhost/invoice/processing/invoice-processor?creator_search=&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871023098\", {\"maxDepth\":0})</script>\n"}}