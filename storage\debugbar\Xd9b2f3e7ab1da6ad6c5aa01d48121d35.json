{"__meta": {"id": "Xd9b2f3e7ab1da6ad6c5aa01d48121d35", "datetime": "2025-07-21 01:18:32", "utime": **********.433337, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.010837, "end": **********.433352, "duration": 0.4225149154663086, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.010837, "relative_start": 0, "end": **********.378236, "relative_end": **********.378236, "duration": 0.3673989772796631, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.378244, "relative_start": 0.36740684509277344, "end": **********.433354, "relative_end": 1.9073486328125e-06, "duration": 0.05510997772216797, "duration_str": "55.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46549352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025900000000000003, "accumulated_duration_str": "2.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.40671, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.568}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.417486, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.568, "width_percent": 16.602}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4237368, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.17, "width_percent": 15.83}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-529560341 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-529560341\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1868888867 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1868888867\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1696637658 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696637658\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1306133276 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060707911%7C10%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNTSmtlTXpRb2xnQmtiRThaV0hBQVE9PSIsInZhbHVlIjoiczhMZkEvbVF0R0RmdThVT2NXbVo5U0REUnpoOGdSaEpDUUhpWUVZb3B3dlMyaW5QUnFvOFVjYXd2OWtvZHlDVGo1dTBQR1liZkR0Z2tDM3k5aXR3Ni91RmQ5MHpVdEVXOVlSYUtvV0F6SUV1U0NTUVhLNHJCNDZBYk9NQW1OamdQNWM3TTYwK1ZyT2dONWw2TUdqa2tnRit3cWlIVVc2OFVKZGNZYXljcXRvaGZqeXgyZldwMEhURjhwVnRKT29vb2E2SGNtYnBtU21RSWtSSFhZZ3JUWGp0bERyN3VHRjVSRzBBM2RsNG1ieEhLRndDQXdSM29nTWJIZ2M3SUU1enprZmc0eklXMkJUL0NOZ2diMFQrTzIwR21Bcmt1ZXlhZ3prTzBKZFVSci8xNlBkL1Zub2FLdXRQWm5hei90RE15NEpOSGR2QjkwVjRHeVg5UHBNZzNjSUhIenhZdEQyVmVaT2tmZ2cxRGVrVDl0dm43TVZJTkNXU010VFZXOGxyUWlzYWU1L3ZUMHU2K2ladU5YYldyR3gvUjVwQytNbzNaT3A1ZE56Mm1HRzZDQ1lINUtOVTg4aGVINmJZL25uYVNCWWgyL21jODhmMTR3TGExYXhCT3YvWDBTYStXRUEvUzJBcVpHTjJaRnRjRWFNTjdNRUZHcFJ0dDEzUllKeDMiLCJtYWMiOiI0ZmNhMzY2Mjc5ZTA0N2RkYTM3MTc0NDU5YWY3ZjhiMzE5OGE1ODg5MzQ5N2M5NjI2ZmEzZTFmMjdkMmU1MWMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVzelMxQkVWOG1pbFU4YUU3L3I5ZVE9PSIsInZhbHVlIjoieS9IanNKVjR6VnBtOUtWN1lxMkV3d2lCck1idmRnVGNPdU1La2JUb2VpVDFJS3pGNGdZSllWWjMzUjlhSTNSUCtwMFJUako4QlRqeitaNWVhbzlWTTZQd055QVZBdGpOa0ZPNEh1QXp6VXl5ZlgyVDBTY0ZCSGxMMWFQbHBSMUF4d3R3Q3RWcUJjN0NIWXpISE1UWGdsYTJZQ251ZVlsYXpiQnBHVExwQW9PRTlMWDZ4cU9oL1ZVU0FEajJMdEdId0dmZzE2eUNON2xHZE9udmV5eW03NDl6bENaQXJGOTlXNGlpYm1BRTk3Q2hUQ3BNM3IxVklyRmpPVWZsdTRlWGFUTW8rK3VhYS9GaXFrVGNwVnNGZGhBWk9wQWdUQzU4ZHh0V3RTanNIWldHT0lwMnJsaWZuVEFlMys5YmdLaDdFQVh3ZzdEUDMrN2szb0xDYXpDeU9sSytjN0tTcW1yd3Y0c0xEYTliNVhTTVBveGovOGt0TnI1VFZ2VjhjckMwVjdTSlpXOEJaMTNKU2JhekR4NkZNUUNObVBYRXlvYTBhT1R4RGYwVmlPM2V0VHZKeTVJV0haNkhVSWtMaFcwREZZRk1XWThmZ1UyT2pJRk5VUDhVM2NETGNGSEZXS29KTnByVVdBSG0yUXRqMXg0cU1xTzc5SnpyU1hPTWthY28iLCJtYWMiOiIyYTFjN2I0NTFmZjVjNjQzZWI1MmY4NTE4YWM3MmYxNmVjODIwN2Q1MDkwYjFlMzIwYjc3MmUzYjBlMGZlMWExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306133276\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-179921568 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179921568\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-429818695 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:18:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZSbEkxM2dTcGw0cFFnSGI2c3c1U3c9PSIsInZhbHVlIjoibGNvVXg0d2xwYnZtcXJ5WVRSS2lhK3dNZDFpZzZ0VDE2M3JML2VCOUd1ZW9vVDkwVVNxQU1DR0kvVXp1U3RsT1VFaE5yUHdyejBLLzVEK0lybmxmVzY0L25UMEphdVBYYTBzTDdVbHhER0s2TDdvRHFBbDRkQWcyVE1DNnN1TWczN3BYZE5zaktTSDhNRW5PbzJUVGRtN1pXc2Y2cXI2TEZMMlZ3VzlOVy9SeGl5cTRuM05GT203SjhYTk1hdU1lOHZWcHI3TjVsdHpyS3p6SnBwVkVmRC9lZUpJVFY3bEFlNnhMV2RzNXl4YmZ4NVB3K3M1NXBlU3N4cmgvVXlTek5iZHl0TEsrcGFoQmdlRDdJK0hlZ1VOSzhNVjJVVXNTVTQrVDl5Y0twc3RTMXlkRWVjU1BuS3ZaVkVmNy85c3F2MFd2NGlzSnBJVVdMbzJDRExTNU1vWmhWeUkvNlpxZnJlOW5zdHd0WnRQNlpBMzdqaVdzQzhXeGhreFhtUkh4bjRuMFhEVmtyQzY5MWt0WFBYa01vNUpNKzhVWit6anJJTFdWb0hpVUZvcElpSE8vMW8vR2tUbkNmckZCS3oxQ0x6eUpkRml5MGJkbmtNSURjU2RsYVhjbkVKNkZyVG5tSmV2aDhYQ2xDNXlIamdFSDEybytGMVA4T0srTzBTdE8iLCJtYWMiOiJmYzU5OWU3NjM2NDEzNDBiNjIyMWQ3ZDkxZGFhYjg4MjMxYzE2ZGRlZDYzZDRkYjk0ZDhkZjc3YzAyMTE4MWUwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRoa0VnWVBndkdBVEdkQjRmWjJqQ0E9PSIsInZhbHVlIjoiNFdyVk01YkZSNWVOUGV3UHgwR2FWdjB2RStzNjRwZDcwVCt4bFpabGhubzVpeDhoTUlJdUpVRE1ETmlCYVp0QzZBYUhiWWc4REM1eFJhTzBlekNEVUtmNTRRdVZWRXlWQ0dxTHgyaVkxaXRGWDJKK0g3cDYzVE5yWEFwaEJ6RS84SXpMWkxMR3dZUS9yOUNUWHkwbmNkeHVOV3BVcDZBRGMyZkRVTDA2Zzc5b2V0UmhaaFZzM0Y5b3RJa2hSRytEL1ZYbThsME5NcGFrL2pYbmxWUWsyU0JSYzRBYjRDUVYwWUFhVzYrUzdrQndtMldDKzNreFg2bnVUWEp2cU41QUZxaW05a2VHNzZERi83U2pWTVk3VEYwRmlDdng0aExjSFp5UWpid1owcTIxSXBOOWZLVldnWmwwRnhRWkJWekdncUxOeWRxUHJpcHh2SXN2dmZMcEwxN0ZTNDNMRkZDVEZ6K240L1dwSEJ1WjB4eXVFUWYyR1BncUhpN2ZDYlErZklPRU1yeVF4cUh3NjRwb0JweGpqRjhUTERZQm9VY2pGL2EyczNTMjk1TytSM1gxZXJFbC9PVWFvZENzVFhkZitjdHV4TVczSThsaDJ2ZXcxaGN4WDczYmdHMHcyN2xNS1lQK3BqUzRya3QzSG1yU1hPdld5VUdodHJSZGRsR1MiLCJtYWMiOiIxYzg2YzM4ZDc2OWVhOTQwYzA0NTNiOWU4ODQ0ZmRkMjk4OGIzYzkzYzY2ZmRkYjk2NTY4YWQ3YzhkOWVmNTZjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZSbEkxM2dTcGw0cFFnSGI2c3c1U3c9PSIsInZhbHVlIjoibGNvVXg0d2xwYnZtcXJ5WVRSS2lhK3dNZDFpZzZ0VDE2M3JML2VCOUd1ZW9vVDkwVVNxQU1DR0kvVXp1U3RsT1VFaE5yUHdyejBLLzVEK0lybmxmVzY0L25UMEphdVBYYTBzTDdVbHhER0s2TDdvRHFBbDRkQWcyVE1DNnN1TWczN3BYZE5zaktTSDhNRW5PbzJUVGRtN1pXc2Y2cXI2TEZMMlZ3VzlOVy9SeGl5cTRuM05GT203SjhYTk1hdU1lOHZWcHI3TjVsdHpyS3p6SnBwVkVmRC9lZUpJVFY3bEFlNnhMV2RzNXl4YmZ4NVB3K3M1NXBlU3N4cmgvVXlTek5iZHl0TEsrcGFoQmdlRDdJK0hlZ1VOSzhNVjJVVXNTVTQrVDl5Y0twc3RTMXlkRWVjU1BuS3ZaVkVmNy85c3F2MFd2NGlzSnBJVVdMbzJDRExTNU1vWmhWeUkvNlpxZnJlOW5zdHd0WnRQNlpBMzdqaVdzQzhXeGhreFhtUkh4bjRuMFhEVmtyQzY5MWt0WFBYa01vNUpNKzhVWit6anJJTFdWb0hpVUZvcElpSE8vMW8vR2tUbkNmckZCS3oxQ0x6eUpkRml5MGJkbmtNSURjU2RsYVhjbkVKNkZyVG5tSmV2aDhYQ2xDNXlIamdFSDEybytGMVA4T0srTzBTdE8iLCJtYWMiOiJmYzU5OWU3NjM2NDEzNDBiNjIyMWQ3ZDkxZGFhYjg4MjMxYzE2ZGRlZDYzZDRkYjk0ZDhkZjc3YzAyMTE4MWUwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRoa0VnWVBndkdBVEdkQjRmWjJqQ0E9PSIsInZhbHVlIjoiNFdyVk01YkZSNWVOUGV3UHgwR2FWdjB2RStzNjRwZDcwVCt4bFpabGhubzVpeDhoTUlJdUpVRE1ETmlCYVp0QzZBYUhiWWc4REM1eFJhTzBlekNEVUtmNTRRdVZWRXlWQ0dxTHgyaVkxaXRGWDJKK0g3cDYzVE5yWEFwaEJ6RS84SXpMWkxMR3dZUS9yOUNUWHkwbmNkeHVOV3BVcDZBRGMyZkRVTDA2Zzc5b2V0UmhaaFZzM0Y5b3RJa2hSRytEL1ZYbThsME5NcGFrL2pYbmxWUWsyU0JSYzRBYjRDUVYwWUFhVzYrUzdrQndtMldDKzNreFg2bnVUWEp2cU41QUZxaW05a2VHNzZERi83U2pWTVk3VEYwRmlDdng0aExjSFp5UWpid1owcTIxSXBOOWZLVldnWmwwRnhRWkJWekdncUxOeWRxUHJpcHh2SXN2dmZMcEwxN0ZTNDNMRkZDVEZ6K240L1dwSEJ1WjB4eXVFUWYyR1BncUhpN2ZDYlErZklPRU1yeVF4cUh3NjRwb0JweGpqRjhUTERZQm9VY2pGL2EyczNTMjk1TytSM1gxZXJFbC9PVWFvZENzVFhkZitjdHV4TVczSThsaDJ2ZXcxaGN4WDczYmdHMHcyN2xNS1lQK3BqUzRya3QzSG1yU1hPdld5VUdodHJSZGRsR1MiLCJtYWMiOiIxYzg2YzM4ZDc2OWVhOTQwYzA0NTNiOWU4ODQ0ZmRkMjk4OGIzYzkzYzY2ZmRkYjk2NTY4YWQ3YzhkOWVmNTZjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429818695\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1186604394 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186604394\", {\"maxDepth\":0})</script>\n"}}