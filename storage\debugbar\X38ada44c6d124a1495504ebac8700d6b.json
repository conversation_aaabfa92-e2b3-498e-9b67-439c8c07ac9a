{"__meta": {"id": "X38ada44c6d124a1495504ebac8700d6b", "datetime": "2025-07-21 01:35:44", "utime": **********.807097, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.371621, "end": **********.807112, "duration": 0.43549108505249023, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.371621, "relative_start": 0, "end": **********.738006, "relative_end": **********.738006, "duration": 0.36638522148132324, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.738013, "relative_start": 0.3663921356201172, "end": **********.807113, "relative_end": 9.5367431640625e-07, "duration": 0.06909990310668945, "duration_str": "69.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46008280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01638, "accumulated_duration_str": "16.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7688022, "duration": 0.015380000000000001, "duration_str": "15.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.895}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.792937, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.895, "width_percent": 3.785}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.799478, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.68, "width_percent": 2.32}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=17&customer_id=&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-971510465 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-971510465\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-830480711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-830480711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1801083530 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801083530\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1297783133 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"150 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=&amp;creator_id=17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061710058%7C18%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikg4azdkZHFDbW1NeDJVcVk3N1dHU2c9PSIsInZhbHVlIjoiRUg1Vy9pSDFNa0Qzekg5czhkV1RiYzNKeEFmOXZiQy9VS0c2TVJBZmV1d1FUT1hHN0FHaFBqTkdsRGFtSkc5NVlZdU9ZdnR2ck9FNHFWZENYTkdQbkpranJGeTdRSHcxOC9US29BZTFCQm10anE4YW1VVHFnR2ZQV0VaNnQ3TjJXZTk4L3pmbU5MbWljaCswZDFBeWM5eE9QbS9BSFNOQzZFVTZCWGFaRGR6aWdxWFhjTXRUd0RrWXpXMHBzcGdjalNTZG9iNDNXNUZ5c29QSlBzTzFqWW9qSFdIV2F5Q3Y2ZVdIWXUwRFY1VjRrMGlLdHpEVjdjZjRvYytMMzRVNFo2ZDhSWFN1YmpZa280MDZaSUthVkZiZUlwbkFFYy9DQ2dCSzdCTW1vL2pVQzR1aE9YTjhNa0FKbyt3b2xzTmFPUWE1MGFDNU5xcjFuRitwUURSeTVNMVVoMEVRZkJYOHVOanBMNXVFeTdWbXQwYzFkbDVJS3dPUkNTU1V1SUJQd0Nwb0FnSkFpVnJMa2M1NzRUakw0dUZMREo3eWhnNE1ranZkR3VYK2djRGtHRXVWazhQYi84dnJmb1lLTEp4bmJPcFFnamRuajhGamJ2R1kvWE9BSXNFOFdzMUhMTDVoWkhRZDVzaXJWcWR6T0o2SHRpRmZNUis0Vk1GSC9pYWMiLCJtYWMiOiI4NTU3ZDVlMjcyNGQ0MzliMDQ0ZjEzYmJhYjIyNGFlYzI2MDhjOWY0MTY0NTlmOTI0YzVmYjEzOGU4MzFhZDA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii85ZnFmejJyTEM0d2ZIR1RFeGF5M1E9PSIsInZhbHVlIjoiUGxjQVExUTBGUUpRV0ZLVWdISE90R1N6bzJGbHhGeCtmdk1lZ0dCZmdmM00rUWd2aFZFSFBQTitkV0QxNGE3TzBxOGlCQnBOQStGSkkzUERnRTQ4TC9ZWC84MDFtVEUvU1EzelM0VXZvTzk0V1BHNmYwcXRKc1hJblNHODQ1Skt0K09qbk1ZdGMwOThBelhEVDl1aUVGYm1xRHRXdnFMenRZOEdLdHEvbHB6U085aVJXbkh6WkFFZ0pKV0dNcnk2bTRIWG9BZU9USTRjMTZldGNja2I3VWloZ1o3NkdIcG9IY3B4OER6N3NHVHJGdnR3Q0JyV1lRcitwT1p2alhYMFVXdEdTSWZ5UTR1ZUdqenBQbVI1WXhyMFBrYi9hL0ZNbkRyajBNZlJxV1NGVTFBakJxd25KeWRiTjZCZm45Q0I4aHhUbFVnUzdISVhsUlB3RVJzcDhIc2VtR2lKdEhDanVqQm5xZzl3aHIrM3owL25FUW1IVExxaytFOHdNazM5TEFrQmVJNTBscEZ6algvRWhUV0xqMkh5QUo2N1JmdWIvR2srcVh3R3hjMzlSdjRoTS9NY3NPenY5bzlLZmRtd1pVZXViek5jak55L0hmdHh5NG5PTVh1bU1OR3RKL3hLRlRvM2U4VFNVQ2E5K1BMcDBWQXB6UlJYQjc1b3BuZUIiLCJtYWMiOiIxMGM3NjZjODVlZjkyZWNiNGU0YzU0ZGY1NWQ1NjJiNjkzYjBlM2Y2M2U3Y2JiOWFiZDg5Y2Y4ODc5YTZiOTQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297783133\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-18478705 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18478705\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1970771854 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:35:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1jVWZFTDljaTQ4VGtEcmVsUHVGU3c9PSIsInZhbHVlIjoiTmF3Rk1ubnhhZzc5Z2JKK09uT0VuVTJpeHpzN1IxWHV2bzZOY21PT1dUVmtnY3g1b09STXp0Sk5mMk52MHpWaVMrSXVIM3E1QWFQVURWOXk3N2NiS0kzalgyWTVoUHV2SzFwakV0czhOMDAvQ2w5TDBsOWVDV3g3M3FmZ3dOTXpPcnJHbmVkb0FIMjgwL0QxMGVJdjJ4aGVqMENTNjJtY2FiWFZBR0pVWGN1ai8zTGJDa3Nqb0Q5bXRlYVdIUkRwOFhiNnhOcGx2d0Y5RmZmdER4UE5KT3N2d0VLZ3dNalE5aFptam9FWTI2NWtPVy9vV0ZPZ2gzV09Vd0gvQ29XUmRPbjRWUmlueUlodVpRSlhPSVAyUUFLblE3aHR4NkExUDNSZ0tvSU9tTVJWS09CYnMzQTFwMy81MXpBSkhmZDFpK0pubk5ObEM2clhaQnRRamRtWnphcHJiUXdzcURFSzhuNlpxcnJJdEk1TjJZZ09RSVR3M2R3YWVSNEJLb1lQd1E1TFU5M1pPQ3M5RU5rT2RscFZVa3V5bnY5VzZ5S2xHMVE1bzFBMVozdjdmc3NaclI2eG95bWpRRmVDSHM1dUdER1VDMUJWSEVKNTdNdmpOS3ZBMHFHeEExRmk3WlQvU1p6OE55anZmMmlPdFI0azVUR2x3OUJWNE9vSGUvREQiLCJtYWMiOiJlOGQ3MDE2NDNiNDJjNmUwMzc3OWZjYTM1NzFlYjE3NDE1NDM0OTVlNjQ0ZmVjZmVmZWJmNmQxOGVjY2Y5YTc4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:35:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxoamJ0dkJBdFFoVFFjdGpnR0NIdWc9PSIsInZhbHVlIjoiRmpQU1lOVnBFS1dibW41L1VscnBlZkhYSzJMMy9kTW13djZ0dGIwd3c4cEh4M1BIWjkwbk43RlFnek1FTGxHM0lIcVhVa3pMbExRd1hjMjB1QU13cmFqN01vWFR3T1pVOEpUZ2hodXhWQWl5UWxBWm1aNHpJb1dsQ0JjTDFZdjllWHd4S2NMTXhENDdUeEI4VVMrc0xWR0cxdDI1dWZZeVM4eWhCK3h5Q1gvdk56ck83TWRlR0xYMU1acXBjK2VNZEFVUDZIaFpDRzRmbHRqOFZud3V1MEZrUXpNL2JtZXUvYy9rS2p0dXVjSXYySU9NRFc2Z2cyY3kyc0dMTloxSDB0b3ZESkVhd3NES1hUWElTSUIyYmtqa1pNbS9MbWhWYjlpdlF5czIwVStNbHBHRmhUR0VtcExMQUE5OE1jMW5TaHJUWE5scldmWnNXWHB3eHVZODN4cSs1elVWY1Fqay9nN3B1OS9QRmxrYThaWTAvSHBSeEw1ekxUUERwd0dyOGtiMGtQZGpUSE1pWHZWQ1c2Z1E4c0ZHQkVDbDZIZlpoU25Rc0hFdHMvcUxsMXVQNkowa1NMK2ZBV2NtUHJjYW1RR3NZSjlQWXovRzlXS2h0TDBTTlFFWTVOZ1F3a1ZkazVoUlVWNzYvMi9KbGl3MVJQU1hteXNvbVI3VXBVNXMiLCJtYWMiOiJhNTI5YmM1MDdiMjBmYTJkZTU4MWZiYWYxMGZmYjM3OTIxZjg1NzRjOGMxNDhkYjZiMGRlY2Y2ZDc1M2QxNGNjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:35:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1jVWZFTDljaTQ4VGtEcmVsUHVGU3c9PSIsInZhbHVlIjoiTmF3Rk1ubnhhZzc5Z2JKK09uT0VuVTJpeHpzN1IxWHV2bzZOY21PT1dUVmtnY3g1b09STXp0Sk5mMk52MHpWaVMrSXVIM3E1QWFQVURWOXk3N2NiS0kzalgyWTVoUHV2SzFwakV0czhOMDAvQ2w5TDBsOWVDV3g3M3FmZ3dOTXpPcnJHbmVkb0FIMjgwL0QxMGVJdjJ4aGVqMENTNjJtY2FiWFZBR0pVWGN1ai8zTGJDa3Nqb0Q5bXRlYVdIUkRwOFhiNnhOcGx2d0Y5RmZmdER4UE5KT3N2d0VLZ3dNalE5aFptam9FWTI2NWtPVy9vV0ZPZ2gzV09Vd0gvQ29XUmRPbjRWUmlueUlodVpRSlhPSVAyUUFLblE3aHR4NkExUDNSZ0tvSU9tTVJWS09CYnMzQTFwMy81MXpBSkhmZDFpK0pubk5ObEM2clhaQnRRamRtWnphcHJiUXdzcURFSzhuNlpxcnJJdEk1TjJZZ09RSVR3M2R3YWVSNEJLb1lQd1E1TFU5M1pPQ3M5RU5rT2RscFZVa3V5bnY5VzZ5S2xHMVE1bzFBMVozdjdmc3NaclI2eG95bWpRRmVDSHM1dUdER1VDMUJWSEVKNTdNdmpOS3ZBMHFHeEExRmk3WlQvU1p6OE55anZmMmlPdFI0azVUR2x3OUJWNE9vSGUvREQiLCJtYWMiOiJlOGQ3MDE2NDNiNDJjNmUwMzc3OWZjYTM1NzFlYjE3NDE1NDM0OTVlNjQ0ZmVjZmVmZWJmNmQxOGVjY2Y5YTc4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:35:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxoamJ0dkJBdFFoVFFjdGpnR0NIdWc9PSIsInZhbHVlIjoiRmpQU1lOVnBFS1dibW41L1VscnBlZkhYSzJMMy9kTW13djZ0dGIwd3c4cEh4M1BIWjkwbk43RlFnek1FTGxHM0lIcVhVa3pMbExRd1hjMjB1QU13cmFqN01vWFR3T1pVOEpUZ2hodXhWQWl5UWxBWm1aNHpJb1dsQ0JjTDFZdjllWHd4S2NMTXhENDdUeEI4VVMrc0xWR0cxdDI1dWZZeVM4eWhCK3h5Q1gvdk56ck83TWRlR0xYMU1acXBjK2VNZEFVUDZIaFpDRzRmbHRqOFZud3V1MEZrUXpNL2JtZXUvYy9rS2p0dXVjSXYySU9NRFc2Z2cyY3kyc0dMTloxSDB0b3ZESkVhd3NES1hUWElTSUIyYmtqa1pNbS9MbWhWYjlpdlF5czIwVStNbHBHRmhUR0VtcExMQUE5OE1jMW5TaHJUWE5scldmWnNXWHB3eHVZODN4cSs1elVWY1Fqay9nN3B1OS9QRmxrYThaWTAvSHBSeEw1ekxUUERwd0dyOGtiMGtQZGpUSE1pWHZWQ1c2Z1E4c0ZHQkVDbDZIZlpoU25Rc0hFdHMvcUxsMXVQNkowa1NMK2ZBV2NtUHJjYW1RR3NZSjlQWXovRzlXS2h0TDBTTlFFWTVOZ1F3a1ZkazVoUlVWNzYvMi9KbGl3MVJQU1hteXNvbVI3VXBVNXMiLCJtYWMiOiJhNTI5YmM1MDdiMjBmYTJkZTU4MWZiYWYxMGZmYjM3OTIxZjg1NzRjOGMxNDhkYjZiMGRlY2Y2ZDc1M2QxNGNjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:35:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970771854\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1661916615 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"150 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=17&amp;customer_id=&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661916615\", {\"maxDepth\":0})</script>\n"}}