{"__meta": {"id": "Xfbc49b4b1bd177e1ec1bfe71aa08e76a", "datetime": "2025-07-21 01:34:57", "utime": **********.072029, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061696.577092, "end": **********.072051, "duration": 0.49495911598205566, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1753061696.577092, "relative_start": 0, "end": 1753061696.970796, "relative_end": 1753061696.970796, "duration": 0.3937041759490967, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753061696.970816, "relative_start": 0.3937239646911621, "end": **********.072053, "relative_end": 1.9073486328125e-06, "duration": 0.10123705863952637, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991608, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025790000000000004, "accumulated_duration_str": "25.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.019531, "duration": 0.024300000000000002, "duration_str": "24.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.223}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.055067, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.223, "width_percent": 2.52}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0629752, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.743, "width_percent": 3.257}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=&customer_id=&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-569470406 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-569470406\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-348389808 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-348389808\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1355651708 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355651708\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1073387395 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=&amp;creator_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061687088%7C15%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImN5Q05vQUJpS2lpc2tVb1dlV21tNnc9PSIsInZhbHVlIjoiREMzMVJJRDlxRGdJQ0drWGdFVHYzWCt0bk9wb1p0ZlRZZ0VWUHlJRmowTjd1SmZKR293dCtRY2FIRkhPbGV2UzdhUXQ5bnpxL1JET3JoUmhQaVk4UUFYTDNTNEVQN093RDhKcHVFSW1JbFBEMWplcjB2RW1MT0t6cU9lVmQxeWFVMVZsNk1DRTRSdTRvZ1d4WWVTV08vZUFseEVxUFUrNTJ1ZC9lelVhWHg0S1ZKL2VBWnY2UFVCb1VJa1NHaXkwV2U3cGI2YmdIczJHWlhIajlZYXJ2T2Mya1YralRmbTVRUVQwNy9vVU1jRndCWjlrcHVoSkdpb3N3VDczbHMzUFhEZUMzOCt0TkJSQmhzWGp5ZVBJUWY4dGh1TlNDVm52VWFpeTBCSEtPVWpXZFE1WDJUNGlsZFdZcEZjSG5EUEJDYk1uTlByQ1BKeit5RWxyTkdPckhCcy9aTmZRWDVCaVl2L1p3bTJ0bFl5cHhNSlRLUHFkU2lGaVd0ZnJvNXVWV0czZ2dIVjRTTnNpdklyUllSMXEzSEFHNGsvRTJzd1FzeHZyT05PSEExUFZhY0s1MjJkeXd5UWlGRE9GSGhLaVYzaGFqSjA2NDQ3RG9DZXpRdVlFZWJua1R1cHo2cnJubWlWVnRPayszckFzU21PUlZ3VVJuRk5hbTRSdHA3cUUiLCJtYWMiOiIyOTAzNjg4MGUwMWE1YTZiNWE4MmVhYjdmOWMxMWVhZDZkNjk5NTJjMzMyOTRhMTk3NjNjMDMyNzlmMWQwY2EzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjR5MzFEVW0yMkxQd3kzNFY5WXNoRXc9PSIsInZhbHVlIjoialFOY3BrNGIwSW55NStDeWF0KzR1UUY2WUhkTFhVdElCSmlzbkU2MnIrbDVrektqU1lnQ05lYWMzNGU3NWQyc3h1ZmdwSlc1N2xTMVdneUEvMlpqQWovR1hTaWhZYjlFKy9nSVpCclh5NkRTcVlybUhQVitnbW96N3VKVkFycnd4aXhXeGlDQkNVTWZOck1QeitiOHBhUml3SzVBUDlOL2ZaNk9zQkNPZmQyOHRCZmZVYTBCZUllWWlDSEhwK3NLaHBGNnBNN1VRNzVxd1hBM0RCVkJTaU8rOGZSK1BvTTl5UHBydFpsZXFPQTA1YUYzcXIvdVo4OTVETnI4NE1HWHUwRE5VRDA3YUE5d3haRXZHZjVsVlA0c24rM2ZhRmMyYkVOMXI2L2dTVlJFYWdWSGxUdndmVW5mMFFwVDlzbG9vaXpZTURrZnU4aHRvNUhRSmhGUEJjRm9URys2RHRsYWY5TmJ2ZmlCNHo0b05oajlaZnhGMUVOeDVOUUN2SWF2cGZqRkJ4T3g5K0VnbXJtbWlZTVZXKzBjeEEyUG4wRTFVSkFCMjhCUG1hdUlic0twL1BGLzhnSVlkOUc2RVdQSDFUWlhJeElwREF5aHEyQXZORmtiUnJDY1E3Mk12QWg4ZGZBZDExbUFlNkZvK2FJdFlZLzF0UWZmT3FIb1h5dWYiLCJtYWMiOiI1ZTlkOTJiYjlkNTViMjRiMTE2OTYzZjk2Y2E3MDliNTIxYjBiNDQ1YTI0MmUwMGE3OTc0YzhkMGY1OWU0OGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073387395\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-820207010 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820207010\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1796441056 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllxNFF6SEVkZnM0c0F3YTVBQ1VzSUE9PSIsInZhbHVlIjoiNUJiRlIrbjNNazBqdDZOVG5UeGRhSnJDQ2k0VEdDTHZUbmFLZVVHSmJSY0VBQi9QQmpnTW1TUjZFeUtyUGUxbkpWY3VLMFFwamxoM0VIaEEzc2QzamZwN3lKZXM2TUJISlZMUFNHOU5wL0NmT29oZlBrOERIZzc0ZjI3WWhoU0JFaHFQWjVHRW93bTk5enNMRGltOS9SNjUvQ3FXRnZ5QzFuUFJnM3JwdTZ0TW9LTkZZTTd4N2VwQ3NFb2xpMjVXYXFUanUrOWdPMGZDaHRIRmdBMmhqdC9uRnFqaFlIdUxpZ1Z0SlpJa0pXLzd4c1JRVkZXYk5HVGdpbXEzSDlhVlNLSHE0cDh4cW5RVGZvQU9HcnpqNHJIMXVsZFFibW0zS3Eya2VMNEZpQ0k4WkI1UWZWckhqYmhZNkpPTngwM0N1aUcxT25QU0VJdHA3U3BKWThXYUtXZ1ZKd3NlWDRCdTlVM0d6bVNnQVhUcVR2VnFUUGFrM2kvbFEwcklWL2gxUzNRY09FUm10MkNVK1I0RnFkb3FmZVpLMWhKdkxnVXFHY0JpOW1NUTY5VGVrZ0trelI2dEdINVZ0eGRoTGM4eHlYSWlncnZSV0lzcTgyLzFTWkoyM1ZobEZsRktqbzdBUFhDRmo2MWcxcGpzc3dCUEFjcjExTmQ3MnUrSGkzYXciLCJtYWMiOiI1NTVmNWE3ZjhiMjMxMDBiMzBjYzAwMzQyZGFhNDkwZWQ5OWZhNmMyYTNlODk5N2Y0YjY0MmQ5NDk0M2MzMGQ5IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVIeUNhbi9udFZtWXA3NXpMMW9wV0E9PSIsInZhbHVlIjoiZy9iVjk2bU1hd2Z0RE01YWt4ME94alpmQy80bmFzN01oaTJlMFhlekZTYmhMS0xMa2gycXlKeGxnN3hEZW84ZXpLT2x4VFBDV3VsN0xiKzc4RlFqUXE1clRyVEJBUXJYWWhZYXlNWTdJK2NiZ3kwc1RrMkxud0pLSFZvYU5CazdDL29XY3JwMWZwLzA2SUhqS2xydzdEUHhJQStXZitSTGxNR0Ura2htekF5aUlNTU5WYVBib0RQaDRzUUtNNmFMU1BsR1Rva3dMb0ptOXRLcW82K2VIaEVvdTlxQ0JUYWpqZTZycmxjQmpMUXB0RW5EVjF3Y2FqTERBL3BmUDNmM0ttR2prTVUwa1ljVTh0cFlDQmQ1RkpWM216ek9xcTNiSmZGc1UyWXFQcUNiK2hnT3QzTUNKQVlCTHk1OXNZQmJyOTdPRkdyajk2YjJ5bytCMVlMR003N2F2bThtMFdQelZQNnU0cWlOQkc0WTVrd0JReHdONWxRckROV1ZqSEZsQTh4ZkxkZXJRRlpWQUxuRVEzMW9GeUt5NGNpMTFLa2d4cmdvd2tDejFHUGdOTDA5amxQbm9ScEs0UXFFL2xaalhTd0hjOVVjaG1KaUhCc0wyTnJhTmFOcjNPUUliS1JjT3FxazlDZ2QzNzRwQ3dwcXQ3dzlqN1gvZDFyZm9OQnUiLCJtYWMiOiI3ZDk3YjZlNjJmNmY0MzkxZWIzMzY2ODQxMmEwNTNjODRiOGI3YmYxMmYzMGY1MmE4YmM0NWIzMDEzM2Q3ZDcyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllxNFF6SEVkZnM0c0F3YTVBQ1VzSUE9PSIsInZhbHVlIjoiNUJiRlIrbjNNazBqdDZOVG5UeGRhSnJDQ2k0VEdDTHZUbmFLZVVHSmJSY0VBQi9QQmpnTW1TUjZFeUtyUGUxbkpWY3VLMFFwamxoM0VIaEEzc2QzamZwN3lKZXM2TUJISlZMUFNHOU5wL0NmT29oZlBrOERIZzc0ZjI3WWhoU0JFaHFQWjVHRW93bTk5enNMRGltOS9SNjUvQ3FXRnZ5QzFuUFJnM3JwdTZ0TW9LTkZZTTd4N2VwQ3NFb2xpMjVXYXFUanUrOWdPMGZDaHRIRmdBMmhqdC9uRnFqaFlIdUxpZ1Z0SlpJa0pXLzd4c1JRVkZXYk5HVGdpbXEzSDlhVlNLSHE0cDh4cW5RVGZvQU9HcnpqNHJIMXVsZFFibW0zS3Eya2VMNEZpQ0k4WkI1UWZWckhqYmhZNkpPTngwM0N1aUcxT25QU0VJdHA3U3BKWThXYUtXZ1ZKd3NlWDRCdTlVM0d6bVNnQVhUcVR2VnFUUGFrM2kvbFEwcklWL2gxUzNRY09FUm10MkNVK1I0RnFkb3FmZVpLMWhKdkxnVXFHY0JpOW1NUTY5VGVrZ0trelI2dEdINVZ0eGRoTGM4eHlYSWlncnZSV0lzcTgyLzFTWkoyM1ZobEZsRktqbzdBUFhDRmo2MWcxcGpzc3dCUEFjcjExTmQ3MnUrSGkzYXciLCJtYWMiOiI1NTVmNWE3ZjhiMjMxMDBiMzBjYzAwMzQyZGFhNDkwZWQ5OWZhNmMyYTNlODk5N2Y0YjY0MmQ5NDk0M2MzMGQ5IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVIeUNhbi9udFZtWXA3NXpMMW9wV0E9PSIsInZhbHVlIjoiZy9iVjk2bU1hd2Z0RE01YWt4ME94alpmQy80bmFzN01oaTJlMFhlekZTYmhMS0xMa2gycXlKeGxnN3hEZW84ZXpLT2x4VFBDV3VsN0xiKzc4RlFqUXE1clRyVEJBUXJYWWhZYXlNWTdJK2NiZ3kwc1RrMkxud0pLSFZvYU5CazdDL29XY3JwMWZwLzA2SUhqS2xydzdEUHhJQStXZitSTGxNR0Ura2htekF5aUlNTU5WYVBib0RQaDRzUUtNNmFMU1BsR1Rva3dMb0ptOXRLcW82K2VIaEVvdTlxQ0JUYWpqZTZycmxjQmpMUXB0RW5EVjF3Y2FqTERBL3BmUDNmM0ttR2prTVUwa1ljVTh0cFlDQmQ1RkpWM216ek9xcTNiSmZGc1UyWXFQcUNiK2hnT3QzTUNKQVlCTHk1OXNZQmJyOTdPRkdyajk2YjJ5bytCMVlMR003N2F2bThtMFdQelZQNnU0cWlOQkc0WTVrd0JReHdONWxRckROV1ZqSEZsQTh4ZkxkZXJRRlpWQUxuRVEzMW9GeUt5NGNpMTFLa2d4cmdvd2tDejFHUGdOTDA5amxQbm9ScEs0UXFFL2xaalhTd0hjOVVjaG1KaUhCc0wyTnJhTmFOcjNPUUliS1JjT3FxazlDZ2QzNzRwQ3dwcXQ3dzlqN1gvZDFyZm9OQnUiLCJtYWMiOiI3ZDk3YjZlNjJmNmY0MzkxZWIzMzY2ODQxMmEwNTNjODRiOGI3YmYxMmYzMGY1MmE4YmM0NWIzMDEzM2Q3ZDcyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796441056\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-707043823 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"148 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=&amp;customer_id=&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707043823\", {\"maxDepth\":0})</script>\n"}}