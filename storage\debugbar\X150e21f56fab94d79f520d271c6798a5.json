{"__meta": {"id": "X150e21f56fab94d79f520d271c6798a5", "datetime": "2025-07-23 18:20:39", "utime": **********.807586, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.36474, "end": **********.807603, "duration": 0.44286298751831055, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.36474, "relative_start": 0, "end": **********.731453, "relative_end": **********.731453, "duration": 0.3667130470275879, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.731462, "relative_start": 0.36672210693359375, "end": **********.807604, "relative_end": 1.1920928955078125e-06, "duration": 0.0761420726776123, "duration_str": "76.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46536336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024620000000000003, "accumulated_duration_str": "24.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7604249, "duration": 0.023260000000000003, "duration_str": "23.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.476}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.792216, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.476, "width_percent": 2.031}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.79801, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.507, "width_percent": 3.493}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImtVUml2NWJOZTBvaHFZczVOcVFKcUE9PSIsInZhbHVlIjoiejFRbFg3dmVRbjhOZTdRZ3hQWVU3QT09IiwibWFjIjoiMjlhNTc3ZjgyZWNkZmE3NTI1OTY5Y2M0MDI1NGFhYjQyMDYzYzU0Yzg4ZDUyY2E2YzI2NDBkYjU2Mjg1YTkwYiIsInRhZyI6IiJ9\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1399552303 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1399552303\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1734593912 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1734593912\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2060990482 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060990482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2037081852 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImtVUml2NWJOZTBvaHFZczVOcVFKcUE9PSIsInZhbHVlIjoiejFRbFg3dmVRbjhOZTdRZ3hQWVU3QT09IiwibWFjIjoiMjlhNTc3ZjgyZWNkZmE3NTI1OTY5Y2M0MDI1NGFhYjQyMDYzYzU0Yzg4ZDUyY2E2YzI2NDBkYjU2Mjg1YTkwYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294838549%7C6%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imh1V3hUWVNoMU9MSEZkZlkvRHM3Vmc9PSIsInZhbHVlIjoiVEV0ek9YemYydGQ0OTJNaDVoWnVTSW1KZGhMa1ZtYm45K3dDUTVJZlZzOWNSckw3cEo0VHlUdGNQWFRDS0didllHZDA0QzVtK3FlcUd2WklKdmExVVh2OE5zaVRIOVhWVjcrdjRodTZmNXNybXpFbkQrVnlKdm5vSWczaHI0N3RQcUEyMXhVNmFCSHROWDZOb3V3OVBHb0JBTzZrbnVVZ0tYRk8xdmJCbURNcm14YVYvSytiRlBDNDNOZForWU11SXBYV05uNGx3Qkhid3BvODdDNVRTMzM3Q2RGeWtjY09tQjh0emlmQ0tkYnlsSzFyUmNiVW9oNVFaQ0lLNkR2bmNyaXNjNFplcXlMMHlPS3ZjemtmSEgyaWp5eVBBLzlVMkoxdkZvK1Azek00L09aQTJzSU9UWUI1dGxDWDBkMXJxRFIzU2Ywb1lzcVAyemxIQzY3dUpLdHprYUpPMllhMXQ3UGxtV0ZvUzcvZVVWeGhEZ0VORkJHWFJPT1NDcXB5UjFBRXNLaEdMa0NEbjd2em1tWnB5SE1nRUhrTWlPWnJPNWk5OG1KeForZHhwZ0pyeFBZb0xTTHA3Wnc3YXJYenBvS1ZqVFdnUTdiY2VkVUZXSHhyVk11WHJzWE85OFloYUFDbE95aXFZMG5nUko1VEJMcDRsWEtJelBtdEQ4eloiLCJtYWMiOiI5ZTdlN2JjN2JlMDRiMWI1MGNjMjdmZGM1MzMwNGU2OTQ0NTMwNDg2ZWJkYzUxOTMzYjIxYjdiYTg5MzE4Y2E0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5EZm1wSGdWb3Z3RUgvUHJha25DZVE9PSIsInZhbHVlIjoiRUZwb2lMcGxETTZ6ZGxHa0xHVUVXaitwcGtRcXE1L1pLUTg5ZlNueGlad2dBYjZnaTBodzlWRDY0ZVRFTlNUeWo2Qyt2QVV2akRRM2hDZ3BYK2pxbGVlcGFOLytTeHJVY0JWQVdSMVRzNkh1UmxZNndRd3R5bFZLbC9ISzdlTStpdTRrWXBveUJkS3JVZVNUbjRTUUttWUN6MWJydHFURWZ0UzVHZy9WUVBNcERhRFA5c29iS3diOUlFSURuOHU1cTE0c21BYzVlaGdVenA3RDN3UHdNamRYblhuVWVxS0JXVUhJU0N1cnFOQk5sSUwwTE42Z3MxdUxzL2xxVnkzaFR2R05jZHBEVmVXQUgzRVVjbW1WNm92cFZ1UjhYeHBBbTRYcXdOK2p3N0ZVKytJc3oxVG85NzUzL3Ava1Bjcnp5SWtMdVdreWRRdWNrdWNVVDBGSHZFWVlKWmlzRWJLRzZ4Y00xS1E0dGJvSmJUWjBkQUJhTnpSR1gvUU85MVV4cE45bW0xTFJkTEIzdGZsZXd1TWNUM3dPN1FhQnRHMGJwU2tMRWl4L1ZOMEJuUkY1K1NFdkZualMyd3J0ZmF1MlJzT2oxMzR0TS9JcHdmVnd3eFBkSlhCanRBUGRoVlRwemVmUWZnajdmSU53NGdKM0ttYVdiSGsrajRxQ24zaUgiLCJtYWMiOiIzMjdmNTA2ZmY0YmQxNjhjZWRhM2IzZjI2ODM1NDllOTNjYTQ4YzQ3M2ZjZTExODdiYjU0OWZlNTY5Yzc5OTgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037081852\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-335014449 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335014449\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:20:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNPZkp3bFpuVkoyK25uZDhlTlczMnc9PSIsInZhbHVlIjoiZmhBUThUZUpvenM5KzZDWnRoK1RlbFRzYUVQWWNmR2N6WnFOMEwvN1p3R2hYT3RpZFNkZ0trZGgrSXg5VytDWkZDNW83bWRUUytTT3pFT2I4Q2tKejJKUVQxR2dKRWlQRkhVdjl6WXZ3aC9uNHl4N0lsVlFJRE5oNWx1TTQ1Y3p5YXoxZ0dLOFFDYXFqSVpsMG56eXRDUFkvakd5QWRiVTBCMm9KZlVheEM3K0JpZXg5UEJNMVlIc3B3ZTBmYStVTjhPSVdIbzBBejhmSXNwWHRXY0RrT2hXcm1LbW85N3h1OVRrcGxDeWxXUWxFRnFwYlpSbHg2LzJGZEgyVWtwR3NwdkZDOFJrTDlHUUlMbHdvYk56V3MyYWFhakxyc2VQUWR3dWU3S01leWxLNGFtalZ5RXpqNUMzck5aODZwNXFDcWtrdE1HdmlDWGlWNzk1NUdYeGdKcFdmK2VYcE4yazFhaVdtbDB3TW91dUpSSUY4WGFoRFZiVzh6aFlwdHRjeU9rNnFWeXIwSldjYXdWVWRiZzNTVE8rVDA0TVhBc1Y3c0paRUM3RVV1dDNYdlY0ZnhNUWh4SzRHcUdSdlB5OStoOXJVaFpMMVdLQURTb0l4eVJnNkpnOXVrcmk0OHlwaHoxQi9oWUhEaDN3eUpFQWxLU0hwYkpLcEswZnlLeXkiLCJtYWMiOiI2NGJjMGQ2MWEzNDczM2NhYWM5NjdjYjRkNjI3OTNmYjhhMzU5YzhiZjZiZjZiZWI4NTdiMDFiZTA4ZDk4NmU2IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikl6UHRsazJpTURuSGZkVkJtVStGQmc9PSIsInZhbHVlIjoibW9Hd3I4T01KMVdNcWRCejR5Ylljcnk2MGlQTExEYmZzcUlEanpFNWZzanpjeGk1c08zMXQwaVFWWW1VQ0NFZkFYYUZxQnpacE1yRUpSRlB3bjdPc2Z1SHh0czY1a2s2N3VLNDl6TTJaS1FjTWg1VDhLTjZzZW56RWhCVFBXM1lNZm9KYy91WjUrTWZHbGFjTm9mcXpXOFlrMzUzUW5NYnNpdDJUU0JDMjNIVG5mck03M3VYSDNLRjYzSWlnTFhuN1o3UEZmMElpTDU1cDNnVkk4TjFzd2lMUUhqeUJ3VERuWk9IREFLOGRtN3p0T3kxTy90bjN1dWVKSWdaZ3h5Q0FqS2l2YUhYUnE2TDNFZTFHT1NDT3crRUd2TDgzN0NlN0pnZk9GUGpvR0VMcFo4SlhISHVwOTdCVG9ZZ2hzQWxrbytVZ05rU0xhenRGRCtRVlUwMi9rU2JNTk85SlhaY1VMWFhZR2lWNEZlaTI1QU1KbVpVYUJlVS9nNFRkcVBGdHU4a3QzT3liVS85SXNiSTF5S1FMRUpEa2NQZmFEdEpIUmV3QkRJU002LzFaUnowS2ZtUm16N2RwSU5zdG0yclFtVk50MmtJaUthL20vTWVQRVk3ZVc0VEYwZlVaUVU1SndGZ0hidlFoVzhjUW1Ebzh5MGNUY2k3VlZCUzVwTzgiLCJtYWMiOiI4NGIwNWVlM2I1ZDJjMWFiZjVjODgwYTZkNTlhNDMxM2JjYTVhZDYyN2E3YjdkNTk3OWQ4NzVhNzg3NTIyZGQ2IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNPZkp3bFpuVkoyK25uZDhlTlczMnc9PSIsInZhbHVlIjoiZmhBUThUZUpvenM5KzZDWnRoK1RlbFRzYUVQWWNmR2N6WnFOMEwvN1p3R2hYT3RpZFNkZ0trZGgrSXg5VytDWkZDNW83bWRUUytTT3pFT2I4Q2tKejJKUVQxR2dKRWlQRkhVdjl6WXZ3aC9uNHl4N0lsVlFJRE5oNWx1TTQ1Y3p5YXoxZ0dLOFFDYXFqSVpsMG56eXRDUFkvakd5QWRiVTBCMm9KZlVheEM3K0JpZXg5UEJNMVlIc3B3ZTBmYStVTjhPSVdIbzBBejhmSXNwWHRXY0RrT2hXcm1LbW85N3h1OVRrcGxDeWxXUWxFRnFwYlpSbHg2LzJGZEgyVWtwR3NwdkZDOFJrTDlHUUlMbHdvYk56V3MyYWFhakxyc2VQUWR3dWU3S01leWxLNGFtalZ5RXpqNUMzck5aODZwNXFDcWtrdE1HdmlDWGlWNzk1NUdYeGdKcFdmK2VYcE4yazFhaVdtbDB3TW91dUpSSUY4WGFoRFZiVzh6aFlwdHRjeU9rNnFWeXIwSldjYXdWVWRiZzNTVE8rVDA0TVhBc1Y3c0paRUM3RVV1dDNYdlY0ZnhNUWh4SzRHcUdSdlB5OStoOXJVaFpMMVdLQURTb0l4eVJnNkpnOXVrcmk0OHlwaHoxQi9oWUhEaDN3eUpFQWxLU0hwYkpLcEswZnlLeXkiLCJtYWMiOiI2NGJjMGQ2MWEzNDczM2NhYWM5NjdjYjRkNjI3OTNmYjhhMzU5YzhiZjZiZjZiZWI4NTdiMDFiZTA4ZDk4NmU2IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikl6UHRsazJpTURuSGZkVkJtVStGQmc9PSIsInZhbHVlIjoibW9Hd3I4T01KMVdNcWRCejR5Ylljcnk2MGlQTExEYmZzcUlEanpFNWZzanpjeGk1c08zMXQwaVFWWW1VQ0NFZkFYYUZxQnpacE1yRUpSRlB3bjdPc2Z1SHh0czY1a2s2N3VLNDl6TTJaS1FjTWg1VDhLTjZzZW56RWhCVFBXM1lNZm9KYy91WjUrTWZHbGFjTm9mcXpXOFlrMzUzUW5NYnNpdDJUU0JDMjNIVG5mck03M3VYSDNLRjYzSWlnTFhuN1o3UEZmMElpTDU1cDNnVkk4TjFzd2lMUUhqeUJ3VERuWk9IREFLOGRtN3p0T3kxTy90bjN1dWVKSWdaZ3h5Q0FqS2l2YUhYUnE2TDNFZTFHT1NDT3crRUd2TDgzN0NlN0pnZk9GUGpvR0VMcFo4SlhISHVwOTdCVG9ZZ2hzQWxrbytVZ05rU0xhenRGRCtRVlUwMi9rU2JNTk85SlhaY1VMWFhZR2lWNEZlaTI1QU1KbVpVYUJlVS9nNFRkcVBGdHU4a3QzT3liVS85SXNiSTF5S1FMRUpEa2NQZmFEdEpIUmV3QkRJU002LzFaUnowS2ZtUm16N2RwSU5zdG0yclFtVk50MmtJaUthL20vTWVQRVk3ZVc0VEYwZlVaUVU1SndGZ0hidlFoVzhjUW1Ebzh5MGNUY2k3VlZCUzVwTzgiLCJtYWMiOiI4NGIwNWVlM2I1ZDJjMWFiZjVjODgwYTZkNTlhNDMxM2JjYTVhZDYyN2E3YjdkNTk3OWQ4NzVhNzg3NTIyZGQ2IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImtVUml2NWJOZTBvaHFZczVOcVFKcUE9PSIsInZhbHVlIjoiejFRbFg3dmVRbjhOZTdRZ3hQWVU3QT09IiwibWFjIjoiMjlhNTc3ZjgyZWNkZmE3NTI1OTY5Y2M0MDI1NGFhYjQyMDYzYzU0Yzg4ZDUyY2E2YzI2NDBkYjU2Mjg1YTkwYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}