{"__meta": {"id": "X08550ea34faa423d5af5c924031eeb88", "datetime": "2025-07-21 01:18:06", "utime": **********.506027, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.072209, "end": **********.506042, "duration": 0.43383312225341797, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.072209, "relative_start": 0, "end": **********.453059, "relative_end": **********.453059, "duration": 0.38085007667541504, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.453067, "relative_start": 0.3808581829071045, "end": **********.506044, "relative_end": 1.9073486328125e-06, "duration": 0.05297684669494629, "duration_str": "52.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521272, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026, "accumulated_duration_str": "2.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4811318, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.769}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4911342, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.769, "width_percent": 17.308}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.49656, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.077, "width_percent": 11.923}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1860902157 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1860902157\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-612005575 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-612005575\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2067819489 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067819489\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-812050912 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060682246%7C7%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcyU0pPOXVVeGlhaUpIYitTOXFSeXc9PSIsInZhbHVlIjoiRFhlQTJ5MDUwTGhyRXNobmY2Umowclc2ZFNtUWNkMkc5RUhxZmpmbytGYy82QzE1c0JiK0FlaGQwVmZYVXdONlkxckN3ZGVOc3Z1RjQ2L3VSYTh5TENaalB4VDNXeEJLckI5TlFwWjBSeDZteWU4dkdqeGlhUVAyeGVuZkxVZE54dHpDQnlsYVpnWEZLSjZSTC9ScmgvcmdhQ2FLRW9IWmN4cVdJUUUxZzBzcUdibmI3dHlXenpOTWYwYkp1SmVIYnVLRk1zRTlVZ2dobFBHZW9kdWdpZ09JTVVzSjZXbERkWFVYekxITW5TRGsvcys2VXdiZExHSGtSSWtGZ0tZbUg1ZDRJWU1mQTB1Y1dpajRPU3IySy9tVE1oeHNqcUpZeGdUZ1JFRENRNFFpL3lMRDVaU1JWNjVKZGVIQ2o3dkFsaXdrWGQzamFwUmR1Y2c4bmhpMFN2TldlV25nb0lnRVN4U2V0SDVyL2NBczZDMjAxcDRNL2cxNFpKR1ZxRUtzVnJCMFFpUW1RRXg2M2wwM3lDaG9LZ1BSQTcwbTkxbUczdWpnY2FGK24vYUhORXROaVVMMEpGY24yZm1IZUc0SmZ0bTVMY2lKd1dPSzFUQjNEampqZmtjM1VSdUU0N1RqNmlPL0FnVjFrTnliRUFzbEo2c0tDTUtrYlFLN2dEdmMiLCJtYWMiOiI2MmM3MTA5N2UwZDBhNWVmYWMzYzBjMmY3ODJkOGYyZTk2MjY4YWYwZGNiNTY0ZGJhNDA1NGY5ZmRmMzEyM2FmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlHdnFhUXlRNTVFcUpKWHo0UGtXcHc9PSIsInZhbHVlIjoiQ2lnUW5vMTNWSTBvRmdwRU91cmJwLzdZOWUyN2JNOCtVaE1kSS9VMWRCeTRpbmRrN1dGdkpUbk9BaU8xc1dhNE9XL2IrdUV0ZVlWYWN3L0pISDBlWkFyOVdOK1Z6WmRiTXFPNmNwSTVPQTRQYmpRMmMxbXF2MnZ2U1VWdm9MZU9mc2V2WE9xS29la3Y2dWlSUE5ycWdId2NLcWVtWjNHUHNlRlV6RE01ODRCVVZkVkhEQlA5dHhvYmFqa0tEVGpoYVJ0WEVoQ0xKSFBUcjk1R1RDcVptQ0JRUWVrTlRyMnVqTGc1Q2pHR0dTbytMUWlqMy8vSEZvbmxFMEhmZ0NaN3RlRzBYOWx2V2dnTDVsMUw3SDRneHMrNU9OMXJ2Wm54bzhzUXZQUFZtVzdDUlNja1QwOXFRcmFaTEFodlFKQzg1eTZ2eWlRZ1UyeE12cjJxNnlXRThaMWkycHdjSlJnekduSlJXZ0szcGVsTXg4cGtKRkFCb2dkeGM5TkdLL3hxcXd0clhiYlVTQ1BjNFpxMGpCWEhiWkhsVlM3VHJYMktmOG1mT3M5RENHM1ZZUHV5NWQ5Q0VhSjNQQjhkYzRLKy9jdTkxVmIwTFY3SWxLcytndnJZVk51NG5tQTF3MzRCZFZ6NGFJeHBUdkMwNXpnTGFsQTJRU1JWSXJhWmpMZ1AiLCJtYWMiOiJmMmExZjU5ZGU1OGMwMzk4OGM1NGE2NTVkYWMwYTU4NGU0YzNmOGUyMDY1YmY0MDA5OWI3NTk2NzI1ODFlNzgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-812050912\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2117381396 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117381396\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:18:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJJbmdBR0tyR2c2akgvczRuMWpzWVE9PSIsInZhbHVlIjoiOGl2UFRkb0Vya2twd05BTjhSQVJSb0UyemdnNVFleG1aSEpWM2N2cDhOVkZaS1BISmtxOHhaMTBsZ1JzVTFVYU5hYzQrSG45RHNDbkl5V1BPRWJOTy9mNWYvREF6SVA3TytJbGc4M0V1MUUrNWNFQVY4c2N1U3JxRjZpTklzL0VpNTNaU3VjMjQ4YU9DeHhTdFVCRXVWWnNoM1hUVHVBRjNQM3FCaWMrWWJ5eitPb0pOZTErdU45RVRLbitNenErV1h6MnUzc3E0TGs4SlJMMlM3eVpJMkpNSThGUFlieU5XbmEvRWhJVXNXZ0N3a2dhaC83L2VMOVRLK1JtTUlGSjJVNmllVG1iNmxLRFcxRW1aR3NmQWJHV3pOWjQrNGtScUpRRHVER3U1SUNKeWpBQUppMy9GSWY0dXI0bTk2aGh0M29kdEtJUHdtUWh4eVdYRzByZXlEQkRxa2FueUdBSnBqaWsveHNtbUtUUitLYkNaSVhYVkV5V3pER3VBTXhnQjdrNk54K0RIdXhsL01QL2dSWXQ4OEVxaGl2YThvL25vOFNldEtxWDhiZzdlU3U2bGhTYmE5ZlRycWt3WWZ0QW1RZFhESHJGQ091ZWh3ai9JNkF5VnFJdTZjd0JMazV6YjdrQm5oK2ZDTWN5dnhNbmhIV21aMjF3NlFlWEJ2WHkiLCJtYWMiOiIxNmYxOTQ0ZTlmNjZjMTc2MDExYjJmNTEyYzhlODQxNWY4YWUxMTQwOTBhMzNiZjg3NDc4ZTQzOGJiY2EzZTAyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikc4Umd3My8xczFHZHdpVkEwTG9FeUE9PSIsInZhbHVlIjoiaG9xam9pL3R1OGwydjFWblN4ZlZ3UzhkR21wdk1PdExZbzc5MUYzdjA0ekszUXE0Wk9KcGl3Znd3cWRBZ0grTkdTbktHWnBreVJqOG1zSlkxK0t3SUFWNXBiMnp5dTZoWnk3aXJCRHBrQTArZTBqakEvYkZSVUMvdDNrKzBKS2JTQ0xjNGZZaGFXbFN2ZVFVaHBvdi9WdDRxSUk3Q1YxeVZOb2Y1TG5lSFhoL0JIYzJTUTVCNnlMeU1ueXM3a2lEcCtSM1o0aDd0Uzl0bTk4a0xDUTd4TmlxQVo0SGp0ZWNGbk1WSUNvenlGZGRlSTNvTnlxY2JkbzIwMTVNb1VZK25waWc2b2RITEkrbUtCeEVZSXZEOGpzVFI2WGVaemkxaGNKZUV5T2xQaFNQSk9QZW0yUm9zMzN5R2VvbVF3VkFNa1lobHV6R1NHNmFqOVJROTVLSnpTb3U0aHFhOEk2QmFYTk1ydzE2OTk4bzJ3RElpZWNDQjc3UTFDYmxjcFVQUmNzUGQ4QzIzWGVzMEx2Zmc2LzNMdmtzdFlyYmFBN1FOeW53Y1k4L3I2VUptRitnZFpyRkNNQm5QUXZEdlNiUUNUQkJ2S1lEdzVlZVJVcHR0V1pYWUtKc292R3FhMFpwcFp5Yk00V2FvWFVsRFBqMEtScHllSkNMUjM4dVlkdloiLCJtYWMiOiIyOTBmZWM1NDUxNDk2NzJmNWE5OWMyMzAxNjM5ZDE4M2EzZjMzZGY2NzA3MDk1NjVmNDU1MTY2OGY5NzAwM2ViIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJJbmdBR0tyR2c2akgvczRuMWpzWVE9PSIsInZhbHVlIjoiOGl2UFRkb0Vya2twd05BTjhSQVJSb0UyemdnNVFleG1aSEpWM2N2cDhOVkZaS1BISmtxOHhaMTBsZ1JzVTFVYU5hYzQrSG45RHNDbkl5V1BPRWJOTy9mNWYvREF6SVA3TytJbGc4M0V1MUUrNWNFQVY4c2N1U3JxRjZpTklzL0VpNTNaU3VjMjQ4YU9DeHhTdFVCRXVWWnNoM1hUVHVBRjNQM3FCaWMrWWJ5eitPb0pOZTErdU45RVRLbitNenErV1h6MnUzc3E0TGs4SlJMMlM3eVpJMkpNSThGUFlieU5XbmEvRWhJVXNXZ0N3a2dhaC83L2VMOVRLK1JtTUlGSjJVNmllVG1iNmxLRFcxRW1aR3NmQWJHV3pOWjQrNGtScUpRRHVER3U1SUNKeWpBQUppMy9GSWY0dXI0bTk2aGh0M29kdEtJUHdtUWh4eVdYRzByZXlEQkRxa2FueUdBSnBqaWsveHNtbUtUUitLYkNaSVhYVkV5V3pER3VBTXhnQjdrNk54K0RIdXhsL01QL2dSWXQ4OEVxaGl2YThvL25vOFNldEtxWDhiZzdlU3U2bGhTYmE5ZlRycWt3WWZ0QW1RZFhESHJGQ091ZWh3ai9JNkF5VnFJdTZjd0JMazV6YjdrQm5oK2ZDTWN5dnhNbmhIV21aMjF3NlFlWEJ2WHkiLCJtYWMiOiIxNmYxOTQ0ZTlmNjZjMTc2MDExYjJmNTEyYzhlODQxNWY4YWUxMTQwOTBhMzNiZjg3NDc4ZTQzOGJiY2EzZTAyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikc4Umd3My8xczFHZHdpVkEwTG9FeUE9PSIsInZhbHVlIjoiaG9xam9pL3R1OGwydjFWblN4ZlZ3UzhkR21wdk1PdExZbzc5MUYzdjA0ekszUXE0Wk9KcGl3Znd3cWRBZ0grTkdTbktHWnBreVJqOG1zSlkxK0t3SUFWNXBiMnp5dTZoWnk3aXJCRHBrQTArZTBqakEvYkZSVUMvdDNrKzBKS2JTQ0xjNGZZaGFXbFN2ZVFVaHBvdi9WdDRxSUk3Q1YxeVZOb2Y1TG5lSFhoL0JIYzJTUTVCNnlMeU1ueXM3a2lEcCtSM1o0aDd0Uzl0bTk4a0xDUTd4TmlxQVo0SGp0ZWNGbk1WSUNvenlGZGRlSTNvTnlxY2JkbzIwMTVNb1VZK25waWc2b2RITEkrbUtCeEVZSXZEOGpzVFI2WGVaemkxaGNKZUV5T2xQaFNQSk9QZW0yUm9zMzN5R2VvbVF3VkFNa1lobHV6R1NHNmFqOVJROTVLSnpTb3U0aHFhOEk2QmFYTk1ydzE2OTk4bzJ3RElpZWNDQjc3UTFDYmxjcFVQUmNzUGQ4QzIzWGVzMEx2Zmc2LzNMdmtzdFlyYmFBN1FOeW53Y1k4L3I2VUptRitnZFpyRkNNQm5QUXZEdlNiUUNUQkJ2S1lEdzVlZVJVcHR0V1pYWUtKc292R3FhMFpwcFp5Yk00V2FvWFVsRFBqMEtScHllSkNMUjM4dVlkdloiLCJtYWMiOiIyOTBmZWM1NDUxNDk2NzJmNWE5OWMyMzAxNjM5ZDE4M2EzZjMzZGY2NzA3MDk1NjVmNDU1MTY2OGY5NzAwM2ViIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-49087823 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49087823\", {\"maxDepth\":0})</script>\n"}}