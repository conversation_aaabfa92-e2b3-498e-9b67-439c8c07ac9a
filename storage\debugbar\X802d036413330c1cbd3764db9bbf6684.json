{"__meta": {"id": "X802d036413330c1cbd3764db9bbf6684", "datetime": "2025-07-21 01:19:36", "utime": **********.008894, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.550465, "end": **********.008912, "duration": 0.****************, "duration_str": "458ms", "measures": [{"label": "Booting", "start": **********.550465, "relative_start": 0, "end": **********.922234, "relative_end": **********.922234, "duration": 0.****************, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.922243, "relative_start": 0.****************, "end": **********.008914, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "86.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01924, "accumulated_duration_str": "19.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9536848, "duration": 0.0178, "duration_str": "17.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.516}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.989023, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.516, "width_percent": 3.378}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.000645, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 95.894, "width_percent": 4.106}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C**********053%7C4%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJoN3ZabWxjMEI4eHBsU3AzQkovUHc9PSIsInZhbHVlIjoiUFV1ZXV1aDhNNDhMbFhhT0MzVUZ3ZjdRb3dIWUN1cDdVZS9sbTdJbkh6c0NndXV0WkZOZWFxMXU1M1RvUmRJWEVHcXNIV3dOZGoxaWQ5WFlZcnloOURhVkszMGkyZWlyUS84QU9QVFhhSUx1Y295c0hHMWF5YThjdUdUbUxLNFZtZEtYV21jWVZXY0pIcCtxdGNtOEEzRmE2Q0xtK3JXcDhwelo0TEtMRWh3dDJqRjFSTmNtQ21QeWJKbTJoN2F6MFhOelhKd3pFYWcvSEwzS0drVEJoS2lqSEs3NlhCeTk4bFdYMndVeDJrNkxxdHJ2citUd2hDT1gyb3NGMENqL2l6UngwWHhtYVByenl2SFdIdGFMSit4UG1uSlRZZzNrVnBJSnhHZDU5empqSlo5MElhRFY0aDFDb0szWlhrZExhaUd3eGx2UkxHbDF3NWpyVmFCNGNobUJqYmZEc2puZkFEeTFTM1FISFdxclhjajgramVQZTZPN3UveW9peFIrSUhyZWJvQndXQVFmQ1hndjJ0dFdwOE9YZ1Z4NGNxZzh5ai8yU2lEejQ0eVlzZG9BL1htUVlDTmVkenovMFpKbkQxcXdnQ2lzK1BQWFR6R2h0ZTVnNFFCak93dDNwaHVmSENKd1hlWmM2SzJaRmJLeGNPZytoOTVyOVN1NG80cnQiLCJtYWMiOiIzYjI1YTZjZjdhODkwNjYxNGM2OGZmMDM5ZTM5NWUwZDI0YzRlYTQ3ZGUxZjJhMjE1ZmQ2ZDU4MGJmOTVmNmFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpBRVBQS2JIZkpkdGZ0RmJNMDA0QUE9PSIsInZhbHVlIjoibGVmMGNXZFBGQUpuOWdQaTFTbTBJUWRQTjFyTXhHVGJqU2tXQmozQTFBbFRQbEJDSjVaZjZzOXZrckdsRUlzRFpJWktXWFI2Vk9NSWN1QkJKazJMOGlpY0lKSWVrUzZDdGZFSkFYUS9WcVVZdld3Y3FYUUhtZ3BTcGxySzhsY0dUcHdHelRmZUpUR2cyR2w3bmdxS2JndkEvMk5GeGhKb3JwWkRoTkRhcnNYRDA4alh2Z01HQkR5UW92NUxPQnNPOGNuRWd2RGJveEpzY0oxdnNjZjhDZWtmVXlWaExZMmd4RHpVaVV5UlAzZy9SUlBjcXUyZFpvUHA1YkVwR2VTRUdVbWdIelY5L2l0dS9TVnFvNFZXdytZK2ZIb0VydUlvcm4yZ1k2MGJaczkwbDN6Z2VuUjJxKzNWeVVBSWJzQzUyMC9xOGpLcDFOYUpFcUFwUDUwbUhtc0JXMkFxaE1VcjV6K0JTMEZEMHhneFFHVGo0M0NDKzRNYVI1aFhWTTBtZVlxYnlrMkw4K2U2akdIM0Q0Mm5sWld6RU5vSDd6ajBGb2hzY3dlTTUrRktDbGorZ0IrMlErbEtERGdhUERVbnNkamsrMWk5V3VnRmhaQ0pVSGtyOFlQemYzc3FKbWtqVHhTKzhqb2FFREc3VHdIcncrMll4cXBDTnZjTlhrTzQiLCJtYWMiOiI0YzYyOGIyMWM3ZDljZDNmNDE1NGViMTJhY2UwYzMwOTNjN2FmNzQyYWEwNGQ4NzkxMDg1ZjRlZTEyZmY0MjczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1335804319 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335804319\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-369560760 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:19:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVYSFMyVW5mMXl3YU1OSVQrRGxCWnc9PSIsInZhbHVlIjoiRFdwTklCbm05K3RzQS83UWxGbllsUjJjRHZraUgvQU1DbElPeWRGUUJIbTVxTHZwOEdweHZtOFRNbUZYZWFYdWp2U3d5SHQyenJFa2tKYi9peGo4L3FLalI5SU4zVzFTcnh0em1wQ21JN2dPaFhrb0JTY0lYUEJMejNxdVlVTFE1dUdyN3BPSTdWb2JkZUc2N0NlRUpSbTg1M25mb0RKSGhXSmdrZTVCMnc1UFBJTkxpS2RRRVpkZ3U0RldzSXJyZ0RmK0t0dllUVFlSZnNZUU85UTlYNmROdE5JNzFkL0o3L2R3TkFjcXdIUm9DOWFmMmFZbW1JUko4R0NYdDdlUEhZU2hsY3JwbjdWdzc0UDJVZXNjZ1ZaOFhESmZIa3p4aExzUTlaTVdXRXZlZVdnNzFRQmp1cE0xSUNZWkIyWTRGM2N4b284OXRGNXJqREg4dHM1ZnNaWmJUQVh0NnFoR3NaSE1ZV3ZQVHh4ZUpzRy9WQnJhMXJ6WWhXUk9QRFJobjBYUjQ3NUcyaUhOa1c5Wk5VQmc2bkczbENLeXlQcXFYNWdOanZtZHFVdEZWYWtoT1FXTGJzclM4UVpaelJIbEd3Z0RlTWtLcWdNY3VLMW5rQTJJVDdPYklVL0NVMEh5cUg5SDhMNGNSQ2NtM0l5aHZyNE9SdmlWMnNLNjhoQWwiLCJtYWMiOiJmYTk5MTBlMzU5MWFhMGY5NWE3YjcwZDc4MDY1MTM5MGM0YzE4MzcxZjI5MDlmNDdiMDAxZTNmYzRiZmJhMDU4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii81NWFiVmNVYzZWS2pFSHc1anpJQ2c9PSIsInZhbHVlIjoiNkorM2kxUFZqYnF6bGovbE95OGpNcTkyWGdUVU40SDBsWSttREZnN2Jpc2tYZUx3bGRkVXZZbUxtNGNiSEJORjhabzRhY3Nxb2ZHVmNUMzM3NnpWV0kxOENDKzh1K0dEVmhuWXpVMFNpTEMvVGtNNGhocEJDMFJrRmRLTkJYSnJ5VXVPS242WVdRaWJRcHQ5bC9FazBZUWNWM3RDMHNXZkhOaW5oY0h3ZXhPendOUXBlUVBmMC9jeGU2RUF2NDhzUG04RTJOeFZoRVREQ3gySzZqRGJsaUdWbWc2Qkp0Q3ZINDFMT1BxYmhvb3RELzh6OFVIZjBITW5MdGliTWZWaXBwWjlMaHNXOHJnSHBpeGl4YnRlNmRLRWVLYmQrSjlVeWx6RXVNbFlSUVR2V1lXWFB0b3BCNGU3VHpyRDhWa214ZGRaZVplZVJSN2E4SWFia3BQTng2OFNYUmxlTnVqYkNFMzU4VWM2Vm1Ta2xCdjRnaXE4L1pCV2ZoMUdQaGJvU3BtY29CeDNRMmRKQU1vVVVubmNkMXY2M3p0eE5mL2dvZHVTQnUxK2VCN3Yyb201bURYeldXY0tEUEg3dE1tWU9XeWdMTmRPMHBrUVUvQisyKzR1SFd2aHA5SEpiejZkRDJCT2hLamtZQU1SYzBvYUVsaGhxUklOWWRTcmhWYVAiLCJtYWMiOiJkMzJjNGI3OWY4Mjk1MTRkYjU4NjU2NmU4MzM1Yzc4ZGI4MTViYTRiYmI4ZDNmYWNlNmQ2MjliZmYzZjFmZjk0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVYSFMyVW5mMXl3YU1OSVQrRGxCWnc9PSIsInZhbHVlIjoiRFdwTklCbm05K3RzQS83UWxGbllsUjJjRHZraUgvQU1DbElPeWRGUUJIbTVxTHZwOEdweHZtOFRNbUZYZWFYdWp2U3d5SHQyenJFa2tKYi9peGo4L3FLalI5SU4zVzFTcnh0em1wQ21JN2dPaFhrb0JTY0lYUEJMejNxdVlVTFE1dUdyN3BPSTdWb2JkZUc2N0NlRUpSbTg1M25mb0RKSGhXSmdrZTVCMnc1UFBJTkxpS2RRRVpkZ3U0RldzSXJyZ0RmK0t0dllUVFlSZnNZUU85UTlYNmROdE5JNzFkL0o3L2R3TkFjcXdIUm9DOWFmMmFZbW1JUko4R0NYdDdlUEhZU2hsY3JwbjdWdzc0UDJVZXNjZ1ZaOFhESmZIa3p4aExzUTlaTVdXRXZlZVdnNzFRQmp1cE0xSUNZWkIyWTRGM2N4b284OXRGNXJqREg4dHM1ZnNaWmJUQVh0NnFoR3NaSE1ZV3ZQVHh4ZUpzRy9WQnJhMXJ6WWhXUk9QRFJobjBYUjQ3NUcyaUhOa1c5Wk5VQmc2bkczbENLeXlQcXFYNWdOanZtZHFVdEZWYWtoT1FXTGJzclM4UVpaelJIbEd3Z0RlTWtLcWdNY3VLMW5rQTJJVDdPYklVL0NVMEh5cUg5SDhMNGNSQ2NtM0l5aHZyNE9SdmlWMnNLNjhoQWwiLCJtYWMiOiJmYTk5MTBlMzU5MWFhMGY5NWE3YjcwZDc4MDY1MTM5MGM0YzE4MzcxZjI5MDlmNDdiMDAxZTNmYzRiZmJhMDU4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii81NWFiVmNVYzZWS2pFSHc1anpJQ2c9PSIsInZhbHVlIjoiNkorM2kxUFZqYnF6bGovbE95OGpNcTkyWGdUVU40SDBsWSttREZnN2Jpc2tYZUx3bGRkVXZZbUxtNGNiSEJORjhabzRhY3Nxb2ZHVmNUMzM3NnpWV0kxOENDKzh1K0dEVmhuWXpVMFNpTEMvVGtNNGhocEJDMFJrRmRLTkJYSnJ5VXVPS242WVdRaWJRcHQ5bC9FazBZUWNWM3RDMHNXZkhOaW5oY0h3ZXhPendOUXBlUVBmMC9jeGU2RUF2NDhzUG04RTJOeFZoRVREQ3gySzZqRGJsaUdWbWc2Qkp0Q3ZINDFMT1BxYmhvb3RELzh6OFVIZjBITW5MdGliTWZWaXBwWjlMaHNXOHJnSHBpeGl4YnRlNmRLRWVLYmQrSjlVeWx6RXVNbFlSUVR2V1lXWFB0b3BCNGU3VHpyRDhWa214ZGRaZVplZVJSN2E4SWFia3BQTng2OFNYUmxlTnVqYkNFMzU4VWM2Vm1Ta2xCdjRnaXE4L1pCV2ZoMUdQaGJvU3BtY29CeDNRMmRKQU1vVVVubmNkMXY2M3p0eE5mL2dvZHVTQnUxK2VCN3Yyb201bURYeldXY0tEUEg3dE1tWU9XeWdMTmRPMHBrUVUvQisyKzR1SFd2aHA5SEpiejZkRDJCT2hLamtZQU1SYzBvYUVsaGhxUklOWWRTcmhWYVAiLCJtYWMiOiJkMzJjNGI3OWY4Mjk1MTRkYjU4NjU2NmU4MzM1Yzc4ZGI4MTViYTRiYmI4ZDNmYWNlNmQ2MjliZmYzZjFmZjk0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369560760\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1010316495 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010316495\", {\"maxDepth\":0})</script>\n"}}