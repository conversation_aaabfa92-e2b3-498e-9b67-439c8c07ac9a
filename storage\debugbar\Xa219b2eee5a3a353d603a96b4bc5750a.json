{"__meta": {"id": "Xa219b2eee5a3a353d603a96b4bc5750a", "datetime": "2025-07-14 17:59:05", "utime": **********.31653, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515944.87134, "end": **********.316544, "duration": 0.4452040195465088, "duration_str": "445ms", "measures": [{"label": "Booting", "start": 1752515944.87134, "relative_start": 0, "end": **********.260881, "relative_end": **********.260881, "duration": 0.3895409107208252, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.260892, "relative_start": 0.38955187797546387, "end": **********.316546, "relative_end": 1.9073486328125e-06, "duration": 0.055654048919677734, "duration_str": "55.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989544, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00438, "accumulated_duration_str": "4.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.288996, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.352}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.300834, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.352, "width_percent": 15.068}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.30895, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.42, "width_percent": 17.58}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjR1TDdESWFrMDlZNi9SSlg0Y2xKUHc9PSIsInZhbHVlIjoiNE5SR0pOS3hGREh4dGg0aUR6T1ZWUT09IiwibWFjIjoiMjViNGNhMTg3MWRhYjgzYTQyNTdjMGQwZjU4YjJiZGMzZWJmNTU3YmRjMjRkYjZmZWUzYmZkZGVmYTc3M2EzYSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-531954194 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-531954194\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-42628857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-42628857\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1566829003 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566829003\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-810464997 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjR1TDdESWFrMDlZNi9SSlg0Y2xKUHc9PSIsInZhbHVlIjoiNE5SR0pOS3hGREh4dGg0aUR6T1ZWUT09IiwibWFjIjoiMjViNGNhMTg3MWRhYjgzYTQyNTdjMGQwZjU4YjJiZGMzZWJmNTU3YmRjMjRkYjZmZWUzYmZkZGVmYTc3M2EzYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; XSRF-TOKEN=eyJpdiI6IkZVaXBNVkVaSUlYaHdXcjFSaGYzQlE9PSIsInZhbHVlIjoiOWljVThJY0docmRWYXlNbE13SHRJdUY3NEZvUHpXSXRDbEhKa0EzV296UTNYNWJRU0h6eTBYYmRHMEJ1eWwvQW05T2h6RlhoclNJSHdvNi9HTzZtdVRpemU0SldES05lWjNONEVqbW80UG52UnVNd1dheFl1MVBrVXBZREhEdk9wVGFnU0dYVG1tVzk3K24wMGlETzRvRmQ0ZEkvWjkxZ3pkeEtpa1crYVNqc1hJbklsdHdvMFdnVzF4bWlpRlhld0NuRDA3b0U5VkNYb3ZPZFRLeFJucXJjS0xNSThLaHVvbElaYXFWYXR5L1V2ZmVnYm10SGJRMi9LN0RYc3BxdUJqUTRVZitWV01oTDM1ek1qdVNheTNNWXZmNk5FZVNZKzY1MWw0VElUdmw5WElLMWpERUxxRldQMFNnSXZRRnlUeTA0T3liM3lIcnozalJJbW9YemduQVNIMUJIa3JoRlU3QVF6NG1jMWFGUmlrYVcrcFBCUy9IcThQemx1UjlDN0tWZVNneERxelE1ZGhRcWdJbXo3Z3BMQXZJN3N2RC8xSk9CU1lqSFVCK1V2U09TMWRCeG1Va3dLekJLYjRMQXRkWVh6SWdTeHRCUFZLZ05tckxpcmozanhaZnVZbFpyenlUb3ZQdkpnd0EzUi9ZcU9QYlRWZ2xkRFBVWFVidTciLCJtYWMiOiI1MGFiZTc2YmVhYTNlMTNkZTVmNjk1ZDgwMDc2YzVjMTc2NTQ1YjZlNjViMGU2NTJmYzlmYTY4ZDc4MDZlOTg5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVvZGNVODliT0toNFlnZ1pOSitkL0E9PSIsInZhbHVlIjoiNWhVZVByTFlqemF0VlNlTWJUczNnVGRWZlBmTnN6VlpNbHlTYTVzdzB6Wk1SZXJ1bVQwKzhBZk9KWnhnYXYyd3JBVW4rZjBvUmFrVEk3M1BhOGVkUTJoRnM3WVZqQzJIWnY3TzVCamM3LzA4bEt5emp5bytic3pHY003cFo1R2FWWStaeDdTMkZUa0VqcE1tbFR5V2hscUE3SDlVQ1ZIb1lFUzVQQUxDSXY0cEpxT245Z0R5a0lpcXdPVHNHU2xvaTc2TWRRckk3TDhLTCsxTzVlcGVkY2wxSnZHL3VDMTRocXdDczg3MnIxSFowVnZvTktESHN2QUhwaDJHVXJ3QVVTQXdUVGl0d05VVkU2bGFyQTRzbkk5MlY5T0wyd2NTY1VYOEthZnhacFVjUmVrSG1OOExhTVhDQUIyVjIrSlRQNUFpTlYwYWtwaDc3QUNNaHZveDZpeVNIUDVhTTF2VjI0QlplSmtvV0UzdWxwenJNWk9wVDI1S1BmQ0FyVTNKU25wTzBBdDBIaUNIK0NJbHNleEp5RDRlM3ZMdzRVYVNDcTlxUzlPM2VTamFYanNqQ3EyUUZTd2ZPSGlIWGtCNlpxbHZGL2plZmpRbk9iMnJCdmFuVVBYb1JacUxLSC9Sb2hFaWpWNGc3Rm4xczBhRGQyMlhJa3FNc0ZoWGlOS3IiLCJtYWMiOiJiM2EwMjc0ZGI3ZGI5M2VmNGIyMmQzN2Q2M2ViNDkwMjNjYzhlNjIwMDY3ZGY0ODljNDlhZDZlMzQ4NWIyOTgwIiwidGFnIjoiIn0%3D; _clsk=14bfr1r%7C1752515944651%7C12%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810464997\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-125074541 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125074541\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1904160827 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:59:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhyS3F6alZUU1BWM04xRTJRb3gzMnc9PSIsInZhbHVlIjoiZjltUTdLRm1xS2dGSkVKQjc5OVphZWVSeVVvdHYvZGloVk1vVFlLVnNxRmUvdnZaQlNhbFhqMzVLMzFkdEpsako2c29QVjFYTmd6eDhxdE8vREk5NGx0SWFpN3JhSXcydUJ5V3ZSRWQrMjNaYzQxVEFpb1FFWkRsZVF3d1d5Y1lJNHNvbTdLdEpJcFdwcGlqNWlVZm9vZlAybXo0WHZnVm1mVUE1emNiSjc0NDh2djdpdXBBZEhYVW1RNDg2RG5MQWdlTURxN1F2ZVpwcGc4ZVBPUTIwYllEZ253c0tsWlFGZzUxc29uWjZTMXF0cUtIVGlFUjRxODM4eGtySGlqRHU0bE5uWHhpb2xmTmh2NEc1SDRNNUZjZ2hMLy9FeHh6RVdoWW1XSUxTa1VuU0FOZjZYR3FCU2N6UUpnWjV2dlI0b3IrR0tTdFBuanM0UDNqQXB1U3ZFZTh6ZHJIMzBoZHlmV3g0UCtaZlUwbFJsK0dUREpUc042bWt5RHBubDdRRWFIV1d1SWdaVW41Tm5VRFp0Nk5BUkVITkVDV3NzSE4xb1o3VlF1eFhBU2V0d2VGOFp1cDl1TS93SGhnSVFqUGZMZnMxRmFFbUpHbnNYM0RQQ2hSQTFqM2JKRU12ZFE5TFpycXZVcDJSK2hGUHYxT29zMm9sZ2dXc2FmNnlrN24iLCJtYWMiOiI5ZDM3YzlmYTg0YmY4YjUyYjc0YmM3ZDhhZWM5MGU0MTNmOTk4YzU0NmIzNDExZDcyOTVhODFiYzRkMzU4MjNiIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:59:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlorMGt2bWdYWHFVUjRBaG9SS2VvNWc9PSIsInZhbHVlIjoidkVTWWhoWWRpR2ZZSVVyYzcwSHd6NXpoNElqZmVueWRiZk5pUTc0RjhmcmdJRlZScHZZUWY3MTZYTWtMUXV6bUhZekRYMFNDS3Z4UjJBMTMzTThmR2NEb2hxcVp3cW50R1hQM1BVYVZiZklZajBJMXdrbWFNTzFSTFVCQUd4YkdIQVJ3T29xcU9YVVlTbVplQ2VNWmh1YXI1WVJhUEc2cHV1RFZ3NllmWE5ud3h2WXEvYnJaUmJOKzh5MEozKzMrdHFVTzh4NjFmdW91eW8zc3RWMVpKV0psTzg3RE95UmtmVnR1eEhhSjkyUGlHMllPaXh1dVJLa0c0QU9RS0xwUHRLMm5ZL1pTcU1ETEl6cUx6elNCczRuN0xwWU5qRCtqdU1DR1M5aHpNeTRMUEorSTNQT1ZqY3VTVlhVTW9WNHNtbnhnNUdoZExncFBjLzZVYmg2MmNzQmFoU2w3QVlZWXpJeDMrTGJ5U1hMYnFBYXJsSFFRS2swU3hESXorY1lzL3VlZ2NPRGhDd3phMXZCWVpCRlpWa240MHJOWXVjYmppZE5XWGJyaGFXMXppWVAvOENFUm9UQ2pjZXlJSE9ONTRqY2FEQ0ZwUWo5L2NOWFhZM3E0UGRlT2daU0VhdWlWeVcxSW5wQW9vQXhnRUNPeGFTNU45aUk4c0JHYnk0ay8iLCJtYWMiOiJkOTA0MTRhNzM4NWY3YmEzODY5M2M1MjkwYjJkMWVjNmViY2M5MGQ3YjY2MmU1ZWQ4ZmYzOGZmZTI2MGZlMTk3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:59:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhyS3F6alZUU1BWM04xRTJRb3gzMnc9PSIsInZhbHVlIjoiZjltUTdLRm1xS2dGSkVKQjc5OVphZWVSeVVvdHYvZGloVk1vVFlLVnNxRmUvdnZaQlNhbFhqMzVLMzFkdEpsako2c29QVjFYTmd6eDhxdE8vREk5NGx0SWFpN3JhSXcydUJ5V3ZSRWQrMjNaYzQxVEFpb1FFWkRsZVF3d1d5Y1lJNHNvbTdLdEpJcFdwcGlqNWlVZm9vZlAybXo0WHZnVm1mVUE1emNiSjc0NDh2djdpdXBBZEhYVW1RNDg2RG5MQWdlTURxN1F2ZVpwcGc4ZVBPUTIwYllEZ253c0tsWlFGZzUxc29uWjZTMXF0cUtIVGlFUjRxODM4eGtySGlqRHU0bE5uWHhpb2xmTmh2NEc1SDRNNUZjZ2hMLy9FeHh6RVdoWW1XSUxTa1VuU0FOZjZYR3FCU2N6UUpnWjV2dlI0b3IrR0tTdFBuanM0UDNqQXB1U3ZFZTh6ZHJIMzBoZHlmV3g0UCtaZlUwbFJsK0dUREpUc042bWt5RHBubDdRRWFIV1d1SWdaVW41Tm5VRFp0Nk5BUkVITkVDV3NzSE4xb1o3VlF1eFhBU2V0d2VGOFp1cDl1TS93SGhnSVFqUGZMZnMxRmFFbUpHbnNYM0RQQ2hSQTFqM2JKRU12ZFE5TFpycXZVcDJSK2hGUHYxT29zMm9sZ2dXc2FmNnlrN24iLCJtYWMiOiI5ZDM3YzlmYTg0YmY4YjUyYjc0YmM3ZDhhZWM5MGU0MTNmOTk4YzU0NmIzNDExZDcyOTVhODFiYzRkMzU4MjNiIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:59:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlorMGt2bWdYWHFVUjRBaG9SS2VvNWc9PSIsInZhbHVlIjoidkVTWWhoWWRpR2ZZSVVyYzcwSHd6NXpoNElqZmVueWRiZk5pUTc0RjhmcmdJRlZScHZZUWY3MTZYTWtMUXV6bUhZekRYMFNDS3Z4UjJBMTMzTThmR2NEb2hxcVp3cW50R1hQM1BVYVZiZklZajBJMXdrbWFNTzFSTFVCQUd4YkdIQVJ3T29xcU9YVVlTbVplQ2VNWmh1YXI1WVJhUEc2cHV1RFZ3NllmWE5ud3h2WXEvYnJaUmJOKzh5MEozKzMrdHFVTzh4NjFmdW91eW8zc3RWMVpKV0psTzg3RE95UmtmVnR1eEhhSjkyUGlHMllPaXh1dVJLa0c0QU9RS0xwUHRLMm5ZL1pTcU1ETEl6cUx6elNCczRuN0xwWU5qRCtqdU1DR1M5aHpNeTRMUEorSTNQT1ZqY3VTVlhVTW9WNHNtbnhnNUdoZExncFBjLzZVYmg2MmNzQmFoU2w3QVlZWXpJeDMrTGJ5U1hMYnFBYXJsSFFRS2swU3hESXorY1lzL3VlZ2NPRGhDd3phMXZCWVpCRlpWa240MHJOWXVjYmppZE5XWGJyaGFXMXppWVAvOENFUm9UQ2pjZXlJSE9ONTRqY2FEQ0ZwUWo5L2NOWFhZM3E0UGRlT2daU0VhdWlWeVcxSW5wQW9vQXhnRUNPeGFTNU45aUk4c0JHYnk0ay8iLCJtYWMiOiJkOTA0MTRhNzM4NWY3YmEzODY5M2M1MjkwYjJkMWVjNmViY2M5MGQ3YjY2MmU1ZWQ4ZmYzOGZmZTI2MGZlMTk3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:59:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904160827\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2097219977 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjR1TDdESWFrMDlZNi9SSlg0Y2xKUHc9PSIsInZhbHVlIjoiNE5SR0pOS3hGREh4dGg0aUR6T1ZWUT09IiwibWFjIjoiMjViNGNhMTg3MWRhYjgzYTQyNTdjMGQwZjU4YjJiZGMzZWJmNTU3YmRjMjRkYjZmZWUzYmZkZGVmYTc3M2EzYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097219977\", {\"maxDepth\":0})</script>\n"}}