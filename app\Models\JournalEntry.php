<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    protected $fillable = [
        'date',
        'reference',
        'description',
        'attachment',
        'journal_id',
        'category_id',
        'category_type',
        'total_amount',
        'created_by',
    ];


    public function accounts()
    {
        return $this->hasmany('App\Models\JournalItem', 'journal', 'id');
    }

    public function category()
    {
        return $this->hasOne('App\Models\ProductServiceCategory', 'id', 'category_id');
    }

    // Get total debit amount
    public function getTotalDebitAttribute()
    {
        return $this->accounts()->sum('debit');
    }

    // Get total credit amount
    public function getTotalCreditAttribute()
    {
        return $this->accounts()->sum('credit');
    }

    // Calculate and update total amount
    public function updateTotalAmount()
    {
        $totalDebit = $this->accounts()->sum('debit');
        $totalCredit = $this->accounts()->sum('credit');

        // Use the larger amount as total (should be equal in balanced entry)
        $this->total_amount = max($totalDebit, $totalCredit);
        $this->save();
    }

    public function totalCredit()
    {
        $total = 0;
        foreach($this->accounts as $account)
        {
            $total += $account->credit;
        }

        return $total;
    }

    public function totalDebit()
    {
        $total = 0;
        foreach($this->accounts as $account)
        {
            $total += $account->debit;
        }

        return $total;
    }


}
