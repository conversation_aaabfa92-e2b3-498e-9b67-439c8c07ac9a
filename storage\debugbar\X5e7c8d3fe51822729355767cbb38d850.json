{"__meta": {"id": "X5e7c8d3fe51822729355767cbb38d850", "datetime": "2025-07-14 18:30:23", "utime": **********.096943, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517822.660998, "end": **********.096958, "duration": 0.4359598159790039, "duration_str": "436ms", "measures": [{"label": "Booting", "start": 1752517822.660998, "relative_start": 0, "end": **********.020612, "relative_end": **********.020612, "duration": 0.35961389541625977, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.020625, "relative_start": 0.35962700843811035, "end": **********.096959, "relative_end": 1.1920928955078125e-06, "duration": 0.07633399963378906, "duration_str": "76.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46005792, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02644, "accumulated_duration_str": "26.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0487099, "duration": 0.02535, "duration_str": "25.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.877}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.083041, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.877, "width_percent": 1.891}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.089192, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.769, "width_percent": 2.231}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1333135573 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1333135573\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-581634410 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-581634410\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2083628113 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083628113\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1570719317 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/goal</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517820079%7C42%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InMxODdDZ0s3RlJsM25mT3FKanJFR0E9PSIsInZhbHVlIjoiUUVKakNYNW9DbmZGNWxEd0tWek9vaGFHZlpQQy9rQ0VBbDI0UVcwK2w1WXp6RXVQeVFwU2ZkQlQ1Mzl3SHpsRnpjcWhkdHlGM3F6OXFXVm1LY2tqaU9sOGFjdXNtSmtxQ2YzdWlQSjZCTmpkS2syQ2Nza2MxanJzeDVnMllSUnNZcmUvUGdremhVaW54MitPY1cyd0V2MmdiNHhqZkRBMUpGbG52MmdmejFvMitOMnAwdXJLSnlVaExtZFQwR3R6R3RNMER4c3BCMlBrTWJyVjJKODEwMjFKQ1NRTy90NmpxaTRFeThuOVBOdjVWK2tjaDBoWktoeE1DclJXZytDMVcwbGk2aTAxR0ExanRGSnhsTk0yUFRuUTlVUjNXaHdTWDdxOXNvc0x4VU5SM0RTdkVCcmpFb3VtOE92cFdKVURiNUdpT2IrOWNIU0JsbWVwYnlXL2NlNUIwQXVIT1phZXFjL3hHeng0YVljNHd5QjRKVzJFempacGNTVG5BcHhob3M2dFJUeUdnTFlzUUdZcWNmTHB1VjNJM1N2V0pFZWFHVjNFb0Vrek1kaFZjTlU3YUVUNWg2QXZVc3E0MksyVXdSL2pUZkF5Q1llajlRcWlOaFdXZ1dYYWoydjFmL213SUJLUFZFNzBlSWdSMUJ6b0t3VVdNRXVKRU1MaElyamIiLCJtYWMiOiIyMDllMGFlYTIyYjYzODRiNTk0ZGYxYjc0NmI4YjE0M2QxMTNiYzA3NWFiMmU0YzRlMTcwZGI2MWVjODRjMjIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ildqbi91L01hK0VNbG9wdC9tNHFGckE9PSIsInZhbHVlIjoicHliSDVUS01HODQ0VjUybEt1WVQ5MHZjc2R1TUN5M1RKYTZHV0UyOExxakpOaFZ5VW9vY0VpaW9ZbWZqSjNvUE1GUGxESEx3TnZJUkV1a1B1bEJ0T2dOWUFhUVBobDdQaWh3SFYwN2tpMDVmZ0NDd015a1pPa3A5eVBZZHRqS3B1VXJKNWRFNDVqQUxQbkRZaDlWQVA4clYvcXhnY3ZnUzlhazVxVEFjYWtqdzEyK3VoS1ZlcE40SlJ6T1FLbm9TeTFVZnVKVEFIOWJ1UmEvTnIxZmdxYzlpRVFJeXlMQ0J1bngrei84eXByWEFLbFBiS0hnTy8rYjUxeHpHYzluYnJGOXpDT04xQ2hEYVpTZ29QTHdTWUtPNy95bTBETTQxeUZ2bk53QkdpME53d0RCU0hjYTVNNFI5aVRsbnJ5NTU2SHBzZXZXdnpSRWViREVSdEZMZFUza0l5aEc4Nll0U2lKaFBHdC9RdnlSNTdHWEI2WW83Tks3eXQ5TUxpNlZKSjNBamR0WDZlb2FpTUZKbEh4bXMyT2tITFltVWNoOTZZcHluTE5JUExUWGpsbHM2Qk5oZHFSblhiUi96d0I0aXJ6N0hxc3JZZFVBWHlwT0Y1VEpvUmIwZ2M2T2U4NWlqbVoyeVVzUUdQcFlGTnh0ei9tNkcydC8xQ1I4OS92LzgiLCJtYWMiOiJjNDUwMGI1MDdkZTQyZjIxZDNiOGEyNDVmZWIzMzJmY2JkZmVmMDg0YWQ1NTdkYzc5MWFjNGYxNGI4ODEwOGMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570719317\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-411036323 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411036323\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2007571956 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:30:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdOQjNPZUdaVk5kZnFsK3d6c24vWWc9PSIsInZhbHVlIjoiNkxPWURVbGZiSlVLYzVxRVY0dEIwV0NabzhLTWszRGhmTTdCMjl0NG9uc2gzYTlhVDdzWnNCaEtkVFBBM2k3WE90VHEwQ0Zvd2ROcmJRbFdlOUtqSGFQbXphVjhLOUVMd3dHWFFLRmlrdk9ObXVJamp3dDRERkpCNFUyKzU0eDZIZmhaUTF0V2ZVb294SURScC85REFKOTN4aDJYWU9ndmxHV0VPUzQrN2liQ0taVTVoTnNSMDRUdkZSWEE2Y3BYZWpoQU5oOFRTdWdaSC9aTk9TQTJ2QXdkU1ZwY0tVWnIyYlcwTml1aWtDTWJXK3JTK1ZlSHJzdFdwTzlYeU1Genk3dGtqcE51YnhBMi9ZTjNNRCt2WU93d3FvYUdFN3lFbW1MeThWZjdqYUhjUjhZeUM4d0JTeFNwZnVLOWp6Z3JDNTlVZ0lGYjJXb3lWSjljVmxsOVhTNXJ5YnY3dUdudXkyMFV2Z1ZSOVhtNnE2THl6QUcvalRsb2xwZm9ldXVVa3hIdldORkovbDJuTG5JM1lXV1BEZkQrZ2dWT0FHMGpaWnVNSThMeFZyU1BwRkQ4UWlsQThiRCs4RWt1Y3Bja0VsSUlNZEkzczZBZUM1TnhvMWVaZDB4VWpRUjJFL0VyS2dCSUJEeiswMUFRUGhiM1p1MmdTREZLdVgwNm1JeWoiLCJtYWMiOiI5MjE4OWM1OTM2YzY4ZjZhYWMyM2JmMTM3OWVhOTc4NzY2MjE4OWQ3N2IxN2I0ZjU0ZmU0ZGU0Nzc0MDQ2MWI0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkwyWGpKalBJd003QlovNDhaSU5VblE9PSIsInZhbHVlIjoiV2JzMmFSRE1JT2N3a2tvb0I3ajBabzFiOVhoa1FkbmxmUjNjWVp2UFN5UGtLZHRYTzVxUUxEOEVhOXBwNDRCcXRxNkdLNU9rb2JJUTJYajNJQjBxSHpzZS9hYWFiVVdNd1o5UjVnemJQNjlyN2JaSDcra1RxdURxVjRVVWxaRk5LUEJ0bXJGeXJHWnFzWlNVaStzQ3dERWdDY3R3QjY0S2x0Mmc3VzhGcTErTUhYS2hlc3BKV3FKYUphLzdnZmFVQitqQWM0b3FvZGx6YjhWdFJuUFgxZWxRbnMzRWN3MG11cHlZUHc0QTRWcVdoQkNieDc1K0tLYWo4aFBqT1hEdVhPOWllcFl2cm13ZDh3OENjNFA2UFM4T0wwakZUeGRlL1BDcFRLM0M4VytWZEhaNlJLTzVybTBDeFFnd3ViekE4U2p4aEhXSlV5dEpmY0Ryak1VdGgvYVZZakwyNXNpSjZqQkhEQ2hOK1lxd3ljeFRBQ2o4T1N0eERoeXlMT2NmOE4vL2c1RThMK21PYXhNZzA4eUlHb1VPZ1lpaFhGSVp6QWs5eVFqbjBtQlJ6NFN1a3pPM3kzRmdkMkQvN0RLd2dTelpramdKT0hKeEZoSTdJcG1GZ3lBN09MYXg3dlBJcTFud3JtYm1wYnppTkI2ZU5IQ2RUeEozdFV2T2pGTkYiLCJtYWMiOiI0MGZlMzMxMWM2Yzk2OTRjN2Q1MDQ1NTFlNGM1ZTVkYzg5YjFkMjRmODEyNTgzMDYzYWE5OWJlM2I3NjQwMzA1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdOQjNPZUdaVk5kZnFsK3d6c24vWWc9PSIsInZhbHVlIjoiNkxPWURVbGZiSlVLYzVxRVY0dEIwV0NabzhLTWszRGhmTTdCMjl0NG9uc2gzYTlhVDdzWnNCaEtkVFBBM2k3WE90VHEwQ0Zvd2ROcmJRbFdlOUtqSGFQbXphVjhLOUVMd3dHWFFLRmlrdk9ObXVJamp3dDRERkpCNFUyKzU0eDZIZmhaUTF0V2ZVb294SURScC85REFKOTN4aDJYWU9ndmxHV0VPUzQrN2liQ0taVTVoTnNSMDRUdkZSWEE2Y3BYZWpoQU5oOFRTdWdaSC9aTk9TQTJ2QXdkU1ZwY0tVWnIyYlcwTml1aWtDTWJXK3JTK1ZlSHJzdFdwTzlYeU1Genk3dGtqcE51YnhBMi9ZTjNNRCt2WU93d3FvYUdFN3lFbW1MeThWZjdqYUhjUjhZeUM4d0JTeFNwZnVLOWp6Z3JDNTlVZ0lGYjJXb3lWSjljVmxsOVhTNXJ5YnY3dUdudXkyMFV2Z1ZSOVhtNnE2THl6QUcvalRsb2xwZm9ldXVVa3hIdldORkovbDJuTG5JM1lXV1BEZkQrZ2dWT0FHMGpaWnVNSThMeFZyU1BwRkQ4UWlsQThiRCs4RWt1Y3Bja0VsSUlNZEkzczZBZUM1TnhvMWVaZDB4VWpRUjJFL0VyS2dCSUJEeiswMUFRUGhiM1p1MmdTREZLdVgwNm1JeWoiLCJtYWMiOiI5MjE4OWM1OTM2YzY4ZjZhYWMyM2JmMTM3OWVhOTc4NzY2MjE4OWQ3N2IxN2I0ZjU0ZmU0ZGU0Nzc0MDQ2MWI0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkwyWGpKalBJd003QlovNDhaSU5VblE9PSIsInZhbHVlIjoiV2JzMmFSRE1JT2N3a2tvb0I3ajBabzFiOVhoa1FkbmxmUjNjWVp2UFN5UGtLZHRYTzVxUUxEOEVhOXBwNDRCcXRxNkdLNU9rb2JJUTJYajNJQjBxSHpzZS9hYWFiVVdNd1o5UjVnemJQNjlyN2JaSDcra1RxdURxVjRVVWxaRk5LUEJ0bXJGeXJHWnFzWlNVaStzQ3dERWdDY3R3QjY0S2x0Mmc3VzhGcTErTUhYS2hlc3BKV3FKYUphLzdnZmFVQitqQWM0b3FvZGx6YjhWdFJuUFgxZWxRbnMzRWN3MG11cHlZUHc0QTRWcVdoQkNieDc1K0tLYWo4aFBqT1hEdVhPOWllcFl2cm13ZDh3OENjNFA2UFM4T0wwakZUeGRlL1BDcFRLM0M4VytWZEhaNlJLTzVybTBDeFFnd3ViekE4U2p4aEhXSlV5dEpmY0Ryak1VdGgvYVZZakwyNXNpSjZqQkhEQ2hOK1lxd3ljeFRBQ2o4T1N0eERoeXlMT2NmOE4vL2c1RThMK21PYXhNZzA4eUlHb1VPZ1lpaFhGSVp6QWs5eVFqbjBtQlJ6NFN1a3pPM3kzRmdkMkQvN0RLd2dTelpramdKT0hKeEZoSTdJcG1GZ3lBN09MYXg3dlBJcTFud3JtYm1wYnppTkI2ZU5IQ2RUeEozdFV2T2pGTkYiLCJtYWMiOiI0MGZlMzMxMWM2Yzk2OTRjN2Q1MDQ1NTFlNGM1ZTVkYzg5YjFkMjRmODEyNTgzMDYzYWE5OWJlM2I3NjQwMzA1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007571956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-735541168 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735541168\", {\"maxDepth\":0})</script>\n"}}