{"__meta": {"id": "Xede27af9232ecdf8c9cff39f7208a822", "datetime": "2025-07-14 18:29:23", "utime": 1752517763.014375, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.538652, "end": 1752517763.014396, "duration": 0.47574400901794434, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.538652, "relative_start": 0, "end": **********.916741, "relative_end": **********.916741, "duration": 0.37808895111083984, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.91675, "relative_start": 0.3780980110168457, "end": 1752517763.014398, "relative_end": 2.1457672119140625e-06, "duration": 0.09764814376831055, "duration_str": "97.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02835, "accumulated_duration_str": "28.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.945561, "duration": 0.02709, "duration_str": "27.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.556}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.988939, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.556, "width_percent": 2.293}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1752517763.00085, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.848, "width_percent": 2.152}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer/eyJpdiI6IlBEbGFiandWS3dEQStEVDdPaWhrd2c9PSIsInZhbHVlIjoiN083MjlYOXdDOHJlQWpqYnpZcVlxZz09IiwibWFjIjoiM2U5OTFmMWNkMWZjMTRjOGY5MDUzZTQwZGU5ZmFjMWVmM2U0ZGM5NDI5Y2JhMzAxYjIyOGQyN2E2YjQzNjFmOCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1763863099 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1763863099\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1452246475 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1452246475\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1163065066 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163065066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1459555754 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6IlBEbGFiandWS3dEQStEVDdPaWhrd2c9PSIsInZhbHVlIjoiN083MjlYOXdDOHJlQWpqYnpZcVlxZz09IiwibWFjIjoiM2U5OTFmMWNkMWZjMTRjOGY5MDUzZTQwZGU5ZmFjMWVmM2U0ZGM5NDI5Y2JhMzAxYjIyOGQyN2E2YjQzNjFmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517756092%7C32%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlQrVjlUY0p4YzJJMSs3RndXZWswQ2c9PSIsInZhbHVlIjoiOXZkdVdLeGpkMFYyN2tBTjlUa0ZBeWhQMTQwaFlWVTJ3ZGxOajI4L0JjbnZjY05xK0Y0Njc2cVVRUlBEOWE2M1FjMlZLY2ZvTGNLZ3ZzWTI5eWtyMHBnMXlRT2RHeXJUbXRmYjlvSm5hRXlvSkxSNy9HMzl5cm5jdVd4cHlwVytRbThCWE9veEhoODlFR1FIUkdLZmg4RlExVUJtaU1oekVuMmgydGp3U0NqbjdQT2lheU1mOEZhNUpDZHdnWlI0ZGtKSklpMmVodDhpZnVyRk1WUFdnTE5DU2paVi85VW0rRWJ3dElXbzlST3N6czc2Snc1QkRKdjczaHpRRHRGQWhncE1aVkJxM0w4TS82R0FwZ25xeVpUU205UU5oOXA3MmF6b3VuU01yVWEyNlhBQjZrWVZ1bXE1R3JJa1JEbm1HL2RLdDVBdGdkMEtwOC9teUh0RmpvM3cxYjVYNmM4MnJYVlQ2MGdLamt3Y2FldWZIUHVncmJGWU5XMDgxUCtodkV4c1hkRjY4VytZY1FuZTl3eVo1ZHBqMVN0czdTYWJzcGlhT1VaUGdaRVVGNXYzRHpEdFJqL0FLZTBsbGVteFlNbHY5Uml3cm13OCtrbWZJV2lCenVlbTZTOTdhUTA2b3QyRjBCOVVYUVZJTW9CUW9CVlRSdHh0cVczaGU4SEciLCJtYWMiOiI5ODBiMDIwY2M2NWYzNTFhNjE1YTgyNWNlNzU3ZjE3YWI3NDUzOWJiZjBmNzkzYWFjNDZmN2U5ZTMwMzNlNWFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imh1Tzd5SVJjV1NlcGJpSWhQTlBoMlE9PSIsInZhbHVlIjoidXcrMFVmY2RJcm9XbTZRaUhhVVl1cUlnQmZlTHNKM09GK0pvS3BVZVdDWkFZS3hsOXFVTjlQZVJxTjNubGgrWkFCR2F2U2tWdnlIS2hkRlBzY0dNclJ6WENYbGVYUG01Y2JtUTA1bkhaYU52c3puU0l1YVU0aDZNYlljWEV3VmRVcDNJVEppdmZvT3duakcrbU02WUZiazFjWWNTN0UwTi9BSVRiNW1rWlFmd2J2eGdzbWs5eTdZTWFFN0YxRlFHT0JIcWQ1aSs0aU1iS1VpUGFwZ0VDbitUbExZVnAweEJRWXM5ZmJUYjRKbEIwZndsb29zYUtEVi9FWFozOG5VTzd6LzZvOHRiSUhteEZrNVF1WmZ1SzVaYzI4cUVDVHd0TWdtelppRExkSEtuYnIydVczQXBRWXhvWUlRSDNCQmh1NTlQbGs4dHRzbjVMSm9kWW1jamc4dGd1WTdna1V5Sk81TWQ1SjJKeU5xMWw1NWZFWFpUQ2JkWkJHTXRaaDVJYjBzVU1PMS9YZkRyZTgvVkhoNTNScE9YUnlMZEVpRm5SWDdIUjhTeWVQaDI1NkNGMW03c2VUVlNDeVRTN0RWby9YQ0k0RnQyb2d6MVFvcTk2WlJyNTJoaSt0RnR5bjFXOHVaZy8rY21temJkUk5BN016V082M3B3VDZPM2Y1WlYiLCJtYWMiOiJkZjRmMWE0MmY2ZTVjNmE0NGY0ZWExNmVjZDYxYWY4ZWRlMDBmNjVjN2M2MWM4Nzc1NjY2ZWU5NDllODdlNmYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459555754\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-989923547 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989923547\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-931365556 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJRVFk0djludGFXQ2VDWlliYVBtcFE9PSIsInZhbHVlIjoianJSL3JTNGo0Q0tEUUthZWt0QmhFdWsrUDN5YzBPSUJEU3h1VmRmc2lFSjlscmNEeXRlSHVsSHVsY1Q1RzRVdGtoYklhV3Q2akVRUTdQblUwSGlKclpSVzVXaUREREFJZ3Q1TGcxYzExdUZJaVFvMSt3cm05dGNDKzVRRkhtd25WcHM5dUdYakdKMEloZzNWMGl1anZ6emc3SWFTT2x3a3hmOUZpaFFPbEgvSStPWlh1NnA3MzZxVFN1eU9ES2ZVSUl3dmdXUHVUdzJ0Rk1sYzBuY29pTkZHN2J6SlVVcGczUVE1a2t4MTB2akQ0QUF5QUU2RDFsY1RaZjFicVkrREpRdWlibi80emgrZzV4L3JTMFFmWWpGVnNyMmJjdnN5MFRaZ3o5cVlIbzloQk0zdUVjNWJuTyttTzlKWHBIMmVZT3I2M2M2b1hyNSt0L2tQRU4rUjZCZW1YeUs2cXFwcktTNFF3c3hLczkyclgrTWtGSzlDV2Z6YmthZWVKckVKdHBTRWNxTkZRbkU5M3ZOVVFZcXRSckkwZmRhbmFraGt4ZUM3WHoycVVRazlIMnkzT3RSUkRSVUZUak9ZczUycUx0VTdWOGZ3SWk0MmQ2cllhbnBEd1g3b0p0MVQ3RitFZ25wWlpQS0JaNjc1ZnVmT2FqQWdLenZQWDdyQ3hDcy8iLCJtYWMiOiJkOTIyN2I1ZGYwMGRjMDcxZTQ1Y2RiNDA5YzE0NGU5MDg5NTkwZjhkOGU5MjAxZTQ2MTQxYzRjYjVlMzg4ZDJmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1DdjB0WjdDYWh1Sy9lbSt6cVFaZVE9PSIsInZhbHVlIjoiRjFNb2k2eVAweVk0ZU93S2dpdWFxSmtmTlNwWVMrdmlUSDNDKzg3dWZ0MXRjUTFPN1Q1UmlESVR5QkhpL0NQbzAwWE8veWhMUFZsaC9sTjR3cVE4b0NWcmdDbnNCTk9hREh3c0UwMFozeWthUm8yQTF4Sm9DTU5oK2VLcTBQQXhxL3RhWU1DK1BiOVkvSVR0bVF5MmZTdG1CNU1rYVNRcytWMlc5TWVza1ZrZjNTODQ1MVNURmtJSVhsZm5KOU1seEkyVlkzbC90K3lsV3lYNmRsOGVqVVBvakxiUXpleXE2WVVBeGNyaUUzcXNhL0JRWll2UkE1ZHlNYjJlOWs1VmVZdjZmT1o2d041SnpORWRSMStwSVFabitidGhBeThISWNpQXdMa0VrdFVreGtpdGQ4R3crdFBpMm4yTWZmb2dKUVBuZnB3eUZUVU1ZUE9wam5BSXdQQ0hrMlBQZVY4aGJIMGpENVlNUjdLUEtwSElPVHh5RnZ4NHJnd0NiZklUdFNqUkZPTHRZY0N3Ky9GcWZUOTNlcjdWZHlLaDE0MitjazNKV0NBU3hpSUtsaEFIU0QzNERqVjZiN1FNdEFHRzIyYS9jemdlM1BTZlRSZkppQ3llZ2M1YlBZSWdKVStCdzFJd0NwZVpmckhoLzN1dS9UaE9JT1FXVXJrcEMwbEMiLCJtYWMiOiJhMTllYmQxNWIxZWQwZTBmNmJhMTNhNzlkNjQwNzhhNDI0ODE3YWQxYTQ3ZWY5MWI4YTM5YTEyOTU1N2JhMmJmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJRVFk0djludGFXQ2VDWlliYVBtcFE9PSIsInZhbHVlIjoianJSL3JTNGo0Q0tEUUthZWt0QmhFdWsrUDN5YzBPSUJEU3h1VmRmc2lFSjlscmNEeXRlSHVsSHVsY1Q1RzRVdGtoYklhV3Q2akVRUTdQblUwSGlKclpSVzVXaUREREFJZ3Q1TGcxYzExdUZJaVFvMSt3cm05dGNDKzVRRkhtd25WcHM5dUdYakdKMEloZzNWMGl1anZ6emc3SWFTT2x3a3hmOUZpaFFPbEgvSStPWlh1NnA3MzZxVFN1eU9ES2ZVSUl3dmdXUHVUdzJ0Rk1sYzBuY29pTkZHN2J6SlVVcGczUVE1a2t4MTB2akQ0QUF5QUU2RDFsY1RaZjFicVkrREpRdWlibi80emgrZzV4L3JTMFFmWWpGVnNyMmJjdnN5MFRaZ3o5cVlIbzloQk0zdUVjNWJuTyttTzlKWHBIMmVZT3I2M2M2b1hyNSt0L2tQRU4rUjZCZW1YeUs2cXFwcktTNFF3c3hLczkyclgrTWtGSzlDV2Z6YmthZWVKckVKdHBTRWNxTkZRbkU5M3ZOVVFZcXRSckkwZmRhbmFraGt4ZUM3WHoycVVRazlIMnkzT3RSUkRSVUZUak9ZczUycUx0VTdWOGZ3SWk0MmQ2cllhbnBEd1g3b0p0MVQ3RitFZ25wWlpQS0JaNjc1ZnVmT2FqQWdLenZQWDdyQ3hDcy8iLCJtYWMiOiJkOTIyN2I1ZGYwMGRjMDcxZTQ1Y2RiNDA5YzE0NGU5MDg5NTkwZjhkOGU5MjAxZTQ2MTQxYzRjYjVlMzg4ZDJmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1DdjB0WjdDYWh1Sy9lbSt6cVFaZVE9PSIsInZhbHVlIjoiRjFNb2k2eVAweVk0ZU93S2dpdWFxSmtmTlNwWVMrdmlUSDNDKzg3dWZ0MXRjUTFPN1Q1UmlESVR5QkhpL0NQbzAwWE8veWhMUFZsaC9sTjR3cVE4b0NWcmdDbnNCTk9hREh3c0UwMFozeWthUm8yQTF4Sm9DTU5oK2VLcTBQQXhxL3RhWU1DK1BiOVkvSVR0bVF5MmZTdG1CNU1rYVNRcytWMlc5TWVza1ZrZjNTODQ1MVNURmtJSVhsZm5KOU1seEkyVlkzbC90K3lsV3lYNmRsOGVqVVBvakxiUXpleXE2WVVBeGNyaUUzcXNhL0JRWll2UkE1ZHlNYjJlOWs1VmVZdjZmT1o2d041SnpORWRSMStwSVFabitidGhBeThISWNpQXdMa0VrdFVreGtpdGQ4R3crdFBpMm4yTWZmb2dKUVBuZnB3eUZUVU1ZUE9wam5BSXdQQ0hrMlBQZVY4aGJIMGpENVlNUjdLUEtwSElPVHh5RnZ4NHJnd0NiZklUdFNqUkZPTHRZY0N3Ky9GcWZUOTNlcjdWZHlLaDE0MitjazNKV0NBU3hpSUtsaEFIU0QzNERqVjZiN1FNdEFHRzIyYS9jemdlM1BTZlRSZkppQ3llZ2M1YlBZSWdKVStCdzFJd0NwZVpmckhoLzN1dS9UaE9JT1FXVXJrcEMwbEMiLCJtYWMiOiJhMTllYmQxNWIxZWQwZTBmNmJhMTNhNzlkNjQwNzhhNDI0ODE3YWQxYTQ3ZWY5MWI4YTM5YTEyOTU1N2JhMmJmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931365556\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-155775268 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6IlBEbGFiandWS3dEQStEVDdPaWhrd2c9PSIsInZhbHVlIjoiN083MjlYOXdDOHJlQWpqYnpZcVlxZz09IiwibWFjIjoiM2U5OTFmMWNkMWZjMTRjOGY5MDUzZTQwZGU5ZmFjMWVmM2U0ZGM5NDI5Y2JhMzAxYjIyOGQyN2E2YjQzNjFmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155775268\", {\"maxDepth\":0})</script>\n"}}