{"__meta": {"id": "X03f7e9140aef4bb566bc2a9a56b57b96", "datetime": "2025-07-14 14:39:47", "utime": **********.48347, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.026847, "end": **********.483483, "duration": 0.4566361904144287, "duration_str": "457ms", "measures": [{"label": "Booting", "start": **********.026847, "relative_start": 0, "end": **********.413667, "relative_end": **********.413667, "duration": 0.38682007789611816, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.413676, "relative_start": 0.386829137802124, "end": **********.483484, "relative_end": 9.5367431640625e-07, "duration": 0.0698080062866211, "duration_str": "69.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46003456, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01895, "accumulated_duration_str": "18.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.442927, "duration": 0.01828, "duration_str": "18.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.464}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.471368, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.464, "width_percent": 1.583}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.476847, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.047, "width_percent": 1.953}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1234090792 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1234090792\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-326033622 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-326033622\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1492765274 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492765274\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-494327134 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503986198%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdOUG1iOUtzVnEyenF6U3JDRWx2QUE9PSIsInZhbHVlIjoiamwzSDB4SEdQWEh6Y0FpTjRvYnVpM0paNit0YVd5NnlNbWtnMnFCOW5xbEl2T2VZR1VySUoydGordUlXSGZtdTZYWW5yRGoyeHFwaHkvQ2ZneDFaeVFBaFRkY0Y4VS9yd3lMZ29iUE9tSHNvQS9RN2dMaDVuN3BtSlZTY1l3NXVCYTBxYWRFMmcxYVI3a0NGNWhENk9ybVVUOUdxd20vZjc1ZmdnZEJqR3RlUU1wZWFuaWtWZWp6aGlKTmhNYUxodklVam5ueEk0SS9WalZsUTNxS2tjVGZvQ0luR2FqL0JtMkxTMC9xUVZXMDlOcFBEaVlLT2YrVThNbFNQd1hESTRtSTQ4UXordUVwdnpsd1VuWFRlT3kxVlRtcStWOFFKWm1xWk01OThMSzJtQ1BkNWNFbUF4b1BGT3FpU01NcDNtd1BZUzdMcGhRaWRxYzRNSE9zWFpKZGFOdjI0MkxKR1pCYW5JL2xwR0NXd3BWMDBsc1doaXB3TWV3YWh6eXJON25Ua2x5THZOeEszdWZFK0ovUUNudGNWMk5rUTlOelZDcGRqbzVyWjlqZCtvVWhERzJ0VXlhcjVMOStQRVpZMFVBWGFOenQ0Z1hpbTIwYzltblZIUU5mYytZOU11d0srUC9tckMySnRpbTlqUDhIaFRCZzJreFZMVkRyam9wbHciLCJtYWMiOiI0MmNmMTQxNDFmMzllNWU5YTA1OTVlNzlhNGFlZGJlMjA5YmIyNjMxZTI1OTU4ODYyMDkyNGFlMzNiZmFiY2VjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZBMWEwcGVkdHNYbWFuOHJQbTZlVGc9PSIsInZhbHVlIjoiVThaeTB0N0gzc1kwNzVkK2N4WjdDN0FNbitMSGpKaVRkMysrNmJ2bG9pS2RLZHBVb1hHUGpuTithdUpMNFlNV2RuQXM1bHpyRHBvZERkSFlFOUxmSDQ4RGNTWUdyN0tsei9XZU4wRW94NGw1eHpRalo4TGxRcWh4akR0d0hnOVhMZzJWcFdQQjBDWGxGdmJlUXhMcmNNV2NFUnAvWW5kTU5kT282YWNCc3VkQWhMSkJJd0tXazl0N0Rad2U0NnA5UUFRRk1nOFlYME5uWDVZeENhcDVpcUduaWZSVmt0ZVpoUDVTd0RHeWdMb0JUVURud3lzM1VFYytFOTViZk9OUmpSYUdEWHRBems1UFB4ZXJIMFVXTnZpOTZISENhNEhDSjQ2RGZiS0hkcHQyM2xIejdNNzI3SFRXSTc0RTdzWTlseEVINUMyWVVpMmlydkUwQnVYbmo0UGZXVlVoNVRWcllieEZlMytvOXlxUHhMSHh6VFdCUEJTSi9KMXFlRjZvVG55MDJNNG5lLzB1elpwMm5rQ3Rpb0QxMk5ua2tSRTBuRW9rN2lhT05TZWlldWJYS3V1ME56N29lVVZMdFFPVXBMQnBjRkFzQkVldU1xcjNCZWozL1hyRnJXL2FlS0h0YnAxZ0IyQkNPMlUyRS90U3hTYmZnYWJMRG5Ic3ExZ3EiLCJtYWMiOiI2OTkxZmFkNzE1ZDczOTE3MGVlYjBmMDNlNjI1YTkzYTgxYjIxZjcyMTlmOTllOGIzZGU0ZjE2OTFiZWNiODdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494327134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-309868438 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309868438\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1238870375 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:39:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5kcjVZd0tCT0c4SHU0c2s5RW1SRXc9PSIsInZhbHVlIjoiQ3F4VFBTb0w0UUZzOGpRbVlHamduYXZLUXdIYXE4dWRtaGVnVmEyZ0hBVzBBZXMvc2s2Y1hVejdhYzVIZ0dkVkR2cnFoVVQrUk84YXBIM1c1Qng2d015bWtSN3dXTFlad3B4T1lEZnZBTnpNWVhicXRIM2FCTTM2ZVA3N1c1d3VRM0tKYjVhUm1mdm9oYUNmL2N3KzFrU1JlZ1Z6UytSWCsydUMvVTZ2WmZMelQxZGxOeXZkUG11VGRZNEtOK0FHK3h1VUo5MXg1a1VVbklkREVTc1h3a2FiY09zVUVEZUNrdzY0MU0rWGJsenZvM0EvdDR3VUpMWEYySGxNSXhoeE50aUFyMnkrQitrZ1JXcjhpWHhGei8rZ3lOZzN3QVhibE5LdDlWcTUxbzB6Z1pPRkdIWlJzWGlWNW5PMS9Va2FuQklKNUZDbGVzLzVjUmhYQlM3bklIVjdLcFV3Umh4UGN4d2lHSlEreFNUVXBXci9acHc3cU8zQlZVcVdmcjVvKy9hV1N4Wlp3ZjZMWXEvYzhZWmJwVE9GMXpSMnArN1BUekV5TEdrcGZ1YnRXQ1Z0VWtndDl2aWFaZGpZUERXZnErUERLTHZVWjdsUkxqdkRFaE9zTlJQa2tnRHFPOHlUOVlzS3RySGhBSWNTRU9nUXlLUmgvOFU4emJoM3VLejgiLCJtYWMiOiIzOTQwZjExNDUzNDdjZDY0MTcwZjIxZjdjMmFkMDJmMzUxZTdmN2FjMjcyZWYxMDU0YmE1MjQ2ZTE4OTZlNTRhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNiT2thazF0cENSV0piWjdlczFrWEE9PSIsInZhbHVlIjoibXRwUFhTcmxNNGxCUkVzNlFpZ2Z2OTg5aVNZRTV5a3Z6Z3h4S0I3bkhhTGkveW1JWWVoTWxEQStsMkVJOFY1U2lRQXU2TUJSVTlSSVYvL1FZTisxUXNPSnY3VGthNXlSRGZodjF6bWVNbXkvTk1yOHZxeG42bk15Y3JRWkhGbksxdFFWaUw1Q2NpWlFnMmJJcWNmSnNGMlF0OFozdi9KNlBHOHlBK2xsd1d1eWVJcjd2cnk0MUFCSDBoL0xqT1c5UjVMbUhoWDJGcXFTRkFyZ0ZLV2YzS1pHSVhrNU9JdVVEUDdmNFRpU0N4QWFmSXpuMUlsanVVUnNPVG1BWnpHNHNsZkVNQXgxZWNIY1REWDVzTVljOWxPcVVTS0RUS3krK2ErMVJ0QjdwUHlycDdLaVFRMVVycDdGcXZQU1hScHB1UFlNTVR1cXE0ZTZBV2dRd05saUU1UHVQcjFFOEVvV1BQV1MyTDIxUDFobll5Q0M5bklzWVdPK2Q4clFKYVNBU05jK2czRWJPcXhQMCtCcFd0ZzZOSHpqWlJPNVJwKzFQWEVQOFphWnZSLzVvdnZVWStwNHdqc3Y3WkwvanZyNldRRFAzZmQxNHlGenZFMERsMU1hSkxsTlNVTzJwTG00SW1ibFQ4UWJxTTB4ZlU4OUJ6YWY5c2ZudXpWNUZRYmciLCJtYWMiOiIzMzEyOWM2YTkwZjc4NTAxYTI2MDc5NDAzNWIyOGQxMjg2N2JhZjdjNTJkYjI1ZTdlYjYyY2EyNDYyZTYzYmJlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5kcjVZd0tCT0c4SHU0c2s5RW1SRXc9PSIsInZhbHVlIjoiQ3F4VFBTb0w0UUZzOGpRbVlHamduYXZLUXdIYXE4dWRtaGVnVmEyZ0hBVzBBZXMvc2s2Y1hVejdhYzVIZ0dkVkR2cnFoVVQrUk84YXBIM1c1Qng2d015bWtSN3dXTFlad3B4T1lEZnZBTnpNWVhicXRIM2FCTTM2ZVA3N1c1d3VRM0tKYjVhUm1mdm9oYUNmL2N3KzFrU1JlZ1Z6UytSWCsydUMvVTZ2WmZMelQxZGxOeXZkUG11VGRZNEtOK0FHK3h1VUo5MXg1a1VVbklkREVTc1h3a2FiY09zVUVEZUNrdzY0MU0rWGJsenZvM0EvdDR3VUpMWEYySGxNSXhoeE50aUFyMnkrQitrZ1JXcjhpWHhGei8rZ3lOZzN3QVhibE5LdDlWcTUxbzB6Z1pPRkdIWlJzWGlWNW5PMS9Va2FuQklKNUZDbGVzLzVjUmhYQlM3bklIVjdLcFV3Umh4UGN4d2lHSlEreFNUVXBXci9acHc3cU8zQlZVcVdmcjVvKy9hV1N4Wlp3ZjZMWXEvYzhZWmJwVE9GMXpSMnArN1BUekV5TEdrcGZ1YnRXQ1Z0VWtndDl2aWFaZGpZUERXZnErUERLTHZVWjdsUkxqdkRFaE9zTlJQa2tnRHFPOHlUOVlzS3RySGhBSWNTRU9nUXlLUmgvOFU4emJoM3VLejgiLCJtYWMiOiIzOTQwZjExNDUzNDdjZDY0MTcwZjIxZjdjMmFkMDJmMzUxZTdmN2FjMjcyZWYxMDU0YmE1MjQ2ZTE4OTZlNTRhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNiT2thazF0cENSV0piWjdlczFrWEE9PSIsInZhbHVlIjoibXRwUFhTcmxNNGxCUkVzNlFpZ2Z2OTg5aVNZRTV5a3Z6Z3h4S0I3bkhhTGkveW1JWWVoTWxEQStsMkVJOFY1U2lRQXU2TUJSVTlSSVYvL1FZTisxUXNPSnY3VGthNXlSRGZodjF6bWVNbXkvTk1yOHZxeG42bk15Y3JRWkhGbksxdFFWaUw1Q2NpWlFnMmJJcWNmSnNGMlF0OFozdi9KNlBHOHlBK2xsd1d1eWVJcjd2cnk0MUFCSDBoL0xqT1c5UjVMbUhoWDJGcXFTRkFyZ0ZLV2YzS1pHSVhrNU9JdVVEUDdmNFRpU0N4QWFmSXpuMUlsanVVUnNPVG1BWnpHNHNsZkVNQXgxZWNIY1REWDVzTVljOWxPcVVTS0RUS3krK2ErMVJ0QjdwUHlycDdLaVFRMVVycDdGcXZQU1hScHB1UFlNTVR1cXE0ZTZBV2dRd05saUU1UHVQcjFFOEVvV1BQV1MyTDIxUDFobll5Q0M5bklzWVdPK2Q4clFKYVNBU05jK2czRWJPcXhQMCtCcFd0ZzZOSHpqWlJPNVJwKzFQWEVQOFphWnZSLzVvdnZVWStwNHdqc3Y3WkwvanZyNldRRFAzZmQxNHlGenZFMERsMU1hSkxsTlNVTzJwTG00SW1ibFQ4UWJxTTB4ZlU4OUJ6YWY5c2ZudXpWNUZRYmciLCJtYWMiOiIzMzEyOWM2YTkwZjc4NTAxYTI2MDc5NDAzNWIyOGQxMjg2N2JhZjdjNTJkYjI1ZTdlYjYyY2EyNDYyZTYzYmJlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238870375\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-38016009 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38016009\", {\"maxDepth\":0})</script>\n"}}