{"__meta": {"id": "X279e865769e32d827b6990f221758a4b", "datetime": "2025-07-21 01:34:16", "utime": **********.0688, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061655.645769, "end": **********.068815, "duration": 0.4230461120605469, "duration_str": "423ms", "measures": [{"label": "Booting", "start": 1753061655.645769, "relative_start": 0, "end": **********.015842, "relative_end": **********.015842, "duration": 0.3700730800628662, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.015852, "relative_start": 0.3700830936431885, "end": **********.068817, "relative_end": 1.9073486328125e-06, "duration": 0.05296492576599121, "duration_str": "52.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46008224, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025700000000000002, "accumulated_duration_str": "2.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.045018, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.482}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0551732, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.482, "width_percent": 17.51}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.061046, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.992, "width_percent": 14.008}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=8&date_from=2025-07-21&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1769478244 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1769478244\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-97149243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-97149243\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-7808695 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7808695\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-985158080 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=&amp;customer_id=8&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061646451%7C10%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhJQmJDQzNkT1d5N2FxYUVCU2NKVEE9PSIsInZhbHVlIjoiR0x1RWpLUlE4cUZtSlBkSi92Um1oY1JSdldMVTFEbkJQUzJwZmFQVWVzVGpEV3lTMmsrVEFsRnpDWm1vbzk3NXVRcGo5cUVnQUE5YmNSdDMrdFEzQXJXR0NGa3hTRHNPblY0MFJaR3FlTDllNnBhZldKWC9Ic2dkUzkvQ1lLa3BEcnJrRlk0alpDV01LZEF1eU04VTV4Wm5Fd1FkSnIxT3NpQ0daOUp4Ly9UV3E1REVFdE9wSXRFL0lIZmRPU2lNc3VJZmR6Z05SOWZZNWRQdUcrVEUwMURiUEFpUGkwQUQ4U3d6bTBQdDZocmFrM1l0TWRVNGJDeDVMT0Y0bXI2bWFtWVo0NCswcnlETU16V25ReDgvb0xLY2lPU1UwT1FhK2dKeDJiWGpiT3IzK0hGdVVyZldtZXowdkJBcDg4UEFLR1NzMWdHdmpxOTBXM01BWWNlWTlteS84bUk0dGVYU0lORGlhV0FYK2VINGpNY0MvczNzdGR0SjRXUE1LTkpJL0ttclZoRGdIL0puRzlqM3RxemhDTlpGN0lhb2o3UU5jN1U5OFJSKzZEWVVoTTJiNVprd3dPUkEwaFE3eGFXdG53aFdjUDZxL1preVNDMmthMmVHQSt1T3kybTMrU2FzWUs5ejhhNWJCRzl2OS9CQjFtK3hDS1laNUJwZWJqQjgiLCJtYWMiOiI4MWJkMWU2M2U5MWFlZjk3MzllMDZkZjg0NzYyMzA5MDkyYTA0YTFlMGZlMGNlM2Y0YzdmMTNiOWUzYjc2ODdiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRxTmdoaGVCWHNubmxianlDK21PTEE9PSIsInZhbHVlIjoiTzI4ajdhQk05TXZ6cHF5Z2VUTFRwMENkUHRUVG9SZllaZ1FybUdIV21aM1ZvWnRVQ1ZQQ29IZklCQmc5RjZNc0YvYzZJQXU2eFBhdXppbWhSK1N1WUJzOFNETm5zeUJWMDQvWXFmSk5lRDQ5ZmQ3bTFlQXZPeWFVa21JZC9qTm14bnRyM1hBeWJMV05PYmpsV3BCbWpNT1haZlVaOWdzY0RYbkcwQ3Q1MnJ0aG5vbjU0Sk80cDlrVmlSUmpSTXdubG1ZQnBLS0RQeFNrQVJ4a25nYUthUUl5bkN5Q0xVaHMrMzZLM0o4bWZMc0RIVFJrV3hQWUFoaFNRZXdacDg5UkZzbUFlVUYxbW81MGtFVXFEMzkyVDd3UklZMmhzdmJSbzJucThFV21LSmI5OGNJcGFPaFg3ekJFaW91OEFyWFBNUUlrUFdGM3Fvbk80UHdvTUMxblJrVVN3UGo3VG5XWlorNXNZVDVnY1ljTWRpcTROaHdSWGlETWJZMVAxRzR1QXJ4UjVlbm95bEFSVVJGQ1pJejJ1N1loVzIzMk1QcFQrUE12MHdnaGlWZGZPUVFYczZwQVRBVWFGK2kzN3h5eFVyNUFVTk1HOEVmR2dvbUlsOHQxYWp0dk55K2JiSzFta2tXZFJZWUg2UHQ0S25odzJHZHNWSEEzQWxVekFFN2IiLCJtYWMiOiI3N2I5M2QyZjg1YTg5ZGY1YmU2NGIxNTNmMzFkYmQzZjE0OGM3NjM2MGUwYzg5OGVhZDBjMzhlYWUxMmQxZTMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985158080\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-908727188 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908727188\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1485834049 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRtUWdldGdCNkdPTldXalNaVENKaXc9PSIsInZhbHVlIjoiWER3TWFIYXVXUHA5MVdXTFVTQnp3N1BPU3VYSFNCdk0vamJ0bWc1ZjlaUVZ3cytRS0FlcmcrTUVDYkcwUUJlSFRxMjlnRTE3cWhLU2ZLb2YwbEQyWGxsUEt3c0FKa3V3TDdkci9zZVJoRFR1Sm1PbnhVdGRPZXliemw1cXhoMStnWjF2Nk5NUGlFTGNPYWo0bzgwd3R2SUFoQ3dNZU1Rdi82M1E2Y3dpYW5NK01ScFdJcGx1RGU1RFZ4UmIxOVBrb2ZYc2RHTHRGdXVNblV3R25sOHcrbG02c2FLZG5COG1lMzhTWCtEcWJmS0lDd1dTUXppQWZWZmRhQ1R5V1RjZ1BVWXhvMlhrSnlrMHNhWENGSUEvVmlDUmNjM0hrZnJkSytKclhKLzhpSWZYVlVEM0NrK2RLaXVXMW9RV1c0d1cwT05JcDh5TW1yMHhDdWJpcDF4YnpmejZkS0E0MllmNW52MGIvVzNDRTdGMS9OaDRmbHhkcEZTMElJcmxyczQ4UEpxdFVzUklkU1Z5MGRDRkNZR1lsY1FNTFBDTnQ4V1d1UG15VlhuL1djOEZZUFZObE9LUGJicGIzd3IrV0RnQ3RhWFlwVWhXSFl6S1Nib0VvQ05RTWs3bXp1WXJtNldaU0Nva3NWRlNhbEQzdGNGU3F4SllkbzF4dmJDZGJxckEiLCJtYWMiOiIyM2NmMTEwNDgyZWQ5YzljN2QyYWMyYzg3NTZjZDVmZWM5MjU3ZDdhYTY3NTllYzBjMjc2YmJiNDQyMjQ2MmQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBuYlkzR096Y0RpMEJXZnp6T2VIWXc9PSIsInZhbHVlIjoiNHRwUkMzRmpIeW9LT29uT3RKU1JZcUYyb1JVdmY2ZnBkbWlaQjdwU3B4SE5NT3owTmtYbDI5SjFCTjZyRXFTYW9UYzZ2K0RVbUZNYWZQblV6Y042djIvanF0OGJCUFI1RzZrTUVIOG1lTjVWa2RHbW5jQmE2Q3RTM2FnNjJSK2laQjNiZnZOV2t1NmplUDRXcmNTdmwwemFQMVBsN25PcE1FczVQOHhHSkpiUjZ6TEhjSkZ3QXY1ejZvMXZHTkRBbjZjdnY2ZzdJTHZIRDJHT0REVjhRa09BcXJFczRxRTRwSExacjJlaFYxa3d0KzM2SDRsVDhTZUd2dEgvL2xpbTFycW5UYmVkSlZBOGlnc2VwNXRjODU3TFRPY1RDeC9BaVVkNDllNFAybGhDY0VSRHlFYzFsMEd4M29GOXNtYlUwbXBNbWtML0RJOVZVQVlmRzZHdzVUOWVsckdVN1ZmRTZyME0rTVJHTFlOcUNlclNCOVgrYUpsYnJhcmZNR2hxSjNxblp6TE5Nc0NoSmxKUFlyZWZ0YzhFbVR2RFpkWnUxdlY4OW1CVDVqMUZjdTdncDNnQ3RPTzNXTm12Z2ppNDdqcEJPOUlyYkRCVWxSejJaSkRHdHl6ZXpmR1kwUHRSdWovZGxMQjh1NVc5NXFNTmVLSEZXWitZVDJIaFBzdS8iLCJtYWMiOiI4YWNjZjRkZWFkMGMwYmMxYjFkMTlmZmE1ZTRmMGRkOWU0ODA0ZmFkNjZhZmM2M2VkOGNhNTIwZDg4YTdhMWFiIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRtUWdldGdCNkdPTldXalNaVENKaXc9PSIsInZhbHVlIjoiWER3TWFIYXVXUHA5MVdXTFVTQnp3N1BPU3VYSFNCdk0vamJ0bWc1ZjlaUVZ3cytRS0FlcmcrTUVDYkcwUUJlSFRxMjlnRTE3cWhLU2ZLb2YwbEQyWGxsUEt3c0FKa3V3TDdkci9zZVJoRFR1Sm1PbnhVdGRPZXliemw1cXhoMStnWjF2Nk5NUGlFTGNPYWo0bzgwd3R2SUFoQ3dNZU1Rdi82M1E2Y3dpYW5NK01ScFdJcGx1RGU1RFZ4UmIxOVBrb2ZYc2RHTHRGdXVNblV3R25sOHcrbG02c2FLZG5COG1lMzhTWCtEcWJmS0lDd1dTUXppQWZWZmRhQ1R5V1RjZ1BVWXhvMlhrSnlrMHNhWENGSUEvVmlDUmNjM0hrZnJkSytKclhKLzhpSWZYVlVEM0NrK2RLaXVXMW9RV1c0d1cwT05JcDh5TW1yMHhDdWJpcDF4YnpmejZkS0E0MllmNW52MGIvVzNDRTdGMS9OaDRmbHhkcEZTMElJcmxyczQ4UEpxdFVzUklkU1Z5MGRDRkNZR1lsY1FNTFBDTnQ4V1d1UG15VlhuL1djOEZZUFZObE9LUGJicGIzd3IrV0RnQ3RhWFlwVWhXSFl6S1Nib0VvQ05RTWs3bXp1WXJtNldaU0Nva3NWRlNhbEQzdGNGU3F4SllkbzF4dmJDZGJxckEiLCJtYWMiOiIyM2NmMTEwNDgyZWQ5YzljN2QyYWMyYzg3NTZjZDVmZWM5MjU3ZDdhYTY3NTllYzBjMjc2YmJiNDQyMjQ2MmQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBuYlkzR096Y0RpMEJXZnp6T2VIWXc9PSIsInZhbHVlIjoiNHRwUkMzRmpIeW9LT29uT3RKU1JZcUYyb1JVdmY2ZnBkbWlaQjdwU3B4SE5NT3owTmtYbDI5SjFCTjZyRXFTYW9UYzZ2K0RVbUZNYWZQblV6Y042djIvanF0OGJCUFI1RzZrTUVIOG1lTjVWa2RHbW5jQmE2Q3RTM2FnNjJSK2laQjNiZnZOV2t1NmplUDRXcmNTdmwwemFQMVBsN25PcE1FczVQOHhHSkpiUjZ6TEhjSkZ3QXY1ejZvMXZHTkRBbjZjdnY2ZzdJTHZIRDJHT0REVjhRa09BcXJFczRxRTRwSExacjJlaFYxa3d0KzM2SDRsVDhTZUd2dEgvL2xpbTFycW5UYmVkSlZBOGlnc2VwNXRjODU3TFRPY1RDeC9BaVVkNDllNFAybGhDY0VSRHlFYzFsMEd4M29GOXNtYlUwbXBNbWtML0RJOVZVQVlmRzZHdzVUOWVsckdVN1ZmRTZyME0rTVJHTFlOcUNlclNCOVgrYUpsYnJhcmZNR2hxSjNxblp6TE5Nc0NoSmxKUFlyZWZ0YzhFbVR2RFpkWnUxdlY4OW1CVDVqMUZjdTdncDNnQ3RPTzNXTm12Z2ppNDdqcEJPOUlyYkRCVWxSejJaSkRHdHl6ZXpmR1kwUHRSdWovZGxMQjh1NVc5NXFNTmVLSEZXWitZVDJIaFBzdS8iLCJtYWMiOiI4YWNjZjRkZWFkMGMwYmMxYjFkMTlmZmE1ZTRmMGRkOWU0ODA0ZmFkNjZhZmM2M2VkOGNhNTIwZDg4YTdhMWFiIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485834049\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2095986392 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=8&amp;date_from=2025-07-21&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095986392\", {\"maxDepth\":0})</script>\n"}}