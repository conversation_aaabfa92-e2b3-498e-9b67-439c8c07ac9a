{"__meta": {"id": "X65dc223f47b5f29184ebe2c290926167", "datetime": "2025-07-21 01:43:58", "utime": **********.872665, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.422252, "end": **********.872678, "duration": 0.4504261016845703, "duration_str": "450ms", "measures": [{"label": "Booting", "start": **********.422252, "relative_start": 0, "end": **********.819791, "relative_end": **********.819791, "duration": 0.3975391387939453, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.8198, "relative_start": 0.39754796028137207, "end": **********.87268, "relative_end": 1.9073486328125e-06, "duration": 0.052880048751831055, "duration_str": "52.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027500000000000003, "accumulated_duration_str": "2.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.849792, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.636}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.859459, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.636, "width_percent": 18.909}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8654451, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.545, "width_percent": 17.455}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-67413032 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-67413032\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-650742306 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-650742306\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1054893995 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054893995\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-240446220 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"149 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753062234366%7C22%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkN0TGIwZThVM2oyZFdCSUhUeS9zL2c9PSIsInZhbHVlIjoiNnp5dDh2a2JOSXZFQmpyVjRBeDRjM3NIb2l1MVgzd1pETFoxSXQ0VmZ3NVMrbyt2MDZrN0IrM2xQL1ZiODdobnh2eWxSQjRYUjQ3ZlVjUXRucWpXT2M5K2R3WUsvNDNtZ1VSdGp4dnpDUFlzbi85azdnUkdJZGJUNk5LK3liUS9ITzBrb1lwWEpGTUxEendaNUZKOFVtc2lORVhMQi9hRnhqbnlpd0labkJYbjNpa0VjZTh1N2k3bU1IM1c5RDl2WlNWbmVoSDNUNGFFVXdra2pUbVZGd3duK3p4Y0YvejRGd0tMNlJtT0k4Z2FYSkVqT1NUQmpFdHh1TzhEODZwWkM1eTVRSElUZThBRllRYkIwa2gzdzM3REt0YThaZTBBcnJNQVMrbVpvS1JqTDhhNW00K1FIbFp2VVlPSGlHOTVPVmlCZUN2Zmcyck9oYlFQUml4Wk5odkUwNGgxKzhrN28xK3pXNWZsQlFnWWpIVDBFME40UmJGNWcvelpuRDErTmtKOThOS3dpOG1QSlh0cXZnQU0yN09YUS9ndGRVV0NNTVVaelZHbERReW1acWF1MTF1RWZWTjkweks3Q0dLSTBOb3JPaklSUVFqb01JR0MvNEpEWURiL2NDNnVMaVlCN0J3NndmK2tVdi9mdVkwY3RoaEJPek81MUJnM2d2OTQiLCJtYWMiOiIzYjBmOTY2NzRhMTQ0YjQ4NDllMmQ2N2RlZWNjY2QyOTA3ZDFkZjBlMGU5NTRkNDg5YTA2NGVjMDk2OTJkNjU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndnNUlHdFJQSmgzSklUd2FmWnlIOGc9PSIsInZhbHVlIjoiekdIL0FieDJ0RDhrVzRTS3ZpRHN3dzViSnMyTTE0ci9mOFdLb1Z2QUlYR2tpZFZ5VEJWU2lPOElEMXhBNkJtb2ZSZ2d4SWN2MzVWS2JDaEFXRlZ5dnhOZnMzM2JDTFIxMnJnbnpiN3NlZGhCWnVHWFZBVnBGdWpoQ202NzBveXhVU3VYUUlVZDZFc2J3U0VPQU01dFZTYXFFcE1saVdCbU9mM0J1MFV3SzgvRmdSSWpLa1BlRmtYR1NHSEp2K25NVUt4S21kc0xHZ2tnTk9pRWlHU21oYnVRTXFQckF4R1F2cUpNMHNmL0xIeVhYRklvam5pbnhRUDVTNEhpelMxL2ZscTZNanBmL2VNd1VjWjlPNVU4RVNQSFA3Z0RVOTROeFpHbDZGcHYyQU9EQUxQaVByb0QrUWtidUxsZHd0emx4SlkxeVlCcFNYLzMzaGw5akhadHliWkZQQWRSZW9vUUhmdk9ZTDBqV0tHY3p1OHlNOXRrRk4zRlpZdDBPdW13MFVBSGxRbXZWeHU1azAyaXNjM2N6VGJuRTdEWC85QjJPVjQvYWdxL0pOazBreDMvN1FxV3F3UXJqbko2YUc0aEJrOStNK3NjQ0l2bkx4L2h1ZVJDM0ZJRFhVRmg4ZFhuRXNSbkE4THlQTG9LS1ZCQkw3bG1nYjNxeHNNOTF5MHoiLCJtYWMiOiIzNTA5ZTFjZDBjOTBkZmI1OWYxNjU5OWE2MWU0OWViZTUzZmEzNDRmYzQ1ZDc0MzIyNjVjODMzNWQyZmQzN2VmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240446220\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-691588332 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691588332\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1731924767 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:43:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBtd0xpcnVKOUpHMEQ2OXZkbjdlR0E9PSIsInZhbHVlIjoiWVZBSjFRUjJHZWNqMWV6TWI3NHNnWjhZcVduck05VGE2eWp4cTZrcnRFZXlYSlplSFkrM21INFEzU0VvVlAzbzZCbmVvc0svc0YrdVRxS29wTFdhMXE5NGFpRHdSNjBkeDVtSmpmWFg0TTkyRklMeGIxSmkyU0xueTVBVlBjTnlnN1BVdTFWUE5DRFoyYU5PS1lUS1NEN1pHWTRaTUNyVDdaL2RMZlRpZzk0R1pISzl6RE1LNzV0bnNaSkdHdS95bElid0ZsbFYreXV6WlU2U1ZoQVo2UVdhS2ZlZzRUQUI1QnYwVFFYOTRhYkw0WS9EWWRGYmx1RmpYckh4OURSS3V1Qk1qUk5KNlNyMVp1VGF3ZDNHcUEydWorcDFFTkVjTUJxSUNid0dyTGJuR1hJbVZoYmRuaS8yaWI3TU9MRDBiZmR6dHA0Vll0SElTUm5aaVVvRmFEMTVjQ3RVN3hLeUE5ZC9CSXM0VGptRFZURHpGU01CcXhvb0Z0SmFQWTdxQU4vUEM2dG5mbUFkOEpVWU1ueWFjdHhtL2o2VVFMbEp0SExIT3NkYUF3NnoyZS9sYkRzMkt5L1RuSW85Vk95UHR5Vjk3bTR2UjdvZDNodC9HditrRGNlemFnNVBPNXpjWlMzb0xKQURTaHFLRElQYnJ3a3UxL3hRelJENnpxUksiLCJtYWMiOiJmNDE0OGQ2MzliYjY0OGVjMmRlMzNlZmQxZWJiMWE5ZDMxZjZkODBhZWRlZTgwZmVhODU3ZWJjYmU3YWRlMzdjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:43:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhkdGZweGpDbmJ2aDJnV2FCa3psVmc9PSIsInZhbHVlIjoiTFpIYzN3Lzhlalc4K0QxTVpiQWdCNyt0WGppZXVxUVR6VzYxSXhYYnRpVjZyS2hXeWU2L0VGZ096U0toZENSdkF0Vm9adFNIVmppSDJGcGkvaU1UTzFMUG5uem1oTk1ZVGVsNEpmR3hCWU51ZlNoSmdmeGx6L1JoVXdDd0NNV2pCcTkrZGJEWUkzemo2Uk1Kc0hGZHVBNHhNaDBFTHpkRWEyaklUSFo2eGQzNkQ5amY5SHpSMUdOT2NwTis3UzRzVCtQcjF6dVMxR3FKb0RNdkMxVHl2V3Fld3h6dW1xWHBaUGlwRUhwTkhZWXlNL3hnaHhTcFRWVTVvczRScE5iZ3ZxK1ZYa29pVSs3NWsxVC9QalBZWEpzazZXeDd4RTlYWjZFUUJjaE9wb0thTm9kTzdCUnlRaEFNSWpSR3M2bnpVWGRXb2ZTNmtEZVZqVHI5b2pIa0VHWDd0QTFIeGZaWHk4K2dqblNVNDVNNnMwcncyZzlXYjBLTnZMdVRETVlkL1lKUDZPcERTZ0Nkem1sZzZ3MWw3dGdxUkJrUFRVN1hndkNMTXoxS0t1Yk96MVNFaE5XYWRlQUNGQ0RaUHg2VTdJVFMwUUFBdHBGR1ZGa3VxWjlFNW5xblR1ZFM2Y0l1c1ltQjRiV2QvSjdrTlFyN2p0NUc5YzFFNjY0K3ZtMzEiLCJtYWMiOiI3YjAxOTgzNmJmYWU2ZWIxZmFkZWNkZjRmNGMyZGU4NDk2OWU2NDQwZWFjZmQwNzM0YzZlYjkzYzY1MGJlMDg5IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:43:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBtd0xpcnVKOUpHMEQ2OXZkbjdlR0E9PSIsInZhbHVlIjoiWVZBSjFRUjJHZWNqMWV6TWI3NHNnWjhZcVduck05VGE2eWp4cTZrcnRFZXlYSlplSFkrM21INFEzU0VvVlAzbzZCbmVvc0svc0YrdVRxS29wTFdhMXE5NGFpRHdSNjBkeDVtSmpmWFg0TTkyRklMeGIxSmkyU0xueTVBVlBjTnlnN1BVdTFWUE5DRFoyYU5PS1lUS1NEN1pHWTRaTUNyVDdaL2RMZlRpZzk0R1pISzl6RE1LNzV0bnNaSkdHdS95bElid0ZsbFYreXV6WlU2U1ZoQVo2UVdhS2ZlZzRUQUI1QnYwVFFYOTRhYkw0WS9EWWRGYmx1RmpYckh4OURSS3V1Qk1qUk5KNlNyMVp1VGF3ZDNHcUEydWorcDFFTkVjTUJxSUNid0dyTGJuR1hJbVZoYmRuaS8yaWI3TU9MRDBiZmR6dHA0Vll0SElTUm5aaVVvRmFEMTVjQ3RVN3hLeUE5ZC9CSXM0VGptRFZURHpGU01CcXhvb0Z0SmFQWTdxQU4vUEM2dG5mbUFkOEpVWU1ueWFjdHhtL2o2VVFMbEp0SExIT3NkYUF3NnoyZS9sYkRzMkt5L1RuSW85Vk95UHR5Vjk3bTR2UjdvZDNodC9HditrRGNlemFnNVBPNXpjWlMzb0xKQURTaHFLRElQYnJ3a3UxL3hRelJENnpxUksiLCJtYWMiOiJmNDE0OGQ2MzliYjY0OGVjMmRlMzNlZmQxZWJiMWE5ZDMxZjZkODBhZWRlZTgwZmVhODU3ZWJjYmU3YWRlMzdjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:43:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhkdGZweGpDbmJ2aDJnV2FCa3psVmc9PSIsInZhbHVlIjoiTFpIYzN3Lzhlalc4K0QxTVpiQWdCNyt0WGppZXVxUVR6VzYxSXhYYnRpVjZyS2hXeWU2L0VGZ096U0toZENSdkF0Vm9adFNIVmppSDJGcGkvaU1UTzFMUG5uem1oTk1ZVGVsNEpmR3hCWU51ZlNoSmdmeGx6L1JoVXdDd0NNV2pCcTkrZGJEWUkzemo2Uk1Kc0hGZHVBNHhNaDBFTHpkRWEyaklUSFo2eGQzNkQ5amY5SHpSMUdOT2NwTis3UzRzVCtQcjF6dVMxR3FKb0RNdkMxVHl2V3Fld3h6dW1xWHBaUGlwRUhwTkhZWXlNL3hnaHhTcFRWVTVvczRScE5iZ3ZxK1ZYa29pVSs3NWsxVC9QalBZWEpzazZXeDd4RTlYWjZFUUJjaE9wb0thTm9kTzdCUnlRaEFNSWpSR3M2bnpVWGRXb2ZTNmtEZVZqVHI5b2pIa0VHWDd0QTFIeGZaWHk4K2dqblNVNDVNNnMwcncyZzlXYjBLTnZMdVRETVlkL1lKUDZPcERTZ0Nkem1sZzZ3MWw3dGdxUkJrUFRVN1hndkNMTXoxS0t1Yk96MVNFaE5XYWRlQUNGQ0RaUHg2VTdJVFMwUUFBdHBGR1ZGa3VxWjlFNW5xblR1ZFM2Y0l1c1ltQjRiV2QvSjdrTlFyN2p0NUc5YzFFNjY0K3ZtMzEiLCJtYWMiOiI3YjAxOTgzNmJmYWU2ZWIxZmFkZWNkZjRmNGMyZGU4NDk2OWU2NDQwZWFjZmQwNzM0YzZlYjkzYzY1MGJlMDg5IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:43:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731924767\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1013417036 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"149 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013417036\", {\"maxDepth\":0})</script>\n"}}