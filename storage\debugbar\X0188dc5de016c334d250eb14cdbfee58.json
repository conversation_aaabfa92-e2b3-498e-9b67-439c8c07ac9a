{"__meta": {"id": "X0188dc5de016c334d250eb14cdbfee58", "datetime": "2025-07-21 01:44:03", "utime": **********.255409, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753062242.831483, "end": **********.255423, "duration": 0.42394018173217773, "duration_str": "424ms", "measures": [{"label": "Booting", "start": 1753062242.831483, "relative_start": 0, "end": **********.198599, "relative_end": **********.198599, "duration": 0.36711621284484863, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.198608, "relative_start": 0.3671250343322754, "end": **********.255424, "relative_end": 9.5367431640625e-07, "duration": 0.05681610107421875, "duration_str": "56.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46007552, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00232, "accumulated_duration_str": "2.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2307148, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.121}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.241527, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.121, "width_percent": 15.517}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.247464, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.638, "width_percent": 13.362}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=15&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-686153208 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-686153208\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-371368553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-371368553\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1373043189 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373043189\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-133663362 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_id=15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753062238836%7C23%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllRUlFKWkcvaDF6VzFnU0ZZd0oyZWc9PSIsInZhbHVlIjoieWVzUUpSbUpFa3lFMHlRRWV0ZVRjM1BsTXdtdmJjdWU0Zm16bDFpN1ZGWUtUMUFTNHJRWFN3ZVU0V29BenNvWkRiaUJNR290azlKUFJ4dXJaYzlzRWh2OVQzOUxMR2g5bHY4N2QrQ2V1a0thcnBLRXRRdDNGQTF4aTJ4bTBRaitXYVBvbHMvUTI1WDlNcDFlVk5zRjJUQ1lDQXR3bFc1N2hyU2ppNnFod1dOTGwvSlNpSEYrK1FoRXhPY3ZXamVYV082WEM4SE56STZpditqcUNnQUMwVGZobUpNbGQ3M1F4eE5wSWdkT1l5QXlJeVo5ajFVU0lOZXdBUzVQVlJ2QUpmL3RKTTdBQ3dMZHFvQXZUc0ljWnIrWTdLRG9Kakg3d29pTmpNZEFWR202NHMwL3hTMU1xTEpvTzMzYkRLT2hibXZ2eSsxWHkrSy9NMTRyTVRBMGEyUHE1QWZmZi9FcjlkUE0rMFllbGpMbXB4WXY2cXVqcFFOeTFISVh6cFpaZjdDTm1aVlJCNjZ5SmY4S3lwTDN5R1FWS2FIK3ZtWi9GbnAzeHRNOEpCZ2E1cTVzZXp4SzZ2dnlOMm1ncUlieVRMWXU3YjBWemYrbkRSMEhSUjJQODZSamUvajdJaHFmdFJJeXBoTE1WUUF1dDhZaFdFdVdCV2FBNDZzSXhzVzkiLCJtYWMiOiJkNjJhYmE2Y2E2NGQ2NjM1Y2NlNmFhMTJhYzJmMjMwZWQ0ZTNhYTJmYTNjMGFjZWQxODc5NTk3YTk2ZDEwMDdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNFRE83bC84SkVwZC90Q2Z0U0FBdVE9PSIsInZhbHVlIjoiTUc4dnBZeFpla3luVDVTVjNrTytFRFFCRE9BbTM2blJoVHdGancrK0thbDVnV09uSUo4NU1ybnlzd1ZXc0U3ajV6eTdPYU1qSHFjVjJrbEowcmR0M01RQXBsYUo4S0Jwa05nakxUM1FkeGpXV0MxRGxnS0ttWTdqMnBwZHJnckorUWk0T3FFcEc1TnpKL0IzTWtCS2RiMUN4clczbmR0M25JdUhUTkM5cUdyL0wrQ0loUVczdDF1dFMyY3BQYnlFa2hPRUhuanU0K2c4dGdQaUx0eUUvcUVZZ0tmVGlxMWh3QVlqanA2bmMvRHFZSEgwWHlST3dIY0ZleDJUakhsRFp0am5nbENUc2tuRWFTMCtHTlZZbWhod1IybnhIcndrRjVIY2RRWENpK0tHOUNtUy9CVC9WS25ZbFlSdGF0VkRYSExtQkcwaitwVUpFay85dTFrdXZidUhHZzhGRkRGZ2dwNVZFbENEMmRjSzdqbEoxMFFibWh1ODJDdkZVUTZUV0xkRkQwK0JGOEZlczhrYm5jV0pDUDdJblp5SWwyd2xseHhMWko3SEdpS0s0NndyaEx4eW0vNkNuRjdBektIVVFBNzNjYnRVbFJFQlNqelE0Y21IdFUyejlBemR5ZHhmd0pGSHA2a2NQczl5aktYMm1IcGU4TjcvY2dROG1aYWwiLCJtYWMiOiI4NGM5MzQzMTVhYzFmZjU1ZmZmZDUwNjZmYzZmYzhiZDBiNjYyODEzZDk3MmI0YjdkYmQwYjgxOTBmMWNkNjMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133663362\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1405689559 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405689559\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1834167179 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:44:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVtNUdOaCsvZ0JNbkhEVWxlTTJaZ0E9PSIsInZhbHVlIjoieDQzamJrS2FrOG1XNURLUVRtNFFmbjRhaDBqTE81dkVWblNPWlhJeU9HcjN1dnFLQ0pNUjB0TmROM1duZVZvU0IyQ1RVQVFrQmtXS2hreS9vVm5oRGhRa28xWFJoZlJsNm51SlI1VG1qcTJKNkVMSnVPc3U3Ri9oQ0tnR1BSM281OXdTUmtGeTl6NFk4WTZ2aEZtSDMwclY2Uk5tbWxYZzdBWGxzRWJSSVdYb0NZMXVmMUJKV092YnpubWREZEVrQnc0WW1aVStVSnk4WUQ0TjdHS29pODBhWGZJN0N5NUFRd3ZaaWl6QjVoQ3pjRnFFQjBsc0FrWURKdk5UOEtwQ2w4RnRnUlZMcEdDcUNQLzBibGQzc2VjcHk1cWVwSXkyaWZ6akFHVFg3M0MvY3ErTUdSd0RhTjdZN0ppbS9JSHVxcFBoazVnV21JRERHN1YxL0cwaE1kQmtzc1EvTGlRdWUwZy9HYVR1Y1dtekpHdklPcjFub2JZak9GWis1eER4NVFCK2ZqcnRaQ2cwajI2WU5FRktOZjdOemM1OUI0dGtNczQzMmRNb1kreFlRSzdBRlcwQ1d4a211eXFwK2YxTW84WENobWVLa3g3eXBTQmlxZWdLRmZKZUZvNDVkdWNSMUNwR1lqQVJKSU40eFErNTI4d2V4ZHN4SEU3Z0c5VEkiLCJtYWMiOiIzYmM2N2VlNThkOTRiOTFjMmYyMmZiNGZiNWE2NWQ3MDM5ZGIwMzA0ZTU1ODg1ZDZlZWQ2YzQyMzdmOTM1Yjg4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:44:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNyckFoRmV1b3EyN1RYNkNHT1FwK2c9PSIsInZhbHVlIjoiTFZPRDRKQlZVVndxOElFSUk4dUJtUmlrbnpFVzJ3aGxlcFlOaHZsc0ZiRFRzK21PRThScjFsVUpwbTZHMkFLdDYxSnNoeFRsSFZYS0FZTUNUY0w2Q1o3M0VhRnVwcVVlNHE5UDVZMExPN0RNc21ydy9Td01vOVpUOTU2bzhWZmE1WVUwZWhXVjdRY01lWDN4SWcyeXdGNEVNV3R0VkwwcUZKSXo2ajVHeXRWbGZ0ckY1TU1tdWFlVFFocmcxSVJNRGc0TFVpd0dHZm9rMkF2N3VZaXpNQzBwdU9FalBKdm1DVWgrbHRQNmZCTVYvdFM1dlk0RHFLNEJrUkRpdk8zenpvMEI1dHh5L1YxWWFhd2ltZFdFT3RBaG1ZVTBydGZ6WVVGbUFvN0k0WHY5Z0J4SE1LL3VYSVVuTU1mSjloZStidGtkQVZsY3RVd0x1Z2ljMDYvUGNkYXdvbWRBb2k2SjVHQ0tnRzk5R0hkbWY4c09CVC91a2d1RURHTnQyRHd6a1VpWXNyWjlsTEVUNnN5dW5ldzUxTUkrOVR4OEpCZjh6UUZZNEZuZGRPcFJ0U0E1OE5rMzhPMmU2Vmo1dkFrQ1FtNjM5K0gxcnRCbnIyMDhpa0c2bWg2djFNSGRVV2xSY1F5SnpGVEZHOFYyRU9tVWI1SlBvWklpdnlqdzhZWTUiLCJtYWMiOiJmOTVlZTNhOWZjYjI0Y2M4OWUyMTUxZTEwYTlhNzAxYjY2MjUyYWJiOGZmN2IwNThjMzcyMTY4YTY3ZDkxZGMwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:44:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVtNUdOaCsvZ0JNbkhEVWxlTTJaZ0E9PSIsInZhbHVlIjoieDQzamJrS2FrOG1XNURLUVRtNFFmbjRhaDBqTE81dkVWblNPWlhJeU9HcjN1dnFLQ0pNUjB0TmROM1duZVZvU0IyQ1RVQVFrQmtXS2hreS9vVm5oRGhRa28xWFJoZlJsNm51SlI1VG1qcTJKNkVMSnVPc3U3Ri9oQ0tnR1BSM281OXdTUmtGeTl6NFk4WTZ2aEZtSDMwclY2Uk5tbWxYZzdBWGxzRWJSSVdYb0NZMXVmMUJKV092YnpubWREZEVrQnc0WW1aVStVSnk4WUQ0TjdHS29pODBhWGZJN0N5NUFRd3ZaaWl6QjVoQ3pjRnFFQjBsc0FrWURKdk5UOEtwQ2w4RnRnUlZMcEdDcUNQLzBibGQzc2VjcHk1cWVwSXkyaWZ6akFHVFg3M0MvY3ErTUdSd0RhTjdZN0ppbS9JSHVxcFBoazVnV21JRERHN1YxL0cwaE1kQmtzc1EvTGlRdWUwZy9HYVR1Y1dtekpHdklPcjFub2JZak9GWis1eER4NVFCK2ZqcnRaQ2cwajI2WU5FRktOZjdOemM1OUI0dGtNczQzMmRNb1kreFlRSzdBRlcwQ1d4a211eXFwK2YxTW84WENobWVLa3g3eXBTQmlxZWdLRmZKZUZvNDVkdWNSMUNwR1lqQVJKSU40eFErNTI4d2V4ZHN4SEU3Z0c5VEkiLCJtYWMiOiIzYmM2N2VlNThkOTRiOTFjMmYyMmZiNGZiNWE2NWQ3MDM5ZGIwMzA0ZTU1ODg1ZDZlZWQ2YzQyMzdmOTM1Yjg4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:44:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNyckFoRmV1b3EyN1RYNkNHT1FwK2c9PSIsInZhbHVlIjoiTFZPRDRKQlZVVndxOElFSUk4dUJtUmlrbnpFVzJ3aGxlcFlOaHZsc0ZiRFRzK21PRThScjFsVUpwbTZHMkFLdDYxSnNoeFRsSFZYS0FZTUNUY0w2Q1o3M0VhRnVwcVVlNHE5UDVZMExPN0RNc21ydy9Td01vOVpUOTU2bzhWZmE1WVUwZWhXVjdRY01lWDN4SWcyeXdGNEVNV3R0VkwwcUZKSXo2ajVHeXRWbGZ0ckY1TU1tdWFlVFFocmcxSVJNRGc0TFVpd0dHZm9rMkF2N3VZaXpNQzBwdU9FalBKdm1DVWgrbHRQNmZCTVYvdFM1dlk0RHFLNEJrUkRpdk8zenpvMEI1dHh5L1YxWWFhd2ltZFdFT3RBaG1ZVTBydGZ6WVVGbUFvN0k0WHY5Z0J4SE1LL3VYSVVuTU1mSjloZStidGtkQVZsY3RVd0x1Z2ljMDYvUGNkYXdvbWRBb2k2SjVHQ0tnRzk5R0hkbWY4c09CVC91a2d1RURHTnQyRHd6a1VpWXNyWjlsTEVUNnN5dW5ldzUxTUkrOVR4OEpCZjh6UUZZNEZuZGRPcFJ0U0E1OE5rMzhPMmU2Vmo1dkFrQ1FtNjM5K0gxcnRCbnIyMDhpa0c2bWg2djFNSGRVV2xSY1F5SnpGVEZHOFYyRU9tVWI1SlBvWklpdnlqdzhZWTUiLCJtYWMiOiJmOTVlZTNhOWZjYjI0Y2M4OWUyMTUxZTEwYTlhNzAxYjY2MjUyYWJiOGZmN2IwNThjMzcyMTY4YTY3ZDkxZGMwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:44:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834167179\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1185023154 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=15&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185023154\", {\"maxDepth\":0})</script>\n"}}