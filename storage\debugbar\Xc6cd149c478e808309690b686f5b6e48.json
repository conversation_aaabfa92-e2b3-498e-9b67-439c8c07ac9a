{"__meta": {"id": "Xc6cd149c478e808309690b686f5b6e48", "datetime": "2025-07-14 17:58:24", "utime": **********.87056, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.348895, "end": **********.870577, "duration": 0.****************, "duration_str": "522ms", "measures": [{"label": "Booting", "start": **********.348895, "relative_start": 0, "end": **********.803923, "relative_end": **********.803923, "duration": 0.*****************, "duration_str": "455ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.803934, "relative_start": 0.*****************, "end": **********.87058, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "66.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0048000000000000004, "accumulated_duration_str": "4.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.835647, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.75}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.847242, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.75, "width_percent": 25.208}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.857207, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 78.958, "width_percent": 21.042}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515898579%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVaek9UOXhDMjhjY0grVlVZZnViVEE9PSIsInZhbHVlIjoidjV0RGNZSEY4YkkwZDR2UVZKcWVBNE8zeXlKaDZGMmlhV2dTRDQvbGFuNTFMUG85M2lwWjdTSU9hd0RVNlIvMEdMUW56T2pWczRQT2JFS09TM0psQ1RnQllzVGxUYjlob3Fmb1E4SE4wQjZWQW96SFl3RjNKeHJsMEp2RmhXUEl0UmNBRkJRZFdwd01rUXZ3TFcySkZ4QnhsSkZqNFgvS0dxKy9aQWREUEJZNDNPUThMWHlSNTN0bE5KdHl0NW1UMWs4NE0zU2ZqQUdCNXNjdVcreVYzUzJOeGRhU0V2emdnQjhyMktZeHhtSlh5dVVLZkRiT3ZaTC9BMEIrTzQ0dXAvWVJyTzdyeGo4T01FbVZOVkpqNkExcE83UXFldlVSeUJsSzdwTU8vd0xXanRrU1hTZ3RhVG5mcEVhOFlHNVorVnpwOFdFcGt5eHNoczZwV004eldJbUJSQUs1b3QzaGtSR2xNUWlWM0F5WVlNWWprN21lUmF5ZWl5dnFQbzg3WDRYUzF5VTcwK2ZmODhhV0dEcHpRZFppL2RiVUxSWFVnckg5dGZSUnAyOFJJWjQxWERDMmdKcmp5TXE1RUVrVzZmOUYyTFZNNkFNQm8zMzc0Ky9rVmdNRUttL3hNR280ZUd3UERnM0FTNmd0SzFnSG8xVWV6UkoyaG92Q0QyaVEiLCJtYWMiOiI5ZTUyZTlmZTA3Mzk1MTkwMzZjNjU2M2I2ZWY4Y2VmN2RmZmYyZWM5YzgzOTc5NDI4NGEzMTNjZDIzYmE5NWQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im85L1dXQ1NIbEtpbm56N3VTQ05JS3c9PSIsInZhbHVlIjoiaUh4eStPbHB3N3NGSUVrZWVQZElBRE80eVFSSjZWanZYcHlZdXZ3RjhvTWNrK01HMjR1SXRnVmlaT1JPUFMyMnp0RzZTWHlsbWVVazBFa0prN203QWcrUHh5R3dPWHJEODBIOUt4MUxabDIwaklyeGh5eEQ1UGRGcmkybnMyc3JUSzBpUkM4Y0IzTlB0Qm1lK1NhbEk3NXRaZkkyNEp0V2tTNlJlTmxzTWlqc3JUSURKT0I1VlNhQ1JaSlhhWjN5YmlKdHdiUGI4WFZDYWlsSC9jL2xYSCs2Q0NkdEtucUhiTFJWWDlqdGhVV21xOHEvR3FKdzRpMjVOL0xObmhDSGpIN2RNZGV2TEVwTkwycVRnZ0pEa1JRSmVHcXFqWlM5TGg0eVZEOFFYVnJmMlNBellHMTlGSmdvc0M3RE90YnNkV1M4dnlacEpvRUNON2s4bDZoVThHT2lMQUdqY2s4a0lyQzY1MTkyTU1DTFZHWGRwaVN2WkdIS0tBeHc1eVhLbXhMaDEwVjhlNis5S0hsOE5jMSt5TEczUzIrMkdhNHVsN1Q1U1lBUnI5elR0bnBjc2prNHhSMHU1b0IyNlVmN1pPcTFEN0hjRUhOZzNZV3dnSkhRekM3UEg0N1RlTVQwUnUxYXduSFRnemNkMVFpU1hYNHN0enZuek9sTU85WjAiLCJtYWMiOiJkMjIxOTQwMjM3ODljNWMxMWE5ZWE1YjVmZTU1ZjYwOWUyNDJkNGI3NWE3NjQ4NjEyODQ2ZWU5NzRkZmY2NDdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-243578891 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243578891\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1687269687 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRydWN4dm5XK1dKK1pIYjJ4eXhCZXc9PSIsInZhbHVlIjoiQUx0ZjhhRGhyY01CZUgzS3BjSFJ1Vll2cStheUkwV1RacjkwSVVxc1BRT2JYN1hSTzloQlR0eTkweU5kOWExNFhvcWNJTytHZzczT0dQckozVndNVXIzbHdSYmljbllKRUE0bWRkR2lqRUZtaDVmekNDV0FUOE1lRDVua1RSdjVoZ3BEOU55Z29VRHJtU1R2WWE0dVZuYmxnMDdnV0lZT2ZsRWF3MFczZU8zOG51SjFIa2prT202MWZxT2xka0QveGY1eG5sa2dSRG9GTXY5eDBKV3RIajJQYjFWcTFCWUNlM1pSNGNSazVYbVVQQ3Y4dFh3MjhXZmFPM3BZeExsTEtZNnVsdUpMdmgya0lSa3U4NXBpek1GV0ZzcXY1V25rRWk4ZndGK3hiaTBsWGsxTERpd1VVQVRhTFo1QTd4WVo2dVc5ZFlPMUl2ZkZjOW1oWmwvVTBIRnRhN2JxM0d4dmVEa1NzZlRFYkJoZXhvQXZIQVV5UnRtb2dFaFA4dHo4eW5qT1ZVY3A3L0JhSG0yMmVscFNkLytwbHZkZVhjSG5lQ0ZaNlVFK3l4VUY3b0IvU0RrUTBkNU1ra2MzYkd1WmFzWVBnc3FyUEFFYXRYTEIwQ0NacUp5V2pDeTA5a0lYVTI4RGR6Q296K05YWkptVHUvVVJ4c2l6R2FyVjJOZEUiLCJtYWMiOiIzMDk1ODllZTAwMDc1YzQ1NTg0MmM3ZGRiNGUxM2VlMWY2ZmQ1ZGUyOTc1OTcxZjUyY2JhYWM0ODZmYWFhYTc3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJMTHJqMVJzZVVXdkllT0VUK05TYWc9PSIsInZhbHVlIjoiVzdJNWpianlQSy9VUHc0SUhQN0FyUktvTldXSzNBbUFhZGNvRFJBMi8vMFV1QjRMOVozL3crQ21pcm91R2w0dkZ0TkY1VjhOeEMwQ1EvOWYrOXJlVWpJc1g4UXcrZDFLQ1RwL0JqWWRGd3JYWjZtZklHanI2Zk55Q3lPSTJpV1p4UGNSazFxTkVibHJscmphMGw1YUJyNU5QeDBiRnJyOGcvSU1FMWdXS09adXlUc1JyZHJqNHdyeDZxUG1sbGlPMklSZ2YydnE2MWx1eVI4dkhNZ0p5Snd5VWxuM1BOTXNETjk4N0JZc1I4K1YxaFdUQ0hGanVJWHczbVdsaDc2bzBvWkRHUEErSGpqeXM2QWtoblJhbE1ZdHdhL09HQmdjN1grcUR1cnplSnhHR244VlZCVlpMbUxtcVRRSUo3VC9Ma0s0cUFNRWpRQmFIWWlxanBUTWNnMHE2VE9sNSttbXdnYmUveDFUM3A3bFRUbWlmejdoNmRHWFQ3cWRHTnNNUzlvbWFvMFJMRHRGRXBHMUpBSDB4ZkladjBjd1B2WU0weG5EZ0xLdlZScmkzZzd4M3R3bkdwSGJZTWluOC9kN2Q3WEhzV0YwZjdCK0hwOHdJMnpwYjVTSmNlRzM2V0RobzJXNWNaYjhoWkJCdG5vTjU3b3NnSWs4eGRTYzlQV24iLCJtYWMiOiIxZTc2MThlMjk2ODhmZWMzNTg2MzBkMzk4NDE1MmRjZDhmYmU1NmJhNzYyYmNmZGZhYjYxNGQ4MjM2ZjE2YTJmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRydWN4dm5XK1dKK1pIYjJ4eXhCZXc9PSIsInZhbHVlIjoiQUx0ZjhhRGhyY01CZUgzS3BjSFJ1Vll2cStheUkwV1RacjkwSVVxc1BRT2JYN1hSTzloQlR0eTkweU5kOWExNFhvcWNJTytHZzczT0dQckozVndNVXIzbHdSYmljbllKRUE0bWRkR2lqRUZtaDVmekNDV0FUOE1lRDVua1RSdjVoZ3BEOU55Z29VRHJtU1R2WWE0dVZuYmxnMDdnV0lZT2ZsRWF3MFczZU8zOG51SjFIa2prT202MWZxT2xka0QveGY1eG5sa2dSRG9GTXY5eDBKV3RIajJQYjFWcTFCWUNlM1pSNGNSazVYbVVQQ3Y4dFh3MjhXZmFPM3BZeExsTEtZNnVsdUpMdmgya0lSa3U4NXBpek1GV0ZzcXY1V25rRWk4ZndGK3hiaTBsWGsxTERpd1VVQVRhTFo1QTd4WVo2dVc5ZFlPMUl2ZkZjOW1oWmwvVTBIRnRhN2JxM0d4dmVEa1NzZlRFYkJoZXhvQXZIQVV5UnRtb2dFaFA4dHo4eW5qT1ZVY3A3L0JhSG0yMmVscFNkLytwbHZkZVhjSG5lQ0ZaNlVFK3l4VUY3b0IvU0RrUTBkNU1ra2MzYkd1WmFzWVBnc3FyUEFFYXRYTEIwQ0NacUp5V2pDeTA5a0lYVTI4RGR6Q296K05YWkptVHUvVVJ4c2l6R2FyVjJOZEUiLCJtYWMiOiIzMDk1ODllZTAwMDc1YzQ1NTg0MmM3ZGRiNGUxM2VlMWY2ZmQ1ZGUyOTc1OTcxZjUyY2JhYWM0ODZmYWFhYTc3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJMTHJqMVJzZVVXdkllT0VUK05TYWc9PSIsInZhbHVlIjoiVzdJNWpianlQSy9VUHc0SUhQN0FyUktvTldXSzNBbUFhZGNvRFJBMi8vMFV1QjRMOVozL3crQ21pcm91R2w0dkZ0TkY1VjhOeEMwQ1EvOWYrOXJlVWpJc1g4UXcrZDFLQ1RwL0JqWWRGd3JYWjZtZklHanI2Zk55Q3lPSTJpV1p4UGNSazFxTkVibHJscmphMGw1YUJyNU5QeDBiRnJyOGcvSU1FMWdXS09adXlUc1JyZHJqNHdyeDZxUG1sbGlPMklSZ2YydnE2MWx1eVI4dkhNZ0p5Snd5VWxuM1BOTXNETjk4N0JZc1I4K1YxaFdUQ0hGanVJWHczbVdsaDc2bzBvWkRHUEErSGpqeXM2QWtoblJhbE1ZdHdhL09HQmdjN1grcUR1cnplSnhHR244VlZCVlpMbUxtcVRRSUo3VC9Ma0s0cUFNRWpRQmFIWWlxanBUTWNnMHE2VE9sNSttbXdnYmUveDFUM3A3bFRUbWlmejdoNmRHWFQ3cWRHTnNNUzlvbWFvMFJMRHRGRXBHMUpBSDB4ZkladjBjd1B2WU0weG5EZ0xLdlZScmkzZzd4M3R3bkdwSGJZTWluOC9kN2Q3WEhzV0YwZjdCK0hwOHdJMnpwYjVTSmNlRzM2V0RobzJXNWNaYjhoWkJCdG5vTjU3b3NnSWs4eGRTYzlQV24iLCJtYWMiOiIxZTc2MThlMjk2ODhmZWMzNTg2MzBkMzk4NDE1MmRjZDhmYmU1NmJhNzYyYmNmZGZhYjYxNGQ4MjM2ZjE2YTJmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687269687\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}