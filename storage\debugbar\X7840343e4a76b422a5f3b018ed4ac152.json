{"__meta": {"id": "X7840343e4a76b422a5f3b018ed4ac152", "datetime": "2025-07-14 18:28:44", "utime": **********.997905, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.500612, "end": **********.997927, "duration": 0.4973149299621582, "duration_str": "497ms", "measures": [{"label": "Booting", "start": **********.500612, "relative_start": 0, "end": **********.93014, "relative_end": **********.93014, "duration": 0.42952799797058105, "duration_str": "430ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.930152, "relative_start": 0.42953991889953613, "end": **********.99793, "relative_end": 3.0994415283203125e-06, "duration": 0.06777811050415039, "duration_str": "67.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991128, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00327, "accumulated_duration_str": "3.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.967031, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.138}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.979729, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.138, "width_percent": 16.82}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9870079, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.957, "width_percent": 18.043}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/create?6=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1871390196 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1871390196\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-106275128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-106275128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1513625952 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513625952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1804809113 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/invoice/create?6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517721361%7C25%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJQWTlnZ0tRQVROUzYxWW45RjhQbUE9PSIsInZhbHVlIjoiaWl0YTMzMVNDQVRKZEVTeDhYeTdwMEY2R01CS2hsMlJreWl6K3ZzU0t2UUZYcjhRK1N1aFlRUVdVbExrZEJoYXYvV2dqQ1U1bERHTjkrVXlRc3Jqdkp6SGc0RjVITmFKM2VBalZPcDd4M2ZmQnJSY2tIbkNuMGtldXJhRkxYbVdBV1Q5bUVNcDY3NGdZSFQ4enVCV3FDT2s5T1puN0dNcVZIcWdsbVEzZitDS3F5b0lZUm04VnNwZW1TMWhDejdDYitXWjFGUUlINXFSY0pnNDN6c1FpL0xZVSt3ZjhGd09UeGVlcm5HTytUNXZaMENKbm5FTXY3a2Y0L2FrSVJpUkRnUjhKa2Q3Q3BDb1MyQWtSak83ZTBRZjRqMm1uQ1BGclJsQXgvMWk1a1pwNjEwelk3b2liY1l0V3Fic25IaHpteXE1Q1FWdStnZ2FYYU8rcHpHRC85NjhxNG1zelI5Q0lHKzBoL05pN3Z0dDlUQXFBVnBGaGFDKzFlU1NPbEQ2ZzJ5ejB4ZzZaUitlVHJoU3dUYjFrODZvN21Jd0lvbjRzbjk5M09ad0h4OUxRTHlMVXpkQUV0VGFUUzNrd3NEc1VTS2VLZjZ3Z1JLNEdTNUVpTW1WYXhKZnVEem1DZmkrcUhkSFdwc0tVMlpxODBZeDFxVFhzekdZL1pmRlN3UVgiLCJtYWMiOiI0YmE2MGU5NDhmZTlhZTgyM2NlMmRhMjYyZjkxOTQyNjkwYjc1NjYzOWE0YzQ1ZGYzMzc3N2I0MjQ3N2U3ZjZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFNUStoVnMwd2R0MWJwSXZieGRkQ3c9PSIsInZhbHVlIjoiR2VYcm9wdUpmMkRvUXl6MlN6eGZxR1pZamdCWmtJRHYyaGJGazJ0cFcwR3ZkcDdHdUlFKzliUS9QSkMyZmVJM2Jqd0xVSmhLKzluckJxY2RYWGdBNk9oeXhXaldLLzBFUXMwRDB5amcrK0VJUUdJcU9UOCtHV1ZwWGhXOUoramF5cGdqV295VzF0bjkxOFBoQ09lazN1TTdYcU50U3lRWkw1UGtXcEJ6SjNkUHl6S245YzNnaGd4N3hvQjROMmxXRFRuRUgvM3pkTVcybFlOQmVpdGs5WU96bkRKU3lyT0xIUzdTbzMxY2lBa1hsamVsZ0tHZGFwa2x1TG9FNG40MW1pSytLTEZ5OGRBQjQ2NDRnakVHNnpFd0lzUk16Sjl1d3Y4WjdTa2UwUXpFSllIazVPUk9ITmE5U3g5UXJVb2oxaHlWMDNEb0p4NE10Wm5rSGNPZ3hOclV5SGNSL0lNSWYyN0w2aEs2OGEzUjF0ME00aUtnR3l1aExWVk5HSHprWkZ4ZEZ4d1VPdGc1eGtJSDc0V0c3R3VGSjlEN2l0bHZpb1duNDAycURPeisvNFhhZHRQWXdYdHl0d2lUTmFWR3BMWDhCa3RLVmdtdXkvWU1KQ1lRYmtaZ1Nianc0M0N6L1RRaUJaS3B2ckhQNFJpekloejNNYXVFMGowbmdYYUsiLCJtYWMiOiI4Y2E1ZmFiZTIwZmRmMzQwODNhYWIzNDdlODRkYmQ3ZWM4MGM1MDgyNGFiYzYwNWRjZGIzMzM1ODM5MTkzNjExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804809113\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-553944133 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553944133\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1310648392 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:28:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZPK3ZuTGtsM3FzZkcvcWd3K1BGQWc9PSIsInZhbHVlIjoiUzVHRVg5QjcwNkRtSmwyRnV1TWtEb1hFclFDU1lDQWRVeEkwcXBMR3JSald6WWVvVU9BazBkOEhNR1RvTk14RGFIdzFLQTc1TE00b3VTd1ZsM2g4OHFadmw0K05Jc1dXYkdkRHhGSEh1QnRJUzIycmppRDR0bmt5eGYrWmsrY1IwZXF3Y1VZVEdTaHk4TXFOMnl2dWR3VDl1bmZvZG4rNy9uWGFyQUh4YXBLN0kwL3ZkWDUwVnV4aU1PMGc1N1dqQm5MSXQ4WUJodGhkaGE5Y3d2M09GMDM5b29Cc01LUWt4WjhBZkZrSXBacVBmK1d4cW1DL3JrZmxGYTJ6Y0REYUYxeGpaZjFicE1XdGgyWWtXRmQ3Q1FMTFZIejNvUk8wdVBUdFptbXZ5MWljQVgxc1N3WEp5a2pUdjIrdzRLb3VmYzRoZWNEd1ZUQ053V2xUQldOWktlUUlJSU9zRSsxRCtwMmJjRXNIZDJueTl4WTZzaVRrSHlKOWtZSm9aTWNvcW9GRXJFZEppNE9lSEx1QjVBak1PbGZ4aElFOG5YTmZDSTF3Z0RiNDIwN2lyZTd1dDlrR2VpZWxXOTFSNHFiOWFVN2hqOENmN1B5UHViQXpUUVpPTnZKV2tHblZjYi8rdzY5MHJLcE1PVUFiQmNHdjJlaFduTzI5c3ZPSXlGdWwiLCJtYWMiOiI1Nzc4ZjkwMGY4MWIyZTAwNzc3Zjg3MmM5ZTdlNDRjNDIxMGVjZTU1ZWY0YWVmOGQ1NWQ0NTU0YTQ4OTg0OWZkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijd0cE56blk5TFFHZldzTjNCc2RQUUE9PSIsInZhbHVlIjoiK1Jobmc0dWNmMVlQOGFJSU1HQ3RMaDVDR29BWXVzZi9UcnpWWjEvVU9nUHVtNE5LT1RxemN0Q01HM1JNMUZyWmZrZG8zOTdHMTduWXRZR1dSTlBxeFhQSk1SRk5WYkdnZ0MzY255OTVyREMra0NGQ0NSazlJbEhtWjJrYVh5VnFwb2JqQWpQVnozdHp3djJtTEdoQU8wWnZ2b20rNGIyck42OGVsTXVsQXFxOExDOG1oblpKT1IvZnV4YlhnSmMrNVJyTUdXUSt0Nm9rbVRYTE5oYXM3TU53UU8rSHVNVm1YT0pXOXpCYnJOQldsdDlvYzFYaGs1WEVVS2dhaW1vU2xaZWYzM0l4d2tDMGY0TTRZNk8xWTVCdEtkY0djZHpEM1dueTI0SFRHZmdaeGwzTll1NGFpZjFuR2d2eEVkdDRhaEVoSlRrc3dMaWN1WSt6WXNKekRzcy9ndncweDhMRUhaNjJiUEswcjdiR0lkRzAvd0lwMTl5UXJmdjA2UjVHMW5DYW5aeDFiWmFEemp0V3VCUFBzSmhOcWlsZGxZTTJhTXdTVzNPNFg1NTQ2OTFNY2x3ZVJQSEF3cTc3bEJFL2g2THljeVh3M3V4T1FmSGVkdEN5WTVKOUtBTWNGL3pqVHlQSWY1ZDdHNGVOUk4xeUUyMTdxYTVaenVlUEFMR3giLCJtYWMiOiI5MWQ2YjkyZWYwNDk2MGY0OWYxOTJmODc3YjI1MTQ0OTZkN2JlODdmZTBhNTQ3ZjI3YmE0NjkxNWM2YmNiOWI5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZPK3ZuTGtsM3FzZkcvcWd3K1BGQWc9PSIsInZhbHVlIjoiUzVHRVg5QjcwNkRtSmwyRnV1TWtEb1hFclFDU1lDQWRVeEkwcXBMR3JSald6WWVvVU9BazBkOEhNR1RvTk14RGFIdzFLQTc1TE00b3VTd1ZsM2g4OHFadmw0K05Jc1dXYkdkRHhGSEh1QnRJUzIycmppRDR0bmt5eGYrWmsrY1IwZXF3Y1VZVEdTaHk4TXFOMnl2dWR3VDl1bmZvZG4rNy9uWGFyQUh4YXBLN0kwL3ZkWDUwVnV4aU1PMGc1N1dqQm5MSXQ4WUJodGhkaGE5Y3d2M09GMDM5b29Cc01LUWt4WjhBZkZrSXBacVBmK1d4cW1DL3JrZmxGYTJ6Y0REYUYxeGpaZjFicE1XdGgyWWtXRmQ3Q1FMTFZIejNvUk8wdVBUdFptbXZ5MWljQVgxc1N3WEp5a2pUdjIrdzRLb3VmYzRoZWNEd1ZUQ053V2xUQldOWktlUUlJSU9zRSsxRCtwMmJjRXNIZDJueTl4WTZzaVRrSHlKOWtZSm9aTWNvcW9GRXJFZEppNE9lSEx1QjVBak1PbGZ4aElFOG5YTmZDSTF3Z0RiNDIwN2lyZTd1dDlrR2VpZWxXOTFSNHFiOWFVN2hqOENmN1B5UHViQXpUUVpPTnZKV2tHblZjYi8rdzY5MHJLcE1PVUFiQmNHdjJlaFduTzI5c3ZPSXlGdWwiLCJtYWMiOiI1Nzc4ZjkwMGY4MWIyZTAwNzc3Zjg3MmM5ZTdlNDRjNDIxMGVjZTU1ZWY0YWVmOGQ1NWQ0NTU0YTQ4OTg0OWZkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijd0cE56blk5TFFHZldzTjNCc2RQUUE9PSIsInZhbHVlIjoiK1Jobmc0dWNmMVlQOGFJSU1HQ3RMaDVDR29BWXVzZi9UcnpWWjEvVU9nUHVtNE5LT1RxemN0Q01HM1JNMUZyWmZrZG8zOTdHMTduWXRZR1dSTlBxeFhQSk1SRk5WYkdnZ0MzY255OTVyREMra0NGQ0NSazlJbEhtWjJrYVh5VnFwb2JqQWpQVnozdHp3djJtTEdoQU8wWnZ2b20rNGIyck42OGVsTXVsQXFxOExDOG1oblpKT1IvZnV4YlhnSmMrNVJyTUdXUSt0Nm9rbVRYTE5oYXM3TU53UU8rSHVNVm1YT0pXOXpCYnJOQldsdDlvYzFYaGs1WEVVS2dhaW1vU2xaZWYzM0l4d2tDMGY0TTRZNk8xWTVCdEtkY0djZHpEM1dueTI0SFRHZmdaeGwzTll1NGFpZjFuR2d2eEVkdDRhaEVoSlRrc3dMaWN1WSt6WXNKekRzcy9ndncweDhMRUhaNjJiUEswcjdiR0lkRzAvd0lwMTl5UXJmdjA2UjVHMW5DYW5aeDFiWmFEemp0V3VCUFBzSmhOcWlsZGxZTTJhTXdTVzNPNFg1NTQ2OTFNY2x3ZVJQSEF3cTc3bEJFL2g2THljeVh3M3V4T1FmSGVkdEN5WTVKOUtBTWNGL3pqVHlQSWY1ZDdHNGVOUk4xeUUyMTdxYTVaenVlUEFMR3giLCJtYWMiOiI5MWQ2YjkyZWYwNDk2MGY0OWYxOTJmODc3YjI1MTQ0OTZkN2JlODdmZTBhNTQ3ZjI3YmE0NjkxNWM2YmNiOWI5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310648392\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/invoice/create?6=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}