{"__meta": {"id": "Xcc001bf11b0f0fece35e2dd1ee2f8b0a", "datetime": "2025-07-21 01:35:02", "utime": **********.155553, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061701.707408, "end": **********.155591, "duration": 0.4481830596923828, "duration_str": "448ms", "measures": [{"label": "Booting", "start": 1753061701.707408, "relative_start": 0, "end": **********.080198, "relative_end": **********.080198, "duration": 0.3727900981903076, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.080207, "relative_start": 0.3727991580963135, "end": **********.155593, "relative_end": 1.9073486328125e-06, "duration": 0.07538580894470215, "duration_str": "75.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02333, "accumulated_duration_str": "23.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.110646, "duration": 0.02229, "duration_str": "22.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.542}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1421702, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.542, "width_percent": 1.886}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.147974, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.428, "width_percent": 2.572}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1956431471 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1956431471\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-650436014 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-650436014\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1204161419 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204161419\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-479494893 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"150 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061696821%7C16%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjU4MXBseEpjZ2FaMXRJdTVWNy95a1E9PSIsInZhbHVlIjoiTThpOWhrNXJzUXNqVDV2Y09CZXllN1h3aHpDcGkwdGVadmt4VkFuaDFIQVUrcWJYK1JZaXo2YzIvUUgza2ZyNmkxOW5HbWtlT3ZYb1ZvbEVjTmZWdlMrdDZMU3hNRWc2NEhwVmViTFdJV0dLL09CeVpzK3JwVTJIaWUwRVI1RkRkMUZFVmZ4Q2l5TDdudVJLT1h3ZE1UYkpjekxvOXVOd05Ma1FGU2JQNUd4TXh5djFKVGtTRWIvOXNlVFFCN0JSUEJqNmQvMWRzeUwvNS9PeXZCcTVEcHBEcTR2cGJGVUI1OVVYei83SlY4NCtxUlJOUFc5bUp0eUlaRmNObkZ3dktGZ0ZCUFR3N24wMk5UeDBUQnEyS3libUMvcU1yQ3RVUmNDZEYwSWJvSDg0T3JYN1l0S2EyajMrYjljd3hONkk5ejZRbkdRM2tkZ0p1WG1oL25USmwrWEl0b2NXeDJ3Tkp1UXpLalJOWGFDMHVUVUcxZGNaMWpmaTEyNXNSZ2ZLbjBSZk1QQ1AxQ053K2swbm9PekVMc1FwdVVCNHd4V21ucVFQbEZxMzlPZWFpMTZDRVpaOTVMU2pscVptQ3BFQ3VvV1hRM3M5Y2pEMGVzckYzbHJZb1E1UGdwUU5FVzR1cXZqSnc2aUE4K2p5aGZXRU9pcENzQWl0S2I5N3FTdFIiLCJtYWMiOiJmNzRjOTNiN2M4M2IyYTk2M2U5OWI4MmQzNjczMzdmNDhlYmNmYzIyNjQ0MzU2YmQ1MTM2MjhiMDJjNzJiOWJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNQVmI2ZVFudWx5YVlrb1E0ZlZGV0E9PSIsInZhbHVlIjoib0tYUEkrUWlTWHB6aFgvTVYyQ2hHak1sLy8yT2ovTXdVVGpVUlZ2VlJiRWQwWVZiS3g1ZzRva0dhRXZ6K0dtTjZkbDI1d1JwTi9LekhBdkZLM2ZlN05OZXhITkR6VHN0K3lGTjRvTmMzN05JRjBWOXhpcG5ucE5qdklrekprUmM5RE5oRTNpUFFJVlVVeitTc3dVcks2cURESGZZT3hNNXoxL0tyaStIdklYVXJuNUQvRWxlMU51SytVekNSU1hoZzhPZk9adm1aeHQ0UHJSQkF0bFA3dUl3aEkxeTZrczQxRGI1bUhpa0k5bzdUTWk4RHl1SmZPY05TRkUyYU9YeWl5TkJoajgwQlVCVmQreG41dFF0KytDT0R4OFB5MS9BNUxsNEJOMjZxeEV5R3FOQVRMZmY0UmI3U0FmOVlKM0k2TElFSVlyWE1QMHpBSE5BYm9vaW0wUnBvL2JXNnVwMnVFWCs0SmpUZDdMOVl3ZWMycDhZQkRZek1LUE5sOHdrb0ZmdlhKY2VHR0hoNldLaVBueW9CWWFQeUx0aE9LNndlY1FmODJvSHZkcnJlenhTNTVhV1RxL0FadFMwbjZOaDY3c0lVQ2R5aFM3bXlPUmNER0tmNmluUC93d1pucDFQWnFwWWdnbEFGb2o2bEE4WlduTlFZMUZIQ21ObzBsbjciLCJtYWMiOiJlODA1OGNjMmM2YWQ0Njc0MzMyOTAzOTg1MTA5NTE1ZWU0OWE4OThhMDRmNThlY2FmNDU5MzIwNTBhZDdiNDliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-479494893\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-293818111 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293818111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-654520866 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:35:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBNclRpUHlxc1I5YitmRkdpNk5PUHc9PSIsInZhbHVlIjoiOVVuVThCSmpwRVpTZ0kxdHhqRXFhci9pQ0VxMEo4dTFid3dYdVNSd2Z4NGJSU054eWVzcFdNUnRKaVZYblJRZ3krcU54dHBWZi85VXBid01uaCtlS2NMdzlmT1NsSXNEa1BkSWNpaXozdkh1N1E0eTlqTzcyRlRQbHpuWXE0Y1JwSnNYY1VFNFoveFVQOFVHb0FHd1BRK2tlS2NTVDMvZ1I5L05SSktYVWIxRXNpN3Z6UFpUR2sxV1RxZkxKQ0VJNk8zSjJBYm9rS1hGdFN6T1krekF4T2lFWWlKUC80d3A3MEVhUC9lY0l3U2pLMUMyeElNY3ZiczZxT2pUQVBhVEtTYndjMVF6aE1ENjV4UXVVcEdqR1g0WGZuUDJyYURRUjZJQVRuRUpLZ213VEMwVCtpaGw1Rmx4Y2wybEtCUUw3UGtucUpGUGZyQ3ZDWXpTQlBKTFdiK0RLK2FPY09oNnJFT0tDeG9WNXVnTGpuZ3o4WUlxMmd5UDJwSWZoVFJEeEJhVWpnZ2x1UUFWcjE5WEdjQ2tFU2c0d3NSSGxlVzd1WURqbWZGSGR2UmV4QVRZUThrcUl2WjRXUjhHY1FJNXFIK0o4dWJ2OVZOdWkrdXFheEE3bk5LZE9xS1pEVkhDaWM3dEZRWE9OblVwakpBQUM0MGRUNzZUZ05mMTk3T0MiLCJtYWMiOiIwNzUyMzM4OTlkMmY4ODFjNjgzNWNkMTlmZmMzNzA2ZjM5ZmUyMzIyODY5ODBhYjg0YjBiMDNjNzM4MWRhZjhlIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:35:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImcwMEthZHlWV1J6ek5pbmhYaVdFcXc9PSIsInZhbHVlIjoicjQra2FQanNUck9lcmZkemFOWHNycmg2cENRbURuSEs5bnVwc0M5WUxFYVY5cnhrVWJHNGcvTXA2NnNtdEJIQVhweVVPM0hvMDBVbmp4TUZyOW1VOG4ralRnVzFBVXFESGlhTkwxRmZrOUVEM1lrSElOR1RTd1JQOVVJL1ZmOWNFS3YyYi9lZ01iaE9SbnNDbjV4a1JEak1nNFFVcGphYitPVEVTWWlWdlQzZjQwS1U5dmFOQVBaZXBTOUIraTNLVXA3Q0xoMzBiMDJXcmdISkx4bUtKYUFFWElKcTdaN2NDYW5wOXBNeGcvajJQak55VTBkbk9ZTlRQeFgwNUtyd3VoaHZBc0toZElWMTRTQVdpQ2tTM0lLZjFCOGltYlB5NE1WRWR4NTVTRzlnOVM3UHh0NXVzUUZSSWk1YmYydWZ4ZE5zejd4L0FtMGNlMk5NV2J4UDdTRkhDaVVaeGFwNTlnZUhwNnR2QWxRK1NiNnd4aUVXZndDWmdlL2FWbTZSQ2R5Z2pRbUw0WXVyWTNqL0VhWG5PVVNIQldDbUJKNTdaNXFuNGJWa3pVM0Q5cVVwMUxDUGZ5YmwxbXQ2TXlueUl5M0FETW9DUUV3b1d1SFRNbXk3UTg3S0tqb0dkcDNhQmVnRXNCcHNQNi9QSnlPU2NXNEx3SkZPYXZSSXRIM1MiLCJtYWMiOiIyYWRiNTQ3NDc3NDNkZDkzOWU2ZjYwNWY1ZmQ5MWZjYzU1NDcxNmJmNTZiZGIxZDRhZjUwNTQxODJlNWU5Nzc0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:35:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBNclRpUHlxc1I5YitmRkdpNk5PUHc9PSIsInZhbHVlIjoiOVVuVThCSmpwRVpTZ0kxdHhqRXFhci9pQ0VxMEo4dTFid3dYdVNSd2Z4NGJSU054eWVzcFdNUnRKaVZYblJRZ3krcU54dHBWZi85VXBid01uaCtlS2NMdzlmT1NsSXNEa1BkSWNpaXozdkh1N1E0eTlqTzcyRlRQbHpuWXE0Y1JwSnNYY1VFNFoveFVQOFVHb0FHd1BRK2tlS2NTVDMvZ1I5L05SSktYVWIxRXNpN3Z6UFpUR2sxV1RxZkxKQ0VJNk8zSjJBYm9rS1hGdFN6T1krekF4T2lFWWlKUC80d3A3MEVhUC9lY0l3U2pLMUMyeElNY3ZiczZxT2pUQVBhVEtTYndjMVF6aE1ENjV4UXVVcEdqR1g0WGZuUDJyYURRUjZJQVRuRUpLZ213VEMwVCtpaGw1Rmx4Y2wybEtCUUw3UGtucUpGUGZyQ3ZDWXpTQlBKTFdiK0RLK2FPY09oNnJFT0tDeG9WNXVnTGpuZ3o4WUlxMmd5UDJwSWZoVFJEeEJhVWpnZ2x1UUFWcjE5WEdjQ2tFU2c0d3NSSGxlVzd1WURqbWZGSGR2UmV4QVRZUThrcUl2WjRXUjhHY1FJNXFIK0o4dWJ2OVZOdWkrdXFheEE3bk5LZE9xS1pEVkhDaWM3dEZRWE9OblVwakpBQUM0MGRUNzZUZ05mMTk3T0MiLCJtYWMiOiIwNzUyMzM4OTlkMmY4ODFjNjgzNWNkMTlmZmMzNzA2ZjM5ZmUyMzIyODY5ODBhYjg0YjBiMDNjNzM4MWRhZjhlIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:35:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImcwMEthZHlWV1J6ek5pbmhYaVdFcXc9PSIsInZhbHVlIjoicjQra2FQanNUck9lcmZkemFOWHNycmg2cENRbURuSEs5bnVwc0M5WUxFYVY5cnhrVWJHNGcvTXA2NnNtdEJIQVhweVVPM0hvMDBVbmp4TUZyOW1VOG4ralRnVzFBVXFESGlhTkwxRmZrOUVEM1lrSElOR1RTd1JQOVVJL1ZmOWNFS3YyYi9lZ01iaE9SbnNDbjV4a1JEak1nNFFVcGphYitPVEVTWWlWdlQzZjQwS1U5dmFOQVBaZXBTOUIraTNLVXA3Q0xoMzBiMDJXcmdISkx4bUtKYUFFWElKcTdaN2NDYW5wOXBNeGcvajJQak55VTBkbk9ZTlRQeFgwNUtyd3VoaHZBc0toZElWMTRTQVdpQ2tTM0lLZjFCOGltYlB5NE1WRWR4NTVTRzlnOVM3UHh0NXVzUUZSSWk1YmYydWZ4ZE5zejd4L0FtMGNlMk5NV2J4UDdTRkhDaVVaeGFwNTlnZUhwNnR2QWxRK1NiNnd4aUVXZndDWmdlL2FWbTZSQ2R5Z2pRbUw0WXVyWTNqL0VhWG5PVVNIQldDbUJKNTdaNXFuNGJWa3pVM0Q5cVVwMUxDUGZ5YmwxbXQ2TXlueUl5M0FETW9DUUV3b1d1SFRNbXk3UTg3S0tqb0dkcDNhQmVnRXNCcHNQNi9QSnlPU2NXNEx3SkZPYXZSSXRIM1MiLCJtYWMiOiIyYWRiNTQ3NDc3NDNkZDkzOWU2ZjYwNWY1ZmQ5MWZjYzU1NDcxNmJmNTZiZGIxZDRhZjUwNTQxODJlNWU5Nzc0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:35:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654520866\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1849704131 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"150 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849704131\", {\"maxDepth\":0})</script>\n"}}