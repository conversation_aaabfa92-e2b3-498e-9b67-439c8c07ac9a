{"__meta": {"id": "X8b930c1bf9848987812451483995ee89", "datetime": "2025-07-21 01:17:39", "utime": **********.406357, "method": "GET", "uri": "/add-to-cart/1896/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060658.919674, "end": **********.406371, "duration": 0.4866971969604492, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1753060658.919674, "relative_start": 0, "end": **********.315243, "relative_end": **********.315243, "duration": 0.3955690860748291, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.315252, "relative_start": 0.39557814598083496, "end": **********.406373, "relative_end": 1.9073486328125e-06, "duration": 0.09112095832824707, "duration_str": "91.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49571064, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1452\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1452-1676</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00689, "accumulated_duration_str": "6.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3540359, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.705}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.36555, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.705, "width_percent": 6.676}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.380978, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.382, "width_percent": 9.869}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3835292, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 43.251, "width_percent": 6.386}, {"sql": "select * from `product_services` where `product_services`.`id` = '1896' limit 1", "type": "query", "params": [], "bindings": ["1896"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1456}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.388646, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1456", "source": "app/Http/Controllers/ProductServiceController.php:1456", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1456", "ajax": false, "filename": "ProductServiceController.php", "line": "1456"}, "connection": "kdmkjkqknb", "start_percent": 49.637, "width_percent": 5.66}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 1896 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["1896", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1460}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.393265, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 55.298, "width_percent": 39.768}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1529}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.397483, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 95.065, "width_percent": 4.935}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-591444142 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591444142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.387641, "xdebug_link": null}]}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  1897 => array:9 [\n    \"name\" => \"option- خيار\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1897\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1896 => array:8 [\n    \"name\" => \"zucchini-كوسا\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"id\" => \"1896\"\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/1896/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1279507882 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1279507882\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBxRndFd0FaZ0N3WmFZN0padHFNa0E9PSIsInZhbHVlIjoiV2xlckhYc21yV08vaVZBQ0kzTXY2U2ZwbS80NldPQ2xZc25iZEgzcFkxazEyeEIyRGNuL05GSk5ZNjlncDNlUXRseFJFTW1FZVBKQjk0bmxCYzV1dDN4U2NnTk1KbWRrMGkvREp0ZVJRbVA4N0pMazFtaFZIeUVtUzdGczlnczh6QWNjT1kvOEJrbHdDcmxqL1JGeC95Ui83NHg5YXJPUDl0NXdjeDFXTEpEUm5WZkFINWVlM29EWW8vN29OOVB3Z0hRcWllc1VURFlCVlVFZFF4U0I0YWhSbFh2M3JpUFZDSTRhaXVyY0Z3Q0Z2aTFGUzFhUmVGMHpvTzVMck1WY28venFnV1RrMlo5d1dZcEl3Zmh0UE00SFJDcXB1ME83VS8zSUZQeU5rUTlZVGpKN3YzUXlJaklPUzNNQi8xYU9PajFXTUhkSzdLVlBXY09VcWdFaUZQS2c4RjNhbTdrTzRFYXg5alB6cnZtZVMzMWhoVUQ1ZzlCUU5tMXErWXdYdlpwZDFjRmFPZ1lHajdqYTViZkw2SDlnWUtlMGJlb2pyQlBSMmsrMFk4UkRZSURkTlpVbERnYmFML21pUFZjS1BYT1FvQ3dKd2FpTUlic2tEQ1dOY0d1QzdaK3RRbGNMcjhvb0ZlQmJ3YlhXUXJYZC94VkdKemgwY0R0ZmZWVWsiLCJtYWMiOiIzNDU3MDhhNzNlMzJkNmQ2YzE0OWUwZjlhY2YzYjdlYjMwZGMyZjhiNTg3OGQ0ZTYxOWMyMGFlMjcwMDNmNGI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRobHhYa3FPdFJzaHh5MlhiME5PUXc9PSIsInZhbHVlIjoiY2xiS1RJZ0tTU1NCRnNuWXhucURUTmF6ZkVYcE91MmQ4bzJVeUt3TmFtUkMwSWQ3RzRLRThQbDh2ZGF1Y1g5S21ZTGNWQVR2QXZxQytHU3hJbXBjZjlxd2VZdlVzaFVDMHFNK1NyV3V2U0xxQU9rNEVEeFdBeDdMMmRpeFV5Y2wxcFRqWC9zSStwcHhoZnBYNnhHZUhqYW1zWjJxQVhTM0R3STFQdHhLazk3UndFdWxtcDB1cXNDRlJwVjFhckdtWlRKNE9PbmhrS0JCQm1BdkpVU1hpbmx4N3hQTnNNZzZncWFwcU1zVFp1OEVCbDNYTWd0SHoxUVcxN3JycUxWbVpxODMrNHdHNzZBN2YrMHlaU0NTVWxnalpFSVhCYnluLzg0UWRnNFlsd09OU050dzhQU3NWR1k2cE0yZXJFa0Znb0k5YjlLNk5kZXpUUFJMZVZKbkxEZ1JleGFSdnByN0RKQkZ6VmVFYlJoQW1oKzgvOWE3ZGFQZlgxTXU3OUtQbkNJN3JJUHlaU3ljeFhGQ3NhOWdQTjNPRUtPd2RXWnliRnRsWjZJMTNRdjYwbXlMNVg1dXB0NWtLcE9LR3ZjZ0JlbXora053aXpDSEFDS3ZpaURJbWtvdkhlMm5xYm81dmR2VEd0STVUWUlsOEdYUVk0M2F6OXgyWCtYRktyQlkiLCJtYWMiOiIyZjUyZWVkNGNmYTMyYWU2MDk0M2ZiMTFkNDRiMWYwNmVjN2U4M2M1MWVmZTdiNjAzZmYyNjA5MDg4ZDUwMGJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1764130255 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1764130255\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVZUytYQkRYdml2ZTVwOFRTbjd3ZGc9PSIsInZhbHVlIjoicTljN0NuQmFPTmlvZUVpRzNTbFFJSWJHc0g5dVFsZTFOUGtQdUp2NTBWY1dGOUdZZjhWSXdocVpaWExmNHRzSVpTaGNEdmh3RmYrTHdIMXEwbmpPZGxiaWNkM2hRMWlJM1VQdEtBTlE2eVIvc0w4RTUybTc1Tko2ek1reWl3bE9ES2duSWFPV1BUMDJabWNhME1uM3Y0b2tqbTBTQlIzVHh0WlNLSTJGdkhPT3VLc3FwOXJrelJxU0x4Z3NhKzB2dE5mams4ZlVXeVcvMm54OEhBRVdGcUxXMnFiK3ZBSzJ3T2s1ODlsTTloS0lhTXpsT2hZK1ljUTJHMGNpaFd5TDFoR2ZIMmZ1THBNYmtnanIrT3lOeUpHRG1Ra1haeHlMeml3QjIyQUlXUDFDa3BCdW5lR0ZwVE5uMkJvZTJYcDJWMzhNVXViM3BET01yVjBxT1VsRDBnMm80Ym5jdWJjNjM0RUh5ZE05ZnlPN3hnK2ZTbTBPZnJXSXNLTXlCY2VOZzJ5bko1ZFVLZmdlT010bjBlaG9FS2NMV2w1bHl3RVhtVmdKbHZZVVdXaEdMak1hR0tEYUFQK2c5cnJWN05uVFlyd1FJMnJaZmpFTFVEMGFnRVlpYmJKNjRIVEpLYjd3WjRWbXl4cU5vckZLc01Qa3pmdXljSlg1RUw0NTUyem4iLCJtYWMiOiJmZTlhYzg1YTkyZTljYzFkNWM1OWM0MThkYTQyNjY5NTAxNGMzMWUxMWQ1OTU2MjM3MDY1YTc4OGIyNTlmNTk4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFxa3oyTWE3YTk4ZmM3SG41cWdPTWc9PSIsInZhbHVlIjoicnFaRS95bmFVMzlIcFM3cVJTSEZvbGtpK1BMSFRLYmluYVRPdVpaNmI2TFc5RCt0UWZGSk5yNS9kM1pkU3pObzN2K25BcXJIbTdkbzhGUVNGeU1sQlJTQ2RhRTZSSlVjdHpOMERmRmRXV0pPZzVGcUllL0lGejZSWk1JdGJoKzNyY0ZpYmI4VjdrdUEvL09GUFN4bnk1WEY1em9ZdHhaZjVXTFJCRUpMVk81b0dBaGJMN1ZYVGpTNTQyUlp5cW1TWEM3cUJLVmkzSE5kVU5sREN4eFBxQXJwNGpKSDV6TVRvd3VWYm1mRE5zQWt5dzBnVlpKU1pQTmdlSktyZktDc3NadjY4Lzh1c0ZsSUhUcjJSSldRWDFmNHlZOGROVEpmSGFKNi9QLytzbHVhL09neVljTWpjeDJVc1dBeE1UOTdTaXl3aC9DOURwcXV0a0VOMkR1azlKdmVzeTIxNitnUmFURml5Yzl2QUVwZU1MSElPYWRiV0tweXNIalhMU1BacUIyRFJpQVpVWTJFdjZvZTlWTkhmVURLSWJCWW5BWmpGNWN4WHpyRGN0dVFjYW9LSUhCU2M4VC9FQm5HdmtJaE1tZk1VMnhvbElwNDAvQzJGcnlPQXRRekRqUkpkZ1RhMWx5U0hSL3JtQ241N1ZUSG1Sby9KRlRpOUpjZ3QzbEgiLCJtYWMiOiIyYzkzMWE2OTdmYWI2NDRiY2JhMjJkODc4ZGViMTU4Y2JmNDgzNGY0YjQ4MGMxNzQ5Y2NjZDViMDRkYWZlZDgzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVZUytYQkRYdml2ZTVwOFRTbjd3ZGc9PSIsInZhbHVlIjoicTljN0NuQmFPTmlvZUVpRzNTbFFJSWJHc0g5dVFsZTFOUGtQdUp2NTBWY1dGOUdZZjhWSXdocVpaWExmNHRzSVpTaGNEdmh3RmYrTHdIMXEwbmpPZGxiaWNkM2hRMWlJM1VQdEtBTlE2eVIvc0w4RTUybTc1Tko2ek1reWl3bE9ES2duSWFPV1BUMDJabWNhME1uM3Y0b2tqbTBTQlIzVHh0WlNLSTJGdkhPT3VLc3FwOXJrelJxU0x4Z3NhKzB2dE5mams4ZlVXeVcvMm54OEhBRVdGcUxXMnFiK3ZBSzJ3T2s1ODlsTTloS0lhTXpsT2hZK1ljUTJHMGNpaFd5TDFoR2ZIMmZ1THBNYmtnanIrT3lOeUpHRG1Ra1haeHlMeml3QjIyQUlXUDFDa3BCdW5lR0ZwVE5uMkJvZTJYcDJWMzhNVXViM3BET01yVjBxT1VsRDBnMm80Ym5jdWJjNjM0RUh5ZE05ZnlPN3hnK2ZTbTBPZnJXSXNLTXlCY2VOZzJ5bko1ZFVLZmdlT010bjBlaG9FS2NMV2w1bHl3RVhtVmdKbHZZVVdXaEdMak1hR0tEYUFQK2c5cnJWN05uVFlyd1FJMnJaZmpFTFVEMGFnRVlpYmJKNjRIVEpLYjd3WjRWbXl4cU5vckZLc01Qa3pmdXljSlg1RUw0NTUyem4iLCJtYWMiOiJmZTlhYzg1YTkyZTljYzFkNWM1OWM0MThkYTQyNjY5NTAxNGMzMWUxMWQ1OTU2MjM3MDY1YTc4OGIyNTlmNTk4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFxa3oyTWE3YTk4ZmM3SG41cWdPTWc9PSIsInZhbHVlIjoicnFaRS95bmFVMzlIcFM3cVJTSEZvbGtpK1BMSFRLYmluYVRPdVpaNmI2TFc5RCt0UWZGSk5yNS9kM1pkU3pObzN2K25BcXJIbTdkbzhGUVNGeU1sQlJTQ2RhRTZSSlVjdHpOMERmRmRXV0pPZzVGcUllL0lGejZSWk1JdGJoKzNyY0ZpYmI4VjdrdUEvL09GUFN4bnk1WEY1em9ZdHhaZjVXTFJCRUpMVk81b0dBaGJMN1ZYVGpTNTQyUlp5cW1TWEM3cUJLVmkzSE5kVU5sREN4eFBxQXJwNGpKSDV6TVRvd3VWYm1mRE5zQWt5dzBnVlpKU1pQTmdlSktyZktDc3NadjY4Lzh1c0ZsSUhUcjJSSldRWDFmNHlZOGROVEpmSGFKNi9QLytzbHVhL09neVljTWpjeDJVc1dBeE1UOTdTaXl3aC9DOURwcXV0a0VOMkR1azlKdmVzeTIxNitnUmFURml5Yzl2QUVwZU1MSElPYWRiV0tweXNIalhMU1BacUIyRFJpQVpVWTJFdjZvZTlWTkhmVURLSWJCWW5BWmpGNWN4WHpyRGN0dVFjYW9LSUhCU2M4VC9FQm5HdmtJaE1tZk1VMnhvbElwNDAvQzJGcnlPQXRRekRqUkpkZ1RhMWx5U0hSL3JtQ241N1ZUSG1Sby9KRlRpOUpjZ3QzbEgiLCJtYWMiOiIyYzkzMWE2OTdmYWI2NDRiY2JhMjJkODc4ZGViMTU4Y2JmNDgzNGY0YjQ4MGMxNzQ5Y2NjZDViMDRkYWZlZDgzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1193037972 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1897</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">option- &#1582;&#1610;&#1575;&#1585;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1897</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1896</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">zucchini-&#1603;&#1608;&#1587;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1896</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193037972\", {\"maxDepth\":0})</script>\n"}}