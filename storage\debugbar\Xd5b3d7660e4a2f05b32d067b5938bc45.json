{"__meta": {"id": "Xd5b3d7660e4a2f05b32d067b5938bc45", "datetime": "2025-07-14 18:35:34", "utime": **********.751376, "method": "GET", "uri": "/product-expiry", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.196048, "end": **********.751394, "duration": 0.5553460121154785, "duration_str": "555ms", "measures": [{"label": "Booting", "start": **********.196048, "relative_start": 0, "end": **********.634175, "relative_end": **********.634175, "duration": 0.4381270408630371, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.634186, "relative_start": 0.4381380081176758, "end": **********.751397, "relative_end": 2.86102294921875e-06, "duration": 0.11721086502075195, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48257888, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-expiry", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ProductExpiryController@index", "namespace": null, "prefix": "", "where": [], "as": "product.expiry.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductExpiryController.php&line=20\" onclick=\"\">app/Http/Controllers/ProductExpiryController.php:20-232</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.032, "accumulated_duration_str": "32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.669133, "duration": 0.02991, "duration_str": "29.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.469}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.715798, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.469, "width_percent": 2.313}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.730989, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 95.781, "width_percent": 2.969}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.733732, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.75, "width_percent": 1.25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show product expiry, result => null, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1357481722 data-indent-pad=\"  \"><span class=sf-dump-note>show product expiry</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">show product expiry</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357481722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.737628, "xdebug_link": null}]}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/product-expiry\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "error": "تم رفض الإذن.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/product-expiry", "status_code": "<pre class=sf-dump id=sf-dump-1090911935 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1090911935\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1298615220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298615220\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1249833222 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1249833222\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1691013918 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752518033032%7C49%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVaMFRhbEE5RGtYYTVaN3dwMnQ0UFE9PSIsInZhbHVlIjoiSGtvaTFtWTNveERRckw4QStCNXNwLy9xbVhHYmdDVENZbmMzN1ZOemRmNGNyNDV0OFQ3eEdSN3hpTDhZa0lqRWxJMW8wOWs0Z0xHbTV6OHQ4dFQ3UWRoTmNzZEVNWGd6Zk4zQVNZYnBoQUZjNlprSTNiZ1FyLzN5SWdaT0xTM3R6bGF3bjdqcDVISmo0bVNWbWU4SzZ3RDk5L0VqYmVWWTlQZk94Z2R1MFFYZHZlUlNWNUsxY20zbmJOZHo1N0tGUGxTalVSRkwyaU00RXZIYlhlQkVURTY3ZjgrQVVtVEIwY3EwQWcxYmFxUXlkb1B2ZVpSWnEyd2NCSHh6c0xkNHFITVh2MUxvaGZpZHBSTFFvVWtNSGszMWs0WHgvR2VEUmVoQzZKWVlRRnNrcGpQWHYxdG5yQkRNOUhYK0dHT1ZSaE9maWVDMEZqK2g2dnh3RmZrSmx0OE4xSUtFQ0dCR00xaHFjT2Y4V1lPeGZjZG16Z3FwTGpiZlVtZUVPZ1B0SHgrdTVvTitwUEg3NXpUdlFlMElmcGt4UWkrdUcrOHBMdWtiWSs1bzRjV1BPam1qeXdzN1IxRE5aSkFtZDBZTlBFL0lwRlJDSUJhZitxWmV5Tlc0Ym5aRGtyREVWT3VzSHorTE5CNUUvVEdmWlpaeEZSUUgwRlZpSjJ4YkwwdWUiLCJtYWMiOiJlOGI2MWRhNTA2NGJiOWUwZjg3M2Y4MzEzNDUxZjRkNGE1MzEwNjk3MDEyZGRjNzlkYjg4MzVkNDEwNTJmMGY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJPUnozWTNEMmlmYW5TTzJ5VlhySkE9PSIsInZhbHVlIjoiTmx3bGxUc0tpQ3F0Y0lqNkdKUmdFb1lUQ2JmNWI5aUlHZFFRSmhZN1ZOSXREbzArdUxVNkt1NWM4NG5ZSkl1a3RTdGFhT1R6UG82SEZnVjlVb3NkOTFzcHlmbFhFcXcwM3c5U2hIMFJiVEp1cUpBR0NaVktqS092WXMzSmg3ZHFKUG9uZW9rQlJkUll5dGRKL1dOanBYVEtXaVlGRWpTYi9SWDA3WEkwMUR1VnFTaGdZZGxqRkxvUW9tczVsaE9ZMTZPWGk5MkNUekxyN0NPTTJPdzVqQVNOMnhvTVhFR1hPcCsza0Q3cW9kSnM5VHJSYURsQVdoUzFxWHZ1ZkhXYzkrMEkvbDZxaGRYUlA4TGlIWHZjak40VmN5VXlxL2pZcjBKMk10bDhKWkxsSVJZTlZaYkFhVUZFcVY1VDdqb1BEcFpIbjZFTlBzSlAxSTUxWGtMdEF6UndFR2FYQ21NUy9DRjRibWxYMlF3T0xGL2UzMDlHWlNUazN5cTdpM3ZIenhHOHJ5dDFZS051Z3dlSnNhNzVKSUp3b25zbmJOSG1hWkE2R0xEUTBsbWF4dEdMWTFEMm84a1YvdTZ3LytUQ2E0NktEdmJNZFhveW5GWnEwZ24xN1gya3ZJTlhhM1doVXBINEZERWU0YWZ1OE9EZTdKbkI3cUtyNU5Pbm0wWGEiLCJtYWMiOiI0ZmQ0OWM5MDQ1OTUwODVkZjAzMmRhZGQ4Y2YxZTY4ZjIzOGUyY2E0YTI2M2ZjYTI2N2FjNzJhOTY2ODRkNjcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691013918\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-651471601 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651471601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1179938293 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:35:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldRU0kyN0N3NFlwS3F3TXVDNVJ4Q1E9PSIsInZhbHVlIjoiR2tqOGZpT2pPUGN4K09EVmdKNUR6dFFVMGlUL296US9SdFJ6UzBuZFpqYnhicDdGTnU4VThna0J6Y0swakY4N2tqUnlOZ0x6aUVwc0krOW5ZaWg3WU95cTBrKzIyOXZFZEF6aXF4d3E0OXg1OGtFUTRNWCtaYTRCb2YraHcvRGNJb05lbEZndEdXQmZtaWNTcnZkMDRuOElmdzNwdE1IRTRnVVVVRW00eUQ2SVNTOGVteW9SQVZtckJSVTZ5OXJzdmczU00zSTd5VVBlTURUV2ltTUdIeDBFeVlIc0ZCYTliWUtnak1Wek56NVROMXRlNFVxSEdLYUJpOVJ6bkZlUWVWcFFLbUR5WXBQSDJXamgzSTIxaVYzaUVjMGc0MnJrd2NWWXU3Zjl4QjJ0Y0ZrVkphSk1FOUhuVUZDRnRQZ3dpQkFabS9pNldURHEzUWcxbXZ4VUhNM2xuYmNjUk5BeWR6ZFVpTVYvZmR2ajAwZFd2b2twTjZIT3doZ3YzQzBFUVBsN3FyU1d0WXNSWTA1RGdFQW9STEtkS3ZsMnVZWFJsSHlobHVkOStXOVB6azNYV2drWkhFVHNjMG04QUF4VGRITEt5QVFBd0MwYTQvL3pMa0dnQ3lSMUVNb2pVMC9YdW14U2EwcGVZMXM1eE4rU0xXYjBIYTg3alArbDQxc20iLCJtYWMiOiJjOTI0OGUyYzYyMGFkNDJiNGIwZDg1ZjhkMzdiMjA5M2U5MzJkMDY1YWRmMmEzODhmNGI5NjQ2ZWRkMThmNDdmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:35:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFHNkQ4WDZKazIwM2xXcDIra3JCMnc9PSIsInZhbHVlIjoiQjBWZUNnZm54c1dDMmxSaytQbHovZXBVR3h3c05yNWxUamhSbWZMRVdTYjlVcENTNU9paHh6REcvOHNuSGZtVnhrZU9jR2R5ZEk2dXA2aDZaS0l6Nnp3cEtGZGtDWDFSV1dwYllJWU1MRjZ2Y1ZGSCtOem5wL1JNVnFWMFAvZEYvTVgxWEJGS0ZFbEhPYm9QdFZGMlhrcmRFNStmQm1CRWtKNmVmOHNSZlErMFdvd1QrN1BmRUR6K0h5dnJsK3VsV3orakRycWUwLysrNDNwejFJN3ZSMUhOTTM2ZHNmOXI2a2liSFZ1UWxsOHludXA2VUJRa2VuQ1NYYm1HUTAybFBDTEN2anBJYnM3QXIyMEhWUDZaaEx6dXRYWTBhR3EvOWQ2TnZIdFN1c3Z6VmFnMktEdzFydkwzbWtqK1k0cWI2UDlhbDVnMThROHppM00xYXg1NzRzNVorZlhnU2JWbmtGVStjaHB1SDFSemVvKzZrbnFGOE1UMmI1UUFYL0VpZ2FqSUJ3dlVFUGVKWitkZGJOdGZZVktjeUYxT2NKTUxLeXpMMjRRRnJJVVZXcE1MQkkrbkRwaWZvd0t1UDl6Wkk5cGNjWDl0OWtuYmRsT29MZmhkTjgrWXJOU1lTemRWcENoSmRIdzdKdmh0MG5LYVM0Z2JxSnZ2eGpzd1RIekciLCJtYWMiOiIxZmM1ZDE4MTgzNzMwZGIxNzY5YWE1OTE4OGMwOTNmNmNjMWQ2ODYyNjk2Zjc0NzgxNjZkNTA1MTZhNTRkMTM5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:35:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldRU0kyN0N3NFlwS3F3TXVDNVJ4Q1E9PSIsInZhbHVlIjoiR2tqOGZpT2pPUGN4K09EVmdKNUR6dFFVMGlUL296US9SdFJ6UzBuZFpqYnhicDdGTnU4VThna0J6Y0swakY4N2tqUnlOZ0x6aUVwc0krOW5ZaWg3WU95cTBrKzIyOXZFZEF6aXF4d3E0OXg1OGtFUTRNWCtaYTRCb2YraHcvRGNJb05lbEZndEdXQmZtaWNTcnZkMDRuOElmdzNwdE1IRTRnVVVVRW00eUQ2SVNTOGVteW9SQVZtckJSVTZ5OXJzdmczU00zSTd5VVBlTURUV2ltTUdIeDBFeVlIc0ZCYTliWUtnak1Wek56NVROMXRlNFVxSEdLYUJpOVJ6bkZlUWVWcFFLbUR5WXBQSDJXamgzSTIxaVYzaUVjMGc0MnJrd2NWWXU3Zjl4QjJ0Y0ZrVkphSk1FOUhuVUZDRnRQZ3dpQkFabS9pNldURHEzUWcxbXZ4VUhNM2xuYmNjUk5BeWR6ZFVpTVYvZmR2ajAwZFd2b2twTjZIT3doZ3YzQzBFUVBsN3FyU1d0WXNSWTA1RGdFQW9STEtkS3ZsMnVZWFJsSHlobHVkOStXOVB6azNYV2drWkhFVHNjMG04QUF4VGRITEt5QVFBd0MwYTQvL3pMa0dnQ3lSMUVNb2pVMC9YdW14U2EwcGVZMXM1eE4rU0xXYjBIYTg3alArbDQxc20iLCJtYWMiOiJjOTI0OGUyYzYyMGFkNDJiNGIwZDg1ZjhkMzdiMjA5M2U5MzJkMDY1YWRmMmEzODhmNGI5NjQ2ZWRkMThmNDdmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:35:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFHNkQ4WDZKazIwM2xXcDIra3JCMnc9PSIsInZhbHVlIjoiQjBWZUNnZm54c1dDMmxSaytQbHovZXBVR3h3c05yNWxUamhSbWZMRVdTYjlVcENTNU9paHh6REcvOHNuSGZtVnhrZU9jR2R5ZEk2dXA2aDZaS0l6Nnp3cEtGZGtDWDFSV1dwYllJWU1MRjZ2Y1ZGSCtOem5wL1JNVnFWMFAvZEYvTVgxWEJGS0ZFbEhPYm9QdFZGMlhrcmRFNStmQm1CRWtKNmVmOHNSZlErMFdvd1QrN1BmRUR6K0h5dnJsK3VsV3orakRycWUwLysrNDNwejFJN3ZSMUhOTTM2ZHNmOXI2a2liSFZ1UWxsOHludXA2VUJRa2VuQ1NYYm1HUTAybFBDTEN2anBJYnM3QXIyMEhWUDZaaEx6dXRYWTBhR3EvOWQ2TnZIdFN1c3Z6VmFnMktEdzFydkwzbWtqK1k0cWI2UDlhbDVnMThROHppM00xYXg1NzRzNVorZlhnU2JWbmtGVStjaHB1SDFSemVvKzZrbnFGOE1UMmI1UUFYL0VpZ2FqSUJ3dlVFUGVKWitkZGJOdGZZVktjeUYxT2NKTUxLeXpMMjRRRnJJVVZXcE1MQkkrbkRwaWZvd0t1UDl6Wkk5cGNjWDl0OWtuYmRsT29MZmhkTjgrWXJOU1lTemRWcENoSmRIdzdKdmh0MG5LYVM0Z2JxSnZ2eGpzd1RIekciLCJtYWMiOiIxZmM1ZDE4MTgzNzMwZGIxNzY5YWE1OTE4OGMwOTNmNmNjMWQ2ODYyNjk2Zjc0NzgxNjZkNTA1MTZhNTRkMTM5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:35:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179938293\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1917815373 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/product-expiry</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1578;&#1605; &#1585;&#1601;&#1590; &#1575;&#1604;&#1573;&#1584;&#1606;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917815373\", {\"maxDepth\":0})</script>\n"}}