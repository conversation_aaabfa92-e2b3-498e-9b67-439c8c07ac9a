<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Models\Pos;
use App\Models\PosPayment;
use App\Models\PosProduct;
use App\Models\User;
use App\Models\warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;

class InvoiceProcessingController extends Controller
{
    /**
     * عرض قائمة فواتير نقاط البيع
     */
    public function index()
    {
        // استخدام نفس طريقة استرجاع الفواتير كما في تقارير نقاط البيع
        $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
            ->with(['customer', 'warehouse', 'posPayment'])
            ->orderBy('id', 'desc')
            ->get();

        // إضافة معلومات الدفع لكل فاتورة
        foreach ($posPayments as $pos) {
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }
        }

        return view('invoice_processing.index', compact('posPayments'));
    }

    /**
     * عرض تفاصيل فاتورة محددة
     */
    public function show($ids)
    {
        try {
            $id = Crypt::decrypt($ids);
        } catch (\Throwable $th) {
            // إذا فشل فك التشفير، نستخدم المعرف كما هو
            $id = $ids;
        }

        $pos = Pos::find($id);

        if (!$pos) {
            return redirect()->route('invoice.processing.index')->with('error', __('الفاتورة غير موجودة.'));
        }

        $posPayment = PosPayment::where('pos_id', $pos->id)->first();
        $customer = $pos->customer;
        $items = $pos->items;

        // إذا لم يكن هناك معلومات دفع، نقوم بإنشاء كائن فارغ
        if (!$posPayment) {
            $posPayment = new PosPayment();
            $posPayment->payment_type = 'cash';
            $posPayment->amount = $pos->getTotal();
            $posPayment->discount = $pos->getTotalDiscount();
            $posPayment->date = $pos->pos_date;
        }

        return view('invoice_processing.show', compact('pos', 'customer', 'items', 'posPayment'));
    }

    /**
     * تصدير الفاتورة كملف PDF
     */
    public function pdf($ids)
    {
        try {
            $id = Crypt::decrypt($ids);
        } catch (\Throwable $th) {
            // إذا فشل فك التشفير، قد يكون المعرف غير مشفر
            $id = $ids;
        }

        $pos = Pos::find($id);

        if (!$pos) {
            return redirect()->route('invoice.processing.index')->with('error', __('الفاتورة غير موجودة.'));
        }

        try {
            // استخدام نفس طريقة عرض PDF الموجودة في النظام
            return redirect()->route('pos.pdf', Crypt::encrypt($pos->id));
        } catch (\Throwable $th) {
            return redirect()->route('invoice.processing.index')->with('error', __('حدث خطأ أثناء إنشاء ملف PDF.'));
        }
    }

    /**
     * عرض ملخص فواتير نقاط البيع (POS Summary)
     */
    public function summary()
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // استرجاع جميع فواتير نقاط البيع للمستخدم الحالي
        $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
            ->with(['customer', 'warehouse', 'posPayment'])
            ->orderBy('id', 'desc')
            ->get();

        // إضافة معلومات الدفع لكل فاتورة
        foreach ($posPayments as $pos) {
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }
        }

        return view('invoice_processing.summary', compact('posPayments'));
    }

    /**
     * عرض ملخص فواتير نقاط البيع في قسم إدارة العمليات المالية (POS Summary in Financial Operations)
     */
    public function posSummary()
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // استرجاع جميع فواتير نقاط البيع للمستخدم الحالي
        $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
            ->with(['customer', 'warehouse', 'posPayment', 'createdBy'])
            ->orderBy('id', 'desc')
            ->get();

        // إضافة معلومات الدفع لكل فاتورة
        foreach ($posPayments as $pos) {
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }
        }

        return view('invoice_processing.pos_summary', compact('posPayments'));
    }

    /**
     * عرض معالج فواتير البيع في قسم إدارة العمليات المالية
     */
    public function invoiceProcessor(Request $request)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // جلب بيانات الفلاتر
        $warehouses = \App\Models\warehouse::where('created_by', \Auth::user()->creatorId())
            ->get()
            ->pluck('name', 'id');
        $warehouses->prepend(__('جميع المستودعات'), '');

        // طرق الدفع المتاحة
        $paymentMethods = [
            '' => __('جميع طرق الدفع'),
            'cash' => __('نقد'),
            'network' => __('شبكة'),
            'split' => __('دفع مقسم'),
            'delivery' => __('تحصيل من مندوب التوصيل'),
            'unpaid' => __('غير مدفوع'),
        ];

        // بناء الاستعلام مع الفلاتر
        $query = Pos::with(['customer', 'warehouse', 'posPayment', 'createdBy', 'shift', 'shift.creator']);

        // تطبيق فلتر التاريخ الافتراضي (اليوم الحالي) إذا لم يتم تحديد أي فلاتر
        $hasFilters = $request->hasAny(['date_from', 'date_to', 'warehouse_id', 'payment_method', 'customer_search', 'creator_search']);

        if (!$hasFilters) {
            // إذا لم توجد فلاتر، اعرض بيانات اليوم الحالي فقط
            $today = now()->format('Y-m-d');
            $query->whereDate('pos_date', $today);

            // تعيين القيم الافتراضية للعرض في النموذج
            $request->merge([
                'date_from' => $today,
                'date_to' => $today
            ]);
        } else {
            // فلتر التاريخ المخصص
            if ($request->filled('date_from')) {
                $query->whereDate('pos_date', '>=', $request->date_from);
            }
            if ($request->filled('date_to')) {
                $query->whereDate('pos_date', '<=', $request->date_to);
            }
        }

        // فلتر المستودع
        if ($request->filled('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // فلتر طريقة الدفع
        if ($request->filled('payment_method')) {
            $paymentMethod = $request->payment_method;

            if ($paymentMethod === 'delivery') {
                // فلتر عملاء التوصيل
                $query->whereHas('customer', function($q) {
                    $q->where('is_delivery', 1);
                });
            } elseif ($paymentMethod === 'unpaid') {
                // فلتر الفواتير غير المدفوعة
                $query->where(function($q) {
                    $q->whereDoesntHave('posPayment')
                      ->orWhere(function($subQ) {
                          $subQ->whereHas('customer', function($customerQ) {
                              $customerQ->where('is_delivery', 1);
                          })->where('is_payment_set', '!=', 1);
                      });
                });
            } else {
                // فلتر طرق الدفع العادية (نقد، شبكة، دفع مقسم)
                $query->whereHas('posPayment', function($q) use ($paymentMethod) {
                    $q->where('payment_type', $paymentMethod);
                });
            }
        }

        // فلتر العميل
        if ($request->filled('customer_search')) {
            $customerSearch = $request->customer_search;
            $query->whereHas('customer', function($q) use ($customerSearch) {
                $q->where('name', 'LIKE', '%' . $customerSearch . '%');
            });
        }

        // فلتر منشئ الفاتورة
        if ($request->filled('creator_search')) {
            $creatorSearch = $request->creator_search;
            $query->whereHas('createdBy', function($q) use ($creatorSearch) {
                $q->where('name', 'LIKE', '%' . $creatorSearch . '%');
            });
        }

        $posPayments = $query->orderBy('id', 'desc')->get();

        // جمع كل معرفات المستخدمين المرتبطة بالفواتير
        $userIds = $posPayments->pluck('user_id')->filter()->unique()->toArray();

        // استرجاع بيانات المستخدمين دفعة واحدة
        $users = [];
        if (!empty($userIds)) {
            $usersCollection = \App\Models\User::whereIn('id', $userIds)->get();
            foreach ($usersCollection as $user) {
                $users[$user->id] = $user;
            }
        }

        // إضافة معلومات الدفع والمستخدم لكل فاتورة
        foreach ($posPayments as $pos) {
            // إضافة معلومات الدفع
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }

            // إضافة معلومات المستخدم
            if (!empty($pos->user_id) && isset($users[$pos->user_id])) {
                $pos->user = $users[$pos->user_id];
            } else {
                $pos->user = null;
            }
        }

        // حساب الإحصائيات
        $statistics = [
            'total_count' => $posPayments->count(),
            'total_amount' => $posPayments->sum(function($pos) { return $pos->getTotal(); }),
            'cash_count' => $posPayments->where('payment_method', 'cash')->count(),
            'network_count' => $posPayments->filter(function($pos) {
                return $pos->posPayment && $pos->posPayment->payment_type === 'network';
            })->count(),
            'delivery_count' => $posPayments->filter(function($pos) {
                return $pos->customer && $pos->customer->is_delivery;
            })->count(),
        ];

        return view('invoice_processing.invoice_processor', compact('posPayments', 'warehouses', 'paymentMethods', 'statistics'));
    }

    /**
     * تغيير حالة الفاتورة إلى مرتجع
     */
    public function returnInvoice($id)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            // تحديث حالة الفاتورة إلى 'returned'
            $pos = Pos::find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            $pos->status_type = 'returned';
            $pos->save();

            return redirect()->back()->with('success', __('تم تحديث حالة الفاتورة إلى مرتجع بضاعة بنجاح'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * تغيير حالة الفاتورة إلى ملغية
     */
    public function cancelInvoice($id)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            // تحديث حالة الفاتورة إلى 'cancelled'
            $pos = Pos::find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            $pos->status_type = 'cancelled';
            $pos->save();

            return redirect()->back()->with('success', __('تم تحديث حالة الفاتورة إلى ملغية بنجاح'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * تغيير نوع الدفع للفاتورة
     */
    public function changePaymentType(Request $request, $id)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            $pos = Pos::with('posPayment')->find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            if (!$pos->posPayment) {
                return redirect()->back()->with('error', __('لا توجد معلومات دفع لهذه الفاتورة.'));
            }

            $posPayment = $pos->posPayment;
            $totalAmount = $posPayment->amount;

            // التحقق من صحة البيانات المرسلة
            $request->validate([
                'payment_type' => 'required|in:cash,network,split',
                'cash_amount' => 'nullable|numeric|min:0',
                'network_amount' => 'nullable|numeric|min:0',
                'transaction_number' => 'nullable|string|max:255',
            ]);

            // تحديث نوع الدفع والمبالغ
            $posPayment->payment_type = $request->payment_type;

            if ($request->payment_type === 'cash') {
                $posPayment->cash_amount = $totalAmount;
                $posPayment->network_amount = 0;
                $posPayment->transaction_number = null;
            } elseif ($request->payment_type === 'network') {
                $posPayment->cash_amount = 0;
                $posPayment->network_amount = $totalAmount;
                $posPayment->transaction_number = $request->transaction_number;
            } elseif ($request->payment_type === 'split') {
                $cashAmount = $request->cash_amount ?? 0;
                $networkAmount = $request->network_amount ?? 0;

                // التأكد من أن مجموع المبالغ يساوي المبلغ الإجمالي
                if (($cashAmount + $networkAmount) != $totalAmount) {
                    return redirect()->back()->with('error', __('مجموع مبالغ النقد والشبكة يجب أن يساوي المبلغ الإجمالي.'));
                }

                $posPayment->cash_amount = $cashAmount;
                $posPayment->network_amount = $networkAmount;
                $posPayment->transaction_number = $request->transaction_number;
            }

            $posPayment->save();

            return redirect()->back()->with('success', __('تم تحديث نوع الدفع بنجاح'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * عرض صفحة تعديل منتجات الفاتورة
     */
    public function editProducts($id)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            // استرجاع الفاتورة مع المنتجات والعلاقات
            $pos = Pos::with(['customer', 'warehouse', 'items.product', 'posPayment'])
                ->find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            // استرجاع جميع المنتجات المتاحة للإضافة مع كمياتها في المستودع
            $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())
                ->where('type', 'product')
                ->with(['taxes', 'warehouseProducts' => function($query) use ($pos) {
                    $query->where('warehouse_id', $pos->warehouse_id);
                }])
                ->get();

            // إضافة الكمية المتاحة لكل منتج
            foreach ($products as $product) {
                $warehouseProduct = $product->warehouseProducts->first();
                $product->available_quantity = $warehouseProduct ? $warehouseProduct->quantity : 0;
            }

            return view('invoice_processing.edit_products', compact('pos', 'products'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * تحديث منتجات الفاتورة ومعلومات الدفع
     */
    public function updateProducts($id, Request $request)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            $pos = Pos::find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            // التحقق من صحة البيانات
            $request->validate([
                'products' => 'required|array',
                'products.*.product_id' => 'required|exists:product_services,id',
                'products.*.quantity' => 'required|numeric|min:1',
                'products.*.price' => 'required|numeric|min:0',
                'products.*.discount' => 'nullable|numeric|min:0',
                'products.*.tax' => 'nullable|string',
                'products.*.description' => 'nullable|string|max:255',
                // معلومات الدفع
                'payment_type' => 'required|string|in:cash,network,split',
                'cash_amount' => 'nullable|numeric|min:0',
                'network_amount' => 'nullable|numeric|min:0',
                'transaction_number' => 'nullable|string|max:255',
                'payment_date' => 'required|date',
                'payment_discount' => 'nullable|numeric|min:0',
            ]);

            // التحقق من توفر المخزون للمنتجات الجديدة
            $stockErrors = [];
            foreach ($request->products as $index => $productData) {
                $product = \App\Models\ProductService::find($productData['product_id']);
                if (!$product) {
                    continue;
                }

                // الحصول على الكمية الحالية في المستودع
                $warehouseProduct = \App\Models\WarehouseProduct::where('warehouse_id', $pos->warehouse_id)
                    ->where('product_id', $productData['product_id'])
                    ->first();

                $currentStock = $warehouseProduct ? $warehouseProduct->quantity : 0;

                // الحصول على الكمية الحالية للمنتج في الفاتورة (إن وجدت)
                $currentPosProduct = \App\Models\PosProduct::where('pos_id', $pos->id)
                    ->where('product_id', $productData['product_id'])
                    ->first();

                $currentPosQuantity = $currentPosProduct ? $currentPosProduct->quantity : 0;

                // الكمية المتاحة = المخزون الحالي + الكمية المستخدمة حالياً في الفاتورة
                $availableStock = $currentStock + $currentPosQuantity;

                if ($productData['quantity'] > $availableStock) {
                    $stockErrors[] = "المنتج '{$product->name}' - الكمية المطلوبة ({$productData['quantity']}) أكبر من المتاح ({$availableStock})";
                }
            }

            if (!empty($stockErrors)) {
                return redirect()->back()
                    ->withErrors(['stock' => $stockErrors])
                    ->withInput();
            }

            // استرجاع المنتجات الحالية قبل الحذف لإعادة المخزون
            $currentProducts = \App\Models\PosProduct::where('pos_id', $pos->id)->get();

            // إعادة المخزون للمنتجات الحالية
            foreach ($currentProducts as $currentProduct) {
                \App\Models\Utility::warehouse_quantity(
                    'plus',
                    $currentProduct->quantity,
                    $currentProduct->product_id,
                    $pos->warehouse_id
                );

                // إضافة سجل في تقرير المخزون
                \App\Models\Utility::addProductStock(
                    $currentProduct->product_id,
                    $currentProduct->quantity,
                    'pos_edit_return',
                    'إعادة مخزون بسبب تعديل فاتورة رقم ' . $pos->id,
                    $pos->id
                );
            }

            // حذف المنتجات الحالية
            \App\Models\PosProduct::where('pos_id', $pos->id)->delete();

            // إضافة المنتجات الجديدة وخصم المخزون
            foreach ($request->products as $productData) {
                // التأكد من وجود المنتج المحدد
                $product = \App\Models\ProductService::find($productData['product_id']);
                if (!$product) {
                    continue; // تخطي المنتج إذا لم يكن موجوداً
                }

                // إنشاء منتج الفاتورة الجديد
                \App\Models\PosProduct::create([
                    'pos_id' => $pos->id,
                    'product_id' => $productData['product_id'],
                    'quantity' => $productData['quantity'],
                    'price' => $productData['price'],
                    'discount' => $productData['discount'] ?? 0,
                    'tax' => $productData['tax'] ?? '0.00',
                    'description' => $productData['description'] ?? null,
                ]);

                // خصم المخزون للمنتج الجديد
                \App\Models\Utility::warehouse_quantity(
                    'minus',
                    $productData['quantity'],
                    $productData['product_id'],
                    $pos->warehouse_id
                );

                // إضافة سجل في تقرير المخزون
                \App\Models\Utility::addProductStock(
                    $productData['product_id'],
                    $productData['quantity'],
                    'pos_edit_sale',
                    'خصم مخزون بسبب تعديل فاتورة رقم ' . $pos->id,
                    $pos->id
                );
            }

            // تحديث معلومات الدفع
            $this->updatePaymentInfo($pos, $request);

            // تحديث تاريخ آخر تعديل للفاتورة
            $pos->touch();

            return redirect()->route('invoice.processing.invoice.processor')
                ->with('success', __('تم تحديث منتجات الفاتورة ومعلومات الدفع بنجاح'));

        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * تحديث معلومات الدفع للفاتورة
     */
    private function updatePaymentInfo($pos, $request)
    {
        // البحث عن سجل الدفع الموجود أو إنشاء جديد
        $posPayment = PosPayment::where('pos_id', $pos->id)->first();

        if (!$posPayment) {
            $posPayment = new PosPayment();
            $posPayment->pos_id = $pos->id;
            $posPayment->created_by = \Auth::user()->id;
        }

        // حساب المبلغ الإجمالي من المنتجات
        $totalAmount = $pos->items()->sum(\DB::raw('price * quantity'));

        // تحديث معلومات الدفع
        $posPayment->date = $request->payment_date;
        $posPayment->payment_type = $request->payment_type;
        $posPayment->discount = $request->payment_discount ?? 0;

        // تحديث المبالغ حسب نوع الدفع
        if ($request->payment_type === 'cash') {
            $posPayment->cash_amount = $request->cash_amount ?? $totalAmount;
            $posPayment->network_amount = 0;
            $posPayment->amount = $posPayment->cash_amount;
            $posPayment->transaction_number = null;
        } elseif ($request->payment_type === 'network') {
            $posPayment->cash_amount = 0;
            $posPayment->network_amount = $request->network_amount ?? $totalAmount;
            $posPayment->amount = $posPayment->network_amount;
            $posPayment->transaction_number = $request->transaction_number;
        } elseif ($request->payment_type === 'split') {
            $posPayment->cash_amount = $request->cash_amount ?? 0;
            $posPayment->network_amount = $request->network_amount ?? 0;
            $posPayment->amount = $posPayment->cash_amount + $posPayment->network_amount;
            $posPayment->transaction_number = $request->transaction_number;
        }

        $posPayment->save();

        return $posPayment;
    }
}
