{"__meta": {"id": "X6f58d375f3a7c2e51ed629fd05f6345e", "datetime": "2025-07-14 18:17:55", "utime": **********.369899, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517074.946971, "end": **********.369913, "duration": 0.4229421615600586, "duration_str": "423ms", "measures": [{"label": "Booting", "start": 1752517074.946971, "relative_start": 0, "end": **********.317886, "relative_end": **********.317886, "duration": 0.37091517448425293, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.317899, "relative_start": 0.3709280490875244, "end": **********.369914, "relative_end": 9.5367431640625e-07, "duration": 0.052015066146850586, "duration_str": "52.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989544, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0035200000000000006, "accumulated_duration_str": "3.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3456059, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.216}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.356104, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.216, "width_percent": 16.193}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.361981, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.409, "width_percent": 21.591}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjRLL2xUOVRXN0JWNUJYUGxvM0pmSEE9PSIsInZhbHVlIjoiZnQ5dVduR0tWUjdKa00wOVVKaTQwdz09IiwibWFjIjoiMzQyODJkMTk1ZGMyZGZkMmRjNTZhZDIwODZjMjA5Yjc1NGM2MjI1YTgyODA3NjAzZDJmMzhhN2UxZTUwNjYxYSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-633842182 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-633842182\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1064652487 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1064652487\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-194575348 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194575348\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-610044925 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLL2xUOVRXN0JWNUJYUGxvM0pmSEE9PSIsInZhbHVlIjoiZnQ5dVduR0tWUjdKa00wOVVKaTQwdz09IiwibWFjIjoiMzQyODJkMTk1ZGMyZGZkMmRjNTZhZDIwODZjMjA5Yjc1NGM2MjI1YTgyODA3NjAzZDJmMzhhN2UxZTUwNjYxYSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517071250%7C20%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpQQ1lrb3FzczJ2dVhmOS9vaktHRUE9PSIsInZhbHVlIjoidnZGZkVGWWtmTDN2TnFyb3VCbEM0Kzh4ZmlpQjNOTDNST3BvTythMEY0U2FhcENhbkJkRDFLMnVuc1d6UVg3V0lSSU84WjhBV2lwOEJ5M0ZBbm1vbWo0VWE5UjV4UXJIcG04Q3RzZFYxajRaaEQxRUpPSTkyeVlEWUZyRFBRaGludkgzNitkZDZ1Uk9mUGwzaURDSWNBNElqU050REtXQ3FXWnFsL2xqMjVqTDVZR3RLNUFtMHJOblQvWmhoOW5WU1RmUGVDRVVBcy9rUmwzalVHVVZ5WVMxYXN3Nkd4RzNjMTAwb0o1RTdLcnBSMitXMnRpYWVnUGN1STJxbngzdWlvQnJKWld3bUc1VEdEYTlHN2NvbzZqaXZZUXBmcmtJUTdpWjVHMlBaTmNyWTUyKzJsUGVWcE5xcHNzdWtXbGhrSmtUeVhQZFZ5YzJsQkwwbVUwS0VqZ1kreDBRUUpCTjI2NDZmNGx3N3NLY2t5SzZqSG9BdGZHcGMranlJNy83dTA4OE40czNybU1aVkh1Qnl0QVBYaURQNHVxNDZqckcvVW9NM25yc1hpMEpudUo2Y2UvdkJ4OGRGU1o0clBheGxyR28vVHh2SWVHM1d4WTdRdjc3OXMwbW5IZk5rdVdRclpPd1ZSNDBFam1ydysyNjU3MlluL3RJeHc5NXE5K3MiLCJtYWMiOiJkZWE1NmNmZWU1NjVjMTk3ZjNkYzNmYWZkM2ZjNjhlN2VkMWNmMTE0YWFiYzAxOGM4Y2VjZTUwZGU3NGZlOTkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imw5TElIWW1hU2tuQjNzZHc5bFBKblE9PSIsInZhbHVlIjoiNDk2UG9rTHU0MVV6UFVwS0k1NzhTUmpnSjNHWmd3MzJKZjQzQW1WT25aWDByZk5zeGtSQVFPL0ZmZ1pwK2lTQ29DK3hvejg0YytEV20zN09pZENTOUFMMUhvMEZsN1M5Q3hLSHNxelA4T2JZNitiQmpBS0JMY1k4b0QvNEZOZkxFNVVaOFY3R1FmNldPSGJoODBJQmZ2MWlOWWJxUll2MVRQVWNENkpsVktUVW9pTEhERXBCMUMvN21kU2xRTGsvZ0lqakZ2bnd3b3Yza2pLVU1XZlVxWFl6OTN1dURJMTdGNU94cFp0Qk9UV1Y1eWo3b0ZtWTh1WHhXYzZtWEFjc3cxR3dPcUFjQW9DakNsZmlYZzdOQVdKLzhQVjJhb0FtazdIR0hHM2lLb0hSWmlMNkdERmRkTDB5eXkvU2N1MjRqTm1zVkprL3VDd2kzUFVTOHVUc050KzNNV2N6N0lRZ0cwTm42UFJxaG9BdXVqbVQ3bUdlVnRwaVA3d0J1RFJWdlFrZ2dIM1hmWnFuWmkreFg1aVpyTmNMbjhhUXN0dithMnNnM1lrUmRwUkdaY1NIS3NlZUptMDZBQXVQMU9VRDBsWlhKTW9haVZ2NEZaUFlNV0ZvUkN1aTgrQitqUkxoWUFRWGJyQ2NVNU01OC9YYkZlWGVFbjdjbUJDeTA4UDciLCJtYWMiOiI1Nzc3NWJhMjRiMzEzZDY4MGUzYWM5OWFjODMxYTdmMDA1MTMzOWNlYTExMTdmMWFmNWI4ZDA2ZmZkOGZhMWJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610044925\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2016560221 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016560221\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1313807596 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1lRE1PeFdWbXYxLzN6TDJOc3VyUkE9PSIsInZhbHVlIjoiZ0JGQTJuZjFQOW5yUE55V0NocmV6bnhnZi9WekQvTUtEWjUyMTNNRVBpNy9mWWtzbVZva0g4MW5kSTNnWDZ3d09HUzQ4Zld1NTdycys4ejlKcnlLcU01V3B3R1Njc05CWU0xRVQzOGJ0TGsrTXBlYlVnNUdJZkJiZ1BBbTBVdVRYTnZtSWVQZm5MdDBIUW9YSDJqNG1QazVvK094cjAzenBpRjduajRqQkNiRkV0SjB2UzA4Y1oybys0R2JUbTg4WE9scnVwMUU1WHNyL1JWWlArczRCVk95WUhMaFlOWGdWR2psOThKVTljaTNSTGdYcG5vRFNWaUs4TEtUdzdRT3YzZlh1ZnJtdThzaTd3SnJmbEUzV3ZyaEVpV21LSGFWZjA1M01aMFB5aCtUVlZyemN1dmNlY0Q3bWVOQjhRNkFVV0JybmVKb0RHeVA0M0tPb2xLTjhDTjgvWkFhbmpSWjFkMUNSRUFYbGZsemZsbTZzODg2WGh6emZibjhMOHdhTGNkcTkzeTBrcysySDZ3TDdTRjBnSXdUeWk5QWNIYjBTTE5UOXhxcW01Vkd4dVUvZzVHR1dya01MNFdMcmE5ZG1ybGc3Y2doRGYyRTVDdFFZaU40Y29oUFhzVTNSbURPb0pnbmlMYzlpNHptNGtCcGlzWTd2d2tuQXhuU25GTjYiLCJtYWMiOiI1MGU3ZGExNDAzM2I0OThjNzZkOTk1MzM3YjliMTkzZGYwMTNjMGM1MWYyNzUzOTQ1NTU0NWY4OTc5ZjA0ZTc3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxoTEQ0Q0lLTEtSZUxnWk5sclVMa1E9PSIsInZhbHVlIjoiUjZTS1JoTy9IdlVzOEE1Qm1GVnZid2tuSGYwelFyVWFNYmdVMzVvaUJ5MjJTaHVqY2FKVHFOYm1Ccnl1dmE5MnZoNEpVOGJlbllOUVBJbzgvSjlOdEdNL3ByNEpKcFZUN21VZXNhc21PdEhPM0ovTDdpN1lqZGppRDlmOG1vTDViN21EbXdJcHBqdGlvb0QvQXhBQ01jUW9RZ1JKc2hIT3BiTW9tbTVDdWRmQ0UwZ1dVLzZuVWU2anovYnFxTXpmaWVndlYwd2k2eUcrbmNRWm1IVmRXcFVpamNuZDUyTGFJSTRFL0xpZDZKWDJaRi8rZUQwelZkSTdGWmc0TyswUVZUN0NYSmZoMnhkbExSa0RCV1l2SHFBNUZZaVJBelhjc1RXK3dMR3RWQm5xclhuN0pXL2M2TWtSNHFKalpzQmNaUFZSalh5SG5icXRxUE9UYzhTSnhLbWtGa3YzVzhNaFhYSmlRQ085THMxMmFjc0JVNUNzeXV0ZkRXeGFXZW5GRVVWdDRPRWUrRTB0VHFUV1dyazZnVTNmQjQ1bE1vbXJhdEVnYk0vOWt1a2tVbS9LU3FJRlIvVEZySkQ2amt0blc0LzdYcHZoNG03akI3M3lTamR3emhNbVE1bnJ2b0F0bWh4TW44M2F3eGFTaVJUS3BkWkdQdVFUSHZ2MzVXZjEiLCJtYWMiOiI4NTVkN2QzNjRmMDIyZGVmYjM4Zjc1OWEzMmE5N2QwOGM5NjEzZjJmMjZiOGU5NjJjMzJjMTFlYzBlYjQ4NzJiIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1lRE1PeFdWbXYxLzN6TDJOc3VyUkE9PSIsInZhbHVlIjoiZ0JGQTJuZjFQOW5yUE55V0NocmV6bnhnZi9WekQvTUtEWjUyMTNNRVBpNy9mWWtzbVZva0g4MW5kSTNnWDZ3d09HUzQ4Zld1NTdycys4ejlKcnlLcU01V3B3R1Njc05CWU0xRVQzOGJ0TGsrTXBlYlVnNUdJZkJiZ1BBbTBVdVRYTnZtSWVQZm5MdDBIUW9YSDJqNG1QazVvK094cjAzenBpRjduajRqQkNiRkV0SjB2UzA4Y1oybys0R2JUbTg4WE9scnVwMUU1WHNyL1JWWlArczRCVk95WUhMaFlOWGdWR2psOThKVTljaTNSTGdYcG5vRFNWaUs4TEtUdzdRT3YzZlh1ZnJtdThzaTd3SnJmbEUzV3ZyaEVpV21LSGFWZjA1M01aMFB5aCtUVlZyemN1dmNlY0Q3bWVOQjhRNkFVV0JybmVKb0RHeVA0M0tPb2xLTjhDTjgvWkFhbmpSWjFkMUNSRUFYbGZsemZsbTZzODg2WGh6emZibjhMOHdhTGNkcTkzeTBrcysySDZ3TDdTRjBnSXdUeWk5QWNIYjBTTE5UOXhxcW01Vkd4dVUvZzVHR1dya01MNFdMcmE5ZG1ybGc3Y2doRGYyRTVDdFFZaU40Y29oUFhzVTNSbURPb0pnbmlMYzlpNHptNGtCcGlzWTd2d2tuQXhuU25GTjYiLCJtYWMiOiI1MGU3ZGExNDAzM2I0OThjNzZkOTk1MzM3YjliMTkzZGYwMTNjMGM1MWYyNzUzOTQ1NTU0NWY4OTc5ZjA0ZTc3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxoTEQ0Q0lLTEtSZUxnWk5sclVMa1E9PSIsInZhbHVlIjoiUjZTS1JoTy9IdlVzOEE1Qm1GVnZid2tuSGYwelFyVWFNYmdVMzVvaUJ5MjJTaHVqY2FKVHFOYm1Ccnl1dmE5MnZoNEpVOGJlbllOUVBJbzgvSjlOdEdNL3ByNEpKcFZUN21VZXNhc21PdEhPM0ovTDdpN1lqZGppRDlmOG1vTDViN21EbXdJcHBqdGlvb0QvQXhBQ01jUW9RZ1JKc2hIT3BiTW9tbTVDdWRmQ0UwZ1dVLzZuVWU2anovYnFxTXpmaWVndlYwd2k2eUcrbmNRWm1IVmRXcFVpamNuZDUyTGFJSTRFL0xpZDZKWDJaRi8rZUQwelZkSTdGWmc0TyswUVZUN0NYSmZoMnhkbExSa0RCV1l2SHFBNUZZaVJBelhjc1RXK3dMR3RWQm5xclhuN0pXL2M2TWtSNHFKalpzQmNaUFZSalh5SG5icXRxUE9UYzhTSnhLbWtGa3YzVzhNaFhYSmlRQ085THMxMmFjc0JVNUNzeXV0ZkRXeGFXZW5GRVVWdDRPRWUrRTB0VHFUV1dyazZnVTNmQjQ1bE1vbXJhdEVnYk0vOWt1a2tVbS9LU3FJRlIvVEZySkQ2amt0blc0LzdYcHZoNG03akI3M3lTamR3emhNbVE1bnJ2b0F0bWh4TW44M2F3eGFTaVJUS3BkWkdQdVFUSHZ2MzVXZjEiLCJtYWMiOiI4NTVkN2QzNjRmMDIyZGVmYjM4Zjc1OWEzMmE5N2QwOGM5NjEzZjJmMjZiOGU5NjJjMzJjMTFlYzBlYjQ4NzJiIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313807596\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1424001230 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLL2xUOVRXN0JWNUJYUGxvM0pmSEE9PSIsInZhbHVlIjoiZnQ5dVduR0tWUjdKa00wOVVKaTQwdz09IiwibWFjIjoiMzQyODJkMTk1ZGMyZGZkMmRjNTZhZDIwODZjMjA5Yjc1NGM2MjI1YTgyODA3NjAzZDJmMzhhN2UxZTUwNjYxYSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424001230\", {\"maxDepth\":0})</script>\n"}}