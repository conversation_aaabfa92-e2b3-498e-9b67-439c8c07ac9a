{"__meta": {"id": "Xa6c59d6a3a560f770021727d2e23633d", "datetime": "2025-07-14 18:29:16", "utime": **********.215639, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517755.753542, "end": **********.215654, "duration": 0.4621119499206543, "duration_str": "462ms", "measures": [{"label": "Booting", "start": 1752517755.753542, "relative_start": 0, "end": **********.144174, "relative_end": **********.144174, "duration": 0.39063215255737305, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.144184, "relative_start": 0.3906421661376953, "end": **********.215655, "relative_end": 1.1920928955078125e-06, "duration": 0.07147097587585449, "duration_str": "71.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991104, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01952, "accumulated_duration_str": "19.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.172026, "duration": 0.01809, "duration_str": "18.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.674}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.199796, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.674, "width_percent": 2.613}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2075748, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.287, "width_percent": 4.713}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1951101313 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1951101313\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1536145769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1536145769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-258402972 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258402972\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-959507370 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517753740%7C31%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVXNW5ram1HOFlvdDNOdWFGaHFlb3c9PSIsInZhbHVlIjoiWUhzVFNwMTlLVjdldDZBazBXMG9ybkFqVndWbWV4NlNwalZWTlZUTmFvNVNxWXloN2pveDdLQit2NG53WVNTMFhNQkxyLzFJWFNEdFdhNHAzU2NGeDJ0aG1neWN3Q21MNGpGZDU4RUhTU0lENi9EWXo5a2d3d3pBclhrTGNWWU96NXJTUm12QXIyREo3YjA3N2gveHdJOExPc3ExbFBKWjFKeFQvcVNiNmVaTnlNU3k3YkR6T0Y3YWdFUUE4NWhUS2dDZVFlVmJlVVFsT2xRblVXaG1PZGV3eFo5MGdzM1g2VkhKQzY5c3lRZG9iUStlRFdFNit4UUNGMEg2dkZwci9zZXdPd281WW5ZUmpndCtXV2QrL0UzQVZpNGFjMmJjdEg0d3dpcTFvMEJZZlJSRG52OXZMRHZLMjhxZm5jYVRHQy85TUU0Y2liUVFxVmNzMUhqRXJaV1BsVWQ3SWgzODZQU1NZYUhITFQ5OW5XMmtNbWpFYXl3OGdlcWVoSU5ZVXgwWTBLNGtXSk9PZGlqN1RmWU5oRktJd1V0S2QweHZyTlhkZ05JMHZjQWtLd2ppM0Q5UjE2QmU0QUR1RW5zRGNLb3czQmpXVGdWLzZsWU9EVHNEbEo3OXl2Wk5HWitiTlhORjVUWXBxdFFCdDV3RTRhUmhscGxLc0Uxc0dzd0kiLCJtYWMiOiI0YWVkZmZkY2I5ZTY1NDllYTRjOGI4Y2VmMjVkMjk3NTUwYjc5YmRkN2ZhOWZiZWNkMjJjNDY2OTJiNDQ1OTk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilg3R2tTNmdPazVFTlN5dXhUdlA2ZUE9PSIsInZhbHVlIjoiMUluTmRheVhoSEVWT1BuZmZSVDJMNkw4YXltakluVUtxckcrbU42eElVbFNaMmMyRStYeUV0UWFrUnBkRWZPdnN3MTVBdkU2ODN5U3M3ekozL2tDQm9JZHZ5TUQ2OUNUMmNLeG0zY3Jvb1BleHlzSk5XZjhTRGZrNG9Cb1M0WnVHRG9KUDQrYVFhZ1hsdkNraCtOY3VPY1JPb2JkNTFUQUFxc1EyR0l1cjQ2RkZQaVpveTZGVGF5WDZ3MTNiMmNRNFZXVDVTT0hEeExZaDY3ajNBMk1uQzBIUXpsRGdrZ2J5bVFpaHIzRlUwMTYxSTlMQ0d5bEVPdnlJVFJ4T2lTdHN0cFZ6azd4TzhHTU9obnlMTlBONnRzQ2IySjdlbzRuNDhwOWw0NEwvaTlkN3JudHd6WVJVSzJXbW5kZUNoUjBHcXlXR21tdHlicGZVVVNxWkc2YnN3WExlbTRWaTdtd0diUUVyZE44Mk9DTVViVVBZOU9FeW1LMXpDb21ZZkdzZlVIb1h2N1orSXo3QzBnU0gzOGQvVi95TVZGZTZTTFBNWlFGV3duN3pNQ2xGOWh1Rk9HbUdTakNScW5LelBtMWJyNUNvMXYrdFFDbHF0eDFrb1JKNUt5NG1YbU5IYi9VdHh0M0lPWHN4VEVoai9FZ0ROSm5lMGhVdEpPcXpsdHUiLCJtYWMiOiIxN2M4YTZmNDJlYTVlN2YyOGJiNDQyMmExYTUxYzc0NmM0MDQzNjQyMGM2OTg1ZWUwYTQ0YmE1MWViYzUyNTFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959507370\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1253359693 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253359693\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-560737441 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtpZnRYZVE1bmovLzRKcFp0WHU5U3c9PSIsInZhbHVlIjoidlhQZjJQeEZkM05COU5IVEM0c2dEME1OcE9xbkJvV1BzeHZjdXF2ODdack1pQlUvcEZFQ1Qyc2ljb2pKSW96S2xxSmdKZmIrYVRaZmhaeEJPWmUrbXNiS1l2WWlpempsV2twTzhidkpibUxORmV2K3pOQVRiNUxXQ0NFS1ZWWGI1RGY4dDFGRWhsbzEwaHdTZlgrcThlY0daMjYwSU0vYlQ0a2hjVFl6ckh0Yi9JWXo0c2pzUDBtWUduT0x6VitNenNCd202TnY4RnMrZXk2K1I0VDBub21OY1JXVy9OMXhQeWpVZ1ZEUVB2WmZEM0pPVnhaTUYyZDBydENRa3JNZy9FV1VwcktQOEdRZWdnVFdJRTQ3eExkTW40MUZ2OWlwQmJBc2s3MWZSbE9VM3d3bHROWGEwSTZ5NHEveC9zdkhFM2dUa3hyNXZEZTZNbFJsSnNhZWxpZEVxYzJXcGcxcEFQOTZ0bkIzZ0RYWTdBWTVVTmxzWkRQOU9WVHZKQk5laW1NUkVZSmZkT0RleWw1Q3ltS2JzWTNWajJDMU00YkE4WWFtdUVQY2UxTmVBWE5VaTBZbzh1MUFoZFRVUy8yalJ6Q2FlUEZjRVVremxrV29qY0FhRHJVMmY1dSs4R3cramFZSW5LY0tNUzlOdFhpQVh1MUgyOTFkQy9BeWcyZkkiLCJtYWMiOiJjOWU2NzQ3YmI2ZWVkMGEyM2NmMGZlZDY2NzQ1ZjNkZDMyNzUwZTA1MDUyNjAwY2RkNGJhNTE2NmE1YWY1Y2ZiIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZOSlNmNGtZdnJYbWRIdG1LUlZ4dmc9PSIsInZhbHVlIjoiTWFwMkNBYzRDQzRvTW5EdkpEWXJQRnZ2eXlkd0luaDhOYUlTUWN5LzIzaVNzZmErZXhqK3p4QmY5VUlyOXlybndab0IrcmhNZnVValduaUgzMUtEQlZmZ1hLOGpYWDNFa1NEQklVQzlVT3lQTVp2UzZTR3RJOWt4WE1mMU9WM3ZTU1FUUTRrYUpRcSs3YVR0azMxY1owbEdBV3BRdU1HV3FsRlNtOGlBQzJER2w3eEVBbUlEZFJrTU1lL002TXVyWVB4WnBqK1lvMTFGYTFubW1DSE55VXVjNHl2V1pNYi9EclNWL2xvb0lZLzdYZ0pyZEpsUGRuMWVNbzd5cjNPZ2QrYVNFdTBKaUhzOGZMRzRrbnlKanpucUVNcFZWaWdWTTJCaEFGZUVXVDBFWEhxVlp2SDVjVzg2UkZyZjJReTR3ejdqZmNPSm11WlRRTXFyaHdOWThROVZpNys4djNCdlpwQStVR0k3anZKRmZQUFo0RENqUzJxdXlVajNpZTFQMmVvUEVkRTl6d0FHOW8zVHd5UkpmY3ZudlV6dDY0TWZPNytlekljckNJdzdueDdPMmVSSU93ZTNMR1JTbS9lZ1gxb0g0d0kxRnFrUkI2ZEhHZk9WT05rRWRQazdkR1VreCtxK2laV1BETm1Ba0liaEIvbTlTWWpKRGc2ZWhadDkiLCJtYWMiOiIwOGE4MmRjMzkwYTk2OTA1ZTRhOWIzOGU0M2M5MGVlMzMyNzUzOGFiNTFlZTNiY2MwNjVlMjI5OTMyMTdhNzAzIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtpZnRYZVE1bmovLzRKcFp0WHU5U3c9PSIsInZhbHVlIjoidlhQZjJQeEZkM05COU5IVEM0c2dEME1OcE9xbkJvV1BzeHZjdXF2ODdack1pQlUvcEZFQ1Qyc2ljb2pKSW96S2xxSmdKZmIrYVRaZmhaeEJPWmUrbXNiS1l2WWlpempsV2twTzhidkpibUxORmV2K3pOQVRiNUxXQ0NFS1ZWWGI1RGY4dDFGRWhsbzEwaHdTZlgrcThlY0daMjYwSU0vYlQ0a2hjVFl6ckh0Yi9JWXo0c2pzUDBtWUduT0x6VitNenNCd202TnY4RnMrZXk2K1I0VDBub21OY1JXVy9OMXhQeWpVZ1ZEUVB2WmZEM0pPVnhaTUYyZDBydENRa3JNZy9FV1VwcktQOEdRZWdnVFdJRTQ3eExkTW40MUZ2OWlwQmJBc2s3MWZSbE9VM3d3bHROWGEwSTZ5NHEveC9zdkhFM2dUa3hyNXZEZTZNbFJsSnNhZWxpZEVxYzJXcGcxcEFQOTZ0bkIzZ0RYWTdBWTVVTmxzWkRQOU9WVHZKQk5laW1NUkVZSmZkT0RleWw1Q3ltS2JzWTNWajJDMU00YkE4WWFtdUVQY2UxTmVBWE5VaTBZbzh1MUFoZFRVUy8yalJ6Q2FlUEZjRVVremxrV29qY0FhRHJVMmY1dSs4R3cramFZSW5LY0tNUzlOdFhpQVh1MUgyOTFkQy9BeWcyZkkiLCJtYWMiOiJjOWU2NzQ3YmI2ZWVkMGEyM2NmMGZlZDY2NzQ1ZjNkZDMyNzUwZTA1MDUyNjAwY2RkNGJhNTE2NmE1YWY1Y2ZiIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZOSlNmNGtZdnJYbWRIdG1LUlZ4dmc9PSIsInZhbHVlIjoiTWFwMkNBYzRDQzRvTW5EdkpEWXJQRnZ2eXlkd0luaDhOYUlTUWN5LzIzaVNzZmErZXhqK3p4QmY5VUlyOXlybndab0IrcmhNZnVValduaUgzMUtEQlZmZ1hLOGpYWDNFa1NEQklVQzlVT3lQTVp2UzZTR3RJOWt4WE1mMU9WM3ZTU1FUUTRrYUpRcSs3YVR0azMxY1owbEdBV3BRdU1HV3FsRlNtOGlBQzJER2w3eEVBbUlEZFJrTU1lL002TXVyWVB4WnBqK1lvMTFGYTFubW1DSE55VXVjNHl2V1pNYi9EclNWL2xvb0lZLzdYZ0pyZEpsUGRuMWVNbzd5cjNPZ2QrYVNFdTBKaUhzOGZMRzRrbnlKanpucUVNcFZWaWdWTTJCaEFGZUVXVDBFWEhxVlp2SDVjVzg2UkZyZjJReTR3ejdqZmNPSm11WlRRTXFyaHdOWThROVZpNys4djNCdlpwQStVR0k3anZKRmZQUFo0RENqUzJxdXlVajNpZTFQMmVvUEVkRTl6d0FHOW8zVHd5UkpmY3ZudlV6dDY0TWZPNytlekljckNJdzdueDdPMmVSSU93ZTNMR1JTbS9lZ1gxb0g0d0kxRnFrUkI2ZEhHZk9WT05rRWRQazdkR1VreCtxK2laV1BETm1Ba0liaEIvbTlTWWpKRGc2ZWhadDkiLCJtYWMiOiIwOGE4MmRjMzkwYTk2OTA1ZTRhOWIzOGU0M2M5MGVlMzMyNzUzOGFiNTFlZTNiY2MwNjVlMjI5OTMyMTdhNzAzIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560737441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2084549342 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2084549342\", {\"maxDepth\":0})</script>\n"}}