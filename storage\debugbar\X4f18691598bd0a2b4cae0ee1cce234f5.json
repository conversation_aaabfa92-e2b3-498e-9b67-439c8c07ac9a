{"__meta": {"id": "X4f18691598bd0a2b4cae0ee1cce234f5", "datetime": "2025-07-23 18:21:20", "utime": **********.199439, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294879.773459, "end": **********.199452, "duration": 0.4259929656982422, "duration_str": "426ms", "measures": [{"label": "Booting", "start": 1753294879.773459, "relative_start": 0, "end": **********.144928, "relative_end": **********.144928, "duration": 0.37146902084350586, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.144936, "relative_start": 0.3714771270751953, "end": **********.199453, "relative_end": 1.1920928955078125e-06, "duration": 0.05451703071594238, "duration_str": "54.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46534016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032300000000000002, "accumulated_duration_str": "3.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1756911, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.777}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.185761, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.777, "width_percent": 20.743}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.191413, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.52, "width_percent": 15.48}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1050824096 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1050824096\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1011119547 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1011119547\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1838387121 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838387121\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-950226596 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294877783%7C14%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM0NEF5aENJRTA5L21BaURUS0hGZFE9PSIsInZhbHVlIjoiT0FlUlNuUjZQSVdXNzRVVDNmUEY3MzhiY2pVZ3pLYmFBWDBYRmFmZmVWWnRFSTZERHRxemlVZU1NZWVOdEFvOWNaMzBpQ1dFdmh6dWlkenZYMVpCVWlidkdnQnc2UE5mT0JhNEh2K1I5VEFBSm0rNDQ3MnFBM0w5cVA4WEMzNmMzTHJUcjhlTEFzWlJuTXZmWDZXVFVTdXUrRTBzVkNWdWJCbGdZTDgycGZYVWdjcXlLYUNCZG5HckZrdWxQdFQvZXhLTWx0SXhQZWNGM1F0S1pLRGlqTjRaeEVRZFYzNzViV1NYM0Q5VHVLWFliVWpmdzA5TVZsaVpDN0NjbkpMRHBRZVJkU28wWnUvTnFVZnJvWVpNOVc2TC9mcyswNmd2YzRIbHFZazZzUEpJdU5lYlBaMVRHUHJLYkdpSVhLeFlGUldmT00wNnMyN1owVEczNm1GUlRML3ZzbDFoQm9YNjMxRlNBVWdySC9HOEh6RllKbHFvTlNkMTVNQTdDNzYxTjdrRExnUzNxRkpKdFZ4MmVwME16Nyt0c0U5cmptVm91a3RwcVBGZE14SnRDd2EzZkZWTzVXMEJGcWhrb0RFQ3lLVzhFcVpvYWlpZ2oyZ0xQdFRCSmQzYWpyUzFOQzJxTGszK3pXZWpqakhPRlcyQzJBTUFGenNKSFZpUEI2T0YiLCJtYWMiOiJkZTU5N2Y1NGVmOWMwYmFhNTkzNzkzMDZiOTExN2M1YTYwZGU2ZGFjZDA3MTY5NjJjMTQxMmVlNDczZDdhMDllIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtyUGlpQVIzcHNkZDFSSm95dHRocWc9PSIsInZhbHVlIjoiTDM1SmVKNGx2VVVCdFpBRVd1SitCNkp5ZVpsM2NHTUVPNytITU52ai9DYTIxSXduTGdlaS94Lzd1SW44N0k2dGxocVVFRkhZNmMvbVV0ak5XWkpJc1E2Q3YvTU1OSmRyYmt3SkF1OS9RTWZwcmhnSVBFOHRQQ05EeTU4TGpsUnJ5c0o4aU0rc25BcVBZMjdvaGRhTHJnL0drQ0xCS3A1QkFsZHVWZWlBc05KTllSR0wxampqQ0xOQnlSRElkWEoyYytHN05BdE1mazc2enZLNVRWajBkOU1hWlNvVnVsVG9tSVhSZVd6MldZcG5vTUhuZS9KWkthcHBkeDdDaC9ETG83M2JIYVo4VndwZlZWU3F6SjRkOUdNaGJtT0RjR2NtUVA1RnU5ZVErbEhFRFYvM0grejZiUjZlL1VCQzBaM2F4MHozbVZQVTRhSFhXMVkrdWFkbWNrWkg4dEJqb0MyZmdDNzZxVmFKcFVERmZzOXppVC92UFZBZk5hWXV5RkVSK3hyd1I4aGhZNVluME9hQVFHbkhrbGp0WE56L3VBT0xSMU9lWEx1b2U4MFRwald0ZnM0d2ZBOUliek81SktwUk5nZlVDdW94L3F5Z2FMcWFyV1hUY2lEVWp5NWVmMmdzRWFpSi9wby9sMjN1Y2dKaTZIWDVyWWhKSVJFQmdvc2wiLCJtYWMiOiJhZGI0MWYzYWQyMWM4YWJkMjBmOWIzNzE2NjlmZGIwMzc5ODgyZTI2NTEyMzQwNGQ0ZjExNjg2NTdmYmJkYjk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950226596\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1757248921 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757248921\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-810302741 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNLQVFQcytwZEJMcnc3OGRiSC9TQVE9PSIsInZhbHVlIjoibWZPSVREQTV6ZHpoM3V3R1RkV2dINEoxdVoxb1Frbyt2QksvQkZSUmdHOWJIWHgxZFRtT3RPby82c2QwVFNxVFowMmZBd3B0alFPeFhCNkd4ZDJQTnRVN1hxVWlmaTQ0OXBTWWppanRTTFozZEtFZFo4ZjZIcDlFRVllNUZuRW1YSGlmMjBwSTQ4TXZMS3hlYkp3TlpDTzlCVkpLc2hVbktESXFPWmZ6aTJXNmw4d0IyMkJLNkpPU2Y3STZBTVhwblczWXJHU1ZOanAyV01vdUh1b2lKZGwzbTk2NGZiUFlNVGtzVDU1NTU1U3ZVRnNJNWRPSUl1RXkwS1hxZXFPb3QwN0UyU0YrU2RYcjJlQmYreXB0bGpLRDhPS3NMUmpaaWF4VEpVMFpUOFBqSHYxWllsNElDMGx3Nm1hWmM1OGc5ZjBwSUhxc3NpQ2gyKysyVDBrdDlNczhWN1M5ZzQ1ZWZyWm1MTUU5YlpHSEE4THgrNlBUdUluOEVjRGJzbDhUNnNDM3Bpam0wRnVoSE5RZVIxWk15YVZEdlJBSTdPY203aWdyWFJUaW1UbllCYmM0MWpBaUhxV3R2WVM1UHljR2NJTWVVRHNiVEZNYmo3SDZFU2k4U0d2UkFLbXUrbWU0NVl3VFdQRlJodmVRd3FMTWFyUlBsTVY2OW1FSzZkMGciLCJtYWMiOiJhNDMxYTlmMjA4YzJhOTBlZTI2NWZiODg3MzY2Y2YyM2Q3ZmZlZmMzMjI0MDc1OWY5ZTlmYjRmYTk4Y2Q3YjY1IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxQelZMT2RyaGErNVBkR05DYzFqb3c9PSIsInZhbHVlIjoiL2I2NGJJbVVxWEh6cWJTUThzRkd3Y3lwQjBjTlh0TmIvczBUb0VOYkszVXhub1NEaFdTd0FNYXZ0TCtlRTJMaGcvM1FsS2dvbno4a1VjSFd3RS8vSWRuVHJLelZ2eEQvR09HeVNzcVdScWx2MEYvRWpqRFlBZWIvMXoxcTBjeCtvZ1FNdW5kNlZ1MVBvMzgrNzVSNWpCUmZnZmNJVDRiM2c1SjNlenNMaWZZTmFzUkNsUmhGNUtvQU1Ya1NDYnFGSkkzY2JjUTYrZWdBeGVoVDB5a3NqalJkR3FoTHJOTDJSSzVoTnRlbXJKY1NGL3VIMWN3aDlvbHF3OXVvazIyZk1FeCs3RlhjQTEvdjV0WnFQVjFTWHBCTXhVK1pWcjNvaXVNcmpSR2V4Nk83dVJVY3ZVZkxxVlk0TlRZSXBqVEtCV1BqaS9BY3YxR0haZzJvUnBwYVNXYkJIaXg0dUR3K1g2QlFXdWRiNGJBRjZYcDBUcUV3TGtEMFhzZmxzNjlhQVZKNHhnSGRrMTR5M2o2ZWJZdnBGcHBjQU5kMWRaRXlQTnhWZmFIMkFKaHloNEhSK0JxaEo0NzlzOGhnR2dhUjZFMnRMSEJ6UWpESitvSkVMdnBSRWF0TU5GTHQ2YlprSVN5NVhnYWNWWEIrMHpqTmp0VWlVK21XZkdjeXZjY3AiLCJtYWMiOiJmMjM1MGZhZTA2Njg3NTM4OTBiNTViYTFhOTFmNTQ4ODliZGQ1YTRkODBiMGJiY2RmOTJhMTEzNzUxYjFlZDllIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNLQVFQcytwZEJMcnc3OGRiSC9TQVE9PSIsInZhbHVlIjoibWZPSVREQTV6ZHpoM3V3R1RkV2dINEoxdVoxb1Frbyt2QksvQkZSUmdHOWJIWHgxZFRtT3RPby82c2QwVFNxVFowMmZBd3B0alFPeFhCNkd4ZDJQTnRVN1hxVWlmaTQ0OXBTWWppanRTTFozZEtFZFo4ZjZIcDlFRVllNUZuRW1YSGlmMjBwSTQ4TXZMS3hlYkp3TlpDTzlCVkpLc2hVbktESXFPWmZ6aTJXNmw4d0IyMkJLNkpPU2Y3STZBTVhwblczWXJHU1ZOanAyV01vdUh1b2lKZGwzbTk2NGZiUFlNVGtzVDU1NTU1U3ZVRnNJNWRPSUl1RXkwS1hxZXFPb3QwN0UyU0YrU2RYcjJlQmYreXB0bGpLRDhPS3NMUmpaaWF4VEpVMFpUOFBqSHYxWllsNElDMGx3Nm1hWmM1OGc5ZjBwSUhxc3NpQ2gyKysyVDBrdDlNczhWN1M5ZzQ1ZWZyWm1MTUU5YlpHSEE4THgrNlBUdUluOEVjRGJzbDhUNnNDM3Bpam0wRnVoSE5RZVIxWk15YVZEdlJBSTdPY203aWdyWFJUaW1UbllCYmM0MWpBaUhxV3R2WVM1UHljR2NJTWVVRHNiVEZNYmo3SDZFU2k4U0d2UkFLbXUrbWU0NVl3VFdQRlJodmVRd3FMTWFyUlBsTVY2OW1FSzZkMGciLCJtYWMiOiJhNDMxYTlmMjA4YzJhOTBlZTI2NWZiODg3MzY2Y2YyM2Q3ZmZlZmMzMjI0MDc1OWY5ZTlmYjRmYTk4Y2Q3YjY1IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxQelZMT2RyaGErNVBkR05DYzFqb3c9PSIsInZhbHVlIjoiL2I2NGJJbVVxWEh6cWJTUThzRkd3Y3lwQjBjTlh0TmIvczBUb0VOYkszVXhub1NEaFdTd0FNYXZ0TCtlRTJMaGcvM1FsS2dvbno4a1VjSFd3RS8vSWRuVHJLelZ2eEQvR09HeVNzcVdScWx2MEYvRWpqRFlBZWIvMXoxcTBjeCtvZ1FNdW5kNlZ1MVBvMzgrNzVSNWpCUmZnZmNJVDRiM2c1SjNlenNMaWZZTmFzUkNsUmhGNUtvQU1Ya1NDYnFGSkkzY2JjUTYrZWdBeGVoVDB5a3NqalJkR3FoTHJOTDJSSzVoTnRlbXJKY1NGL3VIMWN3aDlvbHF3OXVvazIyZk1FeCs3RlhjQTEvdjV0WnFQVjFTWHBCTXhVK1pWcjNvaXVNcmpSR2V4Nk83dVJVY3ZVZkxxVlk0TlRZSXBqVEtCV1BqaS9BY3YxR0haZzJvUnBwYVNXYkJIaXg0dUR3K1g2QlFXdWRiNGJBRjZYcDBUcUV3TGtEMFhzZmxzNjlhQVZKNHhnSGRrMTR5M2o2ZWJZdnBGcHBjQU5kMWRaRXlQTnhWZmFIMkFKaHloNEhSK0JxaEo0NzlzOGhnR2dhUjZFMnRMSEJ6UWpESitvSkVMdnBSRWF0TU5GTHQ2YlprSVN5NVhnYWNWWEIrMHpqTmp0VWlVK21XZkdjeXZjY3AiLCJtYWMiOiJmMjM1MGZhZTA2Njg3NTM4OTBiNTViYTFhOTFmNTQ4ODliZGQ1YTRkODBiMGJiY2RmOTJhMTEzNzUxYjFlZDllIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810302741\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1059407199 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059407199\", {\"maxDepth\":0})</script>\n"}}