{"__meta": {"id": "Xce4ec486424a2e46533bf887c2bdfa48", "datetime": "2025-07-23 18:20:37", "utime": **********.771164, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.292489, "end": **********.771177, "duration": 0.47868800163269043, "duration_str": "479ms", "measures": [{"label": "Booting", "start": **********.292489, "relative_start": 0, "end": **********.685035, "relative_end": **********.685035, "duration": 0.3925459384918213, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.68505, "relative_start": 0.3925609588623047, "end": **********.771179, "relative_end": 1.9073486328125e-06, "duration": 0.08612895011901855, "duration_str": "86.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46535736, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02243, "accumulated_duration_str": "22.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.716594, "duration": 0.02141, "duration_str": "21.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.453}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7533739, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.453, "width_percent": 2.541}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.76038, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.994, "width_percent": 2.006}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-51848664 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-51848664\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-675461987 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-675461987\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-705315937 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705315937\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1291896652 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294833997%7C5%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlKVjdGUDFObUpUQVdoa1hsWWE4dXc9PSIsInZhbHVlIjoiS01ibStWRFVBclZIcFRMRFJRYkZydlZLaFdxTG5aS0Jtelk0bGpvT1QzZ3FWMjBKc2h3WmdJTzVPVGxPZ21WQ05TeFAxNmxub2svY0FNTVdXWjY2NWZwVE85ZzdUQzQ2QUcrdkJZUC91aWNjU1pTTkRIdlVkT29EWGxwV25ENzBJMWdxc05iajRYOElvcmRPRktNS1JKODFCeTVNL0phQU1CZmZWQ1hrMHYra2RGOS9oTUZSczVZVENJUjNWaHNMRXZMUDQ2ejVXTHpucFlyNHBiWkRjQiswRy9VMERCS1JuM0dRU2dielNOSHRxRHdQRkM4c1VFOHoyaVViczVDR25ZL04xeWZacUc5OU9GWTBwY0FRNGVmWDhuOWZUS0FkaXRxdTk1elBlQTdNVUFvNHBSQjZ3YktYSTZXT3dMekRDN1JMOEozVm1vOXU0cm96SjdEN001R2hCZXRWNXdicDlEU0Q5MXoreUFZWENIRk5RR3dUZTBjV05ucTBHSmpPb0o5NUUxOUdyR0duZzhOSEJhVDVxRjVpRUpGMVNLWUpod3dOSm1ZaHFuV0hGOVMyb1BjUnNUTFZsWm05MTVRQ2hmam9IRCszT2hxTUtlK3VicDJzNFNYcE9tR1cxcmxHdG1JM2xVUHN1ZGpFN0hGQktNTm9hVXI2YjkzSG9QK2EiLCJtYWMiOiJkMjhjMmVmZWUyNTAxZGE2MTEyYjQ4MjBiMWM5YzRjN2Q4NTQ1NDg4MzA5MTNlNWE3Y2RmZWZkOGM4NjlkMGQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklXSjUvTjU5Z1RUMEprVnZOWXZ6d2c9PSIsInZhbHVlIjoiblM3L3p4WkRod0E3R1ZEUy9GeFU5TElueFRSOHNzcTRYQUQwUW9MQ2FzakViMGx5c0tmRTB6T1o5dm40MW16TVJhZ0oyRGdQamFqa1RjZDdzMldBbFlJc0kyb0d6MThldDVzd05ZLzk4RGorU0JkUXNQbUVPR1VLTU1qU0ZCVFpZRkcraVF0WlJjK005Z2lUMlVQMkxZTEpEVEs5RmhRbjgrOFlySFpiSFBPZnpzUHJHNFBCQ2Z0Z1pPZVNrY0R6UWZpNC9JRzMzeG5tQ1FEbFdhOVZvQzdOQzBVdXdOaHc4aHFnZkdNMWxyeDVRMlRqRWNDdWdOeTBoeG0wMll5eDkyWWJXQ2ZhRHpLWU55SXZ3a0FEL0tYTGdIV2IxZmYwNFhzNnNac291NXdERXVUdHFWaGs3dmlIeVQ5cWNkQWUyL1ZhMnEwSWVVc2pabnlhS0tFK0ZBellPNEpjbGZIby9ab3Q0eHVERmJSNkVFaUVHTWVISkZuVUJsWFhZYTY4cW5kQ3kxaGF2SFNSRjVBcnF4YjFVOFlEQmNXaVJPai9mZklLZmVGZnMxUDR3K2dxNUNDVE5mOEFIdHFaRm5icmhldmtuaTQ5MmdwenNTNnZuekNnaWwydG5PbFlpanVWdUtxcXdSZFNzL05taUw2dzc4ZmpQSnFQekhwY3B1QVEiLCJtYWMiOiIxM2FkZjE0NWJkNDVlZDMwZDE0YjRjM2E3MDJjYmNhMDg0ZjA5MGI3ZGEzY2E4OGU5MGExNGVmZjY4MjAwOWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291896652\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1100547687 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100547687\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1886105026 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:20:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZwYW5TaDNOM21FR0M5TC9STnplT1E9PSIsInZhbHVlIjoiSjAxVGsrd1hDT2dXWU91NUdkejVnRXJsSjViWk4xQm51aUxZSURWdUx4NUtyeUpYOUE3OG84TGtLRzQ5N2ZyWEJuVzN3eUswVnN2UG5OVjg3Q05YYUVUT1J0Q1dCRkNyOVk2QU45bHFueWhKWjhsUFNVM0hhblQxRllxWWM0b3FRakx0RXVjOXZHQmZIUmN1UjBHaDk3NkJPRnZBdjZuRjFNT2YyREREU1Q5UjBpTzhhWEdTTlppK1ErME54NjMxaElKQXFodkpodkRtTE5YdFFPd1FkNFhpRVRBUGpHMm1ERWt0ZE5jS3JRWE91c2hLSVlYUWxpS0ZFaE9hbkd3dVlxMzBYblFJWlJ5OGpXSS8yejArUElmakdiczdmTDBVMDltSHBpbTV5WVRDQ0VrWkVkL2k5Y3dtdWFWQllJYS9vU3NrZnlUTDlGVW10Y3BpUzFTd0txaUFtUTVZdkJLNUZrR2ZmQUR5dkx3QWRrVkkyNG1kMHdKZFFlRDFQb3hmM2pueFFBUzRLeEYwWStBdGNvenZwd1l5eldrUWdBZWNsL0xlN1B6eGkzUTZxL3RuSVNrSUxEVDhaVU1KNkFvc0UrTHpoVXRMbkZzamd5bzhoTGNWbkR6eDVISk41VnZFY2taUktnQXR0QklTQXorbTNNelVnd0M0emlpclUxSSsiLCJtYWMiOiIwYmRmMWY1YTVhNGYyYTIwZTg2ZGM4OGUyOThjNTc4ZWFjZjQ4OWU5YzY2Y2JlMzQ3YTkzMjQ2OTg2YzZlODQwIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBydUNWTTVSZVNGL1VCY2hNblhVUmc9PSIsInZhbHVlIjoiaTlDNVVIenM4SU1ZMXdHMTU4RmpuVnlPNC9KZFRwWXAzNkZPZ0RUTnZET1NjTnhQM3FZL1VvNEdBcEtReHhzWG4rOUpmKzd2NXFVUTh4bTR5ZUlHNDlvYmNsREdPUGd5ZkQyV1B0YlNMTmt0SFlsQ1BqR2NySHczOVlxQzhMeGpQUWgxcHVsZzczcGtveCtBZmpNL0tkTjY0TVdrVnIyOHJDdkNYaTVkbEdRUUdmMUxXQWRiSnNjdTQyR2pGQWFvVW5HaDJWb21QNTVHMHVqYUk3NzFrb0J1MDR5MmtXekJkcVk4dzBIYTFDZXphZStpZzNlaFlDcldQMitNci8ybWw3eGIySWlYdWxXMTFjK0xPSUhKaDY0KytjdjJZQ3pHbHBBazMvYzNlSm1RWUJrYVFmS2N4SUpXTm1vSHMxaDdPZEFpSitXcU5PeGxaSitGdS95dC9qTDNFc2plU2NENHRJT3FlRUE3d3NyMUM5MTF1U1BHZDNZWFdNbE11bHFJblNSTi9CdHhjZ2dWb0NuRUFWMS9YdEtFVDBuYm1FdENYdW1rcGlORXIvMVBxcEtlblcxWlRZaTdveUNTNHE3VU5PQ1Zvak0zbDI3MnlzMDBrWTZSbGIwbDU2NTNjZXRJTU9iU2NYV29FVHg3OFhQOHlBd282Tm1BaXN3NFVidG8iLCJtYWMiOiI0ZWFkYTY1YzNhZmVlOGQ2YTJlYzA2ZjUyNTA5YzA3OWU4NGYwYmVjNWIyYzFjNjEwMGIzYjhkMWZiZDhhOGM2IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZwYW5TaDNOM21FR0M5TC9STnplT1E9PSIsInZhbHVlIjoiSjAxVGsrd1hDT2dXWU91NUdkejVnRXJsSjViWk4xQm51aUxZSURWdUx4NUtyeUpYOUE3OG84TGtLRzQ5N2ZyWEJuVzN3eUswVnN2UG5OVjg3Q05YYUVUT1J0Q1dCRkNyOVk2QU45bHFueWhKWjhsUFNVM0hhblQxRllxWWM0b3FRakx0RXVjOXZHQmZIUmN1UjBHaDk3NkJPRnZBdjZuRjFNT2YyREREU1Q5UjBpTzhhWEdTTlppK1ErME54NjMxaElKQXFodkpodkRtTE5YdFFPd1FkNFhpRVRBUGpHMm1ERWt0ZE5jS3JRWE91c2hLSVlYUWxpS0ZFaE9hbkd3dVlxMzBYblFJWlJ5OGpXSS8yejArUElmakdiczdmTDBVMDltSHBpbTV5WVRDQ0VrWkVkL2k5Y3dtdWFWQllJYS9vU3NrZnlUTDlGVW10Y3BpUzFTd0txaUFtUTVZdkJLNUZrR2ZmQUR5dkx3QWRrVkkyNG1kMHdKZFFlRDFQb3hmM2pueFFBUzRLeEYwWStBdGNvenZwd1l5eldrUWdBZWNsL0xlN1B6eGkzUTZxL3RuSVNrSUxEVDhaVU1KNkFvc0UrTHpoVXRMbkZzamd5bzhoTGNWbkR6eDVISk41VnZFY2taUktnQXR0QklTQXorbTNNelVnd0M0emlpclUxSSsiLCJtYWMiOiIwYmRmMWY1YTVhNGYyYTIwZTg2ZGM4OGUyOThjNTc4ZWFjZjQ4OWU5YzY2Y2JlMzQ3YTkzMjQ2OTg2YzZlODQwIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBydUNWTTVSZVNGL1VCY2hNblhVUmc9PSIsInZhbHVlIjoiaTlDNVVIenM4SU1ZMXdHMTU4RmpuVnlPNC9KZFRwWXAzNkZPZ0RUTnZET1NjTnhQM3FZL1VvNEdBcEtReHhzWG4rOUpmKzd2NXFVUTh4bTR5ZUlHNDlvYmNsREdPUGd5ZkQyV1B0YlNMTmt0SFlsQ1BqR2NySHczOVlxQzhMeGpQUWgxcHVsZzczcGtveCtBZmpNL0tkTjY0TVdrVnIyOHJDdkNYaTVkbEdRUUdmMUxXQWRiSnNjdTQyR2pGQWFvVW5HaDJWb21QNTVHMHVqYUk3NzFrb0J1MDR5MmtXekJkcVk4dzBIYTFDZXphZStpZzNlaFlDcldQMitNci8ybWw3eGIySWlYdWxXMTFjK0xPSUhKaDY0KytjdjJZQ3pHbHBBazMvYzNlSm1RWUJrYVFmS2N4SUpXTm1vSHMxaDdPZEFpSitXcU5PeGxaSitGdS95dC9qTDNFc2plU2NENHRJT3FlRUE3d3NyMUM5MTF1U1BHZDNZWFdNbE11bHFJblNSTi9CdHhjZ2dWb0NuRUFWMS9YdEtFVDBuYm1FdENYdW1rcGlORXIvMVBxcEtlblcxWlRZaTdveUNTNHE3VU5PQ1Zvak0zbDI3MnlzMDBrWTZSbGIwbDU2NTNjZXRJTU9iU2NYV29FVHg3OFhQOHlBd282Tm1BaXN3NFVidG8iLCJtYWMiOiI0ZWFkYTY1YzNhZmVlOGQ2YTJlYzA2ZjUyNTA5YzA3OWU4NGYwYmVjNWIyYzFjNjEwMGIzYjhkMWZiZDhhOGM2IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1886105026\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-771902978 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771902978\", {\"maxDepth\":0})</script>\n"}}