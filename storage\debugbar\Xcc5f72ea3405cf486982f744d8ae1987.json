{"__meta": {"id": "Xcc5f72ea3405cf486982f744d8ae1987", "datetime": "2025-07-23 18:23:40", "utime": **********.894309, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.388224, "end": **********.894323, "duration": 0.5060992240905762, "duration_str": "506ms", "measures": [{"label": "Booting", "start": **********.388224, "relative_start": 0, "end": **********.831041, "relative_end": **********.831041, "duration": 0.44281721115112305, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.831059, "relative_start": 0.44283509254455566, "end": **********.894325, "relative_end": 1.9073486328125e-06, "duration": 0.06326603889465332, "duration_str": "63.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46523168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034000000000000002, "accumulated_duration_str": "3.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.86594, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.059}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8780031, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.059, "width_percent": 15.294}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.88479, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.353, "width_percent": 12.647}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-623977482 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-623977482\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1391609489 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1391609489\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1553688511 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553688511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1497746587 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294910769%7C21%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJYYkpjZTVIZ2NudFVNcnNwc0daS3c9PSIsInZhbHVlIjoicnNuRW5iUWhYTjlwVzExVldmMThqdSttdlg4Q3Q2WVpmT2lBNWV0RnNqdDNxd2VqZ0xYNFpYeDJCRnpZN21DUmZTbyswTjdxVFFRVXRVdmFxU2F5NlFZL2NQUnRvOGFPME4zM05HSEZCVndJUUQzOXFSQWpFcmNxY1ErS1FhYlJJdXcwNG1MTnRka2tQQWhpbjhYcWlEUUh1V3Rod2tWa3lEVUxiaUx1NWFjdzJkcHBLVHZoclI1L0d6dCtEYkJrbzBKVjlxM3JVenBoWE5oMGN5K2Y5UjBhOHhGQSt4b2VsbzErT0NXZmRrR0RGMmtBWlpKcHVybTFrODN2enVBMVpvSWNJQ21ZRHZpTmYrZmgxSnEyZ2ptdVIxdWJ4R3BzM3BaMVkvVkNLTjlSc1V2bWJKb2lmZEdNZE1zaGVZTk9oRm9WdEd1MmhWdGtzWCtyZGdPWVd4RlFxdWc3eUpZcjBWVTgzcURsNkU3L2FwUDNCRjB1Z2hJWlpkdDIyaVJmYk1LZEh0TmY4NWc0THlNR0ZvcU5tOVZSdDRQaHZoSTdxSmkrMzllS0R0aEdGT2Q1WUNVYWVjMXhzRGlPT2dRbm9ta2tKbUk4c2hrbXMvOEVIcWd2blBlcVFabnBIVEVORzdJcFhabVVwSmNxVkRiZzVMOVcrVTQvcHRGc0lRNlQiLCJtYWMiOiI4NjZkZjYxNDk0YzUyNTY3N2Q0MGNlMjM0NmFkYTVhZTA4YmQ0MDhiMmU1ZmEyZGNmNTQyNmJmYmE4MjJlYTY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktadTJtODRzWUtzaXhzbVFSSGVGYlE9PSIsInZhbHVlIjoiTkc1cTlDemFwc253Nlo5WG1DWnAwckRHQkhxWCtWWlpuWlZPdlZabEovYS9CcTdrNEJIQUtWcHZucUtwMk5WbzhPT1BjRHdJNzhmWElYMzh3REJmbGM4dGJvbjdVV3V0Uldjb3ZadndGZlJ4RTgwUjUydVFtbmNSR2JROHFVcHdzZHM1clJ0NDVCOG00MXdxUVNqMWRNbVhHam1LZXNpVGdIU2RNUWpDRG1XVGFVcURCVGVWcjF4WGJiZmVDWWlqbHMzNUlzdkZSTlEwV21VNVVUL2FrOUhUVXdWT0szZndXMjIvcW4vLzcwSi8xSnovTGNVeXk1WGxPbUN4Y2ZwWXFJVXBKMkJVL3VKbEF1KzZ4Ulg4SDNsMkMxZzVLZVUzSmR3RmJlWHR6clNZNGVRNERxbjRyeHA2SXpvZlZ4b3hXbDVxdVo5ano5SGVjZVFaU0xVdi91enVoV1ZRbWxmNHdNc3hnZ2Y3dFdPRGZpdFBOcnRmN2hZNHZYUzRRalFZNUFLUjZsejNwcEhKeTM2SlByR3VLSitzWWo1SE96VWUyNVhUcjlmb3dpZjRxeXBHOU4xbk5NQUFhaS9QQXV4KyswZjBpR0FJQm1DZ0dZa1FRQlhxVWQxWDZoSTcxVjZLTU5ncDdkVXkyWFBkdEdPcEtlcHdXQ0RIQ1JIeFRlSkMiLCJtYWMiOiJhZGJjNTQ4OGI2NzU0OTZiN2I5M2ZhZjQyNjk5OWFiNTI3NDliZTA2MjgxNmVlYmI3Mjc0MzM2NTJjZTlkMjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497746587\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-198111979 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198111979\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:23:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRkUm9vMi9sV2pLcXJhRU9UNXlnUGc9PSIsInZhbHVlIjoiOGpXenJVQWJreXU1bVB0cGsxbzhOa3Z6bVVkTllqUFdnZWFaWXQ5ODNQTVd3VVdXR1pYU0VyWGo5ZDRiYUFVVFRMbVRMY1VNSWF2SEZnQS9RQnRqbi9GdUtPd1huR09Xa1dRdktRQzNqTmVUNmhzT1puc2d2eEhIcm9kcDFjQTBYNSthR1lYME9QMkhZS05mZDIwRDJhK2t5VmcwTVIyUmtVYnJHbnpVT0NHRzB0RjVDcEhOZFlrNUdBZDVEWmtrdzVWQU84UUdhSFd1aXQ4TnhHVDZocGt6T0pUUEtNbDVvTlNxcGN3alJ6OVE4L2RTMEhzSWNYUTZWQ1dVamJLSGhmODBuQWh2TVZnNVM2dVUzYUFrQUVPVVEwcTloNmZEcU52Q1l3aVI2YWc0a0Rxanh4c1NYeEZlckpvRkJTOE1taU1Ubk9WZkFNaC9XeWxmQ1RjOTRPZzEzSnhWbTJpMW9xR0wrU0JKUzd0UVBDYzh5dllRa25mZGdXaTZFbVMvUExLYVQxSktza2lnM093SXExaDVSRERmYWZxWDNEZEVLUzZPQUpnc0Q2VnlSTmh2UWRMdjMyNTl4N29XTVcrZUg5MW1GU1U0TjFXOHNGNy9EQ3dTM0EySGVtSk9aUWdMb3M5TldYNWsrdTUrWmRQR3hsekQ0Q1R3ck9KQ3FiZUgiLCJtYWMiOiJmN2RkNTMzYTBhMzg5ZmU0YjRhYmYxOWU3Yjc2M2E4MWRmNDA4YWZmZDA2M2JkOWY1MDMwY2VlOWVjNThiMmZmIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5RQlM0aGRRRlNocDIyOGFadnpWY3c9PSIsInZhbHVlIjoiUldZZjBlczVrbGFYdDZpUTNVUFg3eExjVm4yUHpKV0RNMGF6cnU1UHJWZExMN1lZdFdxdjVyY3lvbjgyb05CaXllcW5wb0w2cHBEMFQwd3ZrVkVGK1RxdmpqQ2YyeWRvcXkreHNLbmwvaVpkdEl3c21vVGVOVTVndkR6SUs5ajYrSEZDbnpSZTEvekFabmNnK2wyKzAycXRBMFRaeGJ4OVZNUy9kREVrRW5HYXJ3eG5hd0h3SEhENlYvOVA2amtyNXBPdWpVY0V5YTF3U1Z4UTlPYjhCQTJFZjhQQnJQazM1ZEVqMk9xYTIzQ2RFcEpQRlFab2hDclVndXcwd3ZiZnpESmJhK0xXSEh6djZvWk5teCs4YTZ6TTBnbGVKSEswVmgrTUxxam9CdWh4Slg2d3VaVFVNVUM2U2NrQTF3c283Q0pJMWpFTVZnM0xldjR4akNraXkxUWJHeXZ1S2VuQnYvRzVQUTh1TjByMjhaRmdwVUsvK1o3MVFwd2hkZzBwTzJtUm12eXNMQlpOaHZ0ajdTbXpiVStVUlZOZ3ZITlNseVBhWWhHMHVrQWZtQVhtaGhYZ3g5ZTNIbm9ja2NDcTl1c1FtM0RaYUpCbjhxMnJYK3A1YnFVdytpVkd3allCMER4NkQ3dkRlUUQ2dW5TbXNaUzNFMnQ0NmhjTFNnTkkiLCJtYWMiOiIzNDlkZWQ2OThlZDJiNWI5YjMwY2I1MDkwYmM5YjUxNmE4MDY1M2RkNjhmNjVlYzgxMjUyMzUyNTVkZDI1NWVhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRkUm9vMi9sV2pLcXJhRU9UNXlnUGc9PSIsInZhbHVlIjoiOGpXenJVQWJreXU1bVB0cGsxbzhOa3Z6bVVkTllqUFdnZWFaWXQ5ODNQTVd3VVdXR1pYU0VyWGo5ZDRiYUFVVFRMbVRMY1VNSWF2SEZnQS9RQnRqbi9GdUtPd1huR09Xa1dRdktRQzNqTmVUNmhzT1puc2d2eEhIcm9kcDFjQTBYNSthR1lYME9QMkhZS05mZDIwRDJhK2t5VmcwTVIyUmtVYnJHbnpVT0NHRzB0RjVDcEhOZFlrNUdBZDVEWmtrdzVWQU84UUdhSFd1aXQ4TnhHVDZocGt6T0pUUEtNbDVvTlNxcGN3alJ6OVE4L2RTMEhzSWNYUTZWQ1dVamJLSGhmODBuQWh2TVZnNVM2dVUzYUFrQUVPVVEwcTloNmZEcU52Q1l3aVI2YWc0a0Rxanh4c1NYeEZlckpvRkJTOE1taU1Ubk9WZkFNaC9XeWxmQ1RjOTRPZzEzSnhWbTJpMW9xR0wrU0JKUzd0UVBDYzh5dllRa25mZGdXaTZFbVMvUExLYVQxSktza2lnM093SXExaDVSRERmYWZxWDNEZEVLUzZPQUpnc0Q2VnlSTmh2UWRMdjMyNTl4N29XTVcrZUg5MW1GU1U0TjFXOHNGNy9EQ3dTM0EySGVtSk9aUWdMb3M5TldYNWsrdTUrWmRQR3hsekQ0Q1R3ck9KQ3FiZUgiLCJtYWMiOiJmN2RkNTMzYTBhMzg5ZmU0YjRhYmYxOWU3Yjc2M2E4MWRmNDA4YWZmZDA2M2JkOWY1MDMwY2VlOWVjNThiMmZmIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5RQlM0aGRRRlNocDIyOGFadnpWY3c9PSIsInZhbHVlIjoiUldZZjBlczVrbGFYdDZpUTNVUFg3eExjVm4yUHpKV0RNMGF6cnU1UHJWZExMN1lZdFdxdjVyY3lvbjgyb05CaXllcW5wb0w2cHBEMFQwd3ZrVkVGK1RxdmpqQ2YyeWRvcXkreHNLbmwvaVpkdEl3c21vVGVOVTVndkR6SUs5ajYrSEZDbnpSZTEvekFabmNnK2wyKzAycXRBMFRaeGJ4OVZNUy9kREVrRW5HYXJ3eG5hd0h3SEhENlYvOVA2amtyNXBPdWpVY0V5YTF3U1Z4UTlPYjhCQTJFZjhQQnJQazM1ZEVqMk9xYTIzQ2RFcEpQRlFab2hDclVndXcwd3ZiZnpESmJhK0xXSEh6djZvWk5teCs4YTZ6TTBnbGVKSEswVmgrTUxxam9CdWh4Slg2d3VaVFVNVUM2U2NrQTF3c283Q0pJMWpFTVZnM0xldjR4akNraXkxUWJHeXZ1S2VuQnYvRzVQUTh1TjByMjhaRmdwVUsvK1o3MVFwd2hkZzBwTzJtUm12eXNMQlpOaHZ0ajdTbXpiVStVUlZOZ3ZITlNseVBhWWhHMHVrQWZtQVhtaGhYZ3g5ZTNIbm9ja2NDcTl1c1FtM0RaYUpCbjhxMnJYK3A1YnFVdytpVkd3allCMER4NkQ3dkRlUUQ2dW5TbXNaUzNFMnQ0NmhjTFNnTkkiLCJtYWMiOiIzNDlkZWQ2OThlZDJiNWI5YjMwY2I1MDkwYmM5YjUxNmE4MDY1M2RkNjhmNjVlYzgxMjUyMzUyNTVkZDI1NWVhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1039247679 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039247679\", {\"maxDepth\":0})</script>\n"}}