{"__meta": {"id": "Xd3424999c5a607dea606fcbd01eae925", "datetime": "2025-07-21 01:34:47", "utime": **********.046154, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061686.621273, "end": **********.04617, "duration": 0.4248969554901123, "duration_str": "425ms", "measures": [{"label": "Booting", "start": 1753061686.621273, "relative_start": 0, "end": 1753061686.982531, "relative_end": 1753061686.982531, "duration": 0.36125802993774414, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753061686.98254, "relative_start": 0.3612668514251709, "end": **********.046172, "relative_end": 1.9073486328125e-06, "duration": 0.06363201141357422, "duration_str": "63.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46008480, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01262, "accumulated_duration_str": "12.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.010323, "duration": 0.011689999999999999, "duration_str": "11.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.631}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.030477, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.631, "width_percent": 3.09}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.037219, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.721, "width_percent": 4.279}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-28887973 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-28887973\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1864202109 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1864202109\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-675387603 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675387603\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-582193650 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"150 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061682120%7C14%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhGME1tYVVCN1BsaFQ0M1ZlTzA4VWc9PSIsInZhbHVlIjoiUXBYTWpqa25UQjdFLzcvWDZuQm5pbklFZTJXZFVrQWlMdjZrSVNXTWlDRmR3d2dYc1g5QXM4RHFXSFBhblpKcGt0azVySjJQenVpWUE0b25haitxUGczOWdheGNTRjlqWU9CK1JUWDhTSUppQzZPSXdQc0NtcWVGcitFK1dOcjhMMjEwSGlYRGxCc0hDTHNDVFk4QzRoclA0S1NYcFJzTkFtT0plNmFPbERodnJIYnRDQzUvck16ZGhrWmRyaHE5bHNSSFlIK0VIcEc0Nzk3SG9WcC9wZmR2dVArajBHL0xaeVRPaG5EYmEwaWNpZE4rclI0Uzl6REswb2ZscTZhUDNaSEMwbEJqTnZEUktkM0d6ZncyZ3FQNkNGYzM4UEx4Tno1c3ltclpOMVEvYWVrQmJOVno5c1BzaGIxQ1g2WXZ0bVhQZUVzaXc5UlptV0VGaGE3cktMKzUxeWNma2NQVE5pSHRaY1hUS1gwZGsyUGNHdU9GU0FkOHFKQkxyWmtDb09YVUJuTzJHSVlUU3o5L1VjOXRiendteEhZbTFtNGZ1SXRpU3VyOEk4QXBHWStvVHNRdG1uZ1BTWjdGTWEzUCtDNE1USWhUT1hTM1VjajJLTHR4YkxRMys5MUl5YWZTSElWcVNKYjRNWmVsUE14SzlQWUFUL2RkT2gzR0hlZloiLCJtYWMiOiJiNDQwNDNiMmNmNjIxMDQ3M2JhMjAwNjJhMmQ4ZTkyYWIwYWFhZmZkYWUwYzk0OTVmMWNiODE2M2IyZWEwZGRkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iko3bkkrWHBFRERlZlVyNUxTV2FUWXc9PSIsInZhbHVlIjoiTHFWQ0tISWcxbXM2L3FvS0RUWkg4em95SytFMzgydnpyOGtCSENieHRnaEFnVTVNRkdrQ2hSNnlaRThWbTdQWXFOMTVqK2NTOFl1dWhZd1JhcGh1TTVWVFlGeCtESUNyQVpqNUt5a3ExUHJrdU03MEtBUVFsWWtyM1hHaFp0M3I0ejhmR21RWkpMVHhQRzRtY2c0OGhzaDg4c3l0cjUvQjFhMlYxQ3lzWVhsUXdhNlI4NGc2ODJBMlVoeitEeGswYTBrUWJXMlZzUnhIeTI4dWRReVRkbWtGbnVVcEp1ZmJ0S1VRbU4yTU9KdWEyU25INDZVZTNJYXpjSnhMYUR1aTZBanFSWk9RZFFmTlRVZkhkSmVJVFk3SHZBTllVRDI3N0luc3h3K0xKcEtTaGRBb2Z6MzJCYzRHYjE0LzRIblpvOFFzMkgxYkJ5UGM3TzNQZWF1dE9ZS1Q2Q1E4QjBraVpjMEt2emMweGVSTzdmM284bHE3aytnbS9QZGFycFRZbHhWbmJBWUhXZ3h3YlJSeFhsZlBwS3paUFhaUDVSenkzZWtuL3VCeENIVjRCMVkwSzhGL2dsS3RCbU8wNHROSzdOOVJuZUh3SnU4Sk10d1hVUEtPMEg4L3JGckw2QjNBSjV3Uk9kUDhSY3BqTUU2dVR3ZTdTbmpjL2xwSHh1TWIiLCJtYWMiOiJjYTVjYWFlMWEzOTJmMTI0MWM5ODhhYWU0N2NiZGYzOTYxMTQyMjdmMjJjZmFjNmVjZDlkNjgxZjUyOWZhNDZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582193650\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1493408705 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493408705\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-601236278 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpTU3ZJdHphUC9hNkIrZExqaXprTWc9PSIsInZhbHVlIjoiWWhhTGxpYWdLRjA5dWx1QmtCYlNRNlNnS1VUQndmQTFIT3ZTdFFod1Z3N1NwN2tGMGhBaUEwWG8xdTROMGtvVXJoT3lOc0ljZlBQcHJFVDRXanVoc3dUaGpUMkgxRjllYTY1WlZUTGpIQnVpY2FhM3haV1RDVlRsbFU0bEU2Z2x1ZUxZY1FWb2hpdk9yVVI4K21uWGQyQjZ4NkU0WXlwbXpyMXl0Wk5YWCtvR0dudW1VaXpUeDF4ZGthdnlkblUvemE3Mk5mUHZCM1JZVXdFN0dzZm5GZmFKenl5MW5aUWZwWnpwL0dOYnVrUGl1YmNrLzg2VVZ3cC9ib0M5OTd3ZmdETlBwQU13Y3dkZGx3NXcvZ1kvajVReGRDd25OeEYzSXc5TUt1ZlFYYXpId3Nia3lsV1pWa0VhZit0RC9nWlg1WjJNTHVUemV5U0lSRHhIbUl2blVrWEFxaGNQK0dxNXRCaUJOZkVhMkYxb2grUU4zRzVUQjNKQzM0S0Fpc0NhckdLWUQvR1Y5VFNkU3UrMzdtOUx4QzMzelRPZjg4MUI0ZkRNdE1zMXBxTjZld2tZb2NRY2Z2bUVCRTJ6SlA3bFJoU0xQQUNDdC9DaTN6cVdONTU3YXQyeXVEMUJWdFdqWFBlL1NVbWV6MUQzaHF4WFJFUlVFQkZYR2FOdWwzS1IiLCJtYWMiOiI1NDI0OTUxMDc2M2Y4Njg0ZTk5MWQ4ZTRjZmVhODUzNjk3M2Q3MzMxZTkwMDEwOWZkMmNhY2M1NTdmM2M1YzdjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFIKyt4K3F6TkZpb0oyc1hNZ0xBQVE9PSIsInZhbHVlIjoic1F2bW0xM1h2bkNoa0xvZGVJUkxKaDRmYndqQWJuSUkrMjBYdkpjWkV3cGJKVjFBZng1SWJXRGkvdmIvVUxLU0JuOUQxZVlCRk45YVptQnQxVjJZbmlUakY2WmxGM1RtK3RZWUh3bW5TSU4wWGVlWGMyL1NESU1OekFvSmlVRU9YUW5QdWdyaGdNTjk0NUdNNFcrTkpFdC9pNjZUUWZ4cXMwN0gvRVhxd1V3akJrVGJoRzJPeFpUdzVDdklBNFlwME5hL0RkRFUzRTFKSC9xZ3BoT00rL0NSbTcrRmZEeWZ2eE9udHVmd2pLbC8xR1pyUnpsajBKQXMzSXRzSGFDUzBQeGxnek9hVjZNRWVZbWNOYmp3d0NZbDRROHRhL1JWUHJFOVdvcVBRZ2kyTlZTMElGYVVXVGx4NWcvbFNTWW00UzJMdFhER0F6U2VNMnZUd3poMjVWS0h3bXZZeUxSYUFldkh6cGswKy9sSGNxZXNVSklwTVlSVTU1WExpTUc4QVpEbWVOMmhzMEF1eWswaTRPMlZ3S0ZmZ1NlcWhJVjQraHRxZmt5djJQbHhMNmIrb2x5amFDMEtkR0J1UGE2eUVualZiSWMrWktFWFpZWXJ5R2REZW9KSkhjb3Y0cXFWRXI1VHllckI3MDZTV0h5TDhQOE4zdzRHbCt5ZVRyUisiLCJtYWMiOiIxNjdlZjkyOGU4NDY5NGJkMDAyMzMzZDVlNWExNzU3ZmIwMTU0NDMzZDVlMTkzNDFkOWRjNDQ3NGNiOTUzMzg0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpTU3ZJdHphUC9hNkIrZExqaXprTWc9PSIsInZhbHVlIjoiWWhhTGxpYWdLRjA5dWx1QmtCYlNRNlNnS1VUQndmQTFIT3ZTdFFod1Z3N1NwN2tGMGhBaUEwWG8xdTROMGtvVXJoT3lOc0ljZlBQcHJFVDRXanVoc3dUaGpUMkgxRjllYTY1WlZUTGpIQnVpY2FhM3haV1RDVlRsbFU0bEU2Z2x1ZUxZY1FWb2hpdk9yVVI4K21uWGQyQjZ4NkU0WXlwbXpyMXl0Wk5YWCtvR0dudW1VaXpUeDF4ZGthdnlkblUvemE3Mk5mUHZCM1JZVXdFN0dzZm5GZmFKenl5MW5aUWZwWnpwL0dOYnVrUGl1YmNrLzg2VVZ3cC9ib0M5OTd3ZmdETlBwQU13Y3dkZGx3NXcvZ1kvajVReGRDd25OeEYzSXc5TUt1ZlFYYXpId3Nia3lsV1pWa0VhZit0RC9nWlg1WjJNTHVUemV5U0lSRHhIbUl2blVrWEFxaGNQK0dxNXRCaUJOZkVhMkYxb2grUU4zRzVUQjNKQzM0S0Fpc0NhckdLWUQvR1Y5VFNkU3UrMzdtOUx4QzMzelRPZjg4MUI0ZkRNdE1zMXBxTjZld2tZb2NRY2Z2bUVCRTJ6SlA3bFJoU0xQQUNDdC9DaTN6cVdONTU3YXQyeXVEMUJWdFdqWFBlL1NVbWV6MUQzaHF4WFJFUlVFQkZYR2FOdWwzS1IiLCJtYWMiOiI1NDI0OTUxMDc2M2Y4Njg0ZTk5MWQ4ZTRjZmVhODUzNjk3M2Q3MzMxZTkwMDEwOWZkMmNhY2M1NTdmM2M1YzdjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFIKyt4K3F6TkZpb0oyc1hNZ0xBQVE9PSIsInZhbHVlIjoic1F2bW0xM1h2bkNoa0xvZGVJUkxKaDRmYndqQWJuSUkrMjBYdkpjWkV3cGJKVjFBZng1SWJXRGkvdmIvVUxLU0JuOUQxZVlCRk45YVptQnQxVjJZbmlUakY2WmxGM1RtK3RZWUh3bW5TSU4wWGVlWGMyL1NESU1OekFvSmlVRU9YUW5QdWdyaGdNTjk0NUdNNFcrTkpFdC9pNjZUUWZ4cXMwN0gvRVhxd1V3akJrVGJoRzJPeFpUdzVDdklBNFlwME5hL0RkRFUzRTFKSC9xZ3BoT00rL0NSbTcrRmZEeWZ2eE9udHVmd2pLbC8xR1pyUnpsajBKQXMzSXRzSGFDUzBQeGxnek9hVjZNRWVZbWNOYmp3d0NZbDRROHRhL1JWUHJFOVdvcVBRZ2kyTlZTMElGYVVXVGx4NWcvbFNTWW00UzJMdFhER0F6U2VNMnZUd3poMjVWS0h3bXZZeUxSYUFldkh6cGswKy9sSGNxZXNVSklwTVlSVTU1WExpTUc4QVpEbWVOMmhzMEF1eWswaTRPMlZ3S0ZmZ1NlcWhJVjQraHRxZmt5djJQbHhMNmIrb2x5amFDMEtkR0J1UGE2eUVualZiSWMrWktFWFpZWXJ5R2REZW9KSkhjb3Y0cXFWRXI1VHllckI3MDZTV0h5TDhQOE4zdzRHbCt5ZVRyUisiLCJtYWMiOiIxNjdlZjkyOGU4NDY5NGJkMDAyMzMzZDVlNWExNzU3ZmIwMTU0NDMzZDVlMTkzNDFkOWRjNDQ3NGNiOTUzMzg0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-601236278\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1296178771 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"150 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296178771\", {\"maxDepth\":0})</script>\n"}}