{"__meta": {"id": "X93e4b853c4f6202a6caecfb30bc67dba", "datetime": "2025-07-21 01:18:02", "utime": **********.351962, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060681.92018, "end": **********.351976, "duration": 0.4317958354949951, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1753060681.92018, "relative_start": 0, "end": **********.297988, "relative_end": **********.297988, "duration": 0.3778078556060791, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.297996, "relative_start": 0.37781596183776855, "end": **********.351978, "relative_end": 2.1457672119140625e-06, "duration": 0.05398201942443848, "duration_str": "53.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46519136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025599999999999998, "accumulated_duration_str": "2.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.326482, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.625}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3368108, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.625, "width_percent": 16.406}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.342611, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.031, "width_percent": 17.969}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-491446980 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060674456%7C6%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktOU0loV2pSUTcyTEQ0MzNSUkxNNWc9PSIsInZhbHVlIjoiM2YySm05Z1VYVXdyUzNrcm1mU1R1ZkVDMi9URGdadzdxR0l3VkJLZnBCcnd4RDh1MHI2S29tYWN6Q0VnemQyOElld0xxQ2hKVlVrdTRGemQyZ2xSeHNqaFMraW84K0FPQllVcThUYmszS3E5OHFZZzdSY2FOUUZJaTF6aE9UWVhBRnpIUTlRTXVsQW5NVnBvLzVDYk1KdCtpMy91bW8vRDk1TWJrNFhEUzlMdEs3V0wzVDlERE56ek1ZbEhYcGdWNm1xVlBvTEcwa1M4RVZMVFdyRTRLK1hPYU4xNUN1ZHNmeU1La3RBZkpOMEJFcURRQXplelNMMzVIU2g2YzVyWFdIZU1Nd2Y2ZVZMWVkwQ3NTZUxNSkp2ajlvNVRndjBiUHI5dU1UdlpXNlNZZklmQWpxY3FvRGprSy9td3Y2WlpWaWNhUWlXWlVVczFQcXhWTEwvSXVnTW03MVJueXBqa2pnOVBYWHUremIycllXcXQxRlYvNEg4TS9uWFVLbnVGWjZCcUd3WlJwV2NKa3RvZTdEbzJlRW9ZaWxWZkE4N3FQUnVwK2QvYU9OVnVwVWsyUlY2T1pDWTV6a3BRWlhrUER4L2J1a1RIbHJMTVMvNXRYTVF0V2M0Z2ZxakVmVDJUQThjU2JqdGZybXphM2MwdEprZGZGTGtnelVKQnE3VTkiLCJtYWMiOiIxODFkNmFhODYyNGIwYTFlYjcwMzI5Yzc5ODEzNjFmOGJiOWZhNjJjODNlOGIwYWU2MzBjM2Q5ODc0Nzk1YTVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldJNHVDdVFqbGxRNHlqSml6TnNpYlE9PSIsInZhbHVlIjoiQmpUT01tcytBQThIV0lPamJKbG5rT0hQTCt4Z2M5ZTJURXBROFBUc3VFN3dRd0FlUUN6a1ZSbkF0OUgyYnh2MTBLR2NoZzFNcGp5SmJmUEVvZysyb3Rsb3E0Y3hQQytXQjZVdVJmenlPNFE4SHRXUHNpKzZ5dUtod1NlSENuSWVsUmRvaFpSUWRzQUNqNFZoeFZyRGtOREkvZWNZbUpUeFlLRjVjMS9ycGg2U1NMbkJQTE9OWVdrWHFZYmI0eDB2b29QczlWT1k5WjFrUGQrbmtzR1hpeWp5M3cvZXZHcnVjc3p2NzFycDZMSS9RNmNFQlh4Y3ZkYnIwck1sTnZnaFUzR1FUZmxZSU10M0lUV1N4Uys5dm5qc21lT0JBbXhhL0ZlTWkzWWhjQ3d4NEozc3VNRkZpNmREMHo5ajBxSG1GNDVFVy9zMldPclYwalkvM2FVckU3NFR2bnYyS3g0dlFXSVh1ekhHaFR4aEVtZTJkbUxyR0U2MWJIY0dsNUE3UHZBcHNxQzdUdjdsYTB3RDZoU014WkFFS3B1Y3hkcTMyR0JGRkV6WDEyV29EQVdKZ3JPSGg3N2Q0djF3L1dKZkFyZ1dWMkRTM0NibU16MGQxU1ZBVjdxQXdTSEMzaVc2c09KQVhaaGQzcDRMc3hUdlVWUXZaTnNOZHFBOXE2aUIiLCJtYWMiOiI5YjQ5NGViZjEyMDBjM2ZmMjU0NjhlZjgyN2ZlMTcwMzVjYTVjMmNmMTY0NTU0YzNmZTRmNmQxOGNmYmZiZWY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491446980\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1270131406 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270131406\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-494697009 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:18:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVkRFF0TDBWMTMyT1VXZ3lQaEVTUEE9PSIsInZhbHVlIjoicEwzdFZmSHVCSHZEbThEcWsyQS9VSVM4YUU5a3BreEVxS2YvaW9GcVQzRzZua3Y1YU12TFhMSmEzSUgyNDVHYlVER3RKemYzam5yMS9pSkV4YnpoMkxOQUU0bXMzUWdHSHU2RTFBLytlaDJzU1FCZ01RMW90QjVMMDRZMWRaUGc2eHVYUjNzeUVmYjUvTFFVWGJjSlRmcHJuQzlRaVV3MTZNTXFtVDBjTlhCbzdwdG5sVTBNL1lFZUlybGZVcnZObTlpTkRQV3B0RzJNUVBNaUdkYnZjd1MxOGRXUmFWeVN4K213U3E1dXBPSWtEd0cvT0dEV2lmUVEyajFhemo1OUhobkdWYWQybHYrNTNhS09QQWZWZkZrSGFJTWxEVDhtbkhkSmcxTk1xVWpuaDJ0cTBPaVVSMFVPVDc0RXNKZDFaaUlyV0pnV3dTaDBGY1NvLy9oaHB5b3ZUNE9RbFlUdmV5eDI0eGlRaEh5ZjRzMjhNRHpvbGZrUk5OaTNqalkvTU5QVEhKV3VaUnpnekpZbnpOQ0JYODk0M2tVV1ZyaFJ2WTVBQmhnRjdNTnh0d0ZEd0o2cGlid1JzaDhqdEI1UlhzcGlXeFRPMDlIZytaaDJNemtRbi9vSXJvYk4vdXdtNlZIT1E3cTBhOUhTR3lYZG9obWNwRzlBNFN4RFZaNFUiLCJtYWMiOiI1YTFhZGFlYjY4ZWRjMGNkZmFkNmM4NGRkZDliZGE3ZGZmY2JkY2IyODcwYmE4YmYyNDMwOWEwNjM0NWQzYWJkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdVLzBrNDQ2cCthcFFJZG90ZlJKYVE9PSIsInZhbHVlIjoiQTk4WUhWWWhtTU16ZnJtWnhOUTlBYXhBQWNRMjBzWkVwb2Z4NEpITFpKQUVLclFsdnU0OGVSUHViY2llblBjWHl6MEM3MFAzelN6bTZ6NGd0d3hlVi9MNkx2bzJnbVZEZ2FURW9RVitPc3FIcXVsaGlkZDYvVnN2ZUFPeXJ5a00wTUJCZFc5bE9TcHhpTU5tUmhScm1tc3NGa0tKQjUwSTh0MCtpMlVUSWN1dEJTWDBmMDVNRUxRQmp5RUxZOWFwQlVPYTZrRjBPVGZCeGwxUWNHRjFFSVJiSVFsUVkzdVEzZFVBK29IWE1sOHBWbkNuQWszdFZmOVNqYjZIVCswOWtLbGpUUTVla2VxOGFaL1FObklWOEcwaVNhY29FNnpvcjZSWUcwUCtsakg5alJ0RUVhTGxOaUdyRFhNT2QzeVhvQnhTT1kyNGQvZU5iYWtkb00xRjBSVC9iMmdSdzY5ZHhEdG5MRnBpL0dIQzAxZ3BrNzRJYWQ3ZkRtazkzamcvcWkyZ2FzdWcwdmpiU3ZhaEFWei94SVM5SUJyN2NLbDJSRzU2dWxiZ1dFQ1lYWVloLzRUVlVBd0RHbFlHUUQrUzdCRWk0a0h0dC9zL2JocU9zbS92NFdvUDU3Mm9yWURzb3BLeHR3Yll3Tk41U2ZLT3hyVjlydEdycExpNTlleFgiLCJtYWMiOiIxYmNkYzYwNTc3N2E1ZTNiMzBmY2U1ZDczZGNiMWY0YWRmOTBmMzkyYjllMDlhNjFmOTI2NDJhMmYwNGNhZjY3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVkRFF0TDBWMTMyT1VXZ3lQaEVTUEE9PSIsInZhbHVlIjoicEwzdFZmSHVCSHZEbThEcWsyQS9VSVM4YUU5a3BreEVxS2YvaW9GcVQzRzZua3Y1YU12TFhMSmEzSUgyNDVHYlVER3RKemYzam5yMS9pSkV4YnpoMkxOQUU0bXMzUWdHSHU2RTFBLytlaDJzU1FCZ01RMW90QjVMMDRZMWRaUGc2eHVYUjNzeUVmYjUvTFFVWGJjSlRmcHJuQzlRaVV3MTZNTXFtVDBjTlhCbzdwdG5sVTBNL1lFZUlybGZVcnZObTlpTkRQV3B0RzJNUVBNaUdkYnZjd1MxOGRXUmFWeVN4K213U3E1dXBPSWtEd0cvT0dEV2lmUVEyajFhemo1OUhobkdWYWQybHYrNTNhS09QQWZWZkZrSGFJTWxEVDhtbkhkSmcxTk1xVWpuaDJ0cTBPaVVSMFVPVDc0RXNKZDFaaUlyV0pnV3dTaDBGY1NvLy9oaHB5b3ZUNE9RbFlUdmV5eDI0eGlRaEh5ZjRzMjhNRHpvbGZrUk5OaTNqalkvTU5QVEhKV3VaUnpnekpZbnpOQ0JYODk0M2tVV1ZyaFJ2WTVBQmhnRjdNTnh0d0ZEd0o2cGlid1JzaDhqdEI1UlhzcGlXeFRPMDlIZytaaDJNemtRbi9vSXJvYk4vdXdtNlZIT1E3cTBhOUhTR3lYZG9obWNwRzlBNFN4RFZaNFUiLCJtYWMiOiI1YTFhZGFlYjY4ZWRjMGNkZmFkNmM4NGRkZDliZGE3ZGZmY2JkY2IyODcwYmE4YmYyNDMwOWEwNjM0NWQzYWJkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdVLzBrNDQ2cCthcFFJZG90ZlJKYVE9PSIsInZhbHVlIjoiQTk4WUhWWWhtTU16ZnJtWnhOUTlBYXhBQWNRMjBzWkVwb2Z4NEpITFpKQUVLclFsdnU0OGVSUHViY2llblBjWHl6MEM3MFAzelN6bTZ6NGd0d3hlVi9MNkx2bzJnbVZEZ2FURW9RVitPc3FIcXVsaGlkZDYvVnN2ZUFPeXJ5a00wTUJCZFc5bE9TcHhpTU5tUmhScm1tc3NGa0tKQjUwSTh0MCtpMlVUSWN1dEJTWDBmMDVNRUxRQmp5RUxZOWFwQlVPYTZrRjBPVGZCeGwxUWNHRjFFSVJiSVFsUVkzdVEzZFVBK29IWE1sOHBWbkNuQWszdFZmOVNqYjZIVCswOWtLbGpUUTVla2VxOGFaL1FObklWOEcwaVNhY29FNnpvcjZSWUcwUCtsakg5alJ0RUVhTGxOaUdyRFhNT2QzeVhvQnhTT1kyNGQvZU5iYWtkb00xRjBSVC9iMmdSdzY5ZHhEdG5MRnBpL0dIQzAxZ3BrNzRJYWQ3ZkRtazkzamcvcWkyZ2FzdWcwdmpiU3ZhaEFWei94SVM5SUJyN2NLbDJSRzU2dWxiZ1dFQ1lYWVloLzRUVlVBd0RHbFlHUUQrUzdCRWk0a0h0dC9zL2JocU9zbS92NFdvUDU3Mm9yWURzb3BLeHR3Yll3Tk41U2ZLT3hyVjlydEdycExpNTlleFgiLCJtYWMiOiIxYmNkYzYwNTc3N2E1ZTNiMzBmY2U1ZDczZGNiMWY0YWRmOTBmMzkyYjllMDlhNjFmOTI2NDJhMmYwNGNhZjY3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494697009\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}