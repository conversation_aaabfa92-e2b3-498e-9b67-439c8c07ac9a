{"__meta": {"id": "X06b6a03d7946f58ab1958d81c3dd263f", "datetime": "2025-07-14 14:39:52", "utime": **********.880847, "method": "GET", "uri": "/customer/8/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.01267, "end": **********.880864, "duration": 0.8681938648223877, "duration_str": "868ms", "measures": [{"label": "Booting", "start": **********.01267, "relative_start": 0, "end": **********.373288, "relative_end": **********.373288, "duration": 0.36061787605285645, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.373297, "relative_start": 0.3606269359588623, "end": **********.880866, "relative_end": 2.1457672119140625e-06, "duration": 0.5075690746307373, "duration_str": "508ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52939984, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x customer.edit", "param_count": null, "params": [], "start": **********.473656, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/customer/edit.blade.phpcustomer.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fcustomer%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "customer.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.802916, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}, {"name": "1x components.mobile", "param_count": null, "params": [], "start": **********.80438, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/components/mobile.blade.phpcomponents.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fcomponents%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.mobile"}]}, "route": {"uri": "GET customer/{customer}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "customer.edit", "controller": "App\\Http\\Controllers\\CustomerController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=175\" onclick=\"\">app/Http/Controllers/CustomerController.php:175-191</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.023980000000000005, "accumulated_duration_str": "23.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.405365, "duration": 0.0202, "duration_str": "20.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.237}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.43444, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.237, "width_percent": 2.335}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.449462, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 86.572, "width_percent": 3.628}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.451736, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.2, "width_percent": 1.71}, {"sql": "select * from `customers` where `customers`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 179}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.45646, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:179", "source": "app/Http/Controllers/CustomerController.php:179", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=179", "ajax": false, "filename": "CustomerController.php", "line": "179"}, "connection": "kdmkjkqknb", "start_percent": 91.91, "width_percent": 2.669}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'customer' and `record_id` = 8", "type": "query", "params": [], "bindings": ["customer", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 180}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.459097, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 94.579, "width_percent": 2.919}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'customer'", "type": "query", "params": [], "bindings": ["15", "customer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 182}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.461616, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:182", "source": "app/Http/Controllers/CustomerController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=182", "ajax": false, "filename": "CustomerController.php", "line": "182"}, "connection": "kdmkjkqknb", "start_percent": 97.498, "width_percent": 1.251}, {"sql": "select `name`, `id` from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 183}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.463592, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:183", "source": "app/Http/Controllers/CustomerController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=183", "ajax": false, "filename": "CustomerController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 98.749, "width_percent": 1.251}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-510106807 data-indent-pad=\"  \"><span class=sf-dump-note>edit customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510106807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.455407, "xdebug_link": null}]}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/customer/8/edit", "status_code": "<pre class=sf-dump id=sf-dump-864815077 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-864815077\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1637967751 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1637967751\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1253966902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1253966902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1694412194 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503987336%7C18%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlOb2I5SmlnanRnT2JOdUhPN05ocFE9PSIsInZhbHVlIjoiOWJaS25WYlhreU1JVzQzU0Q1UStLejNLK1ZQNGR6RElkQkNkVzJ0RDhqN2JFODB1YjA3aE5icDJ0WEg3ODNqbjlmc1Y3VHdHNjFETXQzeHJmUGt3NmpGeEZqZU9qVmNFZGpqK0VvcE9PbGFseFJYVVczaHovM284ZlhsaEM2RW1qd00zWHl3VWw0QmJMcWdTQllpM3BYbmM5UGJ4NXREL0lmaW1oVXdabnNaVWRpejhjR21ETS9US3EybUkweEgvdDFtYi9iZHdBWEFMSHFHZzN6bmlyYWtqY0dJb3k1eDUzbmVNaVBWcllqZUM5c3E5bWRrcHRaR0cxY004bncrMS9XellqOEhXakhDRzlna29EaTdwV3FmRzRSQnI4eFBnZ0M5MHNpQkxOZXlZazc5SHNNSm45SGdURWtFbjFDMmJQLy9GUzNhTWptRTc3ZFB5ZHFpVEJNb2gvcmxQQWJ1L0dqRkZZTXRjb2hBcjFQK29CdmtaeTBzaEgzNUxOblB0VTZ5RWdReEt4Wkd2ZjRZaEo2Z3NoNWFMMjNWeFpGRHR6NlRuM1JoTEdSVHY4c0VleWhaNkJDb1NCM1pXMFdGeXFOVFErMTlZei9uL0N0ZDBaeXIzM3BldTE0SmtqL2xscVFVbkVmd3pOWVpKbnQ0VHRKUVg3UE5mcHpNL0RSaDkiLCJtYWMiOiI0ZjAxNDcwNDc1N2I2N2RmZDMzZTVjYzJlMmViNmJlMTM5YTJmZGZkNThkMjA1ZmQ4NTZkYjBlMTJhZjBmZGM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVCUjRFKzdUR0s2UWt5QStkUGdiR2c9PSIsInZhbHVlIjoibjRPemNDOWFjZEl6SkxEYkZJWWpOZE4wT1hSYUtkaFEyRVFncEE0N1BRNHYxN0Y1cXBET09zdDc2dEpIb1h6S1cxRmJMZFJvSXVnWU5MQldJbWhCaUx4dmtBWlVIU3htVkVNS0pXa2I0Y0JWNEtFZ1FjTTVudW9PKzUwaERnS2FPOEUwUEFuR29JSjdsektXR3pJeFd4dDlvOGt3cFQ1UlBVTjBOS2NWdGlRRHc2TjFyWXFhdTZMa1JqVStaZElnU1Vmc3dtZTFBYWZkVllTeXdiRVo0enNTbmpXZ0pCd1hnSWFUbHM3dVJ1QnRBc2xsR0VGMXlNcDYyN1NTemtQYlBINUVNZ3QrWS9OSC9EOTJEbyt4QmRQRUNuU2dOc2EyempJUDZmcmwvWXpmV255ckJERFBtUTJwWitHYTBROWtIcnA3cm9rK2Y5OTRhNzAyWXo3bjdld3d0NW0vSUl4UGpab1pxbUxPeFcwaXEyRnNKYm85QnhOT3NweU9rc0NTK2hkangrZnhRTnoxQjdoZ1RtTWlwT1hTQUlsa2l3Y3hsdW1yR09mNnNGM21SYWRab1R3TDBLdzRMa0RsZUxQejl1cWl6UG5PcGo0YlVMVmpRRlE4eWZoQi9lMGZac1UrcVdTTndmRkhNY3FVUURxYzQ3dzhrRTk4OFd3V2tlcWEiLCJtYWMiOiI1OTllZTlmZWNjZDY4MzVjNDFhOWUyMmE4ZjA5M2M3NTJhNjAxZWUzYzYxMjBkN2FjYWMyOGE2M2FiM2Y0OGFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694412194\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-808395969 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808395969\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:39:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRzT1oyUit5QnNNYkp5NUE0c09QNEE9PSIsInZhbHVlIjoiRlNBeWVlZ3UxeEJTaGF5d2VZQU9NSGFldkc4NFdONVBBZG5XVzlUTUdCNFRLUjBsWW9CUS91WlhiekQ2Z0Mzc2cvNmd5ZUZYaExZS05wL3JIeWo0TFE3R1lCWDNqQzRuU2QrQU9WMkZMOTJLdTE5Z1ljSnREQ3VoN0U1K3ZwVlphRGNLZjdUNUQ1RXQvM25uYmt2M05uNkkyYW5RNU0zbVlybFp3L1ZwWHlBTk9LSjZmNVhnNUk5TjhZdnArQk94WS9JVEpHZzBRY0gyZ3JSMk5yc0h2MHR1RkdzR0xlS3Q0dzR0ZzBJd3lJRmRaRHhLQ3BMZ0Q5L3hZSW1LYmU2aUtkMEc4Y09NSHVTVjR4VjJZTm9YeWFicHBuQ1hWWGwwR1R2WGJjbU5MaUdXNTAxa3J2TnFoZUZoTjcrV2tNUEtlb24vMFovUVJMT0k0d0JnYmdKVWRDWDhCVmFnRlNLb0ZiSUV0a2FPRlJjRElSYnVLN1Z0L1Z3YkQwcDBpMW9WVHZ4bUhDdFpERmdDKzlCYnRNZEw5NGtTMUV0ZlBaK0FPUUorMnZlQXZSemdqOW43NTRiaTZaa0xCTlNhUjJRakZ3NWRDdEZBM3FINC9zeWxtaHdybzB1MTNEOCsvelBjVDI1YkhUTGJYb0ptYnJ3aEVnbUFhSkphWWFxVm5MWGsiLCJtYWMiOiIzYmVkNGNhNmQ1ZWY4MTc4ZTNlZGQ3MDNlMjBiNjA3NGNhYWM5ZTllYjk4MDRhM2VlYmFjOGYxNjk1M2I2YTVjIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJEL0JKTXgwYzdQR2tiNWJyekpqd0E9PSIsInZhbHVlIjoicDVCbkN4SlJGTlRyRlR3SWZuVUFHWWx2R0dHL1FUd2FZeHdHWFFXUHc1enZmazllbHI5ZzB4ZURVMng1dWVRcjVWblNCSDVLdVdIZHlzckpza2RsS0ZENDE0akZVMzVxbk95QTc4bGN6bGNIQlFZNXJoa2FrY2RKTmdLRGtUSmN4cGpaV1BBM2ZwUFErbmFWbnhrVkVhOGJQNGdORE0yUnpiUTlJOE9SOFY5ZkNtMkt3R2ZDZUNoUVJ5RW4wanVoVHlWQ2ZXR1FYK1FRTGYyNUdCT000QkhNVUZHWCtuZlNQTEZ6NmMxMHpkdC9OS0V6MXBtSkNMaUkvVnZTM0VFeFc4aUdpYkpJZ1RTSzBJeUo1OVRTdjFLdkVXOTZLNmlHWklwM3paSERPSjZGMXRtNEF0TmtVTU1XTkZqQ21ueGZ0alFDTmVYdmc3bkQ3V2pkUCsvcVEvMzU3OXRuYmpNM2dRRFNXaWs3eWZiRWE5QnM4U2c1Y0cvaWdRaEhkWjBsM01jaUdIS0drQ240eEw3RElTMW1xNjVEMk1uMHFhUmdSMzZkMVhwR2JkeC9NSHd4akU5WHpCSkJMcWtwRjVlSWFDYksrV1pGaW1Yby95S3NteHlZaEdvS21TWVNOYVlSZ3N3K0pnTWQ1TC9XZTRtMkdmMEhLK0srR3Ntck9vZ0wiLCJtYWMiOiI0NWE2ZjBjZDM5YzI5NzhlMzE4NDJlMTcwZDU1NDk4MTliYzllNDVmZmE5NjNjMGI2ZTA2ZGE1N2E2MTU5OWQ3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRzT1oyUit5QnNNYkp5NUE0c09QNEE9PSIsInZhbHVlIjoiRlNBeWVlZ3UxeEJTaGF5d2VZQU9NSGFldkc4NFdONVBBZG5XVzlUTUdCNFRLUjBsWW9CUS91WlhiekQ2Z0Mzc2cvNmd5ZUZYaExZS05wL3JIeWo0TFE3R1lCWDNqQzRuU2QrQU9WMkZMOTJLdTE5Z1ljSnREQ3VoN0U1K3ZwVlphRGNLZjdUNUQ1RXQvM25uYmt2M05uNkkyYW5RNU0zbVlybFp3L1ZwWHlBTk9LSjZmNVhnNUk5TjhZdnArQk94WS9JVEpHZzBRY0gyZ3JSMk5yc0h2MHR1RkdzR0xlS3Q0dzR0ZzBJd3lJRmRaRHhLQ3BMZ0Q5L3hZSW1LYmU2aUtkMEc4Y09NSHVTVjR4VjJZTm9YeWFicHBuQ1hWWGwwR1R2WGJjbU5MaUdXNTAxa3J2TnFoZUZoTjcrV2tNUEtlb24vMFovUVJMT0k0d0JnYmdKVWRDWDhCVmFnRlNLb0ZiSUV0a2FPRlJjRElSYnVLN1Z0L1Z3YkQwcDBpMW9WVHZ4bUhDdFpERmdDKzlCYnRNZEw5NGtTMUV0ZlBaK0FPUUorMnZlQXZSemdqOW43NTRiaTZaa0xCTlNhUjJRakZ3NWRDdEZBM3FINC9zeWxtaHdybzB1MTNEOCsvelBjVDI1YkhUTGJYb0ptYnJ3aEVnbUFhSkphWWFxVm5MWGsiLCJtYWMiOiIzYmVkNGNhNmQ1ZWY4MTc4ZTNlZGQ3MDNlMjBiNjA3NGNhYWM5ZTllYjk4MDRhM2VlYmFjOGYxNjk1M2I2YTVjIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJEL0JKTXgwYzdQR2tiNWJyekpqd0E9PSIsInZhbHVlIjoicDVCbkN4SlJGTlRyRlR3SWZuVUFHWWx2R0dHL1FUd2FZeHdHWFFXUHc1enZmazllbHI5ZzB4ZURVMng1dWVRcjVWblNCSDVLdVdIZHlzckpza2RsS0ZENDE0akZVMzVxbk95QTc4bGN6bGNIQlFZNXJoa2FrY2RKTmdLRGtUSmN4cGpaV1BBM2ZwUFErbmFWbnhrVkVhOGJQNGdORE0yUnpiUTlJOE9SOFY5ZkNtMkt3R2ZDZUNoUVJ5RW4wanVoVHlWQ2ZXR1FYK1FRTGYyNUdCT000QkhNVUZHWCtuZlNQTEZ6NmMxMHpkdC9OS0V6MXBtSkNMaUkvVnZTM0VFeFc4aUdpYkpJZ1RTSzBJeUo1OVRTdjFLdkVXOTZLNmlHWklwM3paSERPSjZGMXRtNEF0TmtVTU1XTkZqQ21ueGZ0alFDTmVYdmc3bkQ3V2pkUCsvcVEvMzU3OXRuYmpNM2dRRFNXaWs3eWZiRWE5QnM4U2c1Y0cvaWdRaEhkWjBsM01jaUdIS0drQ240eEw3RElTMW1xNjVEMk1uMHFhUmdSMzZkMVhwR2JkeC9NSHd4akU5WHpCSkJMcWtwRjVlSWFDYksrV1pGaW1Yby95S3NteHlZaEdvS21TWVNOYVlSZ3N3K0pnTWQ1TC9XZTRtMkdmMEhLK0srR3Ntck9vZ0wiLCJtYWMiOiI0NWE2ZjBjZDM5YzI5NzhlMzE4NDJlMTcwZDU1NDk4MTliYzllNDVmZmE5NjNjMGI2ZTA2ZGE1N2E2MTU5OWQ3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}