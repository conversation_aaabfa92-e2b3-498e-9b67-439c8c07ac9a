{"__meta": {"id": "Xa2f17a075b6df9e134349787a9734f25", "datetime": "2025-07-23 18:21:44", "utime": **********.132686, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294903.655309, "end": **********.132703, "duration": 0.47739410400390625, "duration_str": "477ms", "measures": [{"label": "Booting", "start": 1753294903.655309, "relative_start": 0, "end": **********.073649, "relative_end": **********.073649, "duration": 0.41833996772766113, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.073658, "relative_start": 0.418349027633667, "end": **********.132705, "relative_end": 1.9073486328125e-06, "duration": 0.05904698371887207, "duration_str": "59.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00263, "accumulated_duration_str": "2.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.103345, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.821}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1157, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.821, "width_percent": 14.068}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1221912, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.89, "width_percent": 17.11}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-19295228 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-19295228\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-520910530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-520910530\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-948772366 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948772366\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-891654267 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294893959%7C18%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNpK3FQTEZDaDQzUWpaY2hrWnp3bkE9PSIsInZhbHVlIjoiZGlmMWU0L1ZoWTNQellRc3NlV05NNlN1NjV6eElMOXRYbHFyc2dkWlVoaFVJaDh1NWQxQUdPNE11eU0yQXNXTEwvMC9iRGdUeXpidHpNMEViRDhDK3RzamlDdXZ2M1JLc3ZmV3cxZ29vT2M1LzY3L09BTG9RRnkzbzNzbG5qNndveDdscDBhVjUwYzVLMWdpVkxnSWViUTRmbHd1bXV4bExiT0pGcnk3MG9BZlNYdzFDbXQyVDZvajlmQXYzK1Z5V0ZsMmE0RDR1c2ptSHViNGtUdnhtWUhSeWJPWjZmMFE3dVNXdDdqSkJlM0U2Qm9zSmw4WXdXeFhOQWVqeWdJTDNtR3RwWE5vSUd1ZWlkb0JIZVA3SysyanVBMjNtSkFTd2czMUg1bVV6KzFaeC9Ec1gweFNqVGxPd214S0ViRDdhY2puVDg1dVlCanZSNW5tUnIyek1VYTR0cU1RQVRBN1R6eFEvd2dBOW5XOG9CbTljQnBKcEVNTUZ1N0Uya2R4c25LZnZMVk1iaWNyRmxVTFZ6ZEhIVWZUSWpic3R4TmV0QjRzanBVTkU2QUpVRmZ2bHlSejEwTGRnbVRXd3lESDVlU2tTNjVpOUc5dXRMZVNhWlBuSlJmOG1Hd0lFZi9xdEJmdkxGeC94d05oRlliYzVQZmN3MU1tVXNYYWduangiLCJtYWMiOiJiMTk0MDZlZjJlNjA0ZTcxNTg0MjJiNTRhM2QyZWFhYWYxMTFkOTQwMjJlOTY1NWNhZmQ2NzgxMTMzYmIzNmVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZhOGxWTXpVMDM1MytXZDlRRnV6c1E9PSIsInZhbHVlIjoiY09DeFNIQUdkRUVucVhTazQwWTEzeDB6a0kyMlJ2KzIwZytOdGFja05qSEtoRHFldzJSZ3MzWmVGTmplWU4ybk1HVHdWR0pQZkhYdFpobTJJak1zV3NMSUpzdFJzTjU5akNUVjkwQVBuZnJRYnZDV2VIdlNJc2tiSkJCeWVPR2ZMQjVIcDNmUStPVDc3dXZ4Z0RST1JycmVERnZheko3YVVFUFYzSmJweUxnc2x4QWtZYnVXTnJNbDM0T3pNTGs0Z1FPa2EzZC9udHp2TVEvWi9OVlliWHNrWS80MnhQcmFlVkhQWlI4ek5Ja041dFJ4WndZSVJvN3dONmVGKzMrbUliaWZDVzJHN00wV0ZHaFdRZTlXUCtPT2FNMitNOHB0dGc1R0hyZXZXV3M2RHFHUDVhZnp4NjQyK3R4aTJLWWt3bDV2enR1QXg2MmYwMlRVdlRSRzZGYlQ5WFhleHIrWk1PT3FoTnM0WGFDSG41aVZ5U1NRRFRaVy9IUlVSdDV2V1E2N21wdmIwOEZuZ3NIczYxRTdWMEJSUDJaMk9JRmxzbnRvYkhiQ2RUQUswc1M0M2Z0VUhxMFdLR05XNDRKWXpzaGhuVTNnZ3ptUjZNU1R2elQ2a1NnMFZHcUNhRmc4NmJOcTZXSWxqQnhpcmpkRkRkMmhHZGwzUGJCODBJNGgiLCJtYWMiOiJmNmJjNGRkMWZmYmJjMGM3YzhmNWNmMGJjODQ2ZjdmNmY0MzEwOWY4ZDFjMGU1ODdhZGNjZjI0NTA5ODFmOTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891654267\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-977736282 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977736282\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2105325034 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1DaVEwSkJaTzRwR3JzbnNNOXRBOXc9PSIsInZhbHVlIjoiWVBEVlR6bEpLdG1rRGZWd1paNHFNdjhXdzkwQnBBdGZ4VzFuVjB1QzFhTjFPZHdSUDh4VkI4cHpsVDV6bkJtQlQyamRwcEtFaUpIV2diRU5hbWhwcWhHK0hsMERlbndpVzlpcy96QUxqakhxb09XTW9ENHVyWWVSNEdXbEplOURmaEZUcng4eUFwaG5UNWVRWmg1YzV4Zkx5eTJEOFN5ODBkaUtsUXcyalBCZm9TeXlSRE5DQzFkTjNMcmw5Z2NzMG5HaUJPdDFZYVRKc2ZyZDdPUFNQMzlZTlFQU2ZoQVVUeE5LcWZocEMzQU9VdzJtdm1LZTN3dTFRbnAxZjcyQjBvZlcyNFpKSi9rWlJwNHZOZ0YyelFIaG1EQ3RKQWpRanI5OXRubVd4NVZBaTZlV0VTVEJVMEh1TUx6SStRREIxU2huQm5tbXc1b3dZaHlKQmhaeCtyWDYvRkQ4RUtXdTFEcDBvOVUvTVNTS2ZEbHdRNGVBUHlKdkJmTWEvRUV3cFhLQVk5MWdzTWM4K3pQOGZ4ME9NbXpJSWk3NlJxQ0tHMHNSMGZzbGx5NG81YTZ5Q1RMQlcyY3ovejFwM3l0QVIvb1ZiSE5TWE9qSlp4RmZjdHFzYjB4SmVhNG9oeDF5SFZtUGlySUVhODhtUThacThzMXZodGJHK0I4c3U4M1AiLCJtYWMiOiIxNTJhNDZjZGNjNzdmYmIzNmU3MDJjODU1OGJkZTM4OGZiNzhjZDI4MTc1YTJkZTM4MDIxODQ3YWEwZDQ5NmUyIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkF4ajcxaFY5U1d6OHZxaHNMa2s0Q3c9PSIsInZhbHVlIjoiTFIzTS9XYWRlZExnS1N6Tm5ldHFqc2lySm94ZEYrYzdpQ05RN3ZQQzU5Q05QMG4xZkZJb2tDUGpsLzBZR2kxYVZmaDJkY1Q1TW9iUWo2WlRnNU9xQWZocWt0dnc4UzdxbnpwSHFjN2lEa1J3UHZXek82ZTFSZUhIcHFKYlpsU01nL291Zys0VitQcVNYTWJCYjYyc0tFZkFWRHVZMElMQzBiQlZ5Y25LN1lwK0Q0Ly9ZTThlQ3Y1c2RXZ3IxV2lpMDMwd0c0Mkx6K3FFWHhqMEhKY1NISFNIQjRDN1c0blFJR1R2TUExRm90ZGpra3pGRWhKSWNHU3NIc20yWHduVGswWE14aFladGJHMmk0aUFMdHpROXBCQXpldWJzSm0zckF4UTFDYU9GWkloNUdxYWhMc0RpYXBaaFFhSTd3TWJ1WDVSaWRzbGdhS0xyRlZqcmNMSTQxaFFGelc0eW5DeElPNjJkbVYzdlpTd1AzZHpBaUxBLzVhY0E4czhqM2dqdFhKUE5OYW9HRUorQnJMZXIvSDJxSEM3ZEV2b1NsRktFN2ViSXRXSENQY2NITWhaSUlubFdmVFkzbjl6MUtoSnZvVytia2s4WkZDaXBqTlJ4OWVicTh1NG93cEtIaEwvRC9teUo5a2krNEdXS3Q3dSs1S0xjZUpab1phSzZTa1UiLCJtYWMiOiJiMzkxYmQ0YTNhMWI5MjU5YTMxNjUxNDVkNGEwZWJjN2RjMzcxMmM1ZTFjNDgwZmI5MTA4MWM1MzRhMTdlMjNkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1DaVEwSkJaTzRwR3JzbnNNOXRBOXc9PSIsInZhbHVlIjoiWVBEVlR6bEpLdG1rRGZWd1paNHFNdjhXdzkwQnBBdGZ4VzFuVjB1QzFhTjFPZHdSUDh4VkI4cHpsVDV6bkJtQlQyamRwcEtFaUpIV2diRU5hbWhwcWhHK0hsMERlbndpVzlpcy96QUxqakhxb09XTW9ENHVyWWVSNEdXbEplOURmaEZUcng4eUFwaG5UNWVRWmg1YzV4Zkx5eTJEOFN5ODBkaUtsUXcyalBCZm9TeXlSRE5DQzFkTjNMcmw5Z2NzMG5HaUJPdDFZYVRKc2ZyZDdPUFNQMzlZTlFQU2ZoQVVUeE5LcWZocEMzQU9VdzJtdm1LZTN3dTFRbnAxZjcyQjBvZlcyNFpKSi9rWlJwNHZOZ0YyelFIaG1EQ3RKQWpRanI5OXRubVd4NVZBaTZlV0VTVEJVMEh1TUx6SStRREIxU2huQm5tbXc1b3dZaHlKQmhaeCtyWDYvRkQ4RUtXdTFEcDBvOVUvTVNTS2ZEbHdRNGVBUHlKdkJmTWEvRUV3cFhLQVk5MWdzTWM4K3pQOGZ4ME9NbXpJSWk3NlJxQ0tHMHNSMGZzbGx5NG81YTZ5Q1RMQlcyY3ovejFwM3l0QVIvb1ZiSE5TWE9qSlp4RmZjdHFzYjB4SmVhNG9oeDF5SFZtUGlySUVhODhtUThacThzMXZodGJHK0I4c3U4M1AiLCJtYWMiOiIxNTJhNDZjZGNjNzdmYmIzNmU3MDJjODU1OGJkZTM4OGZiNzhjZDI4MTc1YTJkZTM4MDIxODQ3YWEwZDQ5NmUyIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkF4ajcxaFY5U1d6OHZxaHNMa2s0Q3c9PSIsInZhbHVlIjoiTFIzTS9XYWRlZExnS1N6Tm5ldHFqc2lySm94ZEYrYzdpQ05RN3ZQQzU5Q05QMG4xZkZJb2tDUGpsLzBZR2kxYVZmaDJkY1Q1TW9iUWo2WlRnNU9xQWZocWt0dnc4UzdxbnpwSHFjN2lEa1J3UHZXek82ZTFSZUhIcHFKYlpsU01nL291Zys0VitQcVNYTWJCYjYyc0tFZkFWRHVZMElMQzBiQlZ5Y25LN1lwK0Q0Ly9ZTThlQ3Y1c2RXZ3IxV2lpMDMwd0c0Mkx6K3FFWHhqMEhKY1NISFNIQjRDN1c0blFJR1R2TUExRm90ZGpra3pGRWhKSWNHU3NIc20yWHduVGswWE14aFladGJHMmk0aUFMdHpROXBCQXpldWJzSm0zckF4UTFDYU9GWkloNUdxYWhMc0RpYXBaaFFhSTd3TWJ1WDVSaWRzbGdhS0xyRlZqcmNMSTQxaFFGelc0eW5DeElPNjJkbVYzdlpTd1AzZHpBaUxBLzVhY0E4czhqM2dqdFhKUE5OYW9HRUorQnJMZXIvSDJxSEM3ZEV2b1NsRktFN2ViSXRXSENQY2NITWhaSUlubFdmVFkzbjl6MUtoSnZvVytia2s4WkZDaXBqTlJ4OWVicTh1NG93cEtIaEwvRC9teUo5a2krNEdXS3Q3dSs1S0xjZUpab1phSzZTa1UiLCJtYWMiOiJiMzkxYmQ0YTNhMWI5MjU5YTMxNjUxNDVkNGEwZWJjN2RjMzcxMmM1ZTFjNDgwZmI5MTA4MWM1MzRhMTdlMjNkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105325034\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-40329526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40329526\", {\"maxDepth\":0})</script>\n"}}