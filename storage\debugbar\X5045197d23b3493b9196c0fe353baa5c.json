{"__meta": {"id": "X5045197d23b3493b9196c0fe353baa5c", "datetime": "2025-07-21 01:27:29", "utime": **********.582369, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.081713, "end": **********.582383, "duration": 0.5006699562072754, "duration_str": "501ms", "measures": [{"label": "Booting", "start": **********.081713, "relative_start": 0, "end": **********.498831, "relative_end": **********.498831, "duration": 0.4171180725097656, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.49884, "relative_start": 0.4171271324157715, "end": **********.582385, "relative_end": 2.1457672119140625e-06, "duration": 0.08354496955871582, "duration_str": "83.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024399999999999998, "accumulated_duration_str": "24.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.535492, "duration": 0.023559999999999998, "duration_str": "23.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.557}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5685232, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.557, "width_percent": 1.844}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.574862, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.402, "width_percent": 1.598}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-759318259 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-759318259\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-869724441 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-869724441\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-624595429 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624595429\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-222882062 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753060801217%7C7%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklJTW5ac3J3MTdXaEswQjlZdzY1MXc9PSIsInZhbHVlIjoibUY5WW5LaUprMUlvNERhWnpxajMyQ2pvWUdPdndxdVN0QmdNejN3WWVRUUwyZnBzazlpTXVOYmpXYXNUUWdFNVgvS0Nrd1FjL1Q2dzRNNjFpSWRXMDZ1ZHgrbDE3bFRvSUlKYm9YUFlhMjB4cmR1QmhjUjBjc29iUnBSak1tWGY4TDdwMUdIcU5WM2tNTjFSRmw4elIzUW9IbGVGY0MwODhhRlY0Y2NJNUg4TTJLaEd5TXpVNC9OVU5CRUVRaGRwVVZ5MmVZU0U1UUpJSkovaS9UMnNYTXVMSkt6b2lxRXBCVis0SlQ5bXYxdHpMRVhBSDEyZE01NFpTS0paMUdLVTRsWkJIdW9CVjJDL0JqaUEvY0JRQmRCdWUxTWVVaSs5aC9OWkppMUZzVlJJM2l0OFhBZEpVOUxVS0hkNDk3WjBxa2EweUxwdnY1bWJ0bUJ2V0U2QkhEb29nYmJPS1l6RW5scTA1dUxJY0VzTitLM003WmtzOVpjZmpOWHpkckN0UFdvd20wQWtZeFJUY1liWTAvZG9sQjZFSHpBbXczajM4VVQxVVBPYVUxZUVEbG5lMlFXdTl5Ly9WTGlBTmxCbXl2RlZqZGJIRkd0ODVUREY4NE1MWTAyb255RnJTdnN5bG5sQ3BVMEZJN3hBVGxKNWE0N3k4WndmaDNqMUovbHkiLCJtYWMiOiI0YzVmYmQyYWI1OTQzZmExYWRlMmE4ZTc3YTQwNjI0MmEyYWI0MjU5NTJlN2RiNGY4YTk1MzgwZTVlMTE3ZmYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImQvbitkYkx0SGtsdVo0dkVxWlYvb0E9PSIsInZhbHVlIjoiRzhzcHYvQXdrUWtlOXlhWkw0SHp0RDJpQUdOaWJRT09SdFRWUERCSWs0ZXdSalJ0K3ZzVGQ3Q0hlYSsyczJyMTFmNnFObGVkcGpsS0JNWndQNk5mQzJUeFUrUE9PMHlKVm9kS01zR3J2MUFEd1Y2Z0dDTFE1ZVFydFZHcGpVU0tVYm81cGlJK2JQdFJ6Q2xHUDFrY1l3bkJBSVBTNHlQMXVNRlhZNU9XM2E4Mis5Q0VoSjBUdDJROTJkMlBCVWdDa1dwaHdrdjBGTHFPZk1OaHI4N3lDWGN2WGRvbjZmeTZ0TTBKM01iN2tkdWFsc2dxNVpscmV1WEl3THJyS3JOVTBrRXFQSzkzdkhwTVZHWVFORGtmeXlpQUdxdnJURmNVNEp3RUplUUUyVzNmTTFYaUJrWU01UW1KYUlSaE9QTTdNZkdQYmVFVU1GRTltd3BPSE5IYWhmWWo3ZnB1eDdoQXNxQ3dNSVdZUnh1bzB4VTk1WmNFTzVrN2gzQTdNT3hGamJ3Vno3Mmk5UWVDRzNMdFZEU1hxTVpXWmZic09DYW84enJ2WGI0S3VmSnlhWjVVQzRXUWQ3TjJHbndLRDA1eWdRcmdtQ3dhSDhkSmNYZjl5V1FPbk5LTUUycWlvWTlzcTZnekpTaGZqZ2ZNRjl4ZmxOa1B5TXZjWUUvejlXRXoiLCJtYWMiOiI3ZmZmMDhmN2ZmNGU2N2YwYmY0NGYzZmZjYjc2OWJhOTA4OWI0NDE2ODc3ZjM2ZWM2NDk3YTQ5YTRiYzMzODAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222882062\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1093151542 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093151542\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-518875954 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:27:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR0ZDdHNm1aOUNXc2xyM28xRm1oRHc9PSIsInZhbHVlIjoiVEJlOXNiZGttMFRsZnRGUVp1WkJGbFJqN1B5UG93dEhRSlhmbjdUMzdIRTV3VFY5OEFXQS9XTnd0a0FpNFZCQmVvUUVtVjI0WkZzblpwaCtlTjJaU0NFbW9mS3lLTkJSNVpYZytVeDVuM0FJaGdGYXlVUFNwejdZMWJWa3BOeWlZSWtzbytmTUMra3BoOWJUVy9QVFZQSjZocE5nL21BODVubTRHbTNqVXYrb3NHL2U5bFRvazlhd0JqelROZ0NiMk90WmpCSzJ3WmF6NlVubDY1eXJHNkptMkpEc1hFd3hVSkxtSW5Ic21zOWQweVRhOVFZZVBqQkJ1SG5lZlFiYVJ6b0t4TW4wZmJjNDh3QUMvY0IyM2FWbWl2cElkZ1U1eVpaZENEeGVvbUNId1M2WFZHWURQOVc0NnZyZlN6OThBZzR1dThLV3g0dk9XOGwzZUlBSVdmVTYyL0l4MVNyVCtzbXhhbHdEc1lFNzZUN2RWNnNUdnQ0d29mZFZ5c0d4aUo3THdndGE4YnptaFcwbGs1WUMzM2VJUlVkNzA0a2NHVjBKY3FWSVhCUG9oM0FPSUJlNU5rd2tYTzhUNHI3V1dzVnJzSllLdFFZcWlUY0tJUHQ0Q1E0WXBhbVJ1NXNTZnBvWmZ4d0RmR3NhaFgvTTFyZHJHZXlyVkN4dmJTcXQiLCJtYWMiOiIxNDdhZDYyMWQ5MTZlMzY3YWYxMDI5ZjU1MTY4MjUwZTYxNzlhNWZiOWEzYzEwYjdkYTUwMGFhZWRhZjVkNjQyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:27:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdpczJpRmtnZ2hZYmZrTVh3SlBOcnc9PSIsInZhbHVlIjoiOVRsbGN3cXpMTzltNUVJZk9CYnkwaG5qVmQwV3phdlFtRlpjQmRIUFRCcGw1MlBHK3VKdGpnVys0S3dCQVd2WU9xZ2lndEtLQ2NsaHBWanNIWDBKNTBpMHc4ZmJMSGlONnpFTGRObGxXeFEzMHRiMkUxcXlsa1lZS1FPTk5yQVFTcEI5RXRJdVdjUGxWWHRuaFhjNjkxMVBKOWZyWG9MbWFmUjdvbzdHVFpST00vOG8ra2EwcGc5ZzZwZG9iRGxxWnY1TVJlNjdjbXd2TGF0cHIvSnJjemg5eHVxSGRUY0ZIOUt3U2V1M1hDRWNLd21KdVIvNnRIRi9jSVlEdCsrTm5oMzkvUGFUYlhqb0lhR1prZmJwRFNmYkh6MHBBUWVSWGlxOUdLRUt0SmY5K1ltdmNPaWZqOG9RK0IrclZwcjhLQTZJb2ZjR3VLK0NCaDVWV2lvNFpxUEhZZ1hmZ2F3S25VMDl2ZDlVME5Tc01pTEJmb0VIVUhscHA2blJFRjJiMHJoS0xnM2xNUkhuVElQeVQ2YUtUSjJldjVFbk9uSys0Ylo1azd5clR0RWZ4SUN3WUFSV3ZsUEVDOXVtODFzYWw2ZFdXTDJpRFpJcmI2SHQ3TkFMNmVHYk44RXdhTGozd1dSQVF0NDhqYzF2cEl5cjFNZldJdFM5TjdQSHhZb2IiLCJtYWMiOiI5MjBkN2MyOWYxYzM2YWJjMzI1NzdmMjU4MDBmMjk1ZGM2OWQwOGRmM2M4YTljYmYzMmUyNjViZjNiM2E3N2UwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:27:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR0ZDdHNm1aOUNXc2xyM28xRm1oRHc9PSIsInZhbHVlIjoiVEJlOXNiZGttMFRsZnRGUVp1WkJGbFJqN1B5UG93dEhRSlhmbjdUMzdIRTV3VFY5OEFXQS9XTnd0a0FpNFZCQmVvUUVtVjI0WkZzblpwaCtlTjJaU0NFbW9mS3lLTkJSNVpYZytVeDVuM0FJaGdGYXlVUFNwejdZMWJWa3BOeWlZSWtzbytmTUMra3BoOWJUVy9QVFZQSjZocE5nL21BODVubTRHbTNqVXYrb3NHL2U5bFRvazlhd0JqelROZ0NiMk90WmpCSzJ3WmF6NlVubDY1eXJHNkptMkpEc1hFd3hVSkxtSW5Ic21zOWQweVRhOVFZZVBqQkJ1SG5lZlFiYVJ6b0t4TW4wZmJjNDh3QUMvY0IyM2FWbWl2cElkZ1U1eVpaZENEeGVvbUNId1M2WFZHWURQOVc0NnZyZlN6OThBZzR1dThLV3g0dk9XOGwzZUlBSVdmVTYyL0l4MVNyVCtzbXhhbHdEc1lFNzZUN2RWNnNUdnQ0d29mZFZ5c0d4aUo3THdndGE4YnptaFcwbGs1WUMzM2VJUlVkNzA0a2NHVjBKY3FWSVhCUG9oM0FPSUJlNU5rd2tYTzhUNHI3V1dzVnJzSllLdFFZcWlUY0tJUHQ0Q1E0WXBhbVJ1NXNTZnBvWmZ4d0RmR3NhaFgvTTFyZHJHZXlyVkN4dmJTcXQiLCJtYWMiOiIxNDdhZDYyMWQ5MTZlMzY3YWYxMDI5ZjU1MTY4MjUwZTYxNzlhNWZiOWEzYzEwYjdkYTUwMGFhZWRhZjVkNjQyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:27:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdpczJpRmtnZ2hZYmZrTVh3SlBOcnc9PSIsInZhbHVlIjoiOVRsbGN3cXpMTzltNUVJZk9CYnkwaG5qVmQwV3phdlFtRlpjQmRIUFRCcGw1MlBHK3VKdGpnVys0S3dCQVd2WU9xZ2lndEtLQ2NsaHBWanNIWDBKNTBpMHc4ZmJMSGlONnpFTGRObGxXeFEzMHRiMkUxcXlsa1lZS1FPTk5yQVFTcEI5RXRJdVdjUGxWWHRuaFhjNjkxMVBKOWZyWG9MbWFmUjdvbzdHVFpST00vOG8ra2EwcGc5ZzZwZG9iRGxxWnY1TVJlNjdjbXd2TGF0cHIvSnJjemg5eHVxSGRUY0ZIOUt3U2V1M1hDRWNLd21KdVIvNnRIRi9jSVlEdCsrTm5oMzkvUGFUYlhqb0lhR1prZmJwRFNmYkh6MHBBUWVSWGlxOUdLRUt0SmY5K1ltdmNPaWZqOG9RK0IrclZwcjhLQTZJb2ZjR3VLK0NCaDVWV2lvNFpxUEhZZ1hmZ2F3S25VMDl2ZDlVME5Tc01pTEJmb0VIVUhscHA2blJFRjJiMHJoS0xnM2xNUkhuVElQeVQ2YUtUSjJldjVFbk9uSys0Ylo1azd5clR0RWZ4SUN3WUFSV3ZsUEVDOXVtODFzYWw2ZFdXTDJpRFpJcmI2SHQ3TkFMNmVHYk44RXdhTGozd1dSQVF0NDhqYzF2cEl5cjFNZldJdFM5TjdQSHhZb2IiLCJtYWMiOiI5MjBkN2MyOWYxYzM2YWJjMzI1NzdmMjU4MDBmMjk1ZGM2OWQwOGRmM2M4YTljYmYzMmUyNjViZjNiM2E3N2UwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:27:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518875954\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-186732487 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186732487\", {\"maxDepth\":0})</script>\n"}}