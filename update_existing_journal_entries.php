<?php
/**
 * Update existing journal entries to calculate total amounts
 * Run this script after adding the new category fields
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

use App\Models\JournalEntry;
use App\Models\JournalItem;
use Illuminate\Support\Facades\DB;

echo "<h1>🔄 تحديث القيود الموجودة</h1>";

try {
    // Get all journal entries
    $journalEntries = JournalEntry::all();
    
    echo "<p>عدد القيود الموجودة: " . $journalEntries->count() . "</p>";
    
    $updated = 0;
    $errors = 0;
    
    foreach ($journalEntries as $entry) {
        try {
            // Calculate total amounts for existing entries
            $totalDebit = JournalItem::where('journal', $entry->id)->sum('debit');
            $totalCredit = JournalItem::where('journal', $entry->id)->sum('credit');
            
            // Update total_amount if it's 0 or null
            if ($entry->total_amount == 0 || $entry->total_amount == null) {
                $entry->total_amount = max($totalDebit, $totalCredit);
                $entry->save();
                $updated++;
                
                echo "<p style='color: green;'>✅ تم تحديث القيد رقم {$entry->journal_id} - المبلغ: {$entry->total_amount}</p>";
            }
            
        } catch (Exception $e) {
            $errors++;
            echo "<p style='color: red;'>❌ خطأ في تحديث القيد رقم {$entry->journal_id}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>📊 ملخص التحديث</h2>";
    echo "<p>عدد القيود المحدثة: <strong>{$updated}</strong></p>";
    echo "<p>عدد الأخطاء: <strong>{$errors}</strong></p>";
    
    if ($errors == 0) {
        echo "<p style='color: green;'>🎉 تم التحديث بنجاح!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ تم التحديث مع بعض الأخطاء</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}
?>
