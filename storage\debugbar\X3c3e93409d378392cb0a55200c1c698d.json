{"__meta": {"id": "X3c3e93409d378392cb0a55200c1c698d", "datetime": "2025-07-23 18:22:43", "utime": **********.768499, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.189444, "end": **********.768535, "duration": 0.****************, "duration_str": "579ms", "measures": [{"label": "Booting", "start": **********.189444, "relative_start": 0, "end": **********.562659, "relative_end": **********.562659, "duration": 0.****************, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.562668, "relative_start": 0.*****************, "end": **********.768538, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.07737, "accumulated_duration_str": "77.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6053932, "duration": 0.01943, "duration_str": "19.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.113}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6565542, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.113, "width_percent": 1.254}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6858191, "duration": 0.05697, "duration_str": "56.97ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 26.367, "width_percent": 73.633}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294956986%7C2%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1sdTNJWUFJUW9VZXpJK1daR1pZMEE9PSIsInZhbHVlIjoiUHZnWGd6cmFOa240WFRPeDAvQkduTE56bkN1WTNIajRjRDhDYVVuSjkwbXhqai9QU1dDWkx3VXE2eHdoWlowdWY4REJXZ3BpZmtBS05QY0FVZDA2TmR5N2FBdDlBQW84cVUzZS9zRWR4elYxck9ZYVZGRnNXTC9FS0NCRnVMTnRQbWV6VUlNQklneVFtdVM4ckMvUGROTWFEbFFRcDYxMlRFUmFUc3NRRGVQbXB5OFBpYzFCVXRvclJaaWQ5RnNBb2FUakpmZGxuNkwvT0tEdFFRL0dqY1M0NzkxYzhvdTEyZFBXSnRvN2gwek1PQ2VheXltazd3WEh6UkYzalFPZnJlVVJCMG15STY3bGlCTDVnZE10N05RQmo1VGN1amlSeHVDOHBQSFpEbmlPKzk5S0lVNjVmU0RRTTFTd1pWQWpkbEdacFR3cno3eWZ4ZEVFNUdZWVpobUJma1h3eVlzR2NJOWViemRVOVFQWGppTEdGMG9TVWdPdUwyRTRhNW9LTmNsTzJYOXpTRkk3cmhabFpJZ0dDMEZYUXRtd0lUL3hIUkxVaWMwcFFwb29yZFVEVW1VMWYxb2lKTWswS2lwanR5Z2FwdTFQWkZvNCs3R3BwVEtJNjJsWEg2MU05d3RZU1NDZ2dkMjJtK0JKc2pzakFrVnp0aU9qZEN5WWxicGwiLCJtYWMiOiJlNDBmNzk5NzQwNzZkYzMxMGEzZDU0MmNjZjUxZGNkMzY2NTExMjI4M2YzMzY5MjZlYmQwZTNjYjQ1OWFiZjVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9tNFkwUnJoYUlNbnBoSEdhNEpjbEE9PSIsInZhbHVlIjoiOUFCeEFibW0rRWt4OXZpQ3VoVUtvOStDQzZrM2k1a1l1S3NheWV2QmNJSFFNVU5YOEJZZHUzVjlHdDZVN1VDeGdueXYrNXl0SG96M2VZdjhEazU3MlJYbWlpMGtqOVJBdEcyU0dLSmxEYUM5TWxMZWVlMWorNzR1QkZpbWc5SHU5QUlxOEh3amRUandpRWs1TFlrWmtXeXk0UlpVR3VYWTZqUHBmdDZDZm5OMk1kS1lHOGVBakFZSUtxS2I4ZmZ3LzkxdVcxU2hCRk16cDNMb0FEL3RmQTVhUUZZWGJ3ZUpiUktxYzlWNXZtTy9qVGQvdjQzbS9PTEd2dld2QllvVzVXT1h0dWRRdXBkUDdTbHFTNlhYdHBhOCs5cGxEZldzZFN3OXkxeFFpa2JjU29QUWlQcDIwVzljbEM4cEFzaGtyYkMrQzhSc0FXV2xuOS9TYkxPNVRDbW56ZGd1dzFIYUkremZWUEcwUElBRXhqcDAvMWhWTUM3QzRjWlRiZ0NHejVPUHowWnhLTWFhWUJJbFRsd2FHNng4Mmc4UEIyRFdVZHRGTEp3S3UxLzNyblBxQ25aNWNINnF0L1I0QUs1dWhBSkRDcHVMY2ZsUlJNZnVHSlR6L085eFczZXdyK3M3MlZMb0wzQU8ybS9ZdDA5eThRanBCR0dpN050QnJiTWYiLCJtYWMiOiJlMDBmODBjY2ZjOGZhNjhlYzBiMmIyMzk4OGZkYjU3MWYxNmY5YzRhMTgyNDRkY2VlMTY2YjQwOWFjYmZiZjJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1347697720 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6f86AL2UyJbEsnP8wClSGR79UCod8lW0WDs0fxKW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347697720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5SUDVDS3hyOWNYQWxpSEozdERxY2c9PSIsInZhbHVlIjoiQjNRTk90TFJnelAwOTVLSzRETVFTc21TWE4vTG1teVdqaDhzWnBPNHhsMXpVMENzUzNSc0FVa29wZ0NlK0p5dTRwMFFUY3F4QzNOandybDkxbzBjVzdyelFZU1NuS0M2d3d1UDErMTJpWXJpbXo5K3ZzMHFYYyswcXVVQzg4Q3J1U0tFOHRVcE91UUF4eWxGT2pMNHRxaUFPRmxINCtFY0pJMVZEV0YwdS9PZGRqVElINXcwN0o0dGlpYmZWbFliY0FwYzZxejlXSXdld1ZUZEZORmZqWXNyZ0tXMkpQVTFEaUdTRmZvR2ZqUFJma1lNUGpYNXpJUmJTS29CZmJkYzFrWFZpWCs1NE1nMi80ZU10dmdtbEpSOUxFb3NUZnQyUkRzdis5T0hvSWVQRlNWRVBuSmV2eERVMWs4SXNtZFRtTVVsZmprVFlsc2lycmZwVyt0Rk5YYTFTRU1vRVBRVmRXN2NUQmNRcXBXMWo2cDV2b1lFS2l0NFdScDdBT1JOWTlwSXZvSllwYmljN2I1cnJudmUyOXpXUkUySHJSaXdYTWtwMXVuM0FJd0lxSElLSHV3cmgvOGFENkdNUy9CUmdLR1lGcWxveUJIS3Ivb3RZUU9YNmd5Z0RDOHRVbkFvZ1dSNHJkN0lsT21qSTZqa2gzK2RZZTFHRHRaQkJaaEwiLCJtYWMiOiIxN2E5ZGVhZDFmOGI4M2VkZjE2YTNkZTI5ZWU4OTRlMGMzNjcyNmNkZGFlMzliMDJmYmNiNmNjY2YwNTE1ZWQ0IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZoakJUQkdPblFZUjBhalgyQVllWUE9PSIsInZhbHVlIjoiR1R3VnhRUklXcUpUc0w3UHJoOGhYTFRxZDRiZXk0ZWdUSGx0S2pRUk54aTlSaEhuUFlLSWtKOGh5MDZSSkJ2d01nWnlaYzM4WWhZWkxNQkxwdEk2eFlqRitoWFlaa05ET1oreVpiNjdCcjF3dWowOFMrQVBKQk5MM2kyZUxFZ0tsekpXRi83UXZmamlkUWRkaFRwZEQ1dVV0bGhJZzFydHZHTHJIRUVtUTM1SHlvZ2huZXZ6RTRIbDl5NnpRVHZFLzdSQ29XMHpPZ3dPVWE2cE9KMzFwOVV2Nks4ZXpHWHlZb2w4WktTQWlvd0xTR3l5WDJnejk5SG5VRG10SGh0eDNjd0ExNERyckk4RytNb2NLc1EyU0tBcTFNSVpXeDQyZ0loSHJXMytUTHd0a25oSjBWUFROUFdONURLdDFSSTVld1dqaGRiNEo0ZmorYXNScnR6T1o4SnRxNUlrOGd1dTZSODVwbzFhR1pzelQ4alV0SG1TTzllSWNCRkhzeHZqOVU5SGZQTWtzOXh0c0l0anVKWVQ4bGZMemJpZFp4VThvdzZvNEw0VElxNzRVejc4N2xWNTVhVE1NUXVjbkJwUlN1YTdwYy9BcVdsMjQ3UmJBYjcrMzcxZFJYc3U1NW41MkdYbGg0SWp6Z3ZSTkJQZjdQZEFoZHBhNXdhQVZoc0ciLCJtYWMiOiIzY2VkYzU5YWRhNGFjNTFhMzNlNThmY2YwMzMxMmJlNDU3OTUzMDc1NWI3MzM1NmFhMDU5NTNhZTg5MjY4MDA4IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5SUDVDS3hyOWNYQWxpSEozdERxY2c9PSIsInZhbHVlIjoiQjNRTk90TFJnelAwOTVLSzRETVFTc21TWE4vTG1teVdqaDhzWnBPNHhsMXpVMENzUzNSc0FVa29wZ0NlK0p5dTRwMFFUY3F4QzNOandybDkxbzBjVzdyelFZU1NuS0M2d3d1UDErMTJpWXJpbXo5K3ZzMHFYYyswcXVVQzg4Q3J1U0tFOHRVcE91UUF4eWxGT2pMNHRxaUFPRmxINCtFY0pJMVZEV0YwdS9PZGRqVElINXcwN0o0dGlpYmZWbFliY0FwYzZxejlXSXdld1ZUZEZORmZqWXNyZ0tXMkpQVTFEaUdTRmZvR2ZqUFJma1lNUGpYNXpJUmJTS29CZmJkYzFrWFZpWCs1NE1nMi80ZU10dmdtbEpSOUxFb3NUZnQyUkRzdis5T0hvSWVQRlNWRVBuSmV2eERVMWs4SXNtZFRtTVVsZmprVFlsc2lycmZwVyt0Rk5YYTFTRU1vRVBRVmRXN2NUQmNRcXBXMWo2cDV2b1lFS2l0NFdScDdBT1JOWTlwSXZvSllwYmljN2I1cnJudmUyOXpXUkUySHJSaXdYTWtwMXVuM0FJd0lxSElLSHV3cmgvOGFENkdNUy9CUmdLR1lGcWxveUJIS3Ivb3RZUU9YNmd5Z0RDOHRVbkFvZ1dSNHJkN0lsT21qSTZqa2gzK2RZZTFHRHRaQkJaaEwiLCJtYWMiOiIxN2E5ZGVhZDFmOGI4M2VkZjE2YTNkZTI5ZWU4OTRlMGMzNjcyNmNkZGFlMzliMDJmYmNiNmNjY2YwNTE1ZWQ0IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZoakJUQkdPblFZUjBhalgyQVllWUE9PSIsInZhbHVlIjoiR1R3VnhRUklXcUpUc0w3UHJoOGhYTFRxZDRiZXk0ZWdUSGx0S2pRUk54aTlSaEhuUFlLSWtKOGh5MDZSSkJ2d01nWnlaYzM4WWhZWkxNQkxwdEk2eFlqRitoWFlaa05ET1oreVpiNjdCcjF3dWowOFMrQVBKQk5MM2kyZUxFZ0tsekpXRi83UXZmamlkUWRkaFRwZEQ1dVV0bGhJZzFydHZHTHJIRUVtUTM1SHlvZ2huZXZ6RTRIbDl5NnpRVHZFLzdSQ29XMHpPZ3dPVWE2cE9KMzFwOVV2Nks4ZXpHWHlZb2w4WktTQWlvd0xTR3l5WDJnejk5SG5VRG10SGh0eDNjd0ExNERyckk4RytNb2NLc1EyU0tBcTFNSVpXeDQyZ0loSHJXMytUTHd0a25oSjBWUFROUFdONURLdDFSSTVld1dqaGRiNEo0ZmorYXNScnR6T1o4SnRxNUlrOGd1dTZSODVwbzFhR1pzelQ4alV0SG1TTzllSWNCRkhzeHZqOVU5SGZQTWtzOXh0c0l0anVKWVQ4bGZMemJpZFp4VThvdzZvNEw0VElxNzRVejc4N2xWNTVhVE1NUXVjbkJwUlN1YTdwYy9BcVdsMjQ3UmJBYjcrMzcxZFJYc3U1NW41MkdYbGg0SWp6Z3ZSTkJQZjdQZEFoZHBhNXdhQVZoc0ciLCJtYWMiOiIzY2VkYzU5YWRhNGFjNTFhMzNlNThmY2YwMzMxMmJlNDU3OTUzMDc1NWI3MzM1NmFhMDU5NTNhZTg5MjY4MDA4IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}