{"__meta": {"id": "Xa385a1e5999445686f90e38610ff8414", "datetime": "2025-07-21 01:19:35", "utime": **********.547791, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.153029, "end": **********.547803, "duration": 0.****************, "duration_str": "395ms", "measures": [{"label": "Booting", "start": **********.153029, "relative_start": 0, "end": **********.495465, "relative_end": **********.495465, "duration": 0.****************, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.495475, "relative_start": 0.*****************, "end": **********.547805, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "52.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029, "accumulated_duration_str": "2.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5230892, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.483}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5332382, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.483, "width_percent": 9.655}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.540431, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 74.138, "width_percent": 25.862}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C**********053%7C4%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJoN3ZabWxjMEI4eHBsU3AzQkovUHc9PSIsInZhbHVlIjoiUFV1ZXV1aDhNNDhMbFhhT0MzVUZ3ZjdRb3dIWUN1cDdVZS9sbTdJbkh6c0NndXV0WkZOZWFxMXU1M1RvUmRJWEVHcXNIV3dOZGoxaWQ5WFlZcnloOURhVkszMGkyZWlyUS84QU9QVFhhSUx1Y295c0hHMWF5YThjdUdUbUxLNFZtZEtYV21jWVZXY0pIcCtxdGNtOEEzRmE2Q0xtK3JXcDhwelo0TEtMRWh3dDJqRjFSTmNtQ21QeWJKbTJoN2F6MFhOelhKd3pFYWcvSEwzS0drVEJoS2lqSEs3NlhCeTk4bFdYMndVeDJrNkxxdHJ2citUd2hDT1gyb3NGMENqL2l6UngwWHhtYVByenl2SFdIdGFMSit4UG1uSlRZZzNrVnBJSnhHZDU5empqSlo5MElhRFY0aDFDb0szWlhrZExhaUd3eGx2UkxHbDF3NWpyVmFCNGNobUJqYmZEc2puZkFEeTFTM1FISFdxclhjajgramVQZTZPN3UveW9peFIrSUhyZWJvQndXQVFmQ1hndjJ0dFdwOE9YZ1Z4NGNxZzh5ai8yU2lEejQ0eVlzZG9BL1htUVlDTmVkenovMFpKbkQxcXdnQ2lzK1BQWFR6R2h0ZTVnNFFCak93dDNwaHVmSENKd1hlWmM2SzJaRmJLeGNPZytoOTVyOVN1NG80cnQiLCJtYWMiOiIzYjI1YTZjZjdhODkwNjYxNGM2OGZmMDM5ZTM5NWUwZDI0YzRlYTQ3ZGUxZjJhMjE1ZmQ2ZDU4MGJmOTVmNmFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpBRVBQS2JIZkpkdGZ0RmJNMDA0QUE9PSIsInZhbHVlIjoibGVmMGNXZFBGQUpuOWdQaTFTbTBJUWRQTjFyTXhHVGJqU2tXQmozQTFBbFRQbEJDSjVaZjZzOXZrckdsRUlzRFpJWktXWFI2Vk9NSWN1QkJKazJMOGlpY0lKSWVrUzZDdGZFSkFYUS9WcVVZdld3Y3FYUUhtZ3BTcGxySzhsY0dUcHdHelRmZUpUR2cyR2w3bmdxS2JndkEvMk5GeGhKb3JwWkRoTkRhcnNYRDA4alh2Z01HQkR5UW92NUxPQnNPOGNuRWd2RGJveEpzY0oxdnNjZjhDZWtmVXlWaExZMmd4RHpVaVV5UlAzZy9SUlBjcXUyZFpvUHA1YkVwR2VTRUdVbWdIelY5L2l0dS9TVnFvNFZXdytZK2ZIb0VydUlvcm4yZ1k2MGJaczkwbDN6Z2VuUjJxKzNWeVVBSWJzQzUyMC9xOGpLcDFOYUpFcUFwUDUwbUhtc0JXMkFxaE1VcjV6K0JTMEZEMHhneFFHVGo0M0NDKzRNYVI1aFhWTTBtZVlxYnlrMkw4K2U2akdIM0Q0Mm5sWld6RU5vSDd6ajBGb2hzY3dlTTUrRktDbGorZ0IrMlErbEtERGdhUERVbnNkamsrMWk5V3VnRmhaQ0pVSGtyOFlQemYzc3FKbWtqVHhTKzhqb2FFREc3VHdIcncrMll4cXBDTnZjTlhrTzQiLCJtYWMiOiI0YzYyOGIyMWM3ZDljZDNmNDE1NGViMTJhY2UwYzMwOTNjN2FmNzQyYWEwNGQ4NzkxMDg1ZjRlZTEyZmY0MjczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-319015814 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319015814\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1463028104 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:19:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdKZzF4Z3JGL2hrK2J5SWJxUjk2emc9PSIsInZhbHVlIjoiRFhXaVZ2VjFVaUlNMVZKTWt5d3lDRlpUMHZpV2RLN2lqSmdjWE1TOEtSVU52M2NvOHA3c2doTmI4VkIwVS9VWmNtZERiNmZPSWZHNEFud2s0SUhybkR4OTJGSHRlQTdMeWxLY1o4TlluWkIrZ3VxZU9ZVjczbDBtWmk2WnA0ZG9HdGJLV2dDYzFuNk9rMHVhT1ZyV0twSmlNdkp4WUYwenBxcjVTUENWUWdRaEZob29KYUxBakVYWEFrUk1hSHhXRmJCbDVUY3BPRnZsWFg5NFp3NkpiSm41ZElGMEFrWGRTQlpFWW1vYkcwbU14eUs3azBkcHFTY0hFWmFDb2ljbzZNM2s3TlA0L3lJWWpFUy81RVBNL1ZrcGE0aXNOZTFzb0V6dVBmZG56Y0RwKzFpTDBXSFhCZWdxdENSQVg2RHZQM0FmS2svcEtVZkxOcWhHeHUyRkhVNWQyaUQyNDBFQjF0L09PVVUxQndsUmt0YUMxZkZ1Z0Ivc3ptZnhlMGZOZm5PdS94NlZRU1dyNkRtZExkYTJ5YjBIdXAvU0dueHdhcHhYblp5bGxsOXZWaGRjNUhuSktQUDRkb1RscG1QUU9WVHhYaUFlcGN6bnZNdUo4NUZndkVzMFAyRzNUTXA0VGlWb2lVRnJwYnhySFBPbjYvb0Z4cVBIWGFVY3BWOEYiLCJtYWMiOiI3YmNlZmM3ZGJhZGViZmM5MzZiYmMxMTI5NDE4N2Q2NzllNWVhM2QzZGIxODc3NGYwN2JiMDg3MzVhN2EyOWM1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNIeWhMQ1J0eGtxam44Si9iNmplbHc9PSIsInZhbHVlIjoibUxJeHRTWTRjMlhQWnJEMmU4SjJDc1Y2VmcvRnRzbGwzbE1NaFlxMWJ0cXBMdllBUjNObFREekpzeTdKZkVxRi9MK0tHWGY1L3dndlNZNjRrNm11emFURVZ0OUlTQjQ2VXdFcG1aM2JzU0IzMUxjVGh1cHpqM1lDZ05RUWc5QkFPMUpmUWplMnhqdmlCdXMvY3BlWVI3NHZWT3FNRkZSeWNMRERlWldWMG84TzQ5U2pyZ09MWlJEUHFpYXhSdEZKbDFSK2hkZWl2MitJZ01RZUN6Um5hTytkRDlIVXUrbDZiSTNmK3M0b0lOLzkvRmw4TmFYR2J5RlIxUDc2aHlvdk83dXRSK0hFdUxBeFZWK1VyYXJGU0hBL1g4NU5ySFU3T2VvWUVCemx4Y0RKS3JsZlEyMlJwd1o0Wklvd0UrNCt3U3hJZlgvZVdNd3NoTXU3TzNrSXRLS2hiTDFaQkZEMGxUd2hOTWlkNVhRNS8vNEJNdm9pVUxZa2Y5TmRoOERPUVdUQzZwY25Fckx4VjlpeHFBMnZGQm1URFM5M1VyanZpOGFwSlRqbC9md1JBdVo4UDNTRXl6dks2L2dUMklwUGZlVGFUM3pDWlFLclpLbGZ5YkNFUUhsQjRnUFA0RU1uaU45Yjd5ZEJtYVN0MEVpK3h0aHU4UDRNNGY1clpDZ2UiLCJtYWMiOiJhZTg2MWE1ZDVlOTBjN2IyYTk0NjlkODJhYmUyZWM0NWFiYzViNWMzNGFjNzAxNzZmMjhiZWMyYjllNTBkNzMzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdKZzF4Z3JGL2hrK2J5SWJxUjk2emc9PSIsInZhbHVlIjoiRFhXaVZ2VjFVaUlNMVZKTWt5d3lDRlpUMHZpV2RLN2lqSmdjWE1TOEtSVU52M2NvOHA3c2doTmI4VkIwVS9VWmNtZERiNmZPSWZHNEFud2s0SUhybkR4OTJGSHRlQTdMeWxLY1o4TlluWkIrZ3VxZU9ZVjczbDBtWmk2WnA0ZG9HdGJLV2dDYzFuNk9rMHVhT1ZyV0twSmlNdkp4WUYwenBxcjVTUENWUWdRaEZob29KYUxBakVYWEFrUk1hSHhXRmJCbDVUY3BPRnZsWFg5NFp3NkpiSm41ZElGMEFrWGRTQlpFWW1vYkcwbU14eUs3azBkcHFTY0hFWmFDb2ljbzZNM2s3TlA0L3lJWWpFUy81RVBNL1ZrcGE0aXNOZTFzb0V6dVBmZG56Y0RwKzFpTDBXSFhCZWdxdENSQVg2RHZQM0FmS2svcEtVZkxOcWhHeHUyRkhVNWQyaUQyNDBFQjF0L09PVVUxQndsUmt0YUMxZkZ1Z0Ivc3ptZnhlMGZOZm5PdS94NlZRU1dyNkRtZExkYTJ5YjBIdXAvU0dueHdhcHhYblp5bGxsOXZWaGRjNUhuSktQUDRkb1RscG1QUU9WVHhYaUFlcGN6bnZNdUo4NUZndkVzMFAyRzNUTXA0VGlWb2lVRnJwYnhySFBPbjYvb0Z4cVBIWGFVY3BWOEYiLCJtYWMiOiI3YmNlZmM3ZGJhZGViZmM5MzZiYmMxMTI5NDE4N2Q2NzllNWVhM2QzZGIxODc3NGYwN2JiMDg3MzVhN2EyOWM1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNIeWhMQ1J0eGtxam44Si9iNmplbHc9PSIsInZhbHVlIjoibUxJeHRTWTRjMlhQWnJEMmU4SjJDc1Y2VmcvRnRzbGwzbE1NaFlxMWJ0cXBMdllBUjNObFREekpzeTdKZkVxRi9MK0tHWGY1L3dndlNZNjRrNm11emFURVZ0OUlTQjQ2VXdFcG1aM2JzU0IzMUxjVGh1cHpqM1lDZ05RUWc5QkFPMUpmUWplMnhqdmlCdXMvY3BlWVI3NHZWT3FNRkZSeWNMRERlWldWMG84TzQ5U2pyZ09MWlJEUHFpYXhSdEZKbDFSK2hkZWl2MitJZ01RZUN6Um5hTytkRDlIVXUrbDZiSTNmK3M0b0lOLzkvRmw4TmFYR2J5RlIxUDc2aHlvdk83dXRSK0hFdUxBeFZWK1VyYXJGU0hBL1g4NU5ySFU3T2VvWUVCemx4Y0RKS3JsZlEyMlJwd1o0Wklvd0UrNCt3U3hJZlgvZVdNd3NoTXU3TzNrSXRLS2hiTDFaQkZEMGxUd2hOTWlkNVhRNS8vNEJNdm9pVUxZa2Y5TmRoOERPUVdUQzZwY25Fckx4VjlpeHFBMnZGQm1URFM5M1VyanZpOGFwSlRqbC9md1JBdVo4UDNTRXl6dks2L2dUMklwUGZlVGFUM3pDWlFLclpLbGZ5YkNFUUhsQjRnUFA0RU1uaU45Yjd5ZEJtYVN0MEVpK3h0aHU4UDRNNGY1clpDZ2UiLCJtYWMiOiJhZTg2MWE1ZDVlOTBjN2IyYTk0NjlkODJhYmUyZWM0NWFiYzViNWMzNGFjNzAxNzZmMjhiZWMyYjllNTBkNzMzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463028104\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1015205679 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1015205679\", {\"maxDepth\":0})</script>\n"}}