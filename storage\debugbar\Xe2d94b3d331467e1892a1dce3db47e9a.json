{"__meta": {"id": "Xe2d94b3d331467e1892a1dce3db47e9a", "datetime": "2025-07-14 18:58:05", "utime": **********.170666, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752519484.712339, "end": **********.170693, "duration": 0.45835399627685547, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1752519484.712339, "relative_start": 0, "end": **********.101338, "relative_end": **********.101338, "duration": 0.38899898529052734, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.101347, "relative_start": 0.3890080451965332, "end": **********.170695, "relative_end": 2.1457672119140625e-06, "duration": 0.06934809684753418, "duration_str": "69.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01662, "accumulated_duration_str": "16.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.133003, "duration": 0.01541, "duration_str": "15.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.72}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.157058, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.72, "width_percent": 4.332}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1635292, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.052, "width_percent": 2.948}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&date_to=2025-07-17&payment_method=&warehouse_id=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1478999704 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1478999704\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-97372975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-97372975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1498007884 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498007884\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-17&amp;warehouse_id=&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752519449636%7C53%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZQdG1UallGdCtNTDZVRjUvay91dHc9PSIsInZhbHVlIjoia0NmUkdnOW5yMEoxUmRDR2w5bXdhUlo1RmxRcnNoZFBCNlM1eUxNZXJoay96ZnRGcXlFaW1VQzU2Q1RlVjNoU3h5ZkswNG0za2FrcUUvbDRkYXk2enNsN2VDYlFvaEswR2R1Q1dFbUhSbE1KeWRqMlQ2QTBvMk8yWFRCSjZrM1VCd28zUlQxUVVMZVFFUVRsMG9XRUxYTVNhcEdSNmg4M0oxeXhCT055RmJRVUdRRFJuRmZlSTZPVzlmSG1EU0VoL0pvUEZYMjlUOUtwMDVMNC9IQlV0Z1RYUUttYlo3bDRDLzUrMTBZMDJrV2tybU9KRmxDNEhVVnpYWTMxeGNUc2ZuaFc0RnF1cnBxZklnN2k3QWJTQWlUTDNnRTJiKzJjUVdaOEEydW9zSEJMcFpoaXkrZUNrVGM0dlBPSW1ZWlRVQXhTdVozQjRRUjhnWlNUUlVSZ2dTdElKaTQ1VWcvUDNGOGxDVEhSWWNITExhYXNFOUFhbXJKMjhSRHhZUm50QkJtU29zdmpETFNqN2RMcGFUaXFtZDdEU3YyaWpzTWxCQ2xFcjJWN1pBa2k2emRVY1VBMjc1VDFEMlNibEdZWkF5d3RDN3JiQXZ2Y0N3M3psUEpZd1JqWkpNdGJ2SHU2K1FmMHEyY1dkYWdLaENISmZIV1E3VXo1QkVVaUVKK0ciLCJtYWMiOiJkNmQ2MjYxZjEyYjc3ZWQyNGI0MDBmOWNlNDRlNGEwZTYxNjdmNGRiYWYxYzdiN2VlMTU3ODkzZDBmZmUwYzI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVZeEloS1JCNm14amFDYUlpdGRHRGc9PSIsInZhbHVlIjoicGp3dEpLTEdOV0N1RENMQ2cweU1ENkJJVmJpSXFJZG9TU05najRHOTRmcitDUG5Qcit6ZHZyTVhyNWNrcHJYM2dVTGZGMzY3cEdncHlwdnZPb1R2WW9EdmZJdndZNU8rL0t0aTQ2eFFnSW1EZVVYOTA1TWp5RjQySzYxT1A5bFNRaUV6N1VvVXYyQWFqWWpTUEN6NGRIa0RpREF2TjlTalo1SFQ4M0p4Z3JxUGxzd0ZXYkV4ZitFaytIdjhtQjdsajRUa2ZyNWM0MDdBZHcrRkovSTVCMGNvNm1pb2ZUOUdUcmVaQlFoekdrVGlQRFlpZEIvOHNvcjBraC9sVzBpRVJzckl5ZjFUNnhwZmczbEI4M3A0R2xHRjliYlB5eUZkUmkzbEhaUFlhU0ZSeCthd0MxRlE3MGpYdHIxMjFMS2d6VXZ3cmJyUTVIL3lPb3ZSNnpiLzhDSjlER0ZlY3BqVzZmQ3lXck14Y2U4bytYSzBkcDE3RXUxWXQyVXRXNEJPYloxR1RWdmp2bnJrVG1NZzM5TkVvcGtRcHVNWEJHaDF6TmxKN2pGNHZ3MkJBUUkwc29Kdk1UazZBUm9RVjhGczBXK2NweUhOMk16NDk3NXFnckl2K2liM1FzblVhUnVGemsxaWd1aDJ2NUFXU2xDcGxCSmpCeTJKMFNEZGM0OWkiLCJtYWMiOiI3MDU5ZGM4MjI1MDY4OGYwYTVhNzlkNDU0NDQ0M2ZlOGViZmFmMjViZGNhY2YxYmQyYTE0NTM3YjE1MjE5ODc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-528658859 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:58:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklOb09PSTdaWlMwZnhHUFU0SkhrVHc9PSIsInZhbHVlIjoiSGVpa0pCRldBNGFUUjBUVkpMbWpIeGlPQkFGMHUxUllrZFZzbTFqMjFTTWplSmgwRlNUMkhlZWFqTmhuOHlHd09ob2hGeHdXcnNGSkxPZzE0R2ZkUkQ1cVdGLytRZzlNa20weFRVMFF3c0xSOXczc2ZaVUFqYkV2UjQxNmhTck9TcGcvWVF0cjV3NVMyU0dxSUFVbmtGaC9Obzg3NXhhYzJzQm50Mm5NTjFXakc5NVVYd2h0MVBLN3prYUJTY2dGckl3UmZBWVN0UHdCNHh3MFZHVldMc29Hb0tMVEEzR0g1citSQ0EyS0R3NTIyN1hoaVFXcE8xYkZ1Z3ZYQnYyTHdQYmtkWERNdzY4ek9EU3VDNXhFQXZHVERXeXNsZ0pzWGplSmRCaFMzOTN2SWtyQ2xwT2JjZWttSUlwV0Q3UC9qS3dicFkzc0dpdTgvSmNFNVJ0WjFPMDRaS2hLMWppZCtJQTdpSTRNQjRCU3dBamkvcndYQWZZVVYwdWpJMDFvVzhpMnZWbUR3TmhEdzFJUVdINlU3QmhHdGZwa2dpdDZLc3VmVHVCSm85b3JnOHNWUFR6NGNrM2VndlJMOTJNMEZ6M2UvZjFRME9KMFQwZGpNd21ON1ZLMmxrSVB3Ynd0V1U5bUdTeEVEdDhOMkJUT01MblRjdCtzSG01M0xQRlEiLCJtYWMiOiI4MjdhZTQ4MTVkMDU4Mzc5MDM2YWQ5NjExZDdiM2RlNjUwNzFlY2IzYWU0ZTM0NGU0YzE1YjFlMmRmNjNmYWNiIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik4ybnZoZ2hxelFpMU1NcjhkOXkyWlE9PSIsInZhbHVlIjoiVHpNakFpOVBqSFJhdDFIem9ncDh5dEQ3bmVGODI0c2RSa2psUzN2dngzT2hpUXBuQ3hpUWc4c2hsaVhYTXFNMkV5R3B4RGRFTHgrSmtiQjkxdjhWY0N5OURTVEhnazVWQmJZalQzaTJWM3o0bzd3VUtYL1REbE9oUk1SRVA2NWNNMkJMQ1E4a0VhcXlSd3oxUC9QL1JkcWdYV1RxSXJOYXQxcTJZRGsxYnQrRG5UT0FnSXkxa2Jybkt2aVI0QXlVQ1k1c3JKOXh0blVKcnhrN1YrWFFuRlg1R3VyMS9RUldiaVlWTkx1L044UHVpV3JZN25NbGZ4R3V1NS9uWHhTTGdjdC9uS0RLVjRQeDBNY2dWK3FMejF0dStTdmY4ajZYS3VXRE4xUHQwM29nblk5V2hxbEgzTmhIeDA0NFNKQmlUQXJvSXBpRDhPTjA4NEZmVVQ4VGE1YmM5VUtGalltdXRvYmttV2l0ek1YcUQzejNIamNGSjVHbWJzTzJwWkJrNVVIYTNvajFXWVIrRVYvNDh1TldwNXR5MnF5WGFWMzgyeEh0dmxjRjZNb2dCODNuNzdnS3VndHcrb3k0SStsY2orUyt3V1JwOWQwUWNrN0lnVllWRmNCTWRFLzRWckI4QWFWVnNtODZ2Z2RMbk1jbkZzZDl0VHVUeGRqbGJXM2giLCJtYWMiOiI1NzAyZmQyZDUyNmRjYmFmZmYyNTcxZjk3MjQ0MDVkY2Y3NTcwYzAxNzQ2OTk0MDc4MTFlNTRlZTgyZjc0Y2QzIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklOb09PSTdaWlMwZnhHUFU0SkhrVHc9PSIsInZhbHVlIjoiSGVpa0pCRldBNGFUUjBUVkpMbWpIeGlPQkFGMHUxUllrZFZzbTFqMjFTTWplSmgwRlNUMkhlZWFqTmhuOHlHd09ob2hGeHdXcnNGSkxPZzE0R2ZkUkQ1cVdGLytRZzlNa20weFRVMFF3c0xSOXczc2ZaVUFqYkV2UjQxNmhTck9TcGcvWVF0cjV3NVMyU0dxSUFVbmtGaC9Obzg3NXhhYzJzQm50Mm5NTjFXakc5NVVYd2h0MVBLN3prYUJTY2dGckl3UmZBWVN0UHdCNHh3MFZHVldMc29Hb0tMVEEzR0g1citSQ0EyS0R3NTIyN1hoaVFXcE8xYkZ1Z3ZYQnYyTHdQYmtkWERNdzY4ek9EU3VDNXhFQXZHVERXeXNsZ0pzWGplSmRCaFMzOTN2SWtyQ2xwT2JjZWttSUlwV0Q3UC9qS3dicFkzc0dpdTgvSmNFNVJ0WjFPMDRaS2hLMWppZCtJQTdpSTRNQjRCU3dBamkvcndYQWZZVVYwdWpJMDFvVzhpMnZWbUR3TmhEdzFJUVdINlU3QmhHdGZwa2dpdDZLc3VmVHVCSm85b3JnOHNWUFR6NGNrM2VndlJMOTJNMEZ6M2UvZjFRME9KMFQwZGpNd21ON1ZLMmxrSVB3Ynd0V1U5bUdTeEVEdDhOMkJUT01MblRjdCtzSG01M0xQRlEiLCJtYWMiOiI4MjdhZTQ4MTVkMDU4Mzc5MDM2YWQ5NjExZDdiM2RlNjUwNzFlY2IzYWU0ZTM0NGU0YzE1YjFlMmRmNjNmYWNiIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik4ybnZoZ2hxelFpMU1NcjhkOXkyWlE9PSIsInZhbHVlIjoiVHpNakFpOVBqSFJhdDFIem9ncDh5dEQ3bmVGODI0c2RSa2psUzN2dngzT2hpUXBuQ3hpUWc4c2hsaVhYTXFNMkV5R3B4RGRFTHgrSmtiQjkxdjhWY0N5OURTVEhnazVWQmJZalQzaTJWM3o0bzd3VUtYL1REbE9oUk1SRVA2NWNNMkJMQ1E4a0VhcXlSd3oxUC9QL1JkcWdYV1RxSXJOYXQxcTJZRGsxYnQrRG5UT0FnSXkxa2Jybkt2aVI0QXlVQ1k1c3JKOXh0blVKcnhrN1YrWFFuRlg1R3VyMS9RUldiaVlWTkx1L044UHVpV3JZN25NbGZ4R3V1NS9uWHhTTGdjdC9uS0RLVjRQeDBNY2dWK3FMejF0dStTdmY4ajZYS3VXRE4xUHQwM29nblk5V2hxbEgzTmhIeDA0NFNKQmlUQXJvSXBpRDhPTjA4NEZmVVQ4VGE1YmM5VUtGalltdXRvYmttV2l0ek1YcUQzejNIamNGSjVHbWJzTzJwWkJrNVVIYTNvajFXWVIrRVYvNDh1TldwNXR5MnF5WGFWMzgyeEh0dmxjRjZNb2dCODNuNzdnS3VndHcrb3k0SStsY2orUyt3V1JwOWQwUWNrN0lnVllWRmNCTWRFLzRWckI4QWFWVnNtODZ2Z2RMbk1jbkZzZDl0VHVUeGRqbGJXM2giLCJtYWMiOiI1NzAyZmQyZDUyNmRjYmFmZmYyNTcxZjk3MjQ0MDVkY2Y3NTcwYzAxNzQ2OTk0MDc4MTFlNTRlZTgyZjc0Y2QzIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528658859\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1845811543 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-17&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1845811543\", {\"maxDepth\":0})</script>\n"}}