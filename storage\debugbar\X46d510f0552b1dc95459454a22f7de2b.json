{"__meta": {"id": "X46d510f0552b1dc95459454a22f7de2b", "datetime": "2025-07-21 01:33:56", "utime": **********.410696, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061635.913336, "end": **********.410709, "duration": 0.4973728656768799, "duration_str": "497ms", "measures": [{"label": "Booting", "start": 1753061635.913336, "relative_start": 0, "end": **********.328648, "relative_end": **********.328648, "duration": 0.4153120517730713, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.328657, "relative_start": 0.41532087326049805, "end": **********.410711, "relative_end": 2.1457672119140625e-06, "duration": 0.08205413818359375, "duration_str": "82.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02477, "accumulated_duration_str": "24.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.364355, "duration": 0.02384, "duration_str": "23.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.245}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3972871, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.245, "width_percent": 1.978}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4034061, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.224, "width_percent": 1.776}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1712240633 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1712240633\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1468693593 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1468693593\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1256352271 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256352271\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-373071998 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061249606%7C8%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlOUStYVEF4QnBnTDMvbnJ2R2F0b3c9PSIsInZhbHVlIjoid2VuRGUrODQzWFAzMmU3MFdHNFdIUGQyVC9ib0VaQ1U1MkI2YzNWbGRmSmlwRExnSzZsNVpOMllsK2JoVWlBRjRUYWcwUm5OSWh5VnpnYm1ZdWduMnJ3L2RkdDZsSm9WRWNMM1d3UC9wdTNsV1FXQWhjZTZrWFhmVEZ2VFhGVldvN1V5VkpEd2tzejkzUlo5U09sQ25FaVNGVkVxUDR2SDNWUHdqTVEvc1hwcXlnamwvK2lMR044OUk5WExZQWxpbkpIRDc0SXQ3RFFkNWtrZ1Jrd25kamZodmxoejZXMlZWM0lUaEVVMzN1WUsxL283ay9zdUVtWTV2U0FWNlcwZGZ0Y1RhOFh4UStGd2tRLy9xcmJNOXJLd2t0UHh1Nlcyd3djYlVlaVg1cUZBOHhENlljSjNwdWRmWFJiWmYzdzBsT0ErTUcwbmZ6OXdYMldDR0F4WmNzcWg5YWRteEh0b2k0VjZnTmg4cVQ4KzJrdWRKcVBUYXBQVStSUmtUbFN0MWtMakxyMzE2WHh2MC9hS20xdmtyOU9kQWpHeW43MFJaSDQxZ2U5K2Q1Z1VISFhUMGREM1Bua2c5UnNRNG9IdFpFeFVOMmh3UmZuOVoybG1MK2QrZ240dXk5bTljTGxyNXoyZlNkaVpFUHlqNGhyLzczeHlOOW1zdmZQcTRDa2UiLCJtYWMiOiIxMWQxZjc1MzMzMGJmMjYzMjM0MDI2NTk3ZjFkOTE0YTJhYjNmODZkMjI1ODJkZWFiZTFkMGUzM2NlNjU0MDJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktYNnJUMnlxb1RoWFlFZi9wZjNPYVE9PSIsInZhbHVlIjoiNi91WDY4UGlnSFJzL3BWaThURXdWN3BlSDlpTFA0RW9pZlQ2K2F1Sjczc3VoWFdaRU1PU2Ixdk1LVExGTFpSTlllc2I1M0RaejhaUk84WjZUUGVCRVl0NWxvenEzSVVCMEZuR3pteGtwZ0lWTEhRNkFaS3ZNSUZhRUI1aVFqNDNVcm5sZEJFZTMydFVnTGNnRlE1RGpWbW12RHBpWnJEYWcwbk9IRW1CaTZKUktnZzhFZzQ2T2hLd201T2w1NjAxWU1mVmtxNjIyZzNIZmtTS0dWWGwxbU9uWGxqd2hwYTNBUVRCbnRkR21oY2syNGM0RDRrbExlMGtkcUFkbHlmK01KR3F6bGUzTnFSWlJiKzhpT0ZpOEpid3B3VlBidkQ2M1FjL1lGNFBWSWEzUmM2OUZ5cUMzeFhBRDNsZVNjL0hyZHB5alJOa0E5YWxsWi80VzVCRXR6TzJMa1k2VFNWMHFrcE8xOWZXTGpXZUIyZm5PR1MyL2xjL2h5Z1gzb0xtZ2s1UThEYlFPeitocVVkRDhXMmlncHQyMTBuRk9MSHFSWXF0T2p1SldOU3k5eUUzSUNzMnpkYUhPY1FmeE5hZjF1eE5qa3RNWmRZbkpwd1BVYmM4Tk5pY1hmWmhTSm8yV2hHVFZBMGd1cytYUVo5R1dKNlROV3hqN1owZXJwNXQiLCJtYWMiOiJiM2I0OTI4NWE5NThmZGFkYTZlMDY3ZGU0N2I1NTA2OTc3NmQxMGMyNzRmNmQ1NTY4YjVmYjdiYmQ3NzQ0YWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373071998\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1879155060 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1879155060\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1108870086 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:33:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkU3ZjVidDU3OHZQYUR4QUFVNWU2dXc9PSIsInZhbHVlIjoiU1NpaWdLZE9zMGF3ellVK2ltRVU3OW04S3hHdlNkVXRmTCtyVXo0K3ZJYW1NN2gzcWVXTEpDS0hjbFV2MTlwVUxUUTArbldLTU5JWWpyMG1EWjRHa1BnWldJVThyZzkrSS8yclc2ZFU4bW1ocUY4Y3ZKOXBCYkVPOC9hNi9PejVvcU1ESkRpaFhWSTdnOVJiRlNXYWk2cGk4amZLRTB0STR2QjM3TUxGaWxUdndyNXlLa2VVbWkxampRcGRyNnhETTVWZ3BVSkdSTVU1VWp3TzE4Z3JSci9xbzFWSVJzZkhLVDJLUUdSOVQ1QzByUmoraEJkWW9EUVo3NlVQaHdaWHNyekQ2Q2x0b2w2V1A3cmcwTDIyVkNkZm1Pck9iaXQ0eUp5VjRpNHREUG1kNEd1dEFwemRZbzc0TGRmQmF1Nm0zQ2RFQm1KeHUvdkVySDRheEM2ZisrUWI4cE5DcVBLUFlES29xUGo4U2owU0RVUzBaZHdINmpVaG1abkhSV0tJOCswN1lsdVc2S0liQ1o5Ri9taVYvSTVpTHFaNFdjMFpjSUZYNERlVk9RYnpaaC9HQUxKMXBzSXRpcEdSa3Rlam1EQ1E1SkZkOU9XWG9EdUlxWkVHVXVHMDNwN2NxMUxicXlGMXhVR3RTSGxSdEE5bFBIaE8wcCt1ZXNDTXV4MEsiLCJtYWMiOiJmZmI3NjcxNzllNzI5MTk3YzIwNTNiMWFhNTY2OTIzMmU2NDUzYzllZWRkZmY2NzdjM2E1ZDUyYTYxOWM2ZGQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:33:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjlrTVVnTWlDNlprbVA1OEFvci9UNnc9PSIsInZhbHVlIjoiV2oxeGhpbEZJZlJOUHJCRCt4Y0dackI0NVdrZlJYUmNZNFNsSUhKaTh1b2hPU3hwTmptbi8wMjMzTXJYbEpqekJsT01CSlo3QlAvdUVBRnNNUjdZc3RvVk5rZS9kaTl5K0I0ZnFmdEhTRHpLdlFPZCtxdE13dm9DUUlLY3JpUk0vVitzNTdJK3ZXa2g3bW1XUmg5bW5ENlZtcy9xNHhLMFppMS9POWM5TVpoQWdjb09LOXhQWWNrdVo4UFQyMEdUOG9xei9icURtcnVxa1NTYjFvRStHaVlkQ1pkQkpnUnF3Z3B3WFNWV09lMjh1WU0wQlBrczNPQmplRDRkR0xQbU1GWFR5UGw5YStMT1FEMDVJMitRUVJTYlRxOVFDYlRrS0U2bzYrZjEvdjl0SEFuT1pLQk1BVVVwbmlhQUxhcVRXb29xSVdFd3ROemFpc0dSYjE2bXVlNG5PeUdqb1czNFJyU1RIMndubmNReW1MOWhkL015dFlYYjZITEloTWE3WWc1eTVhcktnRCs1OS9KeStxbG83am5mOGowZDNxMnZGNEZ1MG1GV09vbXVyYXVOVXlqTTNjOWpnQmpIN0ZuS2VERzJDUk1XTFk1Q0RScWFueUw4Q0pzYUpOS0ZPckhLd203dEU4VWVPTGt3YTB3cTk2dFZKanY2MmQ1VHlsb1QiLCJtYWMiOiIwMTZmNTM3YzNiYzg3OWM5NjIwMjgxYzhiMjE1MDk1MzQ3NmRmNjk3M2U0OWQyNjE3MmNkYThjMDYyZjE2ZjEzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:33:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkU3ZjVidDU3OHZQYUR4QUFVNWU2dXc9PSIsInZhbHVlIjoiU1NpaWdLZE9zMGF3ellVK2ltRVU3OW04S3hHdlNkVXRmTCtyVXo0K3ZJYW1NN2gzcWVXTEpDS0hjbFV2MTlwVUxUUTArbldLTU5JWWpyMG1EWjRHa1BnWldJVThyZzkrSS8yclc2ZFU4bW1ocUY4Y3ZKOXBCYkVPOC9hNi9PejVvcU1ESkRpaFhWSTdnOVJiRlNXYWk2cGk4amZLRTB0STR2QjM3TUxGaWxUdndyNXlLa2VVbWkxampRcGRyNnhETTVWZ3BVSkdSTVU1VWp3TzE4Z3JSci9xbzFWSVJzZkhLVDJLUUdSOVQ1QzByUmoraEJkWW9EUVo3NlVQaHdaWHNyekQ2Q2x0b2w2V1A3cmcwTDIyVkNkZm1Pck9iaXQ0eUp5VjRpNHREUG1kNEd1dEFwemRZbzc0TGRmQmF1Nm0zQ2RFQm1KeHUvdkVySDRheEM2ZisrUWI4cE5DcVBLUFlES29xUGo4U2owU0RVUzBaZHdINmpVaG1abkhSV0tJOCswN1lsdVc2S0liQ1o5Ri9taVYvSTVpTHFaNFdjMFpjSUZYNERlVk9RYnpaaC9HQUxKMXBzSXRpcEdSa3Rlam1EQ1E1SkZkOU9XWG9EdUlxWkVHVXVHMDNwN2NxMUxicXlGMXhVR3RTSGxSdEE5bFBIaE8wcCt1ZXNDTXV4MEsiLCJtYWMiOiJmZmI3NjcxNzllNzI5MTk3YzIwNTNiMWFhNTY2OTIzMmU2NDUzYzllZWRkZmY2NzdjM2E1ZDUyYTYxOWM2ZGQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:33:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjlrTVVnTWlDNlprbVA1OEFvci9UNnc9PSIsInZhbHVlIjoiV2oxeGhpbEZJZlJOUHJCRCt4Y0dackI0NVdrZlJYUmNZNFNsSUhKaTh1b2hPU3hwTmptbi8wMjMzTXJYbEpqekJsT01CSlo3QlAvdUVBRnNNUjdZc3RvVk5rZS9kaTl5K0I0ZnFmdEhTRHpLdlFPZCtxdE13dm9DUUlLY3JpUk0vVitzNTdJK3ZXa2g3bW1XUmg5bW5ENlZtcy9xNHhLMFppMS9POWM5TVpoQWdjb09LOXhQWWNrdVo4UFQyMEdUOG9xei9icURtcnVxa1NTYjFvRStHaVlkQ1pkQkpnUnF3Z3B3WFNWV09lMjh1WU0wQlBrczNPQmplRDRkR0xQbU1GWFR5UGw5YStMT1FEMDVJMitRUVJTYlRxOVFDYlRrS0U2bzYrZjEvdjl0SEFuT1pLQk1BVVVwbmlhQUxhcVRXb29xSVdFd3ROemFpc0dSYjE2bXVlNG5PeUdqb1czNFJyU1RIMndubmNReW1MOWhkL015dFlYYjZITEloTWE3WWc1eTVhcktnRCs1OS9KeStxbG83am5mOGowZDNxMnZGNEZ1MG1GV09vbXVyYXVOVXlqTTNjOWpnQmpIN0ZuS2VERzJDUk1XTFk1Q0RScWFueUw4Q0pzYUpOS0ZPckhLd203dEU4VWVPTGt3YTB3cTk2dFZKanY2MmQ1VHlsb1QiLCJtYWMiOiIwMTZmNTM3YzNiYzg3OWM5NjIwMjgxYzhiMjE1MDk1MzQ3NmRmNjk3M2U0OWQyNjE3MmNkYThjMDYyZjE2ZjEzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:33:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108870086\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-976992587 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976992587\", {\"maxDepth\":0})</script>\n"}}