{"__meta": {"id": "X6b631642dde3176a7b8060cef8731c1e", "datetime": "2025-07-23 18:22:55", "utime": **********.763685, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.202527, "end": **********.763702, "duration": 0.5611748695373535, "duration_str": "561ms", "measures": [{"label": "Booting", "start": **********.202527, "relative_start": 0, "end": **********.675433, "relative_end": **********.675433, "duration": 0.47290587425231934, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.675443, "relative_start": 0.4729158878326416, "end": **********.763703, "relative_end": 1.1920928955078125e-06, "duration": 0.08826017379760742, "duration_str": "88.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45994952, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0239, "accumulated_duration_str": "23.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.714593, "duration": 0.0228, "duration_str": "22.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.397}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.747648, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.397, "width_percent": 1.967}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7557452, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.364, "width_percent": 2.636}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1266873938 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294969181%7C4%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxHVWthMm4vMjJYU2ZOcmFKUFl6VVE9PSIsInZhbHVlIjoibFZqSUxua1MwcWVtVXhLYTNFNERxcXFEOFhmNTM1QzkvVGdqbHRRbmpKUmlPS3VEejhNR2lNUTN0cHZlWm1GVEFNT2daK3h6ZUpBVURoWmxybUN4U01aajkwczZLM3lDS015dEY3amVQVnB4MkRqc0s0WHQxZTRyM3FnU0h6UktTaWc2Vjh4c001anBHdHQxSElmZ1FBTHhhYjdVNjk5a3llS2pxcVpjbTNHU2hQVFp3ckpnQ0t3eUVGUnBGbUtHbU9raU5KamFUeGZ6eXk0VlRHQUsvSis1SU4rdXZESitVNk9Udmg1WHdST1YvKzNwOGpObmduUCtXY0VwcVNtQkFxNDhxcm1GNTJZbTVMZWJlUzJoM0d1S3ZXT01MT2JiTXRmQmxjZ3NXR3Zkd2lDSTNuK2FWQWR0UWxBdkQvUlNOSEw4SmI1c2RQelA5VGUzamw5dDgvcXo3a1NVUTNUT0Yvc3NFUFdjbm9sbFQxVmZ3aC9TdHRmNFlSOUJIak5HbDQ5eUtKMGtOTVhYc2ppazFVV1FKaVFLbkJKNXYySGs3UngzV1QyVm9zU3IyWTBHQjd4OWowMEduK2w2TGsrU0xKZ21Xa0wyenRRK3ZwQjVhVU00ZTBqa1hUdUs5RVp3am56L2VGOFJqSmhHTUdCMk1SYWJnY2k2dEV6SzhyT0EiLCJtYWMiOiIyZGEwZjc0OWY5NzUzOTgzM2YxOGQyMDNhZDIzNGRkYWVlMzEyZWZkNmYyNTM4ZDNhNjUyMzgyY2E0NjFlYzA5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iks1Q3hHNlBad1ZtRVdqc0FMaWpFbHc9PSIsInZhbHVlIjoiY245SVVkdThpYjRMbFJIcnZid3MzOVdWZU00cU8vVEg3OTBzYWVvMldxMENka2wxU0xjRFM5cWcvbDRxcmtEWERyNEErdmtRTzQwZHBBSitoMGhPaTY4ZlpkWWlzR2lxRjVxWGhtQW1oR3R4Z3dNU3Y5aWZDQmpKTEpNZEpBQWh6YmI3dmhEd2JvQWl4MXRXdCtFeDZwUGhOemJJUm5veDhTU1ZtaHZyMmd6aTRMWHhZaElSTjdnYjRCNXpuOFJONnlTcHRLaC9EZEluNElWaW84QzVyTHlMUnowc1JiNG5RbTFqeEFZMy8yTDFwbUJYdWl2MS85WmF2NWFmWEVPTnc3OHIxemFvY1E2UVA1MGErMCtuRklqT29xcXl2OEVTV1RaK0VYVWlkZkdWZjByYm1jcFI4a1d6b1ZUOE1WekNDYmgxam9yUDRVVXdKL0hMdlFJYlpTVEdTL095Uk96UFdreVErVk54akloVXI5Z3Y0dEhWUXRFYk42VGJFOXFpZWhPbGlBRHI1bnFEZTRiTE9MdDh6R1lCaEUzL2RKUEtVYlBmb0UwejZxT01xcElwZEFLN2czbGxmTDJnQm9PUDU2WFVqb3B6Z25Bdk5iekh5MGEvelAvRUM2TlRaRHRlOG8yN21TWXBOdE5WbTB6ZDFHWUtjV2xDais0WTNSWGMiLCJtYWMiOiJiNjg3ODI0OTZiMjlmZmM1MDM1ODk4MGU0Y2ViNDEwOTIwNTA5MzNjNjExZGUzOTM1MWM0NzU0YWRhYThlNjM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266873938\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1379875398 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xWPGJpNWbmDLkwLBtG0MdpEYkMbvhJtQ6DvLx5Cj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379875398\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-449305629 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJmZ1VKdk1FOVVvdDk4MDNSTm52Zmc9PSIsInZhbHVlIjoiRi9XZjRmZDdZamt0TGJUVnBnR2ZiTlgvU0VVK2dEdGNyT1VTWXNIblptNm9RLzVsQkY2YzkzUGxkYitXU09Ba2tMY1N1ZTNBWGtqenNqbE9xUStBUWxtY3NLSTR2cHA3b0JMN2hjdzZmbHdoekkxRjM2LzNzaDJ0bVdPUG5jenRKVmFWOE50eUxMM3I4bWJSTVBtUW5uNXJoVWtMejdGcUVhS2FvYzFxTGFBQUZJRWFBN3paZmtUQVJBYWxsN2xMeWtlUUlrZ3oyUlNYUWtLUTludWljdXRZY2Y2bEFmNUxwNFAvbk82T0thamVudHJyN1VBeTZYR0xYWEhMVHJ1VUhEWWNtUFFiK0U2aHJ4KzJZN216T0s1ajdFV3h4VkdhSDMveWwrb3Jud3lOeHRoWFFOaUg5RTFIUnhrN3o3S0RHbS9mWTlyMXhjT2xWTTIwRkppZEw2K2FtSE1ydnZuKzM2NnRqWjM5Q1FhZVdRNXNkVFVuTnVaK1l4TDl2L0Y1S3Bqc1NWbDNpNHV1azNlZy9ZOTFIMUt6QWw1eGF2Q2tqUEdYOVJlTjRjeTVPV3pFaGxGUU53WU9FVzFnd1p5UVNYRG5Wc210VlJXSmdDMVFZeW9mU2sxYjU1eUl2SFowWkQrK1phbWVmN0pwdDNqb0YyZWRTYmxUa3hGTDNEbDQiLCJtYWMiOiI3MWUzMTVjNTVmZGUyODUyYmE3MWY4MTU5ODE2MzFmOGExNGMwMWJhMTgxODMyMTgyOWJiZDg3OTQzOTZjODQxIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZZQmJ2dGpBbTh3c205a1JaTVo2MVE9PSIsInZhbHVlIjoieXlwUjl6K200WHc3RnFiNXQvMm90cGdHTkR2V2poa21wV3ROak50Ris2WTl1blloYVdmZ2pXanVhNmJkY3RBUndlL2paZUhGOFJqN3VROTcvSC9HekRDMzNjWFNvSm9FQnlBMnJQVXc5T1dQK3hkMm9UZmt5bURLclBrU3RtZlA4ZEFhMnprSmYvUGFRU1RuaTBmQ3JRMnZ1emlsWnJ3a0RXWEN3Tlprcnc3ZHVPREkweHh0am1vV09Ib2hvblMyNE4zT2M0MmZsWEF4N0l4Y21ScVpRUnppSzV0dlFGa01wZXRTb002RCtHS2tBOS95THFaTlF3SS9NMGp2WUhLYXBRejFoQ04rb1psc0sxTnF1bUJLYmYrVm1wazQwcFB0TVZJOVd0Z2p2aTVHZXB5dWwwRTJiemljUmNlV3pRUE1SNnF0Sm9hNlR1WVVXSkNDcWEyb0gwRmNwMXVaelA1WG5DOUlTSFlQdEh5bWt2WHJIcGh1bVZobTJRaW9TU3FpOGVBZXBoODkvWnY0Rk1LMlF5T1FiQUxNRHJjZnZ6YkV1YS9oei9ScUNhaFRxTmo2eEg0MS92NkhhZURaVGtOVFVob0FTTXhwU2dmenlFOGl0S0ltTTMzazI2MXBPVWdSWThQejhYMEVYa2NJVHNVeWNwNFR2NWZsNDk5NkRraGQiLCJtYWMiOiIwYTczNDhkYzEyYjE5NzA4YjI3ZTZjYzAwYzFhOWY1MGYxZmIwMmRkNzMyMjdiMzc3NTk0OWNjMzk1MDY3YzllIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJmZ1VKdk1FOVVvdDk4MDNSTm52Zmc9PSIsInZhbHVlIjoiRi9XZjRmZDdZamt0TGJUVnBnR2ZiTlgvU0VVK2dEdGNyT1VTWXNIblptNm9RLzVsQkY2YzkzUGxkYitXU09Ba2tMY1N1ZTNBWGtqenNqbE9xUStBUWxtY3NLSTR2cHA3b0JMN2hjdzZmbHdoekkxRjM2LzNzaDJ0bVdPUG5jenRKVmFWOE50eUxMM3I4bWJSTVBtUW5uNXJoVWtMejdGcUVhS2FvYzFxTGFBQUZJRWFBN3paZmtUQVJBYWxsN2xMeWtlUUlrZ3oyUlNYUWtLUTludWljdXRZY2Y2bEFmNUxwNFAvbk82T0thamVudHJyN1VBeTZYR0xYWEhMVHJ1VUhEWWNtUFFiK0U2aHJ4KzJZN216T0s1ajdFV3h4VkdhSDMveWwrb3Jud3lOeHRoWFFOaUg5RTFIUnhrN3o3S0RHbS9mWTlyMXhjT2xWTTIwRkppZEw2K2FtSE1ydnZuKzM2NnRqWjM5Q1FhZVdRNXNkVFVuTnVaK1l4TDl2L0Y1S3Bqc1NWbDNpNHV1azNlZy9ZOTFIMUt6QWw1eGF2Q2tqUEdYOVJlTjRjeTVPV3pFaGxGUU53WU9FVzFnd1p5UVNYRG5Wc210VlJXSmdDMVFZeW9mU2sxYjU1eUl2SFowWkQrK1phbWVmN0pwdDNqb0YyZWRTYmxUa3hGTDNEbDQiLCJtYWMiOiI3MWUzMTVjNTVmZGUyODUyYmE3MWY4MTU5ODE2MzFmOGExNGMwMWJhMTgxODMyMTgyOWJiZDg3OTQzOTZjODQxIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZZQmJ2dGpBbTh3c205a1JaTVo2MVE9PSIsInZhbHVlIjoieXlwUjl6K200WHc3RnFiNXQvMm90cGdHTkR2V2poa21wV3ROak50Ris2WTl1blloYVdmZ2pXanVhNmJkY3RBUndlL2paZUhGOFJqN3VROTcvSC9HekRDMzNjWFNvSm9FQnlBMnJQVXc5T1dQK3hkMm9UZmt5bURLclBrU3RtZlA4ZEFhMnprSmYvUGFRU1RuaTBmQ3JRMnZ1emlsWnJ3a0RXWEN3Tlprcnc3ZHVPREkweHh0am1vV09Ib2hvblMyNE4zT2M0MmZsWEF4N0l4Y21ScVpRUnppSzV0dlFGa01wZXRTb002RCtHS2tBOS95THFaTlF3SS9NMGp2WUhLYXBRejFoQ04rb1psc0sxTnF1bUJLYmYrVm1wazQwcFB0TVZJOVd0Z2p2aTVHZXB5dWwwRTJiemljUmNlV3pRUE1SNnF0Sm9hNlR1WVVXSkNDcWEyb0gwRmNwMXVaelA1WG5DOUlTSFlQdEh5bWt2WHJIcGh1bVZobTJRaW9TU3FpOGVBZXBoODkvWnY0Rk1LMlF5T1FiQUxNRHJjZnZ6YkV1YS9oei9ScUNhaFRxTmo2eEg0MS92NkhhZURaVGtOVFVob0FTTXhwU2dmenlFOGl0S0ltTTMzazI2MXBPVWdSWThQejhYMEVYa2NJVHNVeWNwNFR2NWZsNDk5NkRraGQiLCJtYWMiOiIwYTczNDhkYzEyYjE5NzA4YjI3ZTZjYzAwYzFhOWY1MGYxZmIwMmRkNzMyMjdiMzc3NTk0OWNjMzk1MDY3YzllIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-449305629\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}