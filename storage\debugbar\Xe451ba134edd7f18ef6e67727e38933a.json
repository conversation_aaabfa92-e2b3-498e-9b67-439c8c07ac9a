{"__meta": {"id": "Xe451ba134edd7f18ef6e67727e38933a", "datetime": "2025-07-14 14:40:12", "utime": **********.472881, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752504011.978125, "end": **********.472897, "duration": 0.49477195739746094, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1752504011.978125, "relative_start": 0, "end": **********.40236, "relative_end": **********.40236, "duration": 0.42423486709594727, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.402371, "relative_start": 0.42424583435058594, "end": **********.472899, "relative_end": 1.9073486328125e-06, "duration": 0.07052803039550781, "duration_str": "70.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46017832, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01718, "accumulated_duration_str": "17.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.432519, "duration": 0.01614, "duration_str": "16.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.946}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.456854, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.946, "width_percent": 2.619}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4636, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.566, "width_percent": 3.434}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1069589437 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1069589437\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1682780269 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1682780269\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1485261208 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485261208\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1213398893 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; XSRF-TOKEN=eyJpdiI6IlluczdDOG5zQzVMenVibEhrNmZ0UlE9PSIsInZhbHVlIjoibzBkOVB0N3NHSmVZVWZpYXJrd3VxVmVtQXpqZ1RQWjVVUWl4RjkyaDdnbWtEQ0dwZzUwK0FvVDdxSkpCZGQ4WUltcFRJeUE5a1NYbEhYWGdCSGdWZGx0bjh0WHI4dStpam5WVEt3YUdRV0hTK0NZN3hqakJ5clA1Tk54VDF0M0tZVkJmWnpkRjVKeFp3NzhWSWx2NXlsb3lEeDArSXNNYVRIR1I4cWZLNzhxakM0YjFlaEp6UUgzRjRKWjB5NVMwRnZKUXg1UkFYOS80eHprQm1TNkU4TlljZlF6K0w4RE1iSTdUQVpjbUQzdU82RnhRMFNCVVd4L2oyT0YwQ1N2RG1WQ2t6dkxycC9JZkFKTVh5RUZoMytVeE40QjBRTnVMNHEwVlYrdGRoQjdWUytweEprUFdzdFh2L0lybDNIN2lDZk0xQlA5OUtxQTVOMkw5SFpmRys1TkM0TzB5bjdNZDYraTE3WWFlNDh2ZFpzUzdSTXdVMjV0T3lRWGtZNlJONkZ4QnlOQlJ4dmJVMkd1bEJOTllpYnAxN05KdFpPcWxJWkg4L2lmV3dWZnM0dkp6OU02d3JJbkNVMWYwUVZtaVVkVjZ6czJ2RGkwVTBRcHBGRFU0TWN3bHI4MEw4OGFBVnFVaWEyaW5FSmNoUVNpWGZzQzQzNUMvL29LdkROWUIiLCJtYWMiOiJjYjgyZGExY2MxMzQwMGMzOWZkOGFmMTg4ZjkyZTRiMGNlN2Y5OTZhY2EwMjkwMzc4ZTA1YmZiYTFlODJmMzVlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InQ1d2NRTjV5Yi9RelF4VnpLTFJmSEE9PSIsInZhbHVlIjoiQjJQZzl5aHZTTk1RWlZVd3lRVEtRenZIN1UzVXB2dVYremRWN1pnRXFLQ1lDbW1ZNWVDVkkxRDNROXVCSThoSVpkMHJ4VXhBcWpzUi9NS2RkSzlUUFR6WkFNOVJ1MWNSdUZFdkVqOWhVWFlsbktwWWUyTW53WmpqVWpaUHdHWGZkeFZVaEQ0TUhIUU5KaUZOcGVxbEFCaFo1VERzZ3BjRWRLTEVJVTAxZ1pyc3prU2JuS1U1enlhZ1BZUHYwU0habHZOekdNTHVMVUQ2ems3Q3ZhTVQ4WjB0YktqYnJ4WW04OGlualJjT2lLTERDczFQQ2E0SDBRbGd2MlNxaXIxcndVUDh2SFMwVys0YUxUZ29oS001SWU0VzVadFJBV0pHNVZkY1RyYkRMTnFpUG5SWHNpNnFtVndkZlFTVElqdjVVckdXbFNOaXIwT25JS3Bub2JTZnJTZU9iYWFWcDBCT3VJRGV4YW5BRGFwMHA1b1hwUlNCQjVGK3BIVVI1Vmx2bTQ2ZXZaTHdzZWlmUFFxSGUvdzZNYUd0WWRsZDdtTnhzWkhCelJwK0xYeEczSzhISUVlZm9GV0ZtNmI4UU8yYTNocGhyNVljamZCck5SSHJFcnpvSFBNQWYzMmN0M2VTQ0tScFpDQ005TVpmcUNnb1ZqQ2UrWEVKNG5Nb1lHVUUiLCJtYWMiOiIwOWIxNzM2NTI2MjU1OWY5NjFkNjAxOTczMWU5NTI5ZTZmY2ZjNDY1ZDMxNTY4MjBmYzhkNzZkMzY5MmNiNDFiIiwidGFnIjoiIn0%3D; _clsk=1g7ulh6%7C1752504011682%7C21%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213398893\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1861552399 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861552399\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-995263091 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:40:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpjaDdQSVducEJ4b24rSFYvUHZ6aFE9PSIsInZhbHVlIjoibGx2OEJGSjNFcG02TE9HR0k2bWYvL1ZaWnZqeFp2T0xLNVRUQ2xzbWFYYXpUN2lCWnBOK056Y3ZFRXltY3RCVlExekt1UStUM21WMWFqSHdvL0EyUFA4YlFkd3NKMURWUzB4a3RyL1JXM0tsd0t6dExiWkFhbWd3Y1A4RHl4aHhscElPN2FRRVd6QjJkTnY1WDZMUWJmeXhMMitmZElkeFlaZU5INjM1QkloaXBKL2lneWJwUHExaGZTVCtXd05TRUFjU2FLU0Nyd2ZsODBUNk9NZi9naFJhblZxL2tCMmpMMnpXWXhnZDVxUDNoNHZ0ZlpGVlZjS2pRbzYwZGludTZaRERaOU1DamY5bEZncmtaOEluY3hpZVZZZ1BNZmFDc3RQQ3RSdnFpTUovUFZLeHJjY0dNdFA4b2tGUFI3WWN4Wkl0TWF4aEtzVVQzU05YUlNTUk5MNHdDamhreTk1ZktZbXlQSUt5TDBaOVd6ZWN3MG5GWXl1OFN2aEM4QkZTRlNxMVI5NGJPZyt4R1RucldRVm43dEtJekc4Y0lPUi93bWpKNm53OXRuZXc2T1hNYmY2OHNNb0I5K2RPMDRKendsN1czbTNSV0FBcWRENFEvN0dYTGVHYkhIMnpJZ3ZjS1FKN2hFREswbXVWRUNvNW9kNlJOZEp4RVgwNlM1dGEiLCJtYWMiOiJjNDRkNTllYmNlOTViNDY1YTA2NDM1MjNlMzIyMDFjMTY5YTlhZWU5MjdmZTJiMGFmNGIzNjA4OWYwOWVjM2ZhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktNcXM0N3VDOTVYdUdMOUtnMFh1ZlE9PSIsInZhbHVlIjoiMFVuK1BweVhYaXdTRGtrSHcrZ1IxR2EvOHNGT0RQWElERDFIS29LZWNLWERpYzNJZjVMN3MwVHRLNkRubDVZWGFVK0xtcXBnamg2MzJyT1hNcUg0NHZyM2ZHWU5GbWNVZnVxTXBOMmEyeEczV1QwZmhYQlh4RlVEUk9LbjJnSWtDeWYwcnBGWXZFSVg2SjlyOGV1aGF4TnBYMlNJSDBKZmNrRkE1bnR3cDk1ZHRFcjFXR3MyakdOWUZBeDV1RVFxM1ZHYzgvUk5OdzVsQmU4NDFzQTZvOFg2WE45NjFZdjMrZkVCVzh0UlZCZ254T1Bmb2lMMjFYd0l4SVZGa21nZmZxbVdFTDU3Mlc0eWpxa0RIbHNxWXlBZmZudC8xVGRFWmJEL0RrL2VjN2Zra2NBZG1QLzBCYVNlSFY0NEE3Y1pxdWxSa09GUjB1bW83a0x3WndMSVcwbCtsbmF0eTRTbUpnNXB1RERhSklNOHFjTVp1a2tKVXBuSk1pdUNuRzRnYm1QUXlpSXRsRXprTDkyWFRnektNUHl0b1lFTStaUVBvYXEyU2FPZ05Nbk0xRFpBMnBrT2VGLzJaeVR0c1RRaDBXZUlXK2laekFVZG5KV2FrYVZEdEJFazdiMnZnTTFLK1ZLTk1RRXhWOC9BSzltTERSSzhWNkU4Q244N2Z6b3QiLCJtYWMiOiIzNDY4ZDU1YjNkMDA5N2IxZjQ0ZTk3MzlhMWUxYzE0OGQ0NWZiMTVjODlkMmFhZWZiYWUwYjgwZjFkYzAyMGYyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpjaDdQSVducEJ4b24rSFYvUHZ6aFE9PSIsInZhbHVlIjoibGx2OEJGSjNFcG02TE9HR0k2bWYvL1ZaWnZqeFp2T0xLNVRUQ2xzbWFYYXpUN2lCWnBOK056Y3ZFRXltY3RCVlExekt1UStUM21WMWFqSHdvL0EyUFA4YlFkd3NKMURWUzB4a3RyL1JXM0tsd0t6dExiWkFhbWd3Y1A4RHl4aHhscElPN2FRRVd6QjJkTnY1WDZMUWJmeXhMMitmZElkeFlaZU5INjM1QkloaXBKL2lneWJwUHExaGZTVCtXd05TRUFjU2FLU0Nyd2ZsODBUNk9NZi9naFJhblZxL2tCMmpMMnpXWXhnZDVxUDNoNHZ0ZlpGVlZjS2pRbzYwZGludTZaRERaOU1DamY5bEZncmtaOEluY3hpZVZZZ1BNZmFDc3RQQ3RSdnFpTUovUFZLeHJjY0dNdFA4b2tGUFI3WWN4Wkl0TWF4aEtzVVQzU05YUlNTUk5MNHdDamhreTk1ZktZbXlQSUt5TDBaOVd6ZWN3MG5GWXl1OFN2aEM4QkZTRlNxMVI5NGJPZyt4R1RucldRVm43dEtJekc4Y0lPUi93bWpKNm53OXRuZXc2T1hNYmY2OHNNb0I5K2RPMDRKendsN1czbTNSV0FBcWRENFEvN0dYTGVHYkhIMnpJZ3ZjS1FKN2hFREswbXVWRUNvNW9kNlJOZEp4RVgwNlM1dGEiLCJtYWMiOiJjNDRkNTllYmNlOTViNDY1YTA2NDM1MjNlMzIyMDFjMTY5YTlhZWU5MjdmZTJiMGFmNGIzNjA4OWYwOWVjM2ZhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktNcXM0N3VDOTVYdUdMOUtnMFh1ZlE9PSIsInZhbHVlIjoiMFVuK1BweVhYaXdTRGtrSHcrZ1IxR2EvOHNGT0RQWElERDFIS29LZWNLWERpYzNJZjVMN3MwVHRLNkRubDVZWGFVK0xtcXBnamg2MzJyT1hNcUg0NHZyM2ZHWU5GbWNVZnVxTXBOMmEyeEczV1QwZmhYQlh4RlVEUk9LbjJnSWtDeWYwcnBGWXZFSVg2SjlyOGV1aGF4TnBYMlNJSDBKZmNrRkE1bnR3cDk1ZHRFcjFXR3MyakdOWUZBeDV1RVFxM1ZHYzgvUk5OdzVsQmU4NDFzQTZvOFg2WE45NjFZdjMrZkVCVzh0UlZCZ254T1Bmb2lMMjFYd0l4SVZGa21nZmZxbVdFTDU3Mlc0eWpxa0RIbHNxWXlBZmZudC8xVGRFWmJEL0RrL2VjN2Zra2NBZG1QLzBCYVNlSFY0NEE3Y1pxdWxSa09GUjB1bW83a0x3WndMSVcwbCtsbmF0eTRTbUpnNXB1RERhSklNOHFjTVp1a2tKVXBuSk1pdUNuRzRnYm1QUXlpSXRsRXprTDkyWFRnektNUHl0b1lFTStaUVBvYXEyU2FPZ05Nbk0xRFpBMnBrT2VGLzJaeVR0c1RRaDBXZUlXK2laekFVZG5KV2FrYVZEdEJFazdiMnZnTTFLK1ZLTk1RRXhWOC9BSzltTERSSzhWNkU4Q244N2Z6b3QiLCJtYWMiOiIzNDY4ZDU1YjNkMDA5N2IxZjQ0ZTk3MzlhMWUxYzE0OGQ0NWZiMTVjODlkMmFhZWZiYWUwYjgwZjFkYzAyMGYyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995263091\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-766776682 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766776682\", {\"maxDepth\":0})</script>\n"}}