{"__meta": {"id": "X424c8253f3f7eb68150de4732a28bebb", "datetime": "2025-07-23 18:21:23", "utime": **********.397304, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294882.9766, "end": **********.397318, "duration": 0.4207179546356201, "duration_str": "421ms", "measures": [{"label": "Booting", "start": 1753294882.9766, "relative_start": 0, "end": **********.342242, "relative_end": **********.342242, "duration": 0.36564207077026367, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.342251, "relative_start": 0.36565113067626953, "end": **********.397319, "relative_end": 1.1920928955078125e-06, "duration": 0.055068016052246094, "duration_str": "55.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46535760, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00312, "accumulated_duration_str": "3.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.370689, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 55.769}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.380518, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 55.769, "width_percent": 28.526}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.388391, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.295, "width_percent": 15.705}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2075372539 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2075372539\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-737056293 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-737056293\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-21269688 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21269688\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-929236603 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294880614%7C15%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJkY2srLzF1K0tJcVJ4eElHK0VwSEE9PSIsInZhbHVlIjoiZEVUUVYvYnd4ZnpFaSsreW9FN2UyRmMrdmxGQVh1WUZNWFVLVDVpNVVYd3daYzExM2tpK3hJMlBkSy9ITEhnK0lGWHNxTE4vYVpFM0JhTkJDV0ozYkdmbGJvWlp3SXcrQ2ZWSkwrQ1pkcGtvSUxRdG13TmRXQWpPa3Zta2N0LzFkcDdkMFdDbnZqc0RPS1k1SDYwR3UrZDJHU0I5TEQvaVVpMmZGMmk3NGxWdzRiaGl5V1MyR1hNTkk2VWo5RGFid0RIcVpsTGR5WExuRmtJb2ZoZHhaZ0NqR3dtK0xmeFdjTy94MmVma3ZtQXFEaVpGN0NPem5wTTRCVVE3NWJ6Sy9xTlNKUWJPV0hIM0V6dUhKemFvcU52WEQrTzF2UTJYaWNZakhSSDJiVUlFVVZjOFE4ZXduZkJoRWc3TXV1SU1oQ0hIemJlMkw4dnBmWUtIcmNOekJ4ejBVL1A4VGVlWlJnQk5hNmxuZWFvbnc2S2l0SzNYYzN0RDNVb0VCUUdGNDNHSmI4aWRDRlZsVHNzZHF6UUVMQk1HamRiemdIemNFSFVmTUtZcXFNWjMzc3ZHeDByS0x1RmgvaEdyNGI5aVZIdU03aG9mS0dYV3A4Qmx5NUNpRVQ0aUJ3RmV1bFVkdmxWcThiY0pOSmE0STNROFpSNnNNSlVXTmRLUGxGZWUiLCJtYWMiOiJlMDY4NThjYjQ1YjQ5NGQ4OTEyMTBkMTFmNDlkMjQ5NTlkNWUzOWJmMmVkZmQ4NTA3NDk0YzQyNjMxMGNjYTAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdjVktmK2xoM1lFOE5XTjlZZlVBRlE9PSIsInZhbHVlIjoidTNMd2oxRmF5cnRpRWkyRlVOMnJ6RmkwaC9NRnlsT2NnMWlsNFRPN2I2SGxqdklnbVgxcGlrM21la2M4VFkvZ20yQVFhK3gvU2hrNDMrdWdUcHdUTWt3R3owV0VxN1pJQlovUlIyUVFpWTdINUFnK3BSRmJNdHVJdThHcG1UUnd6aVhWbDRwRzBhL1BRcjM4QzhacC9MNTlIRmJyN2lYaHV3OWFyRzBtY3RKbHpSOUN4dFg4NHBIaFUrc3kyVTZXTUwzSEVDYkRnRC9ma0NPOUpGb2NXVzhJYUVhV0RFb0ZsOXdTZnkyUGJqT2VCcSsxb0dUYWlxZlFPcHZUM0U1WkJTWlNIOTl4TFpnYk5NM2s0WkhOSm5Xd2U4cjVpVWJhNXNkakJMOWwyUnpKSXpTdlRnYjJiQ1F1TmtkVXlwb3I4N3M1emJialViYkVwZldDeFBCTXlDcmZFandzeUR2eHYxUjE5N3h6OTh4SjQ4VldsUDBDZ051SWMwNldFWk8ydTZSRHhBamFGMWdxeS9QUlQ5bzBQRVozQjNiS09TcGp0RDI5UnZXTzB3ckorMVFINjNkK3k5ZC9NTlpYbHJmWnM0OGdTQXE0VWxKWFArTTZxUWlOekF3RVhqdVh2M3hDb1FuRVdoSldLdkoxcjh1ZVNGVFEvRkxLdlRIb05pQ0oiLCJtYWMiOiJhMjM3ZGNhNWE0YjAyMDQyOTIyNTI3ZDY0ZTRiMGMwYWNkNTM3Y2Y2NTU2MTE4ZjZhNzg3NGI4ZTk1NDAxMjA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929236603\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1437010653 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437010653\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1955669221 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilc0Tzlkd0hpemczaUxkeGlHMjVxdVE9PSIsInZhbHVlIjoicWNYMUJwTVBQVkZ2VnF1ay9oTDl5ZGNacjRnRzdsemp1MmxqamZyMWJIT2xrcDN5Ymp4cGtOVUNTR092SzMxUUNGVVVsbkgrL3dBY0VOK09yNGErTXBoYndQZnAvL2VXZ2puaXpEQitNSGZweUVscy9vMnRlOUU4d2JHT1Q3dk5yWWJwTVV3cE40TVpGU1FVV1V3VzVqWm1JNEhDb05BOVR3ZXRaYnBkUGFuZmZDNTNaanRnZUZCZUo1N1dxYTNDdThkeld4Umx2amg4RjQxam9Wc25DUVk0aFpHMXpyaVVBNlo1eWdzaUFWMlUrVHQzN1AxbjB2VnBLdkR4eWROSTRtYUVtZnR0THM4bXYzcDVyZnRPOTlyWDZkdVZ6ZDZWT1pJV2RBWlZGa2VmRE9sekJYMVFQUTE3T2k1STh0a3E3MzlaL1hHQTFCdlhrdHZTYXpHby84WXNiZmthd29Jd1dGWnc5Uml1WXpXdGZ5bWI2VW00VVpNSGhQQlREa2xCbmdaNUNIU2dSRUFvK3RCL3pxMUdKWjVXZVFmRlNSWDl2eDBUK203VnFISDhFVlpnVlJLTSs3Q2V6R3ZyQnFCQkt3WnNCVklwS0JxT1FaNWlHTmJpNy9hdnhIQTdkN1BSTDBzTnZ6cXV0by9PWVdSaEoyYnhabkh1M1pUdm1ENHkiLCJtYWMiOiI5NTk4ZGY3YmJiZWJkNzg0ZWUzZWUwMGVmNjc2MjEyNGQ0NjhiZDMwODUxODRhYTQyZjk3ZWM2ZTZlM2I4NDczIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IisvY2YvaVdqSGFGMGo2T3doSDN1MXc9PSIsInZhbHVlIjoiS1czMVh5ZGxvUWMzazAzUG9qbkhjRG5VdWR1dHIzcmJnM01KT1F3L2llTGtCeUhuRFp5MVlQTUZQVHU1cWtiTzhBQVBKakRUYS9YdkJ1QjRSaGh3aUxuTkowWkNsbTczU2pZTnZ6ZGU4QjBDbmpKTkNWMXF5YnVHSmU2anFVd0FkenBMdTBaRnVKZ3VuYkZMVSsvTjgrc3RscEF2eGZhNGZMS0RiSk9MTlZjVlE2VnJsSzgwYVVnZ1ozNGZKWXFvek9HRUdDREUzLzdhL3VxcHkrdHUvc29JMDBzemNZNUlPNVQxbDNWZDZHcjUxVzZQZHhmcVlrbHVOaEMzMVJEaVVyNnlxME5LdHpQait2V3dyeU1wdjE1MFJTZWVXY3dwOHgxS0JOcG9GckZjNkdlQ3B3Nk1BZnZqNFFjb3RIREEzMUlEbWN0ZHhlQTA2UE9PcnF0TVF4M0p1dTBIQkdPQUJXZ0R6dmc2S0pxTTdaUEZ4czc3VCtMNVN2TFhMSGt2WUFEQi9hRDNKejBmMFdyK0s3NjAxaFBtMmxKRVRlMlFIQ3JFaGU4NjZwODdlalZoblRsUWVvQkNUcERXOS9BZUIydFh1aUg5bnlnZFIyVWFJbG1DTi9qYmZ0RmptVloydVd6TkgzZFFVUCtSazhyOCtmd1NyMmQvWm5QS1JNVWUiLCJtYWMiOiIyYzVjYWFkMDQzN2IyMzI3ZThjZWJkYmI4NGQyZWU4NjQyMWUyYWI0MDM4YzZhNmIyZjdkMWJhYzRkOGNkMmFlIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilc0Tzlkd0hpemczaUxkeGlHMjVxdVE9PSIsInZhbHVlIjoicWNYMUJwTVBQVkZ2VnF1ay9oTDl5ZGNacjRnRzdsemp1MmxqamZyMWJIT2xrcDN5Ymp4cGtOVUNTR092SzMxUUNGVVVsbkgrL3dBY0VOK09yNGErTXBoYndQZnAvL2VXZ2puaXpEQitNSGZweUVscy9vMnRlOUU4d2JHT1Q3dk5yWWJwTVV3cE40TVpGU1FVV1V3VzVqWm1JNEhDb05BOVR3ZXRaYnBkUGFuZmZDNTNaanRnZUZCZUo1N1dxYTNDdThkeld4Umx2amg4RjQxam9Wc25DUVk0aFpHMXpyaVVBNlo1eWdzaUFWMlUrVHQzN1AxbjB2VnBLdkR4eWROSTRtYUVtZnR0THM4bXYzcDVyZnRPOTlyWDZkdVZ6ZDZWT1pJV2RBWlZGa2VmRE9sekJYMVFQUTE3T2k1STh0a3E3MzlaL1hHQTFCdlhrdHZTYXpHby84WXNiZmthd29Jd1dGWnc5Uml1WXpXdGZ5bWI2VW00VVpNSGhQQlREa2xCbmdaNUNIU2dSRUFvK3RCL3pxMUdKWjVXZVFmRlNSWDl2eDBUK203VnFISDhFVlpnVlJLTSs3Q2V6R3ZyQnFCQkt3WnNCVklwS0JxT1FaNWlHTmJpNy9hdnhIQTdkN1BSTDBzTnZ6cXV0by9PWVdSaEoyYnhabkh1M1pUdm1ENHkiLCJtYWMiOiI5NTk4ZGY3YmJiZWJkNzg0ZWUzZWUwMGVmNjc2MjEyNGQ0NjhiZDMwODUxODRhYTQyZjk3ZWM2ZTZlM2I4NDczIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IisvY2YvaVdqSGFGMGo2T3doSDN1MXc9PSIsInZhbHVlIjoiS1czMVh5ZGxvUWMzazAzUG9qbkhjRG5VdWR1dHIzcmJnM01KT1F3L2llTGtCeUhuRFp5MVlQTUZQVHU1cWtiTzhBQVBKakRUYS9YdkJ1QjRSaGh3aUxuTkowWkNsbTczU2pZTnZ6ZGU4QjBDbmpKTkNWMXF5YnVHSmU2anFVd0FkenBMdTBaRnVKZ3VuYkZMVSsvTjgrc3RscEF2eGZhNGZMS0RiSk9MTlZjVlE2VnJsSzgwYVVnZ1ozNGZKWXFvek9HRUdDREUzLzdhL3VxcHkrdHUvc29JMDBzemNZNUlPNVQxbDNWZDZHcjUxVzZQZHhmcVlrbHVOaEMzMVJEaVVyNnlxME5LdHpQait2V3dyeU1wdjE1MFJTZWVXY3dwOHgxS0JOcG9GckZjNkdlQ3B3Nk1BZnZqNFFjb3RIREEzMUlEbWN0ZHhlQTA2UE9PcnF0TVF4M0p1dTBIQkdPQUJXZ0R6dmc2S0pxTTdaUEZ4czc3VCtMNVN2TFhMSGt2WUFEQi9hRDNKejBmMFdyK0s3NjAxaFBtMmxKRVRlMlFIQ3JFaGU4NjZwODdlalZoblRsUWVvQkNUcERXOS9BZUIydFh1aUg5bnlnZFIyVWFJbG1DTi9qYmZ0RmptVloydVd6TkgzZFFVUCtSazhyOCtmd1NyMmQvWm5QS1JNVWUiLCJtYWMiOiIyYzVjYWFkMDQzN2IyMzI3ZThjZWJkYmI4NGQyZWU4NjQyMWUyYWI0MDM4YzZhNmIyZjdkMWJhYzRkOGNkMmFlIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955669221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1013146165 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013146165\", {\"maxDepth\":0})</script>\n"}}