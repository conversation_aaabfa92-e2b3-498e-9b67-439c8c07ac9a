{"__meta": {"id": "X7f7b9e7484062efa725d93cac2a88749", "datetime": "2025-07-14 17:58:27", "utime": **********.080852, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.635961, "end": **********.080866, "duration": 0.*****************, "duration_str": "445ms", "measures": [{"label": "Booting", "start": **********.635961, "relative_start": 0, "end": **********.002396, "relative_end": **********.002396, "duration": 0.*****************, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.002404, "relative_start": 0.****************, "end": **********.080868, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "78.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024720000000000002, "accumulated_duration_str": "24.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.029837, "duration": 0.02377, "duration_str": "23.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.157}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.062511, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.157, "width_percent": 1.254}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.073404, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 97.411, "width_percent": 2.589}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C**********042%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF0R0REVUhTT1lod3FxOW5rSVdxQWc9PSIsInZhbHVlIjoiakFHYXhzNzlDQ1RRY3BndzVZQ2JzVXk0MzRLcU5Ud2RTZ1FOREg2QVQxMTUxVzdHeStwZTJrSWEyQkc0V0N3azF0Mk5pUEUyK24zR3c1WUgveUJXUXZGT0RYRTlvT1ZCakJuVW9lMGlsYk9KQXBqUTdnV0VZVEUveFFxTjJRMlJvNnpVYVZUSE9kSFJyQ1d5S2FzVnYrMFlkTTRJTEl5eXZBK1JWZjV5a25sT3laeUtMYXU1K0JsTzhOQStvUCthdjBPVzBKb1kyUUMwbjRGcWszNSswVk82dWxkemNlNTJpaWY4cnhoY1NKYjZyV1ZUMmMwTFc1S0gybGgrT3h1ZHZkVmZ6VkcrbG13elVROUhjbnVrNGZVZURycVppZzl0dnI4UlBjeGNoNThPN3liR0lsTzZIeDJ6MnhxQmV3NllzOW8wczVSekZ3YzkvNWVKN0tyR2tpNERSZmZLM2NQRXhtQXRQelVNdDI3bVBBUml2Z3NRYkVCZjdGblFqQzRwRVVUSGtSWnloUUZUTkxPRTlaYVFoU0NwZGdTTVd3TzdHRUhLWDlHTlhucDRncmhXU1NzN2MzM2MrN011ZkMvRktTbmZKSCsvMk9MWCtKK09sRnF6dVl0L0ljbE55Q1lSMGZjWlV2dWU5ekZadXZNNmNpbFhvMmh4MHdWZnN6c3AiLCJtYWMiOiI0YTM2NGEyMGE2MGI0Y2RkYTBiZjhjMTdkYjJjOTllNGQxNDA3MDNhY2UzN2Q3M2I4YjE5Y2UwYWI2ZjQ1NTQwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRKZkx6SkZISWhJWWFodkpaSjVUclE9PSIsInZhbHVlIjoic1AvSzZ0N3d2OExZRldzMkpNcFhBS1h2TlErZ0lGY2VSRTI4QTdmbTBYUC9oUHhMNGtVd2d0cXpjNXQ4c0IzaWpjWWRTRjM4QlV5S09sZmJ1QkYvVFlBMHUvTU14YXZ6WGpCTGYyUk1TcnFmdEcwdGhOZXB3RGJ0VjFSSDJpWnFCMWtVenFkRlR0RksxZjlicE82MTVRSFI4QWc1WUc0ekhhOVlzZWlhRDJIaU9YKzZjaXlxYU1KTmVRUVl5eEpyREdrNkJZVUUrOGJKUFU3ejJXTHdxalAzaVhNWk1qM1F4K3JjMVJvbFY4YXV4ZmpDTjNTUmVrSHdJQzFNRk0yZWJWNFlqZzE4TU9ZQU56Vm5laWdld1YvcGYyNW44dUJtbVA2Rys1NHVNeE1xNngxaDVrRHJqM0tKZ2xEbGIrbjgxdWluZDNjbC9WSnhZb0Jhd3NIb1JQVVYrdTVtcDJhdGZTU1hZU1NoT1J4ZWJkc1FBZEprZ0svVlhZUzdjbUo1bGcyMjFDTFI2aUhuWFBsc3NuTG5aaHozTGFyRUFkbjNSczk1enlZNTlzMXQ0bGQ1eGFWL3F6b3lHN0pZdFBERWlBYlptN0dqMmdhTk8wQ1h0NjM2VDdKcmFCdkE1T3lJN29wT2J5R1dPUWM3d0dmRVBaSVo4RkNicTdNWmV0VFciLCJtYWMiOiI2YzZkZDVhNTAzMDFkMThjMzE0OWVjYWY2YzZlMmY0M2FhZjI5MmEwMGVkZmFmNzE2MDZlNzg4NTg5ZjllMjBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-712410824 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712410824\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1463332146 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImY3YkFJMjdEeVZlTGlkN3p4OUpaNGc9PSIsInZhbHVlIjoiNUF0MCtCUWpaR20rdUg2TnI5WWhNVUk4NFB3eHRIdlV6dDB5RGg4UjF4dlpackF1UTd6azZxUGFhYmZJcVZ3ZUxrRldPbDdxQ0ZPUkxhL3BMS2tJRW9Ga1hXWHMvdjJlNjNmVGlMaTR0MjZ6aGRPVFhmM1V4ZFk0SXpJRk8xdjVTUXJvT3l2Vk53SU9lWU5yWDdpNUNnZTBob0xWWEhxUW12MHhrTm95UVlraUY5QVNnRytZS2dxUWZMTzNPbndmWFh6VVdGaWxiNy9qVTFWd2RrWWRJbXBJQnVKY2tnTW9UTnpteit0UjJHaXRURmFkVWRyVjlKT1dJWVU1KzNUTFRGeG5rVmJ3YnovRHFzQTJOdmtYUktDWGxhMXhNY3YzcTNsdlY1V0JEVnh5RFpxZXQxRUVqREYza3RQOHo5KzUvV2lWVGdmUnM5WUpXZHJ5Q09TYnk1dy9IdTkwekU1RDVoSGJwUXo5L3hIeXlrb0E3T2w5c0FMUkVmb2RUblJDYldRQ2QrMHNSbDhLVjFjZE9aQ0NscjczQjloRVVCTVdOeXFxOG9TeklkaWRtTERuZjZPbmVlc3JlV0tsUkwvb0IrQUNFZ0FMUExCZnh1cis4ZFNYSHB5LzRHVjU1eVlrMDIvWUtDbldHN01EMkxsZFZudThpb0FhWmtaUER1eWQiLCJtYWMiOiIzMzk1OGVhOWI5ZTdhMDgzZGMxMTQ2MTQ0Y2Y0ZWFjY2M5MGYwZmM3ZjU2MzgzNGYxNDFhZTc4ZDgxYTgwNzliIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJUa1ZKMmtYNm1oNVZaZVR2UmlsNWc9PSIsInZhbHVlIjoiZE9mSmxyS09uT3ZLbDNqZ1p0dXg3c0hWTXpkWlBrVG92S3hjb3hrNjFtN1JKcFhpdEkvWUEyWHdwU204NGtIOTNQTkM4ams0ZkdSRUFOZDRQSEVLOG45d2tTNk1zU2ZNM0lRVDN6UVRML2kvR0pwVEdrTnkwR0FXam5IRFVGT0ljb3E3MnBMaE9oNTB3QTNxTnZtLzBuc2VXVm9uZFA2eFpVeFZFR3Bxa3B1ekIvMkhhUkdydE54V2t0V0htRUQ1V0hyVXlXR2hNVFZDam45b1FMb1B2Y1NiaUh2WXF3QXcrejBLeEtSTHAwU2tFbEEzb21HKzF6TEdZcG5CcXJRcS9XSElFV01na0ptRGo1U2p5ZkFxOS9QYUxxM2pQblZOUnk5MmhCOGNhaFhveXBuTlRqQkF2ODRCTUVYMnM0azRVV1ZqOFJURkJ0OUFGNWswaHVBVEJwMEI3K2hFaEIwOVZ5V3hNWUx6NGRBNisyc3lTVUZZelpMY2dRUVFBYWdRT0VvYkZIRGVMcmVweTJ3TVEvYmg1OEZaZTNqS1lxK2FZcUY5d1hIajVvVnQwcStvN0syVERKWnFselYvRnErdERVYlgrNy9CZ3F3MEVBWExOUFFqdzYwUkFhVkNweGQ2YzNTcGRKOENWYWQ4cThvWVBjWE9Ha0pFVHVkNHdOQXgiLCJtYWMiOiIwMWU0ZjljYTE1MGU2MzVhM2Y3NjNiZmIxZmJkOWYxY2MyNDM4ZTFhYjlhMTU3MjVjYWI0NWRkODA5ODkyNmJkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImY3YkFJMjdEeVZlTGlkN3p4OUpaNGc9PSIsInZhbHVlIjoiNUF0MCtCUWpaR20rdUg2TnI5WWhNVUk4NFB3eHRIdlV6dDB5RGg4UjF4dlpackF1UTd6azZxUGFhYmZJcVZ3ZUxrRldPbDdxQ0ZPUkxhL3BMS2tJRW9Ga1hXWHMvdjJlNjNmVGlMaTR0MjZ6aGRPVFhmM1V4ZFk0SXpJRk8xdjVTUXJvT3l2Vk53SU9lWU5yWDdpNUNnZTBob0xWWEhxUW12MHhrTm95UVlraUY5QVNnRytZS2dxUWZMTzNPbndmWFh6VVdGaWxiNy9qVTFWd2RrWWRJbXBJQnVKY2tnTW9UTnpteit0UjJHaXRURmFkVWRyVjlKT1dJWVU1KzNUTFRGeG5rVmJ3YnovRHFzQTJOdmtYUktDWGxhMXhNY3YzcTNsdlY1V0JEVnh5RFpxZXQxRUVqREYza3RQOHo5KzUvV2lWVGdmUnM5WUpXZHJ5Q09TYnk1dy9IdTkwekU1RDVoSGJwUXo5L3hIeXlrb0E3T2w5c0FMUkVmb2RUblJDYldRQ2QrMHNSbDhLVjFjZE9aQ0NscjczQjloRVVCTVdOeXFxOG9TeklkaWRtTERuZjZPbmVlc3JlV0tsUkwvb0IrQUNFZ0FMUExCZnh1cis4ZFNYSHB5LzRHVjU1eVlrMDIvWUtDbldHN01EMkxsZFZudThpb0FhWmtaUER1eWQiLCJtYWMiOiIzMzk1OGVhOWI5ZTdhMDgzZGMxMTQ2MTQ0Y2Y0ZWFjY2M5MGYwZmM3ZjU2MzgzNGYxNDFhZTc4ZDgxYTgwNzliIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJUa1ZKMmtYNm1oNVZaZVR2UmlsNWc9PSIsInZhbHVlIjoiZE9mSmxyS09uT3ZLbDNqZ1p0dXg3c0hWTXpkWlBrVG92S3hjb3hrNjFtN1JKcFhpdEkvWUEyWHdwU204NGtIOTNQTkM4ams0ZkdSRUFOZDRQSEVLOG45d2tTNk1zU2ZNM0lRVDN6UVRML2kvR0pwVEdrTnkwR0FXam5IRFVGT0ljb3E3MnBMaE9oNTB3QTNxTnZtLzBuc2VXVm9uZFA2eFpVeFZFR3Bxa3B1ekIvMkhhUkdydE54V2t0V0htRUQ1V0hyVXlXR2hNVFZDam45b1FMb1B2Y1NiaUh2WXF3QXcrejBLeEtSTHAwU2tFbEEzb21HKzF6TEdZcG5CcXJRcS9XSElFV01na0ptRGo1U2p5ZkFxOS9QYUxxM2pQblZOUnk5MmhCOGNhaFhveXBuTlRqQkF2ODRCTUVYMnM0azRVV1ZqOFJURkJ0OUFGNWswaHVBVEJwMEI3K2hFaEIwOVZ5V3hNWUx6NGRBNisyc3lTVUZZelpMY2dRUVFBYWdRT0VvYkZIRGVMcmVweTJ3TVEvYmg1OEZaZTNqS1lxK2FZcUY5d1hIajVvVnQwcStvN0syVERKWnFselYvRnErdERVYlgrNy9CZ3F3MEVBWExOUFFqdzYwUkFhVkNweGQ2YzNTcGRKOENWYWQ4cThvWVBjWE9Ha0pFVHVkNHdOQXgiLCJtYWMiOiIwMWU0ZjljYTE1MGU2MzVhM2Y3NjNiZmIxZmJkOWYxY2MyNDM4ZTFhYjlhMTU3MjVjYWI0NWRkODA5ODkyNmJkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463332146\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-411838233 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411838233\", {\"maxDepth\":0})</script>\n"}}