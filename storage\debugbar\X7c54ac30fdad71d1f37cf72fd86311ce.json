{"__meta": {"id": "X7c54ac30fdad71d1f37cf72fd86311ce", "datetime": "2025-07-21 01:35:10", "utime": **********.508722, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061709.985557, "end": **********.508747, "duration": 0.5231900215148926, "duration_str": "523ms", "measures": [{"label": "Booting", "start": 1753061709.985557, "relative_start": 0, "end": **********.398034, "relative_end": **********.398034, "duration": 0.4124770164489746, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.398041, "relative_start": 0.41248393058776855, "end": **********.50875, "relative_end": 2.86102294921875e-06, "duration": 0.11070895195007324, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46008480, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028220000000000002, "accumulated_duration_str": "28.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.425652, "duration": 0.027190000000000002, "duration_str": "27.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.35}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4920971, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.35, "width_percent": 1.807}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.498606, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.157, "width_percent": 1.843}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=&customer_id=&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1699314684 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1699314684\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1194635059 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1194635059\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1323072389 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323072389\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-311735883 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=&amp;creator_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061702196%7C17%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImN3SURpRC9TZ1Nsa1lXUU9rdllDVVE9PSIsInZhbHVlIjoibXRueUw1eWlubGdiQ2NoMjFKRWFtSFlGaW1YWjRDNGlkMnR3VS9YMGliY1RKcEFpMDl1OXpOU2E1dEVCUzlrMDJjaExyZ1luYnZRaktNcUdqd2FpajBYSitQajVnNG05c0RxTWkvZTlKckd6b0liTWRKVFU2NlJ4OGk3REFTTnN5R0F1enRldlRUVWQ3YkZ1TmxCUjRvYmhzZnhydy9lOUFpSHRwNU1JM1FreDVZdG5yNVUwT082eUVvRm5MWkhQajEwYXliQXN6NEx6N3QwUDcvUG50elpib3kwWHFNNWlLWktQRDZ5TmhLV3BWbnoySk5UeW9qYjBHUWVDMkZ2SWtwajNYVU0xem43cy9qZW1oTm9IdXJ2UDJiWko5Yld6RDIvMG1sS3BZUG9rd3RaNnJwRjNzN3VJWFdxV0ZXM1V1cjh5ZXN1Wnd0ZmZ4S0JRZlN1WndXdzRCaElqdmdYZHVaOHhieDQ1OXJZTWpVeFJIWjhjTVFsRjB3WXA3RExhK0c3TFpqZGVaU3h3RkRFeU14RkR3dXEyZG1OTjVmZmtVWnJkMm84UUh6VkY5b0t6dDhQa1loUzFWU242ZTNTdFFIQnBTd2dzUFliWG0vVnBXWmNEcXBrQ09RRTFIQnJsUFRSVWJVZTlGTmVtQUwvVzNGamxLajI2VlgrVWV3d04iLCJtYWMiOiIxMTVlNWNhZjI5ZDc4MTU0ZGExZTg0MzYzZWQxZmQ3NGY3N2Y4ZGQ4YjVjM2E4NzVlMjQ3MjgxZmFkNjU2NTEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im02cmZ1UlUzakZhYlFIcUhhblNQWHc9PSIsInZhbHVlIjoiRSt5OUZRV1hGZGVPOVpEZnVtd3J1dGszZ1VycjMyNUhUR1VXZ2xpL2hobHNMVEJmYmxKM0x1NU9iTkl4VG05alBjZHI2ME5rL3JuaXNnTC9LblA1bUkzK1A4eGN1SitNa2lBTHZEUzNZMXhwR3FHWjBtY045SGFIelUwY1BFeGw3OUZzaDNVNnVjN2VIMnRlYWpzRXluQjRWMDI4TEdNdktiV2hqR054NXA4VW4zZVNGcHQvQy8xSjdJb2ppYmVqT2RKTUlGTGpnT0hvY0pWZEN5MDdXUFZtTlg3cHluSE9BY2FLeUUzVTlQcWVLN0R2cjlWRmtzcjA4QUxoTGkyNzRPa2RvbEJuMHloWkpBTjF5dTlZa0RYWGQxQVZZWmRnYmIxN21mYzhKTm0zcnI0RnJBbFJGcmJQNnA2VlNud1JXN2tIdHJ2UUk3T3Vwc1V2YzBPMVRZSXFKa3BnUTZyUEw5NmdNb3JWQ0VpdG8raks1UmFhcmtObDZyd24yd1VDU0xhb3FIREh1aFdZaFIvWlVVVFdPMGpaM0xWcWYzZjI1VXRJbndUY0p5NWZOSGZWS3NiTEFGLzBaSDEwUDlyNWdYUVlqSGVIYktEcGt6cGdHVDJFS1E2MDI0RDRBRXRUaitMcElFNTBSRnBIVUNZS3FMRzlWUHk3ZFlhM3A4L3oiLCJtYWMiOiJkMmQ1OTg0ZmUyM2ZlYmZjNzQ0ZmVjMDdiODU0ODQ5YzViYTQ4N2FmNDA3ZWQ1MGNmZDYxMGVlY2JkODQzYThiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311735883\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1557089445 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557089445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-740802641 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:35:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjQ0L2l6cVp4UlFTWllVQlNXOVZrcEE9PSIsInZhbHVlIjoiZWxJcnJxamNkSnhjVjJmZTBCd01KR3RnU0ZxcEd5Q3ZRRHNDZ0FzSjNuSHhuYjR0L2lrSU9ia1FHcGJlVE1sYnB1VkEyZ3R3K2JGSzVqZ25SU2JBRmU3cUc4emoxMjN5bTRHUkZyWEo2Z0tlUW9yU3ZsYVlrT1BhZlVhRENsK0t3dVlrTEV4WWY0dU1OMTZaZTBwMlhWQ25vMkQxOU4vRnJvRjgweW9oaTRyMHhkUEVEZld3R0gvSHM0QzQwZjE2TzVvWXhlRlBKUG5ZODNqZHZ3Q0IxR0Mwc29KK1g0UWd3cFBhNURnQjhsdGZHVnhJSG1UMzRnT1hkZmdIT1RTdllEc1ZZaXRIMXZQUHpHUVl4bHROVWc3eGRUckxWenVXazJYMktDSmNTY2oybkRIeTE0K04yNjZ2QmtXYjJmK2ZSUkJTSTFvLzJaUkJYeWQyODczeWxuTlIyTWpTa2YxNjZFamR0QldGVloxaHZrb1ZKZkNLZUVRb0tKZ2pmNGprR2Z6L2pkZ1UyR0FhSCt5OGtZWVQyWEVGRXViK1lGSDVkODNWR08xYWcycElKZWUrTml2aDZyKytKc2VuKzIvb2RjM2VVZkxYL2Z1TGluYXJxSURoeis4eUUrUVhybTQ4MGxmQVBETU5zV0U5M0RablhJMnVGdExCSUNMZkpod3YiLCJtYWMiOiJjYmRjNWZiMWJhOGI3N2QyOWI3Mzc0YTNjZjBlNzU3OTJlNGYwNzQ0ZDVjNzgyMmMxMjgxNGQ0ZmRjYWY2MDRhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:35:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9QT0ZNbmRDWHU3cnJPdkJnQ3o4MWc9PSIsInZhbHVlIjoiTnFmdzJmS05CSWo1Vk45aHAyT25wOGRhVmJmSUphVGlLdXNWL2NFVGZWNjZIQUlJNW9Sbmt6NGxzWlRhTVpUWUFGd1hVTHJaYVcycUEzWEtNamFGbjVEZm1vZWQyVW83RHkybG00T1BlMTRjYkswcDBGbkluV3ZpNTZvNjRpbWdFa2xMZlVubFZMWndORk1iMDZNY1Uxc2JYV0s2NmM1dlBtZWxTZzdZNjMrSlpOdHk0RHNGMGRnL091TlJ5YU04SWtMMHJLSVd3QS9wTlNXMUhlUUpEb1hEMS9nbHhHUm9uZG1hM2tRWFNSaXgwWHQ3Q2k3bzhqLzlBZUJWQzIzQUwvSGVSL2djckt1akpicUx1RTBFNncwdVVDWncyY0tMa2pUbUdrK0JYTTV2ekJtOGRZVjNXRWdDY2pVWHFGRWhNZmZQT25xcUVoYXJTYVYrWjVhK2xXbFFOVzlhRHFoeVlYWjhYeVFVS0ZqdjNSb3VDc0Y0dWJGakF5dGhqT1ZSWTh0SUd3UGJrOW14R1YrVWsvbnM1WWJJLys0Y05iZkplS1YwaktrQnRlMmxQc0VLNUZoWnN1dVZFV01OV2JnaVNtUm5sRjBPREtST3krSGo1QWxrUi9KR0ZIS0xySjhNcUl5eEdWdjF2emZLTCtLOWNSR3BuVDUwOFVSSDRVMysiLCJtYWMiOiI2ODExZjQ0YzU2YTBlNDNhMjIyZGFlMGE2YjY4NTA3ZTdkMTZjYmI5Y2YyZDYxNjAxOTBhN2NhODIwNjY4Nzc4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:35:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjQ0L2l6cVp4UlFTWllVQlNXOVZrcEE9PSIsInZhbHVlIjoiZWxJcnJxamNkSnhjVjJmZTBCd01KR3RnU0ZxcEd5Q3ZRRHNDZ0FzSjNuSHhuYjR0L2lrSU9ia1FHcGJlVE1sYnB1VkEyZ3R3K2JGSzVqZ25SU2JBRmU3cUc4emoxMjN5bTRHUkZyWEo2Z0tlUW9yU3ZsYVlrT1BhZlVhRENsK0t3dVlrTEV4WWY0dU1OMTZaZTBwMlhWQ25vMkQxOU4vRnJvRjgweW9oaTRyMHhkUEVEZld3R0gvSHM0QzQwZjE2TzVvWXhlRlBKUG5ZODNqZHZ3Q0IxR0Mwc29KK1g0UWd3cFBhNURnQjhsdGZHVnhJSG1UMzRnT1hkZmdIT1RTdllEc1ZZaXRIMXZQUHpHUVl4bHROVWc3eGRUckxWenVXazJYMktDSmNTY2oybkRIeTE0K04yNjZ2QmtXYjJmK2ZSUkJTSTFvLzJaUkJYeWQyODczeWxuTlIyTWpTa2YxNjZFamR0QldGVloxaHZrb1ZKZkNLZUVRb0tKZ2pmNGprR2Z6L2pkZ1UyR0FhSCt5OGtZWVQyWEVGRXViK1lGSDVkODNWR08xYWcycElKZWUrTml2aDZyKytKc2VuKzIvb2RjM2VVZkxYL2Z1TGluYXJxSURoeis4eUUrUVhybTQ4MGxmQVBETU5zV0U5M0RablhJMnVGdExCSUNMZkpod3YiLCJtYWMiOiJjYmRjNWZiMWJhOGI3N2QyOWI3Mzc0YTNjZjBlNzU3OTJlNGYwNzQ0ZDVjNzgyMmMxMjgxNGQ0ZmRjYWY2MDRhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:35:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9QT0ZNbmRDWHU3cnJPdkJnQ3o4MWc9PSIsInZhbHVlIjoiTnFmdzJmS05CSWo1Vk45aHAyT25wOGRhVmJmSUphVGlLdXNWL2NFVGZWNjZIQUlJNW9Sbmt6NGxzWlRhTVpUWUFGd1hVTHJaYVcycUEzWEtNamFGbjVEZm1vZWQyVW83RHkybG00T1BlMTRjYkswcDBGbkluV3ZpNTZvNjRpbWdFa2xMZlVubFZMWndORk1iMDZNY1Uxc2JYV0s2NmM1dlBtZWxTZzdZNjMrSlpOdHk0RHNGMGRnL091TlJ5YU04SWtMMHJLSVd3QS9wTlNXMUhlUUpEb1hEMS9nbHhHUm9uZG1hM2tRWFNSaXgwWHQ3Q2k3bzhqLzlBZUJWQzIzQUwvSGVSL2djckt1akpicUx1RTBFNncwdVVDWncyY0tMa2pUbUdrK0JYTTV2ekJtOGRZVjNXRWdDY2pVWHFGRWhNZmZQT25xcUVoYXJTYVYrWjVhK2xXbFFOVzlhRHFoeVlYWjhYeVFVS0ZqdjNSb3VDc0Y0dWJGakF5dGhqT1ZSWTh0SUd3UGJrOW14R1YrVWsvbnM1WWJJLys0Y05iZkplS1YwaktrQnRlMmxQc0VLNUZoWnN1dVZFV01OV2JnaVNtUm5sRjBPREtST3krSGo1QWxrUi9KR0ZIS0xySjhNcUl5eEdWdjF2emZLTCtLOWNSR3BuVDUwOFVSSDRVMysiLCJtYWMiOiI2ODExZjQ0YzU2YTBlNDNhMjIyZGFlMGE2YjY4NTA3ZTdkMTZjYmI5Y2YyZDYxNjAxOTBhN2NhODIwNjY4Nzc4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:35:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740802641\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-585129863 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"148 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=&amp;customer_id=&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585129863\", {\"maxDepth\":0})</script>\n"}}