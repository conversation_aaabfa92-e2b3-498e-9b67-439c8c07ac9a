{"__meta": {"id": "Xc8e6486d3d5d73897aaae98028149605", "datetime": "2025-07-14 14:24:47", "utime": **********.257799, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752503086.747041, "end": **********.257813, "duration": 0.5107719898223877, "duration_str": "511ms", "measures": [{"label": "Booting", "start": 1752503086.747041, "relative_start": 0, "end": **********.189945, "relative_end": **********.189945, "duration": 0.442903995513916, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.189957, "relative_start": 0.4429159164428711, "end": **********.257815, "relative_end": 1.9073486328125e-06, "duration": 0.06785798072814941, "duration_str": "67.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45990928, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00383, "accumulated_duration_str": "3.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.228249, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.752}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2425442, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.752, "width_percent": 16.971}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.24959, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.723, "width_percent": 18.277}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/journal-entry/1/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-100577848 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-100577848\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1225072239 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1225072239\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1908310679 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908310679\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-758609568 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503085126%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imwra1EvbkhJRGVEU0YzbitQSmVoNUE9PSIsInZhbHVlIjoiekNlbEV2c3ZMeWM5ZkNjMFViWlIrTW5UM3dDZ3dYV242RjFFdXJaZ3hnQmRmdjdEeGd3VVp1TTQ2RWd0VjJCdVo3a0w5bFQxNEllaFhuK2lmYlpuTDd5MGxkbERZd3lONmxid1hZcWlIM1QrVWZTNXZMOC9nVHBsbjFvcjlYY2lvZkx4b3BtaUplWm9mbjM2MjhsVWdsbHlrZGxENnI1Q3FyNEZDMmNNdEhHS2JUVzluMnJnQ1pyNDNkT0JmeVVFdmFadlFEZmlIN3lObFM1cjdqZ1pqMHc4Q2ZZU1Nna3l3bE8xdXFXS05KYXQybWJGNVV1cTRkR0hoRkFVNjltNVFXUW1DekovL09scHl5RGNGRkQ2cDNKZENSV1RDY01wOWpkOFJKWm44UTBmSk84SjVEY1BaMUVBSWdmeW4yUVBpcEhRRnZEdkgycm5SMUQxU0F0M1dwemtMSjhyQ1NLZUxWZE03bnA0dmJTbUkrajNabWlxTzNUMDVXc2xWTHpnNXkrcE5zdXc5QjBxdHlVVjFsVCtZWUlKSmZlMmxNTWpjMUg3bTVWNmFRSWNjOVM2MFRSQlJtMElDaW9WZ2ZTNWJETnZZSWIwU3hjWlVpZHdsREtPL2kwaFdCZUt5alRMZzgyWnVEVUhMdEZBeDRrR1RCUjlWYStpVkRYditUV00iLCJtYWMiOiJlMjc4OWNhNmM3ZWM2ZjQ5MDNjY2MwY2M3NDcxNTdmYmRkYTg5NzQwNzY4NzgxMDAxY2MyYzJiMmMxMjVmOGI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InI3MDE5M3BraitUejdQT2dKMmY1ZUE9PSIsInZhbHVlIjoiRWJMbkxMU1dFeXpYYU9hNGY4RThOYUp6czE4RFlyaWgyanR2d2ZodDkxbUZJeDVFSTdLVFlVVFN1Y2tvMHBJSkhBQ1RCbUlDbFJramdoci9oK2g0RmxCZWVsZTNBWUNnb3FLL1NFcjM5YXU0WXJZT3hsekFyaG1MSi9QTmc2S1QzZ0Mwa3dITGxuUTM0ZTlkQ0UwT24yS25lbDQ2bmsvM2ttcy85VTlqMmRvbTBqd0MwcjZXajVzM3pWb3NrU29xamVHYXY1eXFNbFVLR1BvT3VFbkV3Y1cvRkVPcTd1VTF2ci84aUFWK0E5UEFCcDJGUVVBZnh5WVV4Rm9uUkExY0xySTdjZERDcjdXRUdtWHNXZWFBczV0cHI5dms2bGZBZkRiK0VZdUNRdnVRNGtiQlhrdmFaRDk3Z1VBd1dsWGlSUlZnZngrczYybncyUUgwTmxxNmRsbEZ4MW1PcGpaKzdGSDU2YVlQRURSRmZOZlRFU1hTUGtJSDEvY1NlTWhST0JwSW9PZnNuZkU0V3hRMU1Cd213SkdMTkdReDhZTXBlU1NLMXFIZ21qWGUrZDhaem1zblhIRHZnekhBMk42cC9NVVhVV3RWSngyNnB1dnZ1b1JXc280d2g0Y1JuNWM2RVhGMm9zVE45YzBEcW9aU0ZLYzZhSi9XVXBCMGpOK08iLCJtYWMiOiI2MmIyNDZiMjQzNmFlZWRkM2UyOWIzYmI4MmUzMGE4MTRiZWZiMTU4MDNhNDg2NjY1ODFmNzdjYjA1NWRkMjM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-758609568\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1487417177 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487417177\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1516361569 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:24:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBSSHBTSEM4TWhDaENjcS82YjR3U3c9PSIsInZhbHVlIjoiWERPeHBhTk9yU0NGdnd4NVQvejR4N3R2Y2NXbzgzNThmYU42MDNYK281UkRUbUZQcVViOFRFbThsUVpOWmlNT0tvNzYxczhuVk9EazdCZjhJYTA0OVdPNHBvNEpma0RhVVVyMFNtRWhobEpBSm5KaHE0S1plaE5kRmNzdlN3TkdDRURHeXRZcE5RSE5qdmVVMnErN3BLNmlCald6ZkJzZ2UxOTVCSzl5OWdhc1NIV1pXVzBKeXEreDB3QnRDazZBZUIrZ1RBc2I5d2I5MlQ1bWkyN0dmaDlEQ0tIVzM4bjlKUE0rdDd6VDdRblVWdElRUUNXT0ZqcnRyTXkxaDU5UnhNSzJRbXJGZmdZNXBjMnNOZ2dLTS8rQjU4L0pXalVTWXNIRzBKeTRxc3F3WlNtT3g2ckJ6OFgycGs5Y2pndllWK1o5SDNDb3FMOGllMjFoTDhETkI5VkRXUmN0ZEZ0SmR5WEcvR25jeFo4b1VJYlNSREpNYzg4K0xEbXFkWTZPVEV5WjJWSWpYcXphelpNUGVBUTZyaTVGa0s2Q1lLdkl1THMvVlV2NGw4WnY4OUg0aWdnR0ZpdjVhalZrbzljWFl5RGw3Rm9nWVZaN2RHcWlNRU1wMkdjdHRLaHpZSUZwa3g5eGJVblQ1U3o3WGh2OVZTM3BKWU5KZDJ3d0FLRlYiLCJtYWMiOiJlYTk4YjM4ZTMwMDI2OGViNmI3Njk1ZGE3ZTM5ODU2MTcyZDFjNDI5MTU0MWI3MDA0MTAwN2U5YWFkMDZjZjY2IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:24:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJjamxsS1EyN05sV3hrNGhnVklqa0E9PSIsInZhbHVlIjoiTWRSNkZ5VWFaZzQvTDlEYTg4bXVacy8xbk11emRtR0NsajdsUll0VUw4S01UazQ5VHVlRFFEdkYyZExtL1BoVmtvTWtNajZuT01JbStNMmJTTDhUMjd1UjBQVEJSbW5oUi94YkF5dmlSb0I2YURiNVpWWUJkN3RNRWI0aFZXSHcyRnpseGlvQ2ZyUmxDYWNqYW0xYVN6M3VOSzJOWlpFa2FKM3JFN0VIeTd3M2xOWnZWNXpEWGhjeVZkcGRKL2cveDFJNlU0dlBQVlJHdHV1aTZlUmNGZlZPVTB6ZlZwYUhXYU8xcG9jZnBaZVNtMGh1MWRhbXJqck1FeVRMQzd0ZDByMkp0MGNuaHdPclRhL0lqYksrcmFGTkhXbzhscVB5RkIzMW9PS2toYVpZbG5FS0ZkQUFuVUZiT3p6aXB4NVVoODFoNTZjdkZ1ZWtxNzV1QTJXWXAyMk13dUI3MzI1RGVLdWpqR1ZqZ1NscFZpZzJscExzWUw5U1lUaXlJUHFhM2ZCTzRFa1A4NzlVV3hjcXMvTzRMU0lOOWlEbXU4dTlDamoyRkp0RlpJMlF0amRNZEl3YkpmdFUxUTlMbDJCKzVTdmRNVFJVUE9nVzRZbENINit6WEo1QTQyay9adTEzWHl5bHdLWmVyNC9OTzdkZzNyZjY5SlJnZ2luTmI0dzYiLCJtYWMiOiI5MTE0MThmMzllZDNjYjllNGExZGVjNDJiOThhMGMxYjgyYjUzMjJhYjA5NmY4MzQyYTk4ZjE2NjU1YWZmZTUzIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:24:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBSSHBTSEM4TWhDaENjcS82YjR3U3c9PSIsInZhbHVlIjoiWERPeHBhTk9yU0NGdnd4NVQvejR4N3R2Y2NXbzgzNThmYU42MDNYK281UkRUbUZQcVViOFRFbThsUVpOWmlNT0tvNzYxczhuVk9EazdCZjhJYTA0OVdPNHBvNEpma0RhVVVyMFNtRWhobEpBSm5KaHE0S1plaE5kRmNzdlN3TkdDRURHeXRZcE5RSE5qdmVVMnErN3BLNmlCald6ZkJzZ2UxOTVCSzl5OWdhc1NIV1pXVzBKeXEreDB3QnRDazZBZUIrZ1RBc2I5d2I5MlQ1bWkyN0dmaDlEQ0tIVzM4bjlKUE0rdDd6VDdRblVWdElRUUNXT0ZqcnRyTXkxaDU5UnhNSzJRbXJGZmdZNXBjMnNOZ2dLTS8rQjU4L0pXalVTWXNIRzBKeTRxc3F3WlNtT3g2ckJ6OFgycGs5Y2pndllWK1o5SDNDb3FMOGllMjFoTDhETkI5VkRXUmN0ZEZ0SmR5WEcvR25jeFo4b1VJYlNSREpNYzg4K0xEbXFkWTZPVEV5WjJWSWpYcXphelpNUGVBUTZyaTVGa0s2Q1lLdkl1THMvVlV2NGw4WnY4OUg0aWdnR0ZpdjVhalZrbzljWFl5RGw3Rm9nWVZaN2RHcWlNRU1wMkdjdHRLaHpZSUZwa3g5eGJVblQ1U3o3WGh2OVZTM3BKWU5KZDJ3d0FLRlYiLCJtYWMiOiJlYTk4YjM4ZTMwMDI2OGViNmI3Njk1ZGE3ZTM5ODU2MTcyZDFjNDI5MTU0MWI3MDA0MTAwN2U5YWFkMDZjZjY2IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:24:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJjamxsS1EyN05sV3hrNGhnVklqa0E9PSIsInZhbHVlIjoiTWRSNkZ5VWFaZzQvTDlEYTg4bXVacy8xbk11emRtR0NsajdsUll0VUw4S01UazQ5VHVlRFFEdkYyZExtL1BoVmtvTWtNajZuT01JbStNMmJTTDhUMjd1UjBQVEJSbW5oUi94YkF5dmlSb0I2YURiNVpWWUJkN3RNRWI0aFZXSHcyRnpseGlvQ2ZyUmxDYWNqYW0xYVN6M3VOSzJOWlpFa2FKM3JFN0VIeTd3M2xOWnZWNXpEWGhjeVZkcGRKL2cveDFJNlU0dlBQVlJHdHV1aTZlUmNGZlZPVTB6ZlZwYUhXYU8xcG9jZnBaZVNtMGh1MWRhbXJqck1FeVRMQzd0ZDByMkp0MGNuaHdPclRhL0lqYksrcmFGTkhXbzhscVB5RkIzMW9PS2toYVpZbG5FS0ZkQUFuVUZiT3p6aXB4NVVoODFoNTZjdkZ1ZWtxNzV1QTJXWXAyMk13dUI3MzI1RGVLdWpqR1ZqZ1NscFZpZzJscExzWUw5U1lUaXlJUHFhM2ZCTzRFa1A4NzlVV3hjcXMvTzRMU0lOOWlEbXU4dTlDamoyRkp0RlpJMlF0amRNZEl3YkpmdFUxUTlMbDJCKzVTdmRNVFJVUE9nVzRZbENINit6WEo1QTQyay9adTEzWHl5bHdLWmVyNC9OTzdkZzNyZjY5SlJnZ2luTmI0dzYiLCJtYWMiOiI5MTE0MThmMzllZDNjYjllNGExZGVjNDJiOThhMGMxYjgyYjUzMjJhYjA5NmY4MzQyYTk4ZjE2NjU1YWZmZTUzIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:24:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516361569\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-673828525 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673828525\", {\"maxDepth\":0})</script>\n"}}