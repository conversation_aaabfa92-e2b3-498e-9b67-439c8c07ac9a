{"__meta": {"id": "Xedaf9050153e496c1de94c8a9e002302", "datetime": "2025-07-21 01:19:37", "utime": **********.147356, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060776.652925, "end": **********.147373, "duration": 0.4944479465484619, "duration_str": "494ms", "measures": [{"label": "Booting", "start": 1753060776.652925, "relative_start": 0, "end": 1753060776.997251, "relative_end": 1753060776.997251, "duration": 0.3443260192871094, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753060776.997259, "relative_start": 0.3443338871002197, "end": **********.147376, "relative_end": 3.0994415283203125e-06, "duration": 0.1501171588897705, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44726592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00217, "accumulated_duration_str": "2.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.026315, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.793}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.03168, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 84.793, "width_percent": 15.207}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1826453996 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1826453996\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2056659482 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753060775053%7C4%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZNM0RGYnhwUUI5Y2xUTElkL0UybkE9PSIsInZhbHVlIjoibXFpcFF0bTZqYW9WdnBJNjJNeEpRRi9HUnRtQWJFekl0R0QwRkxmbU5iNlIwSnlQSFdST25kM25XdVJqN1VrN0ZJQmV5WUNJc2Z0bW0yd2p0UGhrT0duaDNGZjZsbTMwVHhCcU5vWjdCMnJiYWZFTnVmdTI3ZDhoa1J4bGsyai9xMWpQN09jZGh4bmR4dkFVSUdZL3J1VjBDenhBVkpmWmk4SFZMcVFnZnZ1RUFhQS90dWt4UTFOSlcrVzA3bitYclNXb2RSTGhiaFNpa2ZOREhDekk3Y2Nzakd6OW9McTJwMlg2MnhPTWVsNHZyeGVKZzB5MVRqeEZWSUNiVWVCMHBhM1ByVkYvbnJFaEkwakpwcGVXOXMzSjlZTHJrUUZqczZKdnQ3Rk4ycjk0dld4eEp1c08vR3h6OGxDSks0R0JkZXVKdWkrU2laR2cxUW1PNHFQVkprYWg4NThDNkkzVHZYelNvSTZVOU42YmxwQ0pncFo3RWFja0svdlNsandWeVFBekovVkJ2czkxVGJBeGNCOVNEcWVuMmhzbUhhRFF1a3Z2MUY1V0lVY2FrcVdCeXVQU3hETTBtRFVUZnppS2JUOEh3cnN6cWJJMkRRdGpDUG1iUzBpT3ZSNTVQTzFZTHRNOWNtcUxQYVI1dlRaSmsyZnlTWWUwTGh2dWxZTTIiLCJtYWMiOiJiMTgwNGY4MTFjMTNiMmNhODE0OWY0YWVjMTJlOGI0MTUwZmQwMDI0NjhhZjUyZWJlMDg0Zjk4YTU4Mzk2NGZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImsyRHp2OUxZVERwMmlBU0JYTE9uOXc9PSIsInZhbHVlIjoibmFpYlNnYXJJUkdJdG5QbzA4Q2ZDY1VaNXkyclBNSXpzajZUamVRL0lZSU5Kdk0zZ2tlQTRQWXNWLzlsb2xqRjB5NHMvUEpLalZpQVZYYVEwN2grR09OZXpVUDNoWmw5TEd1eXRsV0JxNlNyYVF5S0hRYU9YUmtEcGdweXNBUU5aNENLV2tkdDF5L0NMOE1KRVQrdGRlcWdJVHpJY2xPRWNMaU5jRktURXBDSzBidG5pcCtRdUhpTWFkZXhKYmJxSEtXSlMwMHZJczhjU1dKa2pGU2lMWWZzQ1BYS0w0UG4xWVZzemFJOUVWWVVuaENXR2xtc1k3UGpING4zdVFwTjBZUnBoQUE5SG40N0d6NkU3U1J1Wnp6VVo2dmlYWXdBdk5pQnF1d3QyS3VhM2JjdVZySHVmL1hjOW5sa1B2d09yYnB1Z2tqY1RsZnYyd0FGTjVTcjFrRUxoMjRkMUxranNnR2JqVHhDaGlVUXR0bHF3U2JlZjd1SWVRRjhCM1ZWVWdUSTRaOGNIUFdxMnZRK0JzaFlYdTNHL1ZiUWgzSXNJbHdqWnNUYlNCcVVHMXpRMWF2b3BZZy9MVkU4ejFFM0VXcm11ay8rMXROcm1ERWpFejFuNmRoWlAyT2dZY2F3MjhUMTZGUEswRXB6RG5VcWV0UTFBSG5GaWFVVExGQUwiLCJtYWMiOiI1MjZiYzVmODIzNWExYTJmZjVmM2Y2YThkMmY5OTAwN2Q0ZmQzMDk3NGQ0YmQ2YWJhMDBlNzI0MWRhNjdmOTE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056659482\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-512324402 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512324402\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:19:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBIVHBlTmtsQWVPODhnN1kveUZ0ZHc9PSIsInZhbHVlIjoielkyd2NzSkYxZW1JOCtRbElRbE9ZZ0VRVXVab1hxWkF3M3htTm1ncEpiak5mNEFaMk9oVEo0ZEIyNlhGck5HLzRoMU1IUm56bnprb1VlWUg4SEpDRDhLRzZGbGwyQUlGNzNhRHBtUjVCVzI3cjh4R3p1NWFjYXU5SDNzQ1pyRi9lRXN0UjkzL3dsRWpLK3ZDTGhuczByT1FQTGQ2WGZ1TnhCVDFjTy9BRDdteTBhYVVhdXdrR3VNMFkvK25WUlROSkw1dVgrWVd4UkxYYkRvS1U0TSswOFpuTkdkZlJJZG1qenMwOXVGem5sazc5QTdNVk1Oa0laZnVoWkFqYSszWmd5bXJiSGJlbHFvRlVxamx3RXdscmRJV05LY09YYTh2QmxDNU5HUVFJSit1NzI4Q2FlZFE0Qk1WL0NGYlozaDd1Vk9NTkRDdStBZStYSFlUTXN2cTNYV3VUcS9zSk9Iai9PeGpaaU1Tc3lla2JZZ3UrNXJRdEYxOGhzbW5TbXp2V2NybnNZaFB2dG91OThRN1AzdFN6WXZZLzBIZnBiUWlQM3JVT2NsOXJ4RFJKemxBcEZUZlIvNDcyTnl3RURRQmhMaHFqQzdMNm4xaHlEZzlhMzRrRXRMU1Q2bWFVODgxRmpZR1JLaFYwaWg0MlhSVGk5MWVHdHRPUXZJdEJsY2YiLCJtYWMiOiIyNDAwMmM0ZTYzNGM1ZTU0NGZkNzk3NTY4YjMyNDE3ZjFjY2I1NWI2ODBiMTY2MGE4YWY2ZWMwMDc5NzM0ZmU0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNmY08xOHlyQlRlS3pUYXFWNWdZb1E9PSIsInZhbHVlIjoiNzBxWGNteXhBUU1TMDU4bDZuSHNsc3pFZXo4L0lSZWpLVG91M3pmYXE3NXR0MnF0Wk5oa2tBWkU1UlZiRXdSK1c0bi9xVlkxVzFQNmt2Um1nbHRSYzVHNkRqS0J5NUFmQnkreEFxcXZxeDNEWVJldTlFNFhueUx1QlRRUnd6SjRlL3NoU25qV0RxWll4TXBYSFpObGZwc1BiejVYdzB6a1FrQnIrV3ZEdzhZVm5lRURKR09VTDA0KzVxek04SEplVEVQZUEwSEp2VlI1L1gxbGxmb1IvSGoyYkFrTVIxVEFrRHc3RkYvZ1pFb1lHb1F6WlpRWERYY1BXdjJTbnVpcGwzYytidjlFcm0raTdKKytLS2Nlb1F4OGJzWDlPK1p4Z0x1UG8xZlhtdWxXdE4zem1Vb1NkdllmS0hkSlJpVzhuVmJScEdFbGFqWU8xWXk3d2Z1UkwwdHZDMVNicU1oRWJmZG1xcWo0RzNKUW1yZ2p6VU1mcHZiWW9mQktMUFA3d1dQMGd2ZnJWRGVIeC9RTU8vajdtQ2hvU1NwVlVjSXRvMm5jV1I5dXBhcG4zdFRuVUd1VEFtZWF3VnNmeUJpZ3ZNSGJKcUpWSHBaSlIreFp3cldFV3A3MTJ6NjlOTGFMNktuTnVwRE1qaW5tdjk1dWcxb1JuMzdBeGJ6VjgzQzkiLCJtYWMiOiJmZjgzYjdkMzg5OGU1ZTgzMjMzODhmODgyYzM1ZGEzM2EyM2QzOGE5ODFiZDY0ODdjY2Y2M2QzYjczNGQyMmQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBIVHBlTmtsQWVPODhnN1kveUZ0ZHc9PSIsInZhbHVlIjoielkyd2NzSkYxZW1JOCtRbElRbE9ZZ0VRVXVab1hxWkF3M3htTm1ncEpiak5mNEFaMk9oVEo0ZEIyNlhGck5HLzRoMU1IUm56bnprb1VlWUg4SEpDRDhLRzZGbGwyQUlGNzNhRHBtUjVCVzI3cjh4R3p1NWFjYXU5SDNzQ1pyRi9lRXN0UjkzL3dsRWpLK3ZDTGhuczByT1FQTGQ2WGZ1TnhCVDFjTy9BRDdteTBhYVVhdXdrR3VNMFkvK25WUlROSkw1dVgrWVd4UkxYYkRvS1U0TSswOFpuTkdkZlJJZG1qenMwOXVGem5sazc5QTdNVk1Oa0laZnVoWkFqYSszWmd5bXJiSGJlbHFvRlVxamx3RXdscmRJV05LY09YYTh2QmxDNU5HUVFJSit1NzI4Q2FlZFE0Qk1WL0NGYlozaDd1Vk9NTkRDdStBZStYSFlUTXN2cTNYV3VUcS9zSk9Iai9PeGpaaU1Tc3lla2JZZ3UrNXJRdEYxOGhzbW5TbXp2V2NybnNZaFB2dG91OThRN1AzdFN6WXZZLzBIZnBiUWlQM3JVT2NsOXJ4RFJKemxBcEZUZlIvNDcyTnl3RURRQmhMaHFqQzdMNm4xaHlEZzlhMzRrRXRMU1Q2bWFVODgxRmpZR1JLaFYwaWg0MlhSVGk5MWVHdHRPUXZJdEJsY2YiLCJtYWMiOiIyNDAwMmM0ZTYzNGM1ZTU0NGZkNzk3NTY4YjMyNDE3ZjFjY2I1NWI2ODBiMTY2MGE4YWY2ZWMwMDc5NzM0ZmU0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNmY08xOHlyQlRlS3pUYXFWNWdZb1E9PSIsInZhbHVlIjoiNzBxWGNteXhBUU1TMDU4bDZuSHNsc3pFZXo4L0lSZWpLVG91M3pmYXE3NXR0MnF0Wk5oa2tBWkU1UlZiRXdSK1c0bi9xVlkxVzFQNmt2Um1nbHRSYzVHNkRqS0J5NUFmQnkreEFxcXZxeDNEWVJldTlFNFhueUx1QlRRUnd6SjRlL3NoU25qV0RxWll4TXBYSFpObGZwc1BiejVYdzB6a1FrQnIrV3ZEdzhZVm5lRURKR09VTDA0KzVxek04SEplVEVQZUEwSEp2VlI1L1gxbGxmb1IvSGoyYkFrTVIxVEFrRHc3RkYvZ1pFb1lHb1F6WlpRWERYY1BXdjJTbnVpcGwzYytidjlFcm0raTdKKytLS2Nlb1F4OGJzWDlPK1p4Z0x1UG8xZlhtdWxXdE4zem1Vb1NkdllmS0hkSlJpVzhuVmJScEdFbGFqWU8xWXk3d2Z1UkwwdHZDMVNicU1oRWJmZG1xcWo0RzNKUW1yZ2p6VU1mcHZiWW9mQktMUFA3d1dQMGd2ZnJWRGVIeC9RTU8vajdtQ2hvU1NwVlVjSXRvMm5jV1I5dXBhcG4zdFRuVUd1VEFtZWF3VnNmeUJpZ3ZNSGJKcUpWSHBaSlIreFp3cldFV3A3MTJ6NjlOTGFMNktuTnVwRE1qaW5tdjk1dWcxb1JuMzdBeGJ6VjgzQzkiLCJtYWMiOiJmZjgzYjdkMzg5OGU1ZTgzMjMzODhmODgyYzM1ZGEzM2EyM2QzOGE5ODFiZDY0ODdjY2Y2M2QzYjczNGQyMmQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}