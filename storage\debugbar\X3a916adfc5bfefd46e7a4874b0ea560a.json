{"__meta": {"id": "X3a916adfc5bfefd46e7a4874b0ea560a", "datetime": "2025-07-14 18:07:14", "utime": **********.676796, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.156783, "end": **********.676809, "duration": 0.5200259685516357, "duration_str": "520ms", "measures": [{"label": "Booting", "start": **********.156783, "relative_start": 0, "end": **********.588325, "relative_end": **********.588325, "duration": 0.43154191970825195, "duration_str": "432ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.588338, "relative_start": 0.43155479431152344, "end": **********.676811, "relative_end": 1.9073486328125e-06, "duration": 0.08847308158874512, "duration_str": "88.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027510000000000003, "accumulated_duration_str": "27.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.624519, "duration": 0.026510000000000002, "duration_str": "26.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.365}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.662267, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.365, "width_percent": 1.854}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.669028, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.219, "width_percent": 1.781}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1298935407 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1298935407\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1800566157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1800566157\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111880463 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111880463\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2055128247 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752516430657%7C13%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNmbm05VG5IZVpzak0yRnd2NEgvb3c9PSIsInZhbHVlIjoiVmpIRHlrWm1mTEJPSktwamR6Y3JjU0NGWFhXMHhHWDhoMlVZd3hTVEIxVjJoQnFJNU1OQjVQekxtTzBWREFqd2gvL0tpZ3VoaEM0eURHRjcvdDlCUU5aZndXNlpENkM3d0h0aktoN1liMHRNZHJGWmhKVWtLQXJLTjBIRkw3YVhCaDY2MGZBTERqN2RHTXp2NnM0b0ttQzBDMEwzRk9TWVlBUFBkclU2UlJxMEIwWXZHanB5RFR4Z0ZIKysxM0hkR1YvV3Z6SjFFVEVoSWszV01sODNaZHd1ekZVTHhLdUZnWnlOVFhHbHVKVWtsNTcydzJDUlU0RU9tOWxGM0pyUGZ0RTgvTFhtTk1jMlVqM1hZRTV6ZEFGNzliVnpTMG5JL0J6Y3ByYTFZMGgwQjZSdUV0elJVSmFSUmxxdGxhRmJDY2xHUlBLbGUwcTZEemVObDAxTUFxd21jekVVNnVsNW1UM2tYWEppUFRNSm5oRmxtQXlJWkNHeGlSZjlTdXJ5TzlaQWNyU2hlWXdSUkVMeDdJMXRzaWFjazFpVitpUnRROXhBS3NqMjU5QnNINXk5YTA1em9ZOUx2b0cxYnpURndWVUYrT01KVk0wUWRpNGhLc1RqdXBRem0rb1VFNGd4b1Y3UnUzMDgyWHlJdm11VTBueXU2RXBrMDNiKzA1RTIiLCJtYWMiOiJhNGRhNmM0MDcwMzM1Yzk2YTdhNDdhMGU0YTMwZjNiOGM2MDJkYWFiOWNhYzFkYWQ2OWJjYTgzZmIxNTEyNjgxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFaanV1RnB2T0dNRXU0VnJqQXNKMnc9PSIsInZhbHVlIjoib2R2SGJhR1hIZXkwdjVNamREekRDaU13REEzazRoeFdsVmI5Z3lrQ0lxR0JDNThPZUVQV3doSkZKTVNyeGJiQ0lQbHI3dXFhWHJ2NFdEUUpWejRUOUNqUjFVWWhsS3dKRitqSHdETVRBaXBMUkpXaG9pL1VJWkNMM3BoV2JlYVhzaVk3dUdkT1pMbUY4ejZKeGtGWTE0Zlo1UldqTnVjUXVmYlZmVUNLc0wxRFhvQXdVOFZ4N3dHUS9FQ3Nyc09hL2F4aCs4OEcrTGhDSTltN3BSRlF1U1YvNzdMMXI1R2cwVEpmY2l1NGJyUzNKbkRVMFRvM214UFRzeHNhQ1FFT0dmc1YrVXRJTjgzWXdZQ24zR2F0ZDhJRFNzY2RuK0NDZ2xmSGNzNXY2S3FxOTNML3dLL25xSlZKQjRZR2FnUmM3bVlhUXl0cDZESG85VHM0OExERWkrcVBKbGtYajhCRkFEa2JONElKYm9NK2hpcVZWamVQVUljV2p1SW83eGh0R1E4YVRnQjl3ZEg1MllhL1lUUWVYTE1UMEtjV21IV0xRZjFpeTJMRjd5ME9BOHJjM2o5cnViT3FHazFUQzMrM0cvdXIrK0RqZjZsd2VFUnBtVHRGQjBMcktUSUlJZEw5eUthNkRadXp2OVE4Q0RZUmdMQXVpNytjUXFKTDczaEYiLCJtYWMiOiI3ZjE4NjRhZWU3NWVjYmM4MWMyNDcxZWYyNWExOGUwNzhjM2Q0NmJjYWUzZjUyNzg0MGU5ZGJkODJhMGU0NzY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055128247\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-730378024 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730378024\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1397910482 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:07:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdnU2ZjSVppOFJNZThFbDg5S3hONFE9PSIsInZhbHVlIjoia2VTSDRXQldCdWhsZHZpS09XSlAvVnp1YUNPOHN3YUgvYnA2QU9yNm9FRnJ2a2xTU0lpaEthdExEYUxPUFk3QWszNmtUWjdWZ215bTZHTTFLL2Z3MXMxODcySFdWQkpWMm1KeE5Qc1Z2K0loNlR2d3pxVUZLUVpOcitDNTB5Q2doWnFVWEppb2VLdGZad1BvOThLWUlWZ1pGQkRWZkZWMHBDSXE4azZQQlF4cnVkeE0vUDZ6bHQzbUo5SXg4amJ0SjRmZFJUS254TEZlNktRYkpTeXRzdFMxQ3hkZnBJOVkwdlp5RHZ2Z3VkMExvdWNmMlhMc2h0ektQYnNyYmpCWnVQUy9rVmIzR0xjb0l0WktPS3NZd3lZSnB6bkhEeGJxMjVyVVdnMHFrdmZuL0w4U3A3cW5oRm5VQ1RsQlRNQURqTjNabEMvZlFNZ01OZ0xvM0JPZGFtenVvZVp4Rit1d2pGdS9xTld2U0dtTnUvK2thQnYxMWNGcm95a0JOQllUbXVpYUZiNTFhVGtmV2QrSnpxYnBtSXdtYW5peURxaVJqY1dVZ0k5cS9TaXljTXl1WFFGRFNVYitVZmVYdG5RdW8xQWZLRU1NS0dzRFhmN3FNTjRrWWIxa3FsYk1YUXJ1K2RMQlBaTGxSai9hNUM4ZHdiV0J1SEM3djdkRGVDc2kiLCJtYWMiOiI1ZDI1NjdhZTliY2NjNWU0ZDJkZjI2NTdhMTYwYmM0NmI4MTEwZjFmNzI3MDU5OGI3ZDU4NWViNWU0OGE5NTFlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:07:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJVaHF3amhEZzRWVmNxdUZZMjhHN2c9PSIsInZhbHVlIjoiMDVhWEprRmNWTDdGd1FNSDJDNWRyT0l1elBlUEdjRGRBRGxpRTVuWUt0M05ZeUlpb09WOFZqSUU3ZDNoVFpzVmxKYndVbHZrbllzN0htdGRkVkJldWRERjhXbjNFQXNKaDdSSTQyOU1YOEhDYVVFN2RsVFVoaUM2L1FHZUZ4aStRK0RuYTVacktlKzRuTTdjRzVWWXVnbG9KK3pLMS9IQWFXM3krSmFSR3U5WWwyWlBWblpWNUsyRUs4VEEwUEpUWDI5WEdqMGVFc0hzYmRSa2Q3WXBlWFN6MFk2SEw0SzIzTnVaL1pzb0lNd1g3MWtNREsxeGlmdG9JRmZzRWJZVWYyNjZTY09oVFRMb25tMEVUQi9GN2FEMjlVbVRkelR4NmhvaVBXT0FnaHliQ3UwZk5IY1VJSklLaEtwMzFLcER2V2E0cUU3TWUxRm8yTUZiMkRFUnRQT1haMC9HMThiNzEzdkYzYkorUHVCVVR2QTdWczZqdWNKWDlXbzJNM2xaZ1FZRVNMTUNrdHlWQjd6YkUrby9nZU5oZnByL0FvQlBUMDR3RHBzNHl1WlBKK3pFYWViOTZwaHRqc2d4TVF2N0Y1VEJDQjZrMlpFRWRZcmZaT1hFZ2NFM3NoZytYaWY2TkUrdmNPSUovN1E2MHdJNFZldzdDaHhVTUJpN21BdmwiLCJtYWMiOiI3ODlhZjg1ZDIyZTNmMGE2MGNhODNlZjhkMWQ2ZWI5NGY2NjZmN2QyOWRjYjBlNGYzYThmNTBkOWY5ODE2ZDQyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:07:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdnU2ZjSVppOFJNZThFbDg5S3hONFE9PSIsInZhbHVlIjoia2VTSDRXQldCdWhsZHZpS09XSlAvVnp1YUNPOHN3YUgvYnA2QU9yNm9FRnJ2a2xTU0lpaEthdExEYUxPUFk3QWszNmtUWjdWZ215bTZHTTFLL2Z3MXMxODcySFdWQkpWMm1KeE5Qc1Z2K0loNlR2d3pxVUZLUVpOcitDNTB5Q2doWnFVWEppb2VLdGZad1BvOThLWUlWZ1pGQkRWZkZWMHBDSXE4azZQQlF4cnVkeE0vUDZ6bHQzbUo5SXg4amJ0SjRmZFJUS254TEZlNktRYkpTeXRzdFMxQ3hkZnBJOVkwdlp5RHZ2Z3VkMExvdWNmMlhMc2h0ektQYnNyYmpCWnVQUy9rVmIzR0xjb0l0WktPS3NZd3lZSnB6bkhEeGJxMjVyVVdnMHFrdmZuL0w4U3A3cW5oRm5VQ1RsQlRNQURqTjNabEMvZlFNZ01OZ0xvM0JPZGFtenVvZVp4Rit1d2pGdS9xTld2U0dtTnUvK2thQnYxMWNGcm95a0JOQllUbXVpYUZiNTFhVGtmV2QrSnpxYnBtSXdtYW5peURxaVJqY1dVZ0k5cS9TaXljTXl1WFFGRFNVYitVZmVYdG5RdW8xQWZLRU1NS0dzRFhmN3FNTjRrWWIxa3FsYk1YUXJ1K2RMQlBaTGxSai9hNUM4ZHdiV0J1SEM3djdkRGVDc2kiLCJtYWMiOiI1ZDI1NjdhZTliY2NjNWU0ZDJkZjI2NTdhMTYwYmM0NmI4MTEwZjFmNzI3MDU5OGI3ZDU4NWViNWU0OGE5NTFlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:07:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJVaHF3amhEZzRWVmNxdUZZMjhHN2c9PSIsInZhbHVlIjoiMDVhWEprRmNWTDdGd1FNSDJDNWRyT0l1elBlUEdjRGRBRGxpRTVuWUt0M05ZeUlpb09WOFZqSUU3ZDNoVFpzVmxKYndVbHZrbllzN0htdGRkVkJldWRERjhXbjNFQXNKaDdSSTQyOU1YOEhDYVVFN2RsVFVoaUM2L1FHZUZ4aStRK0RuYTVacktlKzRuTTdjRzVWWXVnbG9KK3pLMS9IQWFXM3krSmFSR3U5WWwyWlBWblpWNUsyRUs4VEEwUEpUWDI5WEdqMGVFc0hzYmRSa2Q3WXBlWFN6MFk2SEw0SzIzTnVaL1pzb0lNd1g3MWtNREsxeGlmdG9JRmZzRWJZVWYyNjZTY09oVFRMb25tMEVUQi9GN2FEMjlVbVRkelR4NmhvaVBXT0FnaHliQ3UwZk5IY1VJSklLaEtwMzFLcER2V2E0cUU3TWUxRm8yTUZiMkRFUnRQT1haMC9HMThiNzEzdkYzYkorUHVCVVR2QTdWczZqdWNKWDlXbzJNM2xaZ1FZRVNMTUNrdHlWQjd6YkUrby9nZU5oZnByL0FvQlBUMDR3RHBzNHl1WlBKK3pFYWViOTZwaHRqc2d4TVF2N0Y1VEJDQjZrMlpFRWRZcmZaT1hFZ2NFM3NoZytYaWY2TkUrdmNPSUovN1E2MHdJNFZldzdDaHhVTUJpN21BdmwiLCJtYWMiOiI3ODlhZjg1ZDIyZTNmMGE2MGNhODNlZjhkMWQ2ZWI5NGY2NjZmN2QyOWRjYjBlNGYzYThmNTBkOWY5ODE2ZDQyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:07:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397910482\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2125365492 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IkJwejFhcWlvaWRYazRGdzJheXNvMGc9PSIsInZhbHVlIjoiYTh3THBETWowZkdNNHl4U0pXcjR0UT09IiwibWFjIjoiZTkwMDFmYjQ1Y2VjMTc2MGUwMTUxMmFkOGQyMGRhNGUxNWFlZmZlY2VhMmUwM2I2ZWU0NTcxOGM3ZWU4ZTY0NyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125365492\", {\"maxDepth\":0})</script>\n"}}