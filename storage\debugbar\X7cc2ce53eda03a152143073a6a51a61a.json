{"__meta": {"id": "X7cc2ce53eda03a152143073a6a51a61a", "datetime": "2025-07-14 17:58:26", "utime": **********.139716, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515905.691428, "end": **********.13973, "duration": 0.4483020305633545, "duration_str": "448ms", "measures": [{"label": "Booting", "start": 1752515905.691428, "relative_start": 0, "end": **********.078942, "relative_end": **********.078942, "duration": 0.3875141143798828, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.078952, "relative_start": 0.3875241279602051, "end": **********.139732, "relative_end": 1.9073486328125e-06, "duration": 0.06077980995178223, "duration_str": "60.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46034096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0043300000000000005, "accumulated_duration_str": "4.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.106647, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.663}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1186202, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.663, "width_percent": 11.316}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.127237, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 72.979, "width_percent": 16.397}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.133058, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.376, "width_percent": 10.624}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1793922672 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1793922672\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1867887023 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1867887023\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1247073821 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247073821\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1311391244 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515898579%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJRVVFVT0oyVGphRkt2OUtld0txcGc9PSIsInZhbHVlIjoiakxpN2hGMWNhRldsM25CbXZDSk15MWMvTURXUlZMMC9lNDR1MWhuWE5YVXBtT29DV25wdEZwNjBPc0JxWEJseEMxSS9YUUJyNnRHUVZXZS9Va3JGREd3d01VRFpONnpKY1BCMUROK0RNcWs2VkFRV1ZVVkM1enFxVEQzMysrclhKUFhpLzhnT09OVHRYWWNrTThlY3ZVVkI0Q1Y3aUlCU0w4ckhzTHRZeE41NCtOc2U3UUsrVFM0aldQVE5STmNpNHQ2bEhyaE1vdi9qMFZzMDRXVXU3NEtmRFFMVVNCSk5kenZCUWk0cDBiMG9XWVdBU1A2aHJYUHhZUFJQeUlEaWcrZ0RaZENDWGpxM0Zlc2l1eU95YzRjSnBBbkI2VHNCb0lXMzdDV3p4ZS9zejJIN0xzVDBxcm5PRlBDY2pBM2ZMVkpVRG84eXRGbTBnR1VnSTdXZHBwYTQwQkRzQ1J1WjNzMW13R0hCNHhiVjkyY0k1ak1nbkxSYVR2UlUwQmlJbThKZGtucXNEcXlaeThMVjRlR01OVmhNRzY5eisvcndWYTFUaFVDN21sZzYvWlgyTnVpdllINDVlYTRxTGUwS2dibnNaTmVvL3kvT25HSS9PbGVjRm1KaHc3bWJJRGxGdzg5TXBFdHVRODNndjlJZCtJYkpSb3h6UzNObFRlUXgiLCJtYWMiOiJkZjU0NTVjYWY5NGRhMTFmNWRiYzMzODk1N2YxNGFmZDFiOTQwN2JiNTM4ZWM1YTYyZGMxYmM3ZDU0NGVlNjJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllicHJWalF1eGVHcnlTL2RuTWFlVEE9PSIsInZhbHVlIjoicWhNeWhtVzlGdXJGVGNhUGlVQnNOdEF5MUg3aHdycHdoN3MzcU9yRVhHUGVvM3M3cUwvWE9LTU9vc2QvYTllUVNOdXIzNjlhc2QzL0xKV3BxZDdReWJDS2w5RmdUZklDZnpvVlUxY2gveHg3TWhRZGdFVHl2U3diNDlCVDg4QU5QUHMzWnVkZG9nZmRuTlZXZXZBaGZYWEdsWkVRdHhRWmxQaGxtY0dwZWwxVEVMWEU3VVJBRDZsYUcwSG8wUnJXa3doTC84MmNmbHgzWGVscStzZ3NVRStrTGlka2lUWCtuTFZnMEF3RVhtUTdmVDMzNlJrN2svcURGOUVhNzJEL01UaS9nUlFycWFoaVFVNnlwL2NWbzJmaUZCODhaOTFWVlp0ZTNyVHJWcWY3d0pBbEp6anFhditmY1VQU0diTlFXdFBteGdEMGNsT1JLYzBsS0R0SkVtak1RZGErQnJuRUhWaHhGbXZWQjRxUlFvQkg1NjdjeUw2cEd1YVE3WWd4TVhsTHd2ZThVU0pSMXl0dmpWWVVBY1ljcDJJZ3JjR1VFT04zczFWeGkrSWJ4SUpSOXNVVlhzbVpDemlaTkxhM3JHWlVIY2JVckszWGZWSnk4N1JDa3RJdEhoQ09wemI2Y1ErbUlyaVNEUEcwRnVTYzVaVEF6Y2VuL1RMZlhYL0ciLCJtYWMiOiI3NDM3OTUwYmJmNmIyM2M5NzIyMTRlYjdiNGE0OTQ2OWVjNWY2NGFhNDc3MWQ5NDAzNWZkYTdiMTQ4Y2E3Y2U3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311391244\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1425764792 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425764792\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1237014543 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhBTTJ5ZFFkaEI1a3JBWEFLekU2NUE9PSIsInZhbHVlIjoiMlIyVTJjdDBLMzJ2aHJ4TXZ6ZEZaam1VYVZkWjIzVnZwSWQxc2MxYUZ2QkFrdldHdTdxcDcvQ3ZsM28rRy9SQ3NWVU56c0lQWjlXVEQybTVnQmR4cXNwV21MdllXM2R2RzdMUlJnRHBLMW1GUjNpVC91Y051cXdrREtYNWRhNGVMMFNIUHZWNUJWUU1JRnpVblpIRno3ZE91VkdpbzN6TEFmWURIeGkvTFQyTXN1N2VZUlVyOU5Oc2Y0UVFkVGVyUkNhTkplaE5hTDBRZ3cyZEFid0xYcW0yMXNUQnVrYlVkd2hLQnhuZldaa2EwQWwrV3hrY1BjL3VIT01pVE1Cc0ZSa2s3TnBpOTdkWG1XcGREQ0JiQ0JmOW5HSm9WZWxHb3BZc2ZqSWhRSWtyQlNwOTNJa1JWN09SWnl4bEE0T2gvWUFvQzhBck5QQWlMMmRmeEwvcHEwU0p5dEZhUW82cHdGb1V6MjlCQUNYUmtXTUxtNVFNRjZBN0NHQXZ4QkxNWERXTzNVZTBJREhZNWxOamRsVGtqSjF1YUx0SUZrL2ZTSHltcUNwZnIwbHRjZEllN0l2bnF0OGhscEM4cHpqY2Qva1A1VjlFVmJWaVdGR1BxYjA2MS9icmpVSDVwVmZYa0IvT1h0dFp6c2UrSXhIWWovOXJobXpxZVZlZW1kc0UiLCJtYWMiOiI2OGZlZGIzMDM0YjFhZGZhNjY1MjAyYTMxNTQzOTVlZjk4MDY3NDg1NDJiZDE2MzJiOWM2YjNjMDJiZjdhZTIwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFuZTFXcHM5Ym5QNkdwZTc3ZzZ4T1E9PSIsInZhbHVlIjoiNWRMc0dUUzQwd2hUV3dUcDI4TjdOZHpZMzU3L2pWYlBUZWpNdlRSOElLQUlZakpLR2RYdjFHQkxBTlAzQ0pqWU9xZGMrcGRRUnRUS1J0MGM2cGFvN3hQWStPaXEraXFuOHZVNVBCNkEzMGFIMk9yUDlPSy9mazM3M1VCRnJ0QjZFSTZFczMyU09YNHNPYVF4TEJKcnZBaHVuVDIydTBnNnV1K2xCWEpjOXQrbVJaU1lIbkJ5YXlqdjhWTm9hOWV2QXFwMndPSUhqQlRkZlJEcVNhZTRVSlhvMzZLZFMyQVF0SjBrcFBpRUNJRFc1U1JmZC9BTlNSZnhPMFF0N29PUE1CR2NFbGg2ZjhPU3B6NjVjQjIzcDg4NmwwMjBsYVpPQWFxd2FkQXRGVzZTYmlVZFJvNnA5cktpMVEwRzBwc0dwMzdsSU1hVHJubGMrbDd2MEtZeWZvYXlscU9NOU9rZXZaVGlZTm5pUkRiSWx3OExKRTdpOW1CRUlpZmVRRllMR2JFbEZkK3hFeTVNeWhyTk9mQzlHdWJtSGZCR0pUclBZS1V4RFhyWUpPMGNKT2VrWTAzRWRGQzBRWTFsYUlZQUw5aUhTSTJidGZrekUrcFdoQTloQ3d6VE4ycEFQM1kwSE81WVplSUlzVWkrbkZrWXZ2T3NlU1plcy9lTTE0R0ciLCJtYWMiOiJjOGY1YTk0MmQzOGI5NWUyZDc0NDdlOTMwZjE3MjJlNDFlYjBiODU4MzI1MzhhMDEyMGQ2ZjNjNzM2MmZmMjg0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhBTTJ5ZFFkaEI1a3JBWEFLekU2NUE9PSIsInZhbHVlIjoiMlIyVTJjdDBLMzJ2aHJ4TXZ6ZEZaam1VYVZkWjIzVnZwSWQxc2MxYUZ2QkFrdldHdTdxcDcvQ3ZsM28rRy9SQ3NWVU56c0lQWjlXVEQybTVnQmR4cXNwV21MdllXM2R2RzdMUlJnRHBLMW1GUjNpVC91Y051cXdrREtYNWRhNGVMMFNIUHZWNUJWUU1JRnpVblpIRno3ZE91VkdpbzN6TEFmWURIeGkvTFQyTXN1N2VZUlVyOU5Oc2Y0UVFkVGVyUkNhTkplaE5hTDBRZ3cyZEFid0xYcW0yMXNUQnVrYlVkd2hLQnhuZldaa2EwQWwrV3hrY1BjL3VIT01pVE1Cc0ZSa2s3TnBpOTdkWG1XcGREQ0JiQ0JmOW5HSm9WZWxHb3BZc2ZqSWhRSWtyQlNwOTNJa1JWN09SWnl4bEE0T2gvWUFvQzhBck5QQWlMMmRmeEwvcHEwU0p5dEZhUW82cHdGb1V6MjlCQUNYUmtXTUxtNVFNRjZBN0NHQXZ4QkxNWERXTzNVZTBJREhZNWxOamRsVGtqSjF1YUx0SUZrL2ZTSHltcUNwZnIwbHRjZEllN0l2bnF0OGhscEM4cHpqY2Qva1A1VjlFVmJWaVdGR1BxYjA2MS9icmpVSDVwVmZYa0IvT1h0dFp6c2UrSXhIWWovOXJobXpxZVZlZW1kc0UiLCJtYWMiOiI2OGZlZGIzMDM0YjFhZGZhNjY1MjAyYTMxNTQzOTVlZjk4MDY3NDg1NDJiZDE2MzJiOWM2YjNjMDJiZjdhZTIwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFuZTFXcHM5Ym5QNkdwZTc3ZzZ4T1E9PSIsInZhbHVlIjoiNWRMc0dUUzQwd2hUV3dUcDI4TjdOZHpZMzU3L2pWYlBUZWpNdlRSOElLQUlZakpLR2RYdjFHQkxBTlAzQ0pqWU9xZGMrcGRRUnRUS1J0MGM2cGFvN3hQWStPaXEraXFuOHZVNVBCNkEzMGFIMk9yUDlPSy9mazM3M1VCRnJ0QjZFSTZFczMyU09YNHNPYVF4TEJKcnZBaHVuVDIydTBnNnV1K2xCWEpjOXQrbVJaU1lIbkJ5YXlqdjhWTm9hOWV2QXFwMndPSUhqQlRkZlJEcVNhZTRVSlhvMzZLZFMyQVF0SjBrcFBpRUNJRFc1U1JmZC9BTlNSZnhPMFF0N29PUE1CR2NFbGg2ZjhPU3B6NjVjQjIzcDg4NmwwMjBsYVpPQWFxd2FkQXRGVzZTYmlVZFJvNnA5cktpMVEwRzBwc0dwMzdsSU1hVHJubGMrbDd2MEtZeWZvYXlscU9NOU9rZXZaVGlZTm5pUkRiSWx3OExKRTdpOW1CRUlpZmVRRllMR2JFbEZkK3hFeTVNeWhyTk9mQzlHdWJtSGZCR0pUclBZS1V4RFhyWUpPMGNKT2VrWTAzRWRGQzBRWTFsYUlZQUw5aUhTSTJidGZrekUrcFdoQTloQ3d6VE4ycEFQM1kwSE81WVplSUlzVWkrbkZrWXZ2T3NlU1plcy9lTTE0R0ciLCJtYWMiOiJjOGY1YTk0MmQzOGI5NWUyZDc0NDdlOTMwZjE3MjJlNDFlYjBiODU4MzI1MzhhMDEyMGQ2ZjNjNzM2MmZmMjg0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237014543\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-351660015 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351660015\", {\"maxDepth\":0})</script>\n"}}