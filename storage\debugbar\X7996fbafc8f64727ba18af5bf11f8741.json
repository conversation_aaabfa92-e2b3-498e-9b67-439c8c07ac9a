{"__meta": {"id": "X7996fbafc8f64727ba18af5bf11f8741", "datetime": "2025-07-14 18:35:36", "utime": **********.724415, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.280777, "end": **********.72443, "duration": 0.4436531066894531, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.280777, "relative_start": 0, "end": **********.655998, "relative_end": **********.655998, "duration": 0.37522101402282715, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.656007, "relative_start": 0.375230073928833, "end": **********.724431, "relative_end": 9.5367431640625e-07, "duration": 0.06842398643493652, "duration_str": "68.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46019576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01523, "accumulated_duration_str": "15.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.686254, "duration": 0.01421, "duration_str": "14.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.303}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.70977, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.303, "width_percent": 3.546}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.71706, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.848, "width_percent": 3.152}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1201314092 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1201314092\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-95192834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-95192834\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-566286916 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566286916\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-681985543 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752518033032%7C49%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImY3NWd2Lyt0K0xPdkhFOWxjMGVHSmc9PSIsInZhbHVlIjoibzJWQldLT0JoMHRoZ254TnNvWWt1WE9paUNOMnNGaG9HbGN6SkdzcUhacjFlRFlvV21xdmtZa0lLcUZneGpoUU1Uamd2T2lKa1AvTmt0NDhtYUNMN3A4WDRrMmpNS0JFODljRThGSXBWSmtjS2RvVUhxYWlzN0xMSGhzKzIrNmxCVHFDZWZvbGpCVmY5YWxRL3prcTltU2tPZTVuVUl6bXVXZzc5ajBWWjJKTnJhdGtjcmppaCtQaU5HTDByOHRXZzkzUEZJdEc3cmNGVjQ4L05NYWtXYnNLWXlJM3FtNzVRWng0cDR4RG8vUWxyaHpVSytyT3pObXVtQjh5UWplckQ3MGtiM1NKWXJ5OHNTNThMdkRiQWU0V3NsRU9UNDlEUEZhdFJja0Z5cWx5c0p5WG9Zek1Ma1pzYWV2aXMvTE5OWnRNalhjaXRDRUtzRDhJcXBzVjlURlh5UVBneGhUQ0VxRzRCcnJGRGlhckpwbGlUaS9lVGJvc3dBdzNQUzdlMm1xRmtmMFhVeHJmd1VDd3NEN2h1NkR3RmxITnkzeEpNQ0lWZnNkVUZ2eldmZG5Na2J4YmNnZUZJbTNRQUJqYzZBeFZqZDVMckt4cEdNbjlXTk9Ud3Flamlic3VENys0UStxTyttTThqR1pvbVlWYnE4M2l6Tno2NGFCWFMzN2wiLCJtYWMiOiI0YjIxMDMzYTY0NWU4ZWM1MmUzMjBhY2VkZDE5MWQ5NzJhODkwNWVjZGU4YzY5Mzk3YjQ4ZDY5M2ZmNzcyMTM5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZOZjBZVDNGSlBmb3NscFN2QnlaL2c9PSIsInZhbHVlIjoiQkM4UlIxdmcvbGVLL3BlRWw0WUdEaUlTa2czQ0RHQ2pPdndOa3dtUFBCMDVJOVYwSzVmSm5panZhNlg1b0FxcEdubDN6TEtYcUZMQ3VNc2YyWXYwb0JCbGljc2NMUFBTckRTOWhoeUFwd1FQTm5DN2dJRitaZmZnc2U2OEtBZTFRUEJ0MWNQOWsreS9RcEovT3gzK0kzZXMwRHlydmNHU0ZpUXJsbXZRRkIxVFlQdmZOSjJvMWxrWG1RUjgxRit4ZlFFREdxcjI0UU9ScUlqcG1CMTNQYjVjVytqRFF3S2dFc2R6NENLRXR2MEJPMFNhWDlhNjZVbS9CSnowOU9EU3hKMkdjVDNwQmJlc0xYWWJrWE43dVBNeDE2WHd4cXIvQ2pBemhRWVBqV20yWm4yNVgwYytXZG5TRTFSRHNBckJkK1huUWE0ZElubDVnY3VrVllyRkVLdzBIeS9ZdzR4WGpXd3FyWFRyd1RwWDl2TlBTV1pTeU5MaW5FZTZna29sTXQ4c25qTWYyNU5adUgzVHI2ZXg1RVgzYTVvdGFTcHhtcXNmTXhrV3dmODR4bVI2KytiamFUM0oyTXk3NnVWZDY5a0M4YkVlUHJHdnJ5dkZpbnRIR3dqS0tJK3ZubkU1YkZKYyszK3NKNVcxb3lMTXNZelErQjQ2MXdYdWU5bXkiLCJtYWMiOiIxNzBkYTgyZTE0Zjk0MjU5ODZlYTMxNmUyYTE2NDAzOGExZTRiYmQ4MDVhMWZjMmY2OWJkMzc0YjBhNjhhOTU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681985543\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1416410895 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416410895\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1615048861 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:35:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBOai9Vd0J0dFgxOVgxSFQ5akRSMEE9PSIsInZhbHVlIjoiaU5DcktpN1pEd2poS2diU2ZWYk1ZY2RXM3dKVGZBTXI4UlJDZS9kTUhRQVpmK09QN1dwKys5WHBrTXJxQU5naTltZk9UVWVyUmdWSkMzclFKNzZrdENqWStrQjNGYUVsSFU1NzlmTldTNkFDWEdyWFZjR282VHR4Y2I2YmRwNlMrRFAxWHRVWlZnVXJSenlvNWozV011Y2Z4NDdIbWtjMmJZRXlBTEhWdDg0M3dpdUFuRFE2M1hOVzUrdzlGUitWYnNkMU5mRGRkS2ZxMFpNN21GWjdmYmpZL0VPTXVvZDh2OU9kamJoV05xSEluSy9NNEh2Z0JWbmFRZ1V5WUFIUUxLQ1pwT2NXTnVBSVdYejBSRkVzVmd2SklCQ2tPZ2NwVFVuMkVKMmRIdHNtUmluNk1xZkV6V3I2amR4T2w5amp3ZkNFZit3NW4ySW5Cd0NhTmVIek50T2IyQXYwenlveUpoUDhSM0dNdUh3R1J6VlZvQ1hMWmtId2FxeHNXZElCU0psbWFuUWR3djZCZTdoalJ0WkNmTHQ5M0RBbTB0dDZaMzZhZWtKUE5CVFpQZVZiaTBscDVCR2RwMmk4Z2tsdmMxd1JaTXhzUktLTmdyY1k3ZXNlRlQzOWNZbG9pNWxtWUtEalNOZ0w3U1FEa3dmTi9Lc3hBZXFKTkhBeDRUVVkiLCJtYWMiOiIxNjViM2ZjY2E1MDRhYzdhYjNkYzU4MTMxMmZkZWNlYWVhYWJiN2EwYjdlOWRjMTMxMjE1MDdmMTE0MTc4YjYzIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:35:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZjV3FuQ1BsSU91ak9DZ0VFOWlmNnc9PSIsInZhbHVlIjoiY2h5SzFLYkgrQ1lZcUk4VWIrRTRMYmVWL3JJRGdBWjJMRjVTazd2aXd1MkgxUm91S21SZ1ptZUhUOUJDWGFrRHhORE9PcVJCUk1HT2lSQ3FkQzI3N0c0b0Z3eFdyZXdZbzdhOUJmNmhPK2hCWFZxVU1pUnZVVEJjeHFFRCtDWnV1dnlWeCtlS2NDYjFnSEJ2bEN2MDVoMWtyanZGYTdldUltVldxbzIvbDNCMko0d1AxZEZPek1BMnJUaXAxYUxoYjdSUFNxSk1aaXVQdXhDOStrTzN3eDV5Q1dldzlGd0swSGtSMWFvdml6VEt5UmJaNE15d2MwMGtGUS9qYVJZZ1FQT0xqdjR6OVlHVFFsMXowQUpuaGZ4Z3pqZlNCY0FISDlPMGo5YUQ1d3FGRk1BRmpvdG1obXRRNml6ckJ6U3c2elpKRTNSRmlsdkpaRDEzeDRYMWZwcmY4VUNpR0I0RCtPVXpFRWVHRlVUWWdoZWpFWHdBQ1JLS3paenJaa082TlBaL01IR0lkVUthcUR0VE45NEtmeXdZdDU5MWNUSTZSZkpaZ2tOWGVXdEFiLzViVmp2WGprL2k4KzRQTlpYZzFZQkFuNHhWdUMzZTFlYmZkZFRONWVEUjUvTk40VTQzZU4rS3N1blZObTE3Mk9JaE5XWWk3RmtXWTRkZVZnSm0iLCJtYWMiOiI0ZTVhZWJkYzUwMDYxYmNhZDRhOTFkY2ExYTkyYTk0YThlZmRhNDhiMzcyNTgwZmFiMjUyMDhlYWRlZWM3ZmI4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:35:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBOai9Vd0J0dFgxOVgxSFQ5akRSMEE9PSIsInZhbHVlIjoiaU5DcktpN1pEd2poS2diU2ZWYk1ZY2RXM3dKVGZBTXI4UlJDZS9kTUhRQVpmK09QN1dwKys5WHBrTXJxQU5naTltZk9UVWVyUmdWSkMzclFKNzZrdENqWStrQjNGYUVsSFU1NzlmTldTNkFDWEdyWFZjR282VHR4Y2I2YmRwNlMrRFAxWHRVWlZnVXJSenlvNWozV011Y2Z4NDdIbWtjMmJZRXlBTEhWdDg0M3dpdUFuRFE2M1hOVzUrdzlGUitWYnNkMU5mRGRkS2ZxMFpNN21GWjdmYmpZL0VPTXVvZDh2OU9kamJoV05xSEluSy9NNEh2Z0JWbmFRZ1V5WUFIUUxLQ1pwT2NXTnVBSVdYejBSRkVzVmd2SklCQ2tPZ2NwVFVuMkVKMmRIdHNtUmluNk1xZkV6V3I2amR4T2w5amp3ZkNFZit3NW4ySW5Cd0NhTmVIek50T2IyQXYwenlveUpoUDhSM0dNdUh3R1J6VlZvQ1hMWmtId2FxeHNXZElCU0psbWFuUWR3djZCZTdoalJ0WkNmTHQ5M0RBbTB0dDZaMzZhZWtKUE5CVFpQZVZiaTBscDVCR2RwMmk4Z2tsdmMxd1JaTXhzUktLTmdyY1k3ZXNlRlQzOWNZbG9pNWxtWUtEalNOZ0w3U1FEa3dmTi9Lc3hBZXFKTkhBeDRUVVkiLCJtYWMiOiIxNjViM2ZjY2E1MDRhYzdhYjNkYzU4MTMxMmZkZWNlYWVhYWJiN2EwYjdlOWRjMTMxMjE1MDdmMTE0MTc4YjYzIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:35:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZjV3FuQ1BsSU91ak9DZ0VFOWlmNnc9PSIsInZhbHVlIjoiY2h5SzFLYkgrQ1lZcUk4VWIrRTRMYmVWL3JJRGdBWjJMRjVTazd2aXd1MkgxUm91S21SZ1ptZUhUOUJDWGFrRHhORE9PcVJCUk1HT2lSQ3FkQzI3N0c0b0Z3eFdyZXdZbzdhOUJmNmhPK2hCWFZxVU1pUnZVVEJjeHFFRCtDWnV1dnlWeCtlS2NDYjFnSEJ2bEN2MDVoMWtyanZGYTdldUltVldxbzIvbDNCMko0d1AxZEZPek1BMnJUaXAxYUxoYjdSUFNxSk1aaXVQdXhDOStrTzN3eDV5Q1dldzlGd0swSGtSMWFvdml6VEt5UmJaNE15d2MwMGtGUS9qYVJZZ1FQT0xqdjR6OVlHVFFsMXowQUpuaGZ4Z3pqZlNCY0FISDlPMGo5YUQ1d3FGRk1BRmpvdG1obXRRNml6ckJ6U3c2elpKRTNSRmlsdkpaRDEzeDRYMWZwcmY4VUNpR0I0RCtPVXpFRWVHRlVUWWdoZWpFWHdBQ1JLS3paenJaa082TlBaL01IR0lkVUthcUR0VE45NEtmeXdZdDU5MWNUSTZSZkpaZ2tOWGVXdEFiLzViVmp2WGprL2k4KzRQTlpYZzFZQkFuNHhWdUMzZTFlYmZkZFRONWVEUjUvTk40VTQzZU4rS3N1blZObTE3Mk9JaE5XWWk3RmtXWTRkZVZnSm0iLCJtYWMiOiI0ZTVhZWJkYzUwMDYxYmNhZDRhOTFkY2ExYTkyYTk0YThlZmRhNDhiMzcyNTgwZmFiMjUyMDhlYWRlZWM3ZmI4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:35:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615048861\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-845738061 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845738061\", {\"maxDepth\":0})</script>\n"}}