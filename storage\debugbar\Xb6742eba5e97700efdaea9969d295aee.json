{"__meta": {"id": "Xb6742eba5e97700efdaea9969d295aee", "datetime": "2025-07-14 18:58:24", "utime": **********.359855, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752519503.87291, "end": **********.35987, "duration": 0.48695993423461914, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1752519503.87291, "relative_start": 0, "end": **********.284215, "relative_end": **********.284215, "duration": 0.41130495071411133, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.284225, "relative_start": 0.4113149642944336, "end": **********.359872, "relative_end": 2.1457672119140625e-06, "duration": 0.07564711570739746, "duration_str": "75.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46003968, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020879999999999996, "accumulated_duration_str": "20.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.316666, "duration": 0.019899999999999998, "duration_str": "19.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.307}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.346144, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.307, "width_percent": 2.538}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.352386, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.845, "width_percent": 2.155}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&date_to=2025-07-10&payment_method=&warehouse_id=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-237095162 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-237095162\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1108365149 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1108365149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1304753355 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304753355\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-10&amp;warehouse_id=&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752519484750%7C54%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV4UUFVaEpKYnBWNHVQOFFjS3ZDSnc9PSIsInZhbHVlIjoiSjVJKzVwbFFLQmwycW5TWDkzTHplVk4ycCtKOU01bWJROVFoZ2xqK3ZidlVic2crSERCdkhlRUJZbEJIT1AxV0JVSCs3STEyYzNVOVd2ZHBXcytza1RRYVFqeDJQQ2pLT0dVZXhydTJEcEM5VWJoZnA3YlhQb29oT0QwYS9CR3Z0Zi9Hd0tVeGkzY2s2MjAyelpML2c5NGJyeVNWVTQ5Y2lwK1MvSE01SEhEWlR4NStjTUo3WUxoSVFSbis2anppaTdzd1FINVAwZ2JFNHM2QkY4dFR1dmxFVFhZMzBycEIycXlJdHlwTUg0Tyt1RUtkMnUvT1J2emRzc1BKZDJBSFBnY1ZiMTBWNGZ4R1dWeW43UktlR0pnNFBwV0ZBdlZNK2t5ZEpxa3E0cmV2V1RRUmN1aTE0ZVNNTUcvY0RsNHpmbGpSaXpiMWlVbW9DVzkwWWthUnRpcmtIbThJNFVwb2ZRaUZUMGdsSmk4RmxvYzJVL1FaL3JFMURjaGZ0Y1J2R0NVRDE1eEpIMjdGdjRVLzRSVXJ0SVdZVHl2Wk0yMW1FWC9vL3JLNWlEUENBUlBMQ0dmWUpYVVgwNmJNWjFRanFtRWpKR2lqSEgxNzhBVExBY1owTTRLZWFtdk1UbkE4dWd2ekVIMWgxUVdSQ1IzeVFqbXlNWEo3NXplOGF6SG8iLCJtYWMiOiI0ZWU3ZTU4MGM1ODFlM2M5MTEyZTY1M2MxNmIxNjE3OTQ4NWUwMjJlODljMTRkNTI4YTE4MWNjZjBhNzM0NmE2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhSanpZaDYrSGZZRFFyVXdoVmdmMHc9PSIsInZhbHVlIjoiVEtsdVZjalZJeUt3eFlkUlRJYWt0VVRhR1krc3RHTnF6aHRwaVd6MG5udTRxSjVTMVJ3azFmMkpTdy83Y0hKY21lZ3VDS2hrc3N5RFBjYVpEc1N3T2p5ZG4wTFZGMnZMOVNwMWcyYkx3dFpXcnBCbmM0Vk5DY0wxenAwREVjZ3Zhc0gxN0tNQ1p1ODBMZzE0bk51Y0VUS3B0U3FhNnJhWkkxUGhtS3Jpd042VmZTT1FEbVJLb2R2ZENlK1M1NTFWQXlqdlR2SXBick4wZkYybzUzNXBsWEZSV3ptMktHTWRQSHNEcGhiUktPOTM1TUk0Yk41Nlh6YUtpd0RqeGl4elFtUThJTDlTWWJvVXU3eDU0MVY3UTkwWnBOYmJvaCtldEZITWZxanhXWjNCSXZWcXFERkJkd2tLRFBlWnI0M0IxcDBPSnF3cFdiZ2tjZldYMk5TU2R6QXhUZXNMRGNreElBdHdYTWFrd3RIVFRpSlljUUNhS0NaVGtCVTBwRmR5WHhTWXpnbnl0RDBGT1htK3lRU2xqcHhnUS84MUNSdFNrZElkUmpDaHNMRTdzTzdBZUdGd1Rwd1dNYU1lRFVYcHJZdDJMVC9mYVNPSi9SbkNTSmFCS3FITk5WeENFMzhlcVd6ZDV5YkNQN014dDRqZE8vdnFMU3ZISjRPUktuUWciLCJtYWMiOiI0ZTU0NWE5NGJjOTZiM2JlNWZiYzM2NDAyNjBhMDYyZmYwYjcyNzkxNTZjMTYyZDAxMmFlYzk4Yjg0OTFmNmFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1691288228 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:58:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikw0NExGS3hUT3FJMW51cXg5NjNlQUE9PSIsInZhbHVlIjoiYVFFWUUrUmQ3Vk1ERXpaN0NkRllSR2ZyL01LcW9jdDZGYXc2ZGZvSzE5NGxzc2g3K0s2QS84WWUrOXBNWXJIck5UUzMvU3B4RU9FZ1dCMm9xNTN3RS9kWDdCY2Q0STc0anJmRUZDcnR2emZRdXo1RVBRNFkyV01xMVMyUFRaM0p4YWhCczVRZXNEaHUzUWtJR2xaVHEzYldKaEdUZ01Ic3I4bW9ENm85YzBWSi9iTzZrNFZCWkdEYlNSSlNSOHBHbS9zTm1xVHVSR0NVSCtGWHFxRDNxK2RqWFpEWnRpTXMrSXd5b3hGMExFRU4xaUZ3WmxVN1BBOXJuVTJBUGw1anlXUnYzSjJLMkRaU1A0eS9UUjVVTGEwa3ZWSlBJU3BzUmdVbkFJYnIwc2Nhd1pCUTlMb25Qak9iOHBtOFQvSVExWjN2MEFPcTgzVTEwdVlXRVJhZlNmN0Q5V25EaUMrZExhWFVuRkd5N1Vhd1kxWjFDODhjY2NpQ1JPd2ZmMEQ1azZBVHBWR1lxaUdVNjJwWnByeHJDTGtMZDhoeWdBQ1cyNk5wcEppUmNlamFoWGZIM2xqQkFVYlFlOXpUbmFFLzVsR1pqK2JOaXgxZkpjem9rU2l2MEVySStxR2gvdzA3R2l5dUFVMHl0UDNTVThxdzJJRmxkMTUvTktBSUxNOHMiLCJtYWMiOiJiNzI1ZDkyMjgwNDUxNDY5M2I2YTA4OThlMzdiZmIzOTNhN2Q4OWRjYWZhNDUxYmUyMTk4MTZkZjk2OGYxN2Y1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1GNlVjMjlhcHFNMUN0T1JKa1Fzanc9PSIsInZhbHVlIjoiQTZKTEhscFlHRHpMRlZmZk9uREVSb1RHeDZXek1OMjJsTlhFcExnTG4vaFpBOXhNOG5Mb1Z4N0h3b2g5ejVSUjdqUDRqMjdKWGRMZzZkaWxCbUlRV1h4eWx6elVybG5pcFlWaE40bWZyVnEvVlhFWURBRnRvSncrZHFmS1lPK1JRaW5iUjluK2dVK0xCRitta3FoYm5Bd2J0MjFteWdSQUU0cU85TnRFNHp5M1EwM3JxN2JtdEtCaWVyc3NrSGJMSmViMEJtbC9YYmVjSmNTUkhacUdxVDFDdjVNSWJyRkpxUGdSRzdlY1VYLzVhZmNZSUhQQSs3cm9wbzNmYTdXcThpU3FsakFtU0FPOGdvcndpeHYyMkpvM2FLdExIYjZtNEtzdjVzOHVWUjI4MGU1MCtNNEMzWFIzcTI1VXZ3bE1mZm8waUxWSVI3U05DTllleEZiN1QxYUp5U2QxYWp2QWwzVzFxa3FBU0dKQWY5cjRpb2toL3pTMGNtSW1aZkVIT0tRb2JqZTZYdU9qTGNuTi9YcHVycm1DRDMydkJraEtCcW1pQVh1dE5sZXB3Qjc4OXREcmtXSkdhQ3RPN1pSWmd2ZVpvNEpLMlRMOEhITUxkQTBZSFF0RG55ZEEzdnFoUWZVYVFJVERFSHdmNG1QOExWSWJHL2dLR2pYemYyeEUiLCJtYWMiOiIyMTViZjk0MTA4YTIyMzI1MTY3ZDg1ZTYzZDIxYmZmMDY3NjcwNTFkMWYwMDVjODYwZGY3YmQ2YTQzNmM1ODMyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikw0NExGS3hUT3FJMW51cXg5NjNlQUE9PSIsInZhbHVlIjoiYVFFWUUrUmQ3Vk1ERXpaN0NkRllSR2ZyL01LcW9jdDZGYXc2ZGZvSzE5NGxzc2g3K0s2QS84WWUrOXBNWXJIck5UUzMvU3B4RU9FZ1dCMm9xNTN3RS9kWDdCY2Q0STc0anJmRUZDcnR2emZRdXo1RVBRNFkyV01xMVMyUFRaM0p4YWhCczVRZXNEaHUzUWtJR2xaVHEzYldKaEdUZ01Ic3I4bW9ENm85YzBWSi9iTzZrNFZCWkdEYlNSSlNSOHBHbS9zTm1xVHVSR0NVSCtGWHFxRDNxK2RqWFpEWnRpTXMrSXd5b3hGMExFRU4xaUZ3WmxVN1BBOXJuVTJBUGw1anlXUnYzSjJLMkRaU1A0eS9UUjVVTGEwa3ZWSlBJU3BzUmdVbkFJYnIwc2Nhd1pCUTlMb25Qak9iOHBtOFQvSVExWjN2MEFPcTgzVTEwdVlXRVJhZlNmN0Q5V25EaUMrZExhWFVuRkd5N1Vhd1kxWjFDODhjY2NpQ1JPd2ZmMEQ1azZBVHBWR1lxaUdVNjJwWnByeHJDTGtMZDhoeWdBQ1cyNk5wcEppUmNlamFoWGZIM2xqQkFVYlFlOXpUbmFFLzVsR1pqK2JOaXgxZkpjem9rU2l2MEVySStxR2gvdzA3R2l5dUFVMHl0UDNTVThxdzJJRmxkMTUvTktBSUxNOHMiLCJtYWMiOiJiNzI1ZDkyMjgwNDUxNDY5M2I2YTA4OThlMzdiZmIzOTNhN2Q4OWRjYWZhNDUxYmUyMTk4MTZkZjk2OGYxN2Y1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1GNlVjMjlhcHFNMUN0T1JKa1Fzanc9PSIsInZhbHVlIjoiQTZKTEhscFlHRHpMRlZmZk9uREVSb1RHeDZXek1OMjJsTlhFcExnTG4vaFpBOXhNOG5Mb1Z4N0h3b2g5ejVSUjdqUDRqMjdKWGRMZzZkaWxCbUlRV1h4eWx6elVybG5pcFlWaE40bWZyVnEvVlhFWURBRnRvSncrZHFmS1lPK1JRaW5iUjluK2dVK0xCRitta3FoYm5Bd2J0MjFteWdSQUU0cU85TnRFNHp5M1EwM3JxN2JtdEtCaWVyc3NrSGJMSmViMEJtbC9YYmVjSmNTUkhacUdxVDFDdjVNSWJyRkpxUGdSRzdlY1VYLzVhZmNZSUhQQSs3cm9wbzNmYTdXcThpU3FsakFtU0FPOGdvcndpeHYyMkpvM2FLdExIYjZtNEtzdjVzOHVWUjI4MGU1MCtNNEMzWFIzcTI1VXZ3bE1mZm8waUxWSVI3U05DTllleEZiN1QxYUp5U2QxYWp2QWwzVzFxa3FBU0dKQWY5cjRpb2toL3pTMGNtSW1aZkVIT0tRb2JqZTZYdU9qTGNuTi9YcHVycm1DRDMydkJraEtCcW1pQVh1dE5sZXB3Qjc4OXREcmtXSkdhQ3RPN1pSWmd2ZVpvNEpLMlRMOEhITUxkQTBZSFF0RG55ZEEzdnFoUWZVYVFJVERFSHdmNG1QOExWSWJHL2dLR2pYemYyeEUiLCJtYWMiOiIyMTViZjk0MTA4YTIyMzI1MTY3ZDg1ZTYzZDIxYmZmMDY3NjcwNTFkMWYwMDVjODYwZGY3YmQ2YTQzNmM1ODMyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691288228\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1224102820 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-10&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224102820\", {\"maxDepth\":0})</script>\n"}}