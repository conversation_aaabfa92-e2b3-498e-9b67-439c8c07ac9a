{"__meta": {"id": "Xc3fa9e8ce34034bf8ccc710268688617", "datetime": "2025-07-23 18:17:19", "utime": **********.718346, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294624.099027, "end": **********.718375, "duration": 15.619348049163818, "duration_str": "15.62s", "measures": [{"label": "Booting", "start": 1753294624.099027, "relative_start": 0, "end": 1753294637.762018, "relative_end": 1753294637.762018, "duration": 13.662991046905518, "duration_str": "13.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753294637.762045, "relative_start": 13.663017988204956, "end": **********.718378, "relative_end": 3.0994415283203125e-06, "duration": 1.9563331604003906, "duration_str": "1.96s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44314184, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.07736, "accumulated_duration_str": "77.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.351208, "duration": 0.07736, "duration_str": "77.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JzkoNRilj8X8jCoyg1z635IfLcsWwRzk1Lbfl9Ft", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1668143624 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1668143624\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1137311735 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1137311735\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-738459253 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"195 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738459253\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-355424858 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-355424858\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-299467917 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:17:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imk3VDl6VUczd3BXWUdyNEVPMExLTWc9PSIsInZhbHVlIjoiSjlHZGJvLzVraDNXa3RjZ1VZdHFpaFI3VTUya3FLYW1ib1crQ1lSS2RsWWhtTVZqTkxvWEtkeHI3NUJNc09tZkUrZ1VaWGd5SDJCY2xMRStuSE93MlNTZTc5a1hhZ3VBLzI1b2RWY1JkUDVIVzN0ejl2dzkyRlkxSndZcFpZYmFsazRINGFyRnFXQzM2cE1MUFU3TE5IY2FGQU9DUDFxYWppRFE5ZkZnUlhpYWZ0MWUrQlhNUU41ODNLUmNQOXVWaWtRWVRVU243bkhMb1JNM3NnNDByemI0Y3RNVm5ETW9jY1hmZkd6cnoxZ1VLYVVJeUZBVEhmUWxRNGhRNTFrS0duQS9SS3hOV1JvcmV1T3J4WG5QeEJ1TncwT1lqbzVaclZic0lJSzlLUUYzYVluYXIzY1BRQlQ3UUFHeG5TUW9VVXM5WWdHRXlwcUJMWTZNb1BXTFRyNmJob2FsOSsvbnR0VU5hdjdiN08wZU5WMkpLbysyN0V1dVR5V1YvSmhPeExMcktTNTVibElzWkJuUVZBMlFDM2xOSlQ1UUdyazR4UWtvSDdMeFNSQjBhdXB4aFkweXVoVFl2K0xIVEJUR2ovVnRJQjdhaTRKRlVLd2pXY0ppRGxYTkQ3M0UrUmloQ0hLMm1HRTZEQnFtNjd5TXRuN20yQVZ6UW05dXJESXQiLCJtYWMiOiIzNjY0OWU2ZTc4YjE2MmRhZjllOWI5MWE1MjlhNTU5YmNlZTA0ZjI4YWQ0NWNlMmMzMDg1MzEyZTEwNDM1Yjg5IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:17:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhuTTczaUVpZWlZOUxKaHVONHpTaUE9PSIsInZhbHVlIjoiVWhtdlZNcUlpWld0WGhOai96MUZkL2Nsc2ZoRXNIZTNYWFJaSWYrNG9TZlFNZy9DMFJBNWplRGFXeU9VZ2t3bk4wdFlYYTdOUDdyRDFWbGdjS0ZLY0QrZ0NkbmRtaWVHQkc5NklGZ25KQ3JnTkIyRnV1MWxIQWIyc0oxb0UvWjJQNS9MV29GQTFJUnRIby96K2NtOE1mbXRmQnA3aVJncEgrWndBM08xeGtmd3VjQWI3TjdjNDNLaTROQkJZeDVac3VHc2s0eXJnOUhKYWN3TXR3cVF3Z0dtNjE1eWhHQXNCNCtoM0dOZW9oVSsrTGlqSXk3NWJtNkpyZWNjSFpPR0lPVHRtSThxMU5nZ0xXZURzQUZkdlZuOWdVK3hwRGl4Z1VTUkFOL2lPQ01WSVJyU2s5OXlFVElWa0NSN1BLaExTbTdyeTU4NG1BOEZQUWdHQjRBUUpyU3hpeXRVWlNPSE0yZUlkRXNPUUxuZCtaczN6V1VzemNmeHNFbFhuL2t2NXNHUXc1NHdrbUpwL1B3S21HU3E0M00wVFRQRHNpK0c4ODgwemU3OUtPVkUweEVFL2xVKzJSQTB4bjZtU2ZTMGtSbTZDTGNieXFiRzJvRjMyWkhTUk1QWGtocGo1dXg2TFVwSUtqanc2d0VOdzdYQTBJSGlaV05pMUs4SjczbGMiLCJtYWMiOiI5YmEzMmE5MzUwYzQzNzA3MWEzNWJmOGJjMzY5ZGE4YWZiOTU0Y2NhMjZjZDE5YTFiNGU0MzJiMmEwM2I2NmI1IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:17:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imk3VDl6VUczd3BXWUdyNEVPMExLTWc9PSIsInZhbHVlIjoiSjlHZGJvLzVraDNXa3RjZ1VZdHFpaFI3VTUya3FLYW1ib1crQ1lSS2RsWWhtTVZqTkxvWEtkeHI3NUJNc09tZkUrZ1VaWGd5SDJCY2xMRStuSE93MlNTZTc5a1hhZ3VBLzI1b2RWY1JkUDVIVzN0ejl2dzkyRlkxSndZcFpZYmFsazRINGFyRnFXQzM2cE1MUFU3TE5IY2FGQU9DUDFxYWppRFE5ZkZnUlhpYWZ0MWUrQlhNUU41ODNLUmNQOXVWaWtRWVRVU243bkhMb1JNM3NnNDByemI0Y3RNVm5ETW9jY1hmZkd6cnoxZ1VLYVVJeUZBVEhmUWxRNGhRNTFrS0duQS9SS3hOV1JvcmV1T3J4WG5QeEJ1TncwT1lqbzVaclZic0lJSzlLUUYzYVluYXIzY1BRQlQ3UUFHeG5TUW9VVXM5WWdHRXlwcUJMWTZNb1BXTFRyNmJob2FsOSsvbnR0VU5hdjdiN08wZU5WMkpLbysyN0V1dVR5V1YvSmhPeExMcktTNTVibElzWkJuUVZBMlFDM2xOSlQ1UUdyazR4UWtvSDdMeFNSQjBhdXB4aFkweXVoVFl2K0xIVEJUR2ovVnRJQjdhaTRKRlVLd2pXY0ppRGxYTkQ3M0UrUmloQ0hLMm1HRTZEQnFtNjd5TXRuN20yQVZ6UW05dXJESXQiLCJtYWMiOiIzNjY0OWU2ZTc4YjE2MmRhZjllOWI5MWE1MjlhNTU5YmNlZTA0ZjI4YWQ0NWNlMmMzMDg1MzEyZTEwNDM1Yjg5IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:17:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhuTTczaUVpZWlZOUxKaHVONHpTaUE9PSIsInZhbHVlIjoiVWhtdlZNcUlpWld0WGhOai96MUZkL2Nsc2ZoRXNIZTNYWFJaSWYrNG9TZlFNZy9DMFJBNWplRGFXeU9VZ2t3bk4wdFlYYTdOUDdyRDFWbGdjS0ZLY0QrZ0NkbmRtaWVHQkc5NklGZ25KQ3JnTkIyRnV1MWxIQWIyc0oxb0UvWjJQNS9MV29GQTFJUnRIby96K2NtOE1mbXRmQnA3aVJncEgrWndBM08xeGtmd3VjQWI3TjdjNDNLaTROQkJZeDVac3VHc2s0eXJnOUhKYWN3TXR3cVF3Z0dtNjE1eWhHQXNCNCtoM0dOZW9oVSsrTGlqSXk3NWJtNkpyZWNjSFpPR0lPVHRtSThxMU5nZ0xXZURzQUZkdlZuOWdVK3hwRGl4Z1VTUkFOL2lPQ01WSVJyU2s5OXlFVElWa0NSN1BLaExTbTdyeTU4NG1BOEZQUWdHQjRBUUpyU3hpeXRVWlNPSE0yZUlkRXNPUUxuZCtaczN6V1VzemNmeHNFbFhuL2t2NXNHUXc1NHdrbUpwL1B3S21HU3E0M00wVFRQRHNpK0c4ODgwemU3OUtPVkUweEVFL2xVKzJSQTB4bjZtU2ZTMGtSbTZDTGNieXFiRzJvRjMyWkhTUk1QWGtocGo1dXg2TFVwSUtqanc2d0VOdzdYQTBJSGlaV05pMUs4SjczbGMiLCJtYWMiOiI5YmEzMmE5MzUwYzQzNzA3MWEzNWJmOGJjMzY5ZGE4YWZiOTU0Y2NhMjZjZDE5YTFiNGU0MzJiMmEwM2I2NmI1IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:17:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299467917\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-102961763 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JzkoNRilj8X8jCoyg1z635IfLcsWwRzk1Lbfl9Ft</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102961763\", {\"maxDepth\":0})</script>\n"}}