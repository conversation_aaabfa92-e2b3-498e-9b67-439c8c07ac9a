{"__meta": {"id": "Xdc380e0940f7e035bcdd1554d0013504", "datetime": "2025-07-14 18:58:41", "utime": **********.345655, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752519520.89358, "end": **********.345671, "duration": 0.4520909786224365, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1752519520.89358, "relative_start": 0, "end": **********.275796, "relative_end": **********.275796, "duration": 0.3822159767150879, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.275807, "relative_start": 0.38222694396972656, "end": **********.345673, "relative_end": 2.1457672119140625e-06, "duration": 0.06986618041992188, "duration_str": "69.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01934, "accumulated_duration_str": "19.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.303101, "duration": 0.01829, "duration_str": "18.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.571}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.330573, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.571, "width_percent": 2.74}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.336792, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.311, "width_percent": 2.689}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&date_to=2025-07-10&payment_method=&warehouse_id=10\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-144943043 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-144943043\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1601418557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1601418557\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-94208887 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94208887\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2057414145 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-10&amp;warehouse_id=10&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752519514732%7C56%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlE3NC8zM2ZOdG1KWDlZWmFMcmUxdGc9PSIsInZhbHVlIjoiUmRPOUIzV3ZRWEdpZ2VHWWkxdlVaT0JzU2o5TmhHbVByNVZpdGpaWXUvTE9MY3dvTTdRYm15Yk9BOFlvaGVZTU0rU3IvWjE4Vk1HazhXaDlGa3V6SUZOclQ4VUFMc0RaK1VlVCsvRld0TWNDZ0txenBUdmY1SHFUeU8yTDFwN3k0bkpPbWgxZ1ZrL1ZlcGw5Y3ZpR3hSZzJrckJiYTNjUXVaZmlZY0pBWTMvY3FSeGJsQk8vWHRVUGtKdUs3TjVuK1k4S2c5YzEwQ3N4Zmd3L3pjZjdrWTQxdmlhVmo3T29nanZvSmNkTUFLL1MzbmQzZkpIdWV1bmdjUmQrbU14eHMzSWo2QmcrR1NTVm94UVA4R1lqaHRKK1NpT3hCWGpQVVJtMWp3UXVMWGVmTWhGZGV5UjFjeW5xeGhEYnRyUkhPcVlzYmEvc0xXUXhRdGhtWTFzbE5uQzlrdkJGMURBbFFBSk1kQkdIRzkvVE1iRG56SENVR3ZEMThKZzF4S3owVDN4TGZhVGtEZlE5RWV2ZlU1SVJBYXUvWmQvendKZlVtcVZPTEJIWXFZWUpubWVaQzlxQ1g0ZWNQNXFvRzNlVVl5YjJ0djlHVE1xN3VaZHFIaU9sdUw5TDBBRkp4dnZHVXRXWHpjYjh2UElZS2swVGFFdFhaeGpSY1NCaU9xVjAiLCJtYWMiOiI4MWM4ZDFmMTViNTE1YmNmMzQxODFjMmFjNTI5MTcyY2I1MDg1YmMzOGEyNTNjZWJjNGZmNDgzMjU0ZTQwNTVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVyRDEvZzduendDZU9UMlhGd3BHdnc9PSIsInZhbHVlIjoidzdyejEyaGJqakZzUEdzS2Yvd3FlbVhmOUdCTUlZTVZ5YU9CU1JLWXBRenhHcHgwSW5BSUFnT3hSTTVhbkQvRGV6dGRNbE13S1JYNGZjV2JudkprRzlZR1VrWHhLd3Q2bmh1K0FRMlVpempUOXhmZHRjYWg4bjlQeUpMekZuMUhkM2h6a2NGYmpSSWkwQk8rdURyMElnK09wLzc4Q0NuemVTcjFsd0ZoM1ZGVUc4MjgyekpQTllOWmtJL0VKeW1GZnBYWWNYNjlPNEFwYlpnTGpVcERNbUI1cWE0MWpZbkJLR0JKSE1ueDd2S3NoRWdUZ2Vlc1NCNmdoNGcyK2JyUUIxbFNVL25ta2JZZWozcUlXT3FGWkV0RmxYeXV4N0NkQWJSK3dGMkxuK240Q0dzK0lIdGxQL0lKeHFWREF1Ym9QdVRQeWh4WFQraEdXcGNjQnUvYmF5eFRIMEU2SXVER2xTTndPRHUxd1I4YVNRVXg1Y1BVdUF4eU03cXplUDNHQVpEZ1Ntd2ZCQkFjYUpMZmdiRGNnU0xJKzhLQTZKcE9RN1FCV1laUVp2bEtjbEF3bXZVY2xzMzBBUE5XeVdFMGs4TDdmV3dnWUREVGpmZExXT0NKdzlWUUJNSVFiRUxmMDFqTUZ0d2FISkVLMDQxVExFOWFhQm1pVHkrQ2NLSUMiLCJtYWMiOiI1MTlkMWI4OWEyNGFlNTAyNzQ5OTFhZjkwZDMxOGNkMGJmMDM2OWZiODhmZmQ3Mzk1MGJkMTk1MjdhYThhYmMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057414145\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-119143928 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119143928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1049408135 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:58:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNPeldKOEdrSHhXREwzanprTDE2N3c9PSIsInZhbHVlIjoidFdmdzQ0RldGTmtyaEdDc295aEFjMlBjaEphT2hnYXJJMnRlcVRNY0hvaTl1Nks1UXd3dmp4SVBNSllqNmZvVXY0aHgrMzhhY1g2STF3VmpPZUlmZ09uRTVLUEZtellzQWZyYWFFcUlKcXdvUmZCNlh2VjNPb3pQakNDcUVxTTZzb3VLRm1kelFZN3E5ZzBWUmdYam1HTjEzWDRESTB2QjdTeHN3YlVINUJvOGlJNm1SQWhTZ3hhbjRBNWJaQ2NsL1dPbG9XbXdKbG1VUUdZeUpiT0NGZlI3QVNZd2VjK1RNT01MR0psakJsTzg3bnF0cUsrN01NWG00TjBDMTJ0N0tzbXp4ZWJvU1JUK3BBYTZ1M0U2aUhvTnJKOXBpUU1rOXUrZTBsZlNKV0xTSGFJZDRjeUxDT1BlL3R4YmNEV2FHdkJxNTlMRFpLc1hwNTVFODc5d3MrTjhFT3M0Y2cvcDRPeEJZRkJXQ3FKeHNoV0VLaEFSOHM5TVd2RkZvYmNteFVFNUVZSHpqYnE0SHJCZm1TdW1oZkxlN0pTdUh6Nkx0Q0t4eE9xQWszSEtJQlFaSUNnVFVLVkM5RlQvY0ZFUWxJSll0YWcyTnV2Qk9iK2ZkSGFzR3I1SEJ2bEd6SkxCQ1JlcndveGgvRmlYSFYraXoyQ1VuUnIzeDc1UFgwWkQiLCJtYWMiOiJiZDFmYzczYmY2ZmYzZjc0MzU3ZDRiMTdmYmI1YWJmOGYzM2I3MmQzMjcwOTA4MGYzZTRlODUxNjUyZWM5OWJhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjlkcEE1ZGw1UXRYYUJLZzA4eEIrNVE9PSIsInZhbHVlIjoiQzBGZjVvMzgyVmtFQ29sbjZSMU9Kb3E5M1NPZXgrdWtSNXlNeHZxWjJxODFqd0Y5OGZFNlFUVHlzRk1DTWFWUERFZmhHcU5jbXJVRG91a2RCc0hsOVJYZlRtRkNqOHRxYm9qN1ZURVozc3F6RFU1VkpOOTUya1V5eFhXOTNjK1ZsK1JSQWJkeVgzclEvaEtkNmdlelZRc0Nra0RzNzdTdGlwT3ZRSWMzUDJDWTNlckd3enVwS2l5VTNMWjdaVzhDWUNXU2xjamYrV2N0NngvRGV0YmYrcUtVa0hBYi91STV5cVdrS2J5UUFqZXB1ZCtkMnY4S0pmTUh6UlluZVRpZVJwZzh0RStOQnFmMEZhYjRTUTB6YUxtZVFPLzJuL0YwSVRYNWZvNDJrS3ZHWnNrR2lDQ2pub1Yxb3RIeklnWjUzRGo0Nzh1SEYzdGhkQ05qOUtHd0pXOWZDemtRdThkbUFUWk8ya0oyZlc3MFM1WHVWWXlLQ1V3QVpJaWF6SVdXY2l6WnRJcG9nNW9RcXNFSUpZRFYwTGcwekpTbnVQWGxEbWVkRjliUUFlTUhsUTRvSUl0TXB5SXcwc2NBUTRHZ1RGWDB1TXp3dW90TVBUbkV0c0VoUXdlZWpiTkJBMHU5ZmJLWmdWK052VmFQdWVuY0Z6Zm54a1ZGWTg5anoxa1MiLCJtYWMiOiJjZjIxYjZkY2JmYTJkMGRjNTkyMzZmNDczMjIxODNkOTc2YjdlYjZjMDRlZDRiMjVhYWI2MDNmM2IzM2IyNTQ3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNPeldKOEdrSHhXREwzanprTDE2N3c9PSIsInZhbHVlIjoidFdmdzQ0RldGTmtyaEdDc295aEFjMlBjaEphT2hnYXJJMnRlcVRNY0hvaTl1Nks1UXd3dmp4SVBNSllqNmZvVXY0aHgrMzhhY1g2STF3VmpPZUlmZ09uRTVLUEZtellzQWZyYWFFcUlKcXdvUmZCNlh2VjNPb3pQakNDcUVxTTZzb3VLRm1kelFZN3E5ZzBWUmdYam1HTjEzWDRESTB2QjdTeHN3YlVINUJvOGlJNm1SQWhTZ3hhbjRBNWJaQ2NsL1dPbG9XbXdKbG1VUUdZeUpiT0NGZlI3QVNZd2VjK1RNT01MR0psakJsTzg3bnF0cUsrN01NWG00TjBDMTJ0N0tzbXp4ZWJvU1JUK3BBYTZ1M0U2aUhvTnJKOXBpUU1rOXUrZTBsZlNKV0xTSGFJZDRjeUxDT1BlL3R4YmNEV2FHdkJxNTlMRFpLc1hwNTVFODc5d3MrTjhFT3M0Y2cvcDRPeEJZRkJXQ3FKeHNoV0VLaEFSOHM5TVd2RkZvYmNteFVFNUVZSHpqYnE0SHJCZm1TdW1oZkxlN0pTdUh6Nkx0Q0t4eE9xQWszSEtJQlFaSUNnVFVLVkM5RlQvY0ZFUWxJSll0YWcyTnV2Qk9iK2ZkSGFzR3I1SEJ2bEd6SkxCQ1JlcndveGgvRmlYSFYraXoyQ1VuUnIzeDc1UFgwWkQiLCJtYWMiOiJiZDFmYzczYmY2ZmYzZjc0MzU3ZDRiMTdmYmI1YWJmOGYzM2I3MmQzMjcwOTA4MGYzZTRlODUxNjUyZWM5OWJhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjlkcEE1ZGw1UXRYYUJLZzA4eEIrNVE9PSIsInZhbHVlIjoiQzBGZjVvMzgyVmtFQ29sbjZSMU9Kb3E5M1NPZXgrdWtSNXlNeHZxWjJxODFqd0Y5OGZFNlFUVHlzRk1DTWFWUERFZmhHcU5jbXJVRG91a2RCc0hsOVJYZlRtRkNqOHRxYm9qN1ZURVozc3F6RFU1VkpOOTUya1V5eFhXOTNjK1ZsK1JSQWJkeVgzclEvaEtkNmdlelZRc0Nra0RzNzdTdGlwT3ZRSWMzUDJDWTNlckd3enVwS2l5VTNMWjdaVzhDWUNXU2xjamYrV2N0NngvRGV0YmYrcUtVa0hBYi91STV5cVdrS2J5UUFqZXB1ZCtkMnY4S0pmTUh6UlluZVRpZVJwZzh0RStOQnFmMEZhYjRTUTB6YUxtZVFPLzJuL0YwSVRYNWZvNDJrS3ZHWnNrR2lDQ2pub1Yxb3RIeklnWjUzRGo0Nzh1SEYzdGhkQ05qOUtHd0pXOWZDemtRdThkbUFUWk8ya0oyZlc3MFM1WHVWWXlLQ1V3QVpJaWF6SVdXY2l6WnRJcG9nNW9RcXNFSUpZRFYwTGcwekpTbnVQWGxEbWVkRjliUUFlTUhsUTRvSUl0TXB5SXcwc2NBUTRHZ1RGWDB1TXp3dW90TVBUbkV0c0VoUXdlZWpiTkJBMHU5ZmJLWmdWK052VmFQdWVuY0Z6Zm54a1ZGWTg5anoxa1MiLCJtYWMiOiJjZjIxYjZkY2JmYTJkMGRjNTkyMzZmNDczMjIxODNkOTc2YjdlYjZjMDRlZDRiMjVhYWI2MDNmM2IzM2IyNTQ3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049408135\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-364733900 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"125 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-10&amp;payment_method=&amp;warehouse_id=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364733900\", {\"maxDepth\":0})</script>\n"}}