{"__meta": {"id": "Xb875a498da3a2f0634705a1a3212f0b0", "datetime": "2025-07-14 18:29:33", "utime": **********.193227, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517772.740945, "end": **********.193243, "duration": 0.4522979259490967, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1752517772.740945, "relative_start": 0, "end": **********.137145, "relative_end": **********.137145, "duration": 0.39619994163513184, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.137154, "relative_start": 0.3962090015411377, "end": **********.193244, "relative_end": 9.5367431640625e-07, "duration": 0.05608987808227539, "duration_str": "56.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46005816, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031000000000000003, "accumulated_duration_str": "3.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1688602, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.806}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.179738, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.806, "width_percent": 15.806}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.18573, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.613, "width_percent": 18.387}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-630419712 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-630419712\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1724884517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1724884517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-20012913 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20012913\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-198834667 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517770707%7C34%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVQdE53d2FUTnl3YmZwNEJDYVVxOGc9PSIsInZhbHVlIjoiNjZlR0xnbytNZU84dWNWSUxkNW5uY2xWaldMdktxbkp1QmpGVjUwUjJSRlpvRGd1TmZURllBZm8zM0gzcGR6WldJRlg2aStPbDc0ejBBU3JxMVIvbTVPbmswMERhakU0UitTT3JsOGJCcHVZZTY0UXQ1dTNJTUNpZ25zYy9aU21uWW9ucTdmc08yTnRPU21lTk1oQ3dUb1loQkkvOWw4eHk5b1ZiWWRWQi8yM2Nja0k0TFplNEJ4a2pRbG5wcTQ4ZXpyaWl2ODlMVjdFb2hiYkZTMVc0ckdXeEFyNkhzbGN0V0M5TENkMm1aUys2WWtLMWFwUGlOSXpoOVRDcTUxVW1uK0JUOXQ0b1VpR0ZqT2cveGRvOG9Fcks3WWl3UHc1WWxQYm5ZNm1NZGFNLzl3bmZBMmxIcVNFNlNxL0hjckdqNkYrNVhVUWFTNlkrN3o2Tm5Qc2JneEhyWW9vdUpiYklKTVBjK2lSaWwzR0JNa2ZJMFpRU0xwbkV1Y2ZBRDRJOGd5ODBVRVhTMHNibVV4TUx4QzE2VmJyK3h3RC9tV1VZS213cFJaUENYTFlpSVNCcERyKzRBVnBTNzFJYWVtMG5FWXAyZFdUTFdyK3EyM3V4NVVaSUMzOWtEMG10VHg0ZlZRc2p1K1poOHFaamE5aCtFcjdHWTBuaGNCYy96VSsiLCJtYWMiOiJjMDE4YTk0OTQzMGU3MTNiMjg0ZWM3NzA4NzEyZWU3YWE1ZGFkNjUyNzVmNDhkOWZkZmJiZTZmNjg1Y2JlODEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZxbXZHcHM1dmJpaEsyK0gwRWM1S3c9PSIsInZhbHVlIjoiUC9DSGhjQWJrWnhKRGdTa1R5d3hraFhtS3RpZnlXNG53TVZ4dktxTFhJUi9ZUFJxZVFRRWIwME5ZZkJoNkFmZm5oRmR0OEEyUnBuVzBndVIzOVd3dmhRUm1GUHRHdlJDeGx5YytLcU82YjBvaFpaVExzdmpwUEhzd3NMandvbGsyR0hJS0EyRE94V3Fhdi9LTHUxNjlQc0d0N2YvWWFwQndjZU0xZExpN0wwc2diS1ZrU1UxUGFUQnFRK3BWNnpSRDZxRmtOM2NhL3hGSVUxenBIUHJDbUpkSWIrVzRwMFlVOUZJV3hncWE1R3RXSWh5SlA0MCtlQ2RIN2FvcTdMMHNzcEFKdkJuazB0S1VHWWpKaVdmRDd6YkdkZVB3bDNBYjhjWjBuajZFaFlHRW5oZXpMSkdzdndCVHhlUHdVNWVaRCtJSHlkT1BzeW5oNWgyZVN2NlprdDcyZE5lZklrMmFVeDhvWXFUekZrWmpHbEVuSXAybldDMENyZWpyWHN6STBVamU4TUg0Q0JwQXpEM1NCSzNKVlVlOXorenRrNExMeWJlQUlXYTNUaUJUN0FPRlFpYUN6RjNFUVpBdTRWeFAyVVRMRnVsZklxa3ZTMlloM1JDTi9HM1o2R05XNTV4Nk9JSjhHV0FSNWRMcW5xTGxUNHFneU14N21sc2d1VnUiLCJtYWMiOiI0NmZhOTNkMjk3MDU0OThkNmJjOTAzOGRhMDU0Njg1ZGI1MTM4NzliNzZkZGU1MTdmZmM4N2UyYzk1MGY2MTgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198834667\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1423901956 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423901956\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1448531169 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpzYWsvWWg3YzVPVTY2cjdFcGU2a0E9PSIsInZhbHVlIjoiMUZHclgrd005UnRIWldjc29KdVliWWxXRGl5UWx2RE5SU1lpR2ZzRnJYY20yaEV0TGFsczJheVdvczBPUkd4QTY1V1ZiOU1RdUVNNVhWM2EzbFdEbFlRR3VVbnlJY0JZN0JzRGJBVnlZQ0FkRDNpenVibHhtVUM4SlY4NmF2KzlJSW0xTm4xN3RVbnk5ajRIL3RNbm84eEpxbGpaeGtVcXJqZ0VNdXBncjlBV012dWZsd3hxaFdWVy9RVXBqNC85Y3p3a01sYVAvNlhMOGVWenZJV0lOUzlXaXBhWVpPMzZuQ0tpaVNzVXpvWFB3UllTaWNjOTlrc1JVaVpMMkxKdDFxS2ZZSTFUdXVlZDZNdkpvcVU4OHZselByZnFYdCs4Wncycis3Z2tJMndBdFJDZzVFZmFtOGRpMCtwcytnakZ2cXJkUm5YWm5aaEd1RHFvUHhWWlNlUHNSME9iZ0lmMmN5L3B4cWtyKzYza1J4ZnBzVy9Zc3lsbFY5TFMrUUJGQnl5bTNyMDlPUzIxcTRsTzl6M2hFRzJxc3Y5Q3pKUDNXUmZpVHoydE1TY1pIS3daT1VmRWtDWnZHSzIrS01CNUFGSkF1YW44YVZsUjZqU2drR3Q1d01MdnVIaVlydFAyditoNUNVY00yMmIwVXpnSFdUbmRoZk1tRUQyMTlQTE0iLCJtYWMiOiIyYzY2M2ZjNzc3MDk5ODMyOTFmYzIyOWI1OWY5Mjc2MDM5MTU5ZGQ4YjE0NTViN2ExM2ZhNzUxMGUyMTg3ZjNiIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRkZlNSM3BzMVRBMHE1WERsNzJtOUE9PSIsInZhbHVlIjoicDNyazVJcnlyVmZzQ3NSNHJQaVB0VnlLUlVjTDUxMGd0ZEM4aDBNYVVwVytwMk9oTUphNmZpNFJDdG1hZHNRaFdKczFvNWhLdWluUTdOZ2htZkUyQm51NCt4Q0Z4VjE5bmhCbVpGYjAyNk1pemp6c3Z1cnR4dmRldzRPa25Sb3hiR0VULzdDRkxjeHVTV0srS2lmWGRVNTJ0UFBMa3NVSGo0NmJ4WDAybEYxVFAzRVpLQSt5SzJpMlFhVVloSXc3NDZSS1FBbzhhZWFFby9qbEc1TEt1YTZ1ejFzYjhacmx5a0p6bmRmc0NwM3k3aWp1Q0p1eEJFOGNWbTIvUGg5K2R2L2RzYUZuMmlDOGlTMy9GcTBiN3ZETm10MXF6R3N4dlZFMTNCZjUwM2FaNmpZTUN5NE1IU3N4L3RnWEQwTkc5cTF0a3R1RTNjcVYzamNmb0tjQUNkWit6UUJJemUyaFVqcjEyeENpUXhRTUVyNzV5RkVnQm5od0RHcWFuYlJGRC95MExReG82VGRtS0s0M1ZtdjN5ejIrM3hKUWJrUFlubm14V0d5RC9tTjJvTUlIQk14ek9kUi82QkFpQlg0dXlHQlAySDVoQkJhalQ3NUF4OEw5b01FZVY5N1pKRDVnZlBLSjh0ckx1Y29MVXRiZHZPc3A4WCt2aTdVRS9hZjciLCJtYWMiOiI2NTNjODFhOTQ4YTA0ZTE4NzU4Y2NlODUyMjUyODdhYjZiMzYwYzI1YWU1Njg3OTkxOTRiZmM4ZjFmZTY2MDEwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpzYWsvWWg3YzVPVTY2cjdFcGU2a0E9PSIsInZhbHVlIjoiMUZHclgrd005UnRIWldjc29KdVliWWxXRGl5UWx2RE5SU1lpR2ZzRnJYY20yaEV0TGFsczJheVdvczBPUkd4QTY1V1ZiOU1RdUVNNVhWM2EzbFdEbFlRR3VVbnlJY0JZN0JzRGJBVnlZQ0FkRDNpenVibHhtVUM4SlY4NmF2KzlJSW0xTm4xN3RVbnk5ajRIL3RNbm84eEpxbGpaeGtVcXJqZ0VNdXBncjlBV012dWZsd3hxaFdWVy9RVXBqNC85Y3p3a01sYVAvNlhMOGVWenZJV0lOUzlXaXBhWVpPMzZuQ0tpaVNzVXpvWFB3UllTaWNjOTlrc1JVaVpMMkxKdDFxS2ZZSTFUdXVlZDZNdkpvcVU4OHZselByZnFYdCs4Wncycis3Z2tJMndBdFJDZzVFZmFtOGRpMCtwcytnakZ2cXJkUm5YWm5aaEd1RHFvUHhWWlNlUHNSME9iZ0lmMmN5L3B4cWtyKzYza1J4ZnBzVy9Zc3lsbFY5TFMrUUJGQnl5bTNyMDlPUzIxcTRsTzl6M2hFRzJxc3Y5Q3pKUDNXUmZpVHoydE1TY1pIS3daT1VmRWtDWnZHSzIrS01CNUFGSkF1YW44YVZsUjZqU2drR3Q1d01MdnVIaVlydFAyditoNUNVY00yMmIwVXpnSFdUbmRoZk1tRUQyMTlQTE0iLCJtYWMiOiIyYzY2M2ZjNzc3MDk5ODMyOTFmYzIyOWI1OWY5Mjc2MDM5MTU5ZGQ4YjE0NTViN2ExM2ZhNzUxMGUyMTg3ZjNiIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRkZlNSM3BzMVRBMHE1WERsNzJtOUE9PSIsInZhbHVlIjoicDNyazVJcnlyVmZzQ3NSNHJQaVB0VnlLUlVjTDUxMGd0ZEM4aDBNYVVwVytwMk9oTUphNmZpNFJDdG1hZHNRaFdKczFvNWhLdWluUTdOZ2htZkUyQm51NCt4Q0Z4VjE5bmhCbVpGYjAyNk1pemp6c3Z1cnR4dmRldzRPa25Sb3hiR0VULzdDRkxjeHVTV0srS2lmWGRVNTJ0UFBMa3NVSGo0NmJ4WDAybEYxVFAzRVpLQSt5SzJpMlFhVVloSXc3NDZSS1FBbzhhZWFFby9qbEc1TEt1YTZ1ejFzYjhacmx5a0p6bmRmc0NwM3k3aWp1Q0p1eEJFOGNWbTIvUGg5K2R2L2RzYUZuMmlDOGlTMy9GcTBiN3ZETm10MXF6R3N4dlZFMTNCZjUwM2FaNmpZTUN5NE1IU3N4L3RnWEQwTkc5cTF0a3R1RTNjcVYzamNmb0tjQUNkWit6UUJJemUyaFVqcjEyeENpUXhRTUVyNzV5RkVnQm5od0RHcWFuYlJGRC95MExReG82VGRtS0s0M1ZtdjN5ejIrM3hKUWJrUFlubm14V0d5RC9tTjJvTUlIQk14ek9kUi82QkFpQlg0dXlHQlAySDVoQkJhalQ3NUF4OEw5b01FZVY5N1pKRDVnZlBLSjh0ckx1Y29MVXRiZHZPc3A4WCt2aTdVRS9hZjciLCJtYWMiOiI2NTNjODFhOTQ4YTA0ZTE4NzU4Y2NlODUyMjUyODdhYjZiMzYwYzI1YWU1Njg3OTkxOTRiZmM4ZjFmZTY2MDEwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448531169\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-841328532 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841328532\", {\"maxDepth\":0})</script>\n"}}