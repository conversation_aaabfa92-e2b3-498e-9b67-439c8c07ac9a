{"__meta": {"id": "Xbd57bcf61c4734dd9d3a9d143f404e9d", "datetime": "2025-07-14 17:58:47", "utime": **********.577648, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.114661, "end": **********.577665, "duration": 0.46300411224365234, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.114661, "relative_start": 0, "end": **********.504347, "relative_end": **********.504347, "duration": 0.38968610763549805, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.504356, "relative_start": 0.3896949291229248, "end": **********.577667, "relative_end": 1.9073486328125e-06, "duration": 0.07331109046936035, "duration_str": "73.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46004256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020449999999999996, "accumulated_duration_str": "20.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.535199, "duration": 0.01952, "duration_str": "19.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.452}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.563533, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.452, "width_percent": 1.858}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.569632, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.311, "width_percent": 2.689}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer/eyJpdiI6IjBab0hPaTJkODVjNlBNVzVHajRKUWc9PSIsInZhbHVlIjoiVjVzVmpCRkNkNDU0UkxRMUlTckxWZz09IiwibWFjIjoiNTc4NGUwMjdiZmE2ODA5ZjU3NGM3ODNhOTY5MGExNjA1ODcyMDUyNzA1Yjk4NjRlMDU1MzA3NTRjZjlhNWFmMyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-44675279 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-44675279\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1413984794 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1413984794\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-57985901 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57985901\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1246000772 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6IjBab0hPaTJkODVjNlBNVzVHajRKUWc9PSIsInZhbHVlIjoiVjVzVmpCRkNkNDU0UkxRMUlTckxWZz09IiwibWFjIjoiNTc4NGUwMjdiZmE2ODA5ZjU3NGM3ODNhOTY5MGExNjA1ODcyMDUyNzA1Yjk4NjRlMDU1MzA3NTRjZjlhNWFmMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515925285%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlGbG0wem1UbnJBUlNGUnlwb3NIL2c9PSIsInZhbHVlIjoiZlJpWXlxbFd3QnppR3RYNFAyKzdsN0cwWjlmYUY4SmFqMVZvS2Qza3lnMHYrTW1BNzUyaEJqRkpHZEpxek1UNGVrci82Yk1qZ0tFblRwaTg2aWtTSWJHblNQMXluMXp6endPYmk1TmZ2Z1owRWhIbUlNMmlxYU1JNEd5UjF0YVlXWGNkM1VCai9NdmltL0FhR3FQb2swcXdSenFzSVU1b0M2ZmJsbVVoaDMraXg3NmFoTHVJQUxmU2xiMTBNeFJ1WEVUS3M3V0VoVUludHNIS0EzODdJbkw5bDQ4Q2J1aXFrenErVE9uSnJMTERiV3BoY1dFcmxyVStENFIwOTF2cmR4bzdMR0RrQ2ZrV0w4bmVreHpaYWtCSGNsUm9IOHEzTzgvRG80ck1hVTF3cmFnaWdmbi84VzJJc0M1NHRUc04zZ0tZQ2RBb1JXZnJlcENPck5DdnVuU1QwUnhYYVZwY0k0b2NGVE9TR0tONGZhbHE5K203MG8rbTdZb21RYzNZMm03aTdaWHRGQmRiVlp3b2N4cWpvRnN0TFBFdGljNnMvZlhRQzlSYktjcmV5RWtZT0dYcDJVV3RBK3dBT1ZBWUVHc1RKSnFyM2Q0a0V4aWtNUkZZR2NYdTMya0JVUHFhNWJaNzA0cWdvV1NMckxXRGFWMGc5WnFwbCtNNjFtS2QiLCJtYWMiOiIwMDM5NDVmYWM1YmQ4MzM3NjhmMWRlOTU3NmFjM2YxNDNiMWRkMTNkNjcwNzAwZTUzNmVlOTg5NWViYWNjYjVjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdrYnlvM2xwSDdBVHFsaTdYTFVwRmc9PSIsInZhbHVlIjoiZ2k3ZzhWSklNUjA2a3RSR2pkRlhrZTAwVEsyZVI4WThTdG1GQXZ1WG1GYmZ6Qkt5cFVDdzIycnIxSVdCRnlnL1J2eW9neXZPaWR5b2lHYVFUT2lhTHlFVVBUdjN4ZmtVWGE0SmxUK1ZhamZXQXhhT0ZhYktnYlRhQmplTGRSUmlWR0dwZmdPeTVTUkljV3J4Y0p4MnQwdUFIWnRGMFlzeUgvT2MvczErSzAwVngzOE9BZkxISGdwN3VTZ2wzcDg4RU5IaE9kbk5yZHkwNU0yZGN1TVYzUGd4Ym1WTEYyTndZQ0RjbGVXcDR2Z1k3NGR0YzY5SVN4TGZSU05LOHJqaVdLRldDRlU3RzZacHl0a0dyT2VHU3ZhRzNua0duRW0wMG1FRXdzWDNjY2JSaWRpRmhYUjIrUlNZUUdoaHE5aWVkQXRDN2xVWVBRVngxbmhLTTMrVWxCT2JXcDRFV3JIMVdhUldtY1lGSTRENHA5R2w5RzAzSUhDTmNpZ3NNdjFtVzNaZllIZUpRdnY3THFCQWVaRVplTjY3Z213RDVyQnk3Y3k3VnpBWStYWW1reUl0QVJLeDlNdHMrNmxhOEJjZ3VPNnh6VjJWaDJOQTNoeS9leWRZYnZYUm5kMGt6aDBQMFVqM1pTamVvelhRdHMvNHd5b1VQQ1B0WTlnTGRQY24iLCJtYWMiOiIzZTdhNjI1MWQzNjQxY2RiM2I2NjczZmY1ZTI4ODVlNDdmZGI0ZmNkZmFjMDI2NzRmODJmNDhkMWEzNjAwYzk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246000772\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1894174229 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894174229\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFVOFVWZE5QcnVXRUY3QS9rTUVTY1E9PSIsInZhbHVlIjoic0hKdkFZMThrZk5vcmdMamszNVRyTW1GNG5JYWUwWmR1a1JMM0lRQXBLaFVvZk5MOWhESE5vVWJlUm54UXNMRVpDUlhtMG94RDZIVmhpSHJuazZJZTU4Vy81RmZQTWE4S3B6Zy9mRWtMNEZWU3B3bzFnMjdvV2FEMUY0cTQxc2d2NzU2c0pUWHZkQWxGZDhGVms5VUxzcU1LVjlZQ1ZxcVg2Ym42OXorQUc0cHRNSVhsOFArWmNQM094VWY0UG16akM4bmRMMTFrMFVlbkY4RGhyYkJYM2FTK0lYWkxvaEI5R0ZzVzJnZVlIVVhZTENWWWNiQmpja3A4ditYU1FiK0JLeHB4T1VuM29RRWVQZUxZVmUwU2Nnb0FXeCtYU0lUYy9FbVlQTStSMmVZNWgrbCtqNHBCVzN3djIxMThXSUxQVVFrNVpJZWVKQlIvTmlUU2hGQ0p1ZHVPenpuZjRudWVGVmYrMVNUVHQ4emMxbkJuK1p4UzRZcTBkSGhUSnVyUkU3STkwUnNoWmpLWGx0N2ZrZFArWGlMUnpHUDYvK2pEa2wvSFkvcjlpZlg4MVhycERXN1FENm43ZXVhenZVRVZjUFAvdlJNaGZqN0xJMm94Um1KYzBWZjdxc2M1d2wzZktncjNEN3RKQ0NmQkxmOGM2bWZJaHNrRFFyQWR6eWciLCJtYWMiOiI2NGUxNTcxNDFlNWZhMjUxNjJmY2RjMDNkM2JmMmJkZjFmMDdkZmVlY2E5ZWYzODFhYzdmYWRkNjgwZWRmZDkyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVuWC9IUFRodTE0c051NzlDa1k5OFE9PSIsInZhbHVlIjoidDYrZ1VMNko5V25kVkt2RlMxZGpYaFAwbzdDWFpWK0piTElrQUxVZU4rRnhGeWVNdHI0OG1jS2RjeFJXNDA5dWs0UnRldDh2d1FKUGZsRm9PUk5SMHBXelBPblBOaUdURFdWUTlISldLUXhQUmowR1RzbCtRZ3Awa3dOM0hZckJSem9Sd2tNWWVNL3NZUmhKM0o2UkFtL1FzZXZGZU1GWjUwdzRBeUdaTlhCR0dYVncxem4rVU1PT0N5SnZIb2ZlK01ZT0dWN3JCTnJRTUtPV0xvNWpjeHNWZ3ZUQTViZGwwRGc2b2Rvb0l1aitTdHdNVFYrU0RkNXVIMEdWWVluQ1VqeXhaN2liL3BQUkVqVnZiTUhKcmloN2xNcVJOVElXUkZGRXRjeDBMWTJtMGJkR3dSVFo1NnhsNHg4SDJ3bzh0aUJRWDRoOCtpUDdVUkhPbHJlczBJdk1zN2VUZDZ3TmhxYmdrcjFGUjNROFB6SnI4cWVIT3pMN3VhNWZ0OEFCZVUvaS9xVVRTNTNGVEhiUDBGejZRWGQ1dlFiR3lvYWQzb0FIc0FBQ2JuWUJiZTRReFBlOGpzVVhEcEg1dzAxVTl5ZnB5cVR1VmZHOWtXYzBNN2IxMTN4ejdFdURnMHQvS2RYMWZsRi9MWHZnbmtvbjRJM01Ya1o2dDN5KzQxTDMiLCJtYWMiOiJlYWNhMWYwZjI5NmYxNzYwOTRhZjkxMzI0MTVjOGViMGU3NDA1ODhjODcwYjcxYzU5MDNkMjA5Y2NmMDk1NTU5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFVOFVWZE5QcnVXRUY3QS9rTUVTY1E9PSIsInZhbHVlIjoic0hKdkFZMThrZk5vcmdMamszNVRyTW1GNG5JYWUwWmR1a1JMM0lRQXBLaFVvZk5MOWhESE5vVWJlUm54UXNMRVpDUlhtMG94RDZIVmhpSHJuazZJZTU4Vy81RmZQTWE4S3B6Zy9mRWtMNEZWU3B3bzFnMjdvV2FEMUY0cTQxc2d2NzU2c0pUWHZkQWxGZDhGVms5VUxzcU1LVjlZQ1ZxcVg2Ym42OXorQUc0cHRNSVhsOFArWmNQM094VWY0UG16akM4bmRMMTFrMFVlbkY4RGhyYkJYM2FTK0lYWkxvaEI5R0ZzVzJnZVlIVVhZTENWWWNiQmpja3A4ditYU1FiK0JLeHB4T1VuM29RRWVQZUxZVmUwU2Nnb0FXeCtYU0lUYy9FbVlQTStSMmVZNWgrbCtqNHBCVzN3djIxMThXSUxQVVFrNVpJZWVKQlIvTmlUU2hGQ0p1ZHVPenpuZjRudWVGVmYrMVNUVHQ4emMxbkJuK1p4UzRZcTBkSGhUSnVyUkU3STkwUnNoWmpLWGx0N2ZrZFArWGlMUnpHUDYvK2pEa2wvSFkvcjlpZlg4MVhycERXN1FENm43ZXVhenZVRVZjUFAvdlJNaGZqN0xJMm94Um1KYzBWZjdxc2M1d2wzZktncjNEN3RKQ0NmQkxmOGM2bWZJaHNrRFFyQWR6eWciLCJtYWMiOiI2NGUxNTcxNDFlNWZhMjUxNjJmY2RjMDNkM2JmMmJkZjFmMDdkZmVlY2E5ZWYzODFhYzdmYWRkNjgwZWRmZDkyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVuWC9IUFRodTE0c051NzlDa1k5OFE9PSIsInZhbHVlIjoidDYrZ1VMNko5V25kVkt2RlMxZGpYaFAwbzdDWFpWK0piTElrQUxVZU4rRnhGeWVNdHI0OG1jS2RjeFJXNDA5dWs0UnRldDh2d1FKUGZsRm9PUk5SMHBXelBPblBOaUdURFdWUTlISldLUXhQUmowR1RzbCtRZ3Awa3dOM0hZckJSem9Sd2tNWWVNL3NZUmhKM0o2UkFtL1FzZXZGZU1GWjUwdzRBeUdaTlhCR0dYVncxem4rVU1PT0N5SnZIb2ZlK01ZT0dWN3JCTnJRTUtPV0xvNWpjeHNWZ3ZUQTViZGwwRGc2b2Rvb0l1aitTdHdNVFYrU0RkNXVIMEdWWVluQ1VqeXhaN2liL3BQUkVqVnZiTUhKcmloN2xNcVJOVElXUkZGRXRjeDBMWTJtMGJkR3dSVFo1NnhsNHg4SDJ3bzh0aUJRWDRoOCtpUDdVUkhPbHJlczBJdk1zN2VUZDZ3TmhxYmdrcjFGUjNROFB6SnI4cWVIT3pMN3VhNWZ0OEFCZVUvaS9xVVRTNTNGVEhiUDBGejZRWGQ1dlFiR3lvYWQzb0FIc0FBQ2JuWUJiZTRReFBlOGpzVVhEcEg1dzAxVTl5ZnB5cVR1VmZHOWtXYzBNN2IxMTN4ejdFdURnMHQvS2RYMWZsRi9MWHZnbmtvbjRJM01Ya1o2dDN5KzQxTDMiLCJtYWMiOiJlYWNhMWYwZjI5NmYxNzYwOTRhZjkxMzI0MTVjOGViMGU3NDA1ODhjODcwYjcxYzU5MDNkMjA5Y2NmMDk1NTU5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6IjBab0hPaTJkODVjNlBNVzVHajRKUWc9PSIsInZhbHVlIjoiVjVzVmpCRkNkNDU0UkxRMUlTckxWZz09IiwibWFjIjoiNTc4NGUwMjdiZmE2ODA5ZjU3NGM3ODNhOTY5MGExNjA1ODcyMDUyNzA1Yjk4NjRlMDU1MzA3NTRjZjlhNWFmMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}