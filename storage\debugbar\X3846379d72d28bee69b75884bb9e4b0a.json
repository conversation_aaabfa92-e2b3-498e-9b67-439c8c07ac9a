{"__meta": {"id": "X3846379d72d28bee69b75884bb9e4b0a", "datetime": "2025-07-14 17:58:53", "utime": **********.274094, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515932.852786, "end": **********.274109, "duration": 0.4213228225708008, "duration_str": "421ms", "measures": [{"label": "Booting", "start": 1752515932.852786, "relative_start": 0, "end": **********.22145, "relative_end": **********.22145, "duration": 0.368664026260376, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.22146, "relative_start": 0.36867403984069824, "end": **********.274111, "relative_end": 2.1457672119140625e-06, "duration": 0.05265092849731445, "duration_str": "52.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46005816, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036099999999999995, "accumulated_duration_str": "3.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2490032, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.773}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2597702, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.773, "width_percent": 18.006}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2657359, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.778, "width_percent": 20.222}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/proposal\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1718939940 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1718939940\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-179755472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-179755472\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1887631066 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887631066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1665877275 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/proposal</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515927620%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndFekVQSXM4Mm5SUnBMMVJkS3NmUmc9PSIsInZhbHVlIjoidmpYUzNTTGdrWmtFS1VibTFKWjMyZEFwK3F5R05LSldxR09oY3crUFpHODdxZkdhVzF0SWd1S2ZRcWVtS3JoUXpveW5vMERSbjR5YytTTGlENU9rZHM5NUh6WjNMWWs1UENyK0ZlU2VUeFYxV0ZIQUpRbzcxeEcxRERXemZJOEtIZ2E2dzNhTnF2MDRxcENNeWxjbGFBcEIxd25hejZZK2xSQlZpY2xFYzNLVmt3NU1qRlVmRkJ0b3hKRG5xNXhYdW15SE55cmx6aXk5SUdVUXo0SHBYQ0JIUS9jWUE1VWtYM2F6UEFEdCtFbEJaM3JlR0c1Q3JFN29yVll0b2RZT1BMTmQveWFETHF5Sm8waFlScDZpUG94VitkVkZGT3dlOXVuUUlaNE13dk1ER1RCMk1MUHhxaFpGeVJFN1ZCYVk0Tit6Ynd1OWs2TXdvYnFRVWEySEw5UUI5aWZ3Y0hFUFVyM1ZXY3MxM2RKWnRxcHFOOWYrNU5SNDI4MVpoc2ZCNmtDMHZaTVBjb3VPd1BrVGJuYzJ4cEJTQnhOMC9JbVhwVk85bnVLZGVGMmNxMzM1T3lkL2VZb3hHcFNkRHN5dS9qSUV5bEsrVGR6YU5YcHpXR0tSbmlFQWxCMlZsUDlEaTgvT1BUZ2FBem5HeFRSZEx2cHhzZ0llT01JYjZvaG8iLCJtYWMiOiJmZDM4ODA1NDFiMjY1OTIyOThiNTExYjQwMmM3YTU5ODVlMmViMzRhNTU4ODM4YzkwNTExMDVkOWM1MGRhZmM4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldZbHVHallrTEMvMkJkclFpTThybHc9PSIsInZhbHVlIjoibFg0WElVU0RXbkFIdnU3MWJmZE9kRUVxczBkVW5YK0tZQjdyRVFoa2VsV3hwcUpYY2xjQUdSRlpROFFqM0N5UjFZeE1weTBnZ2hUVnk5V2hGVklqN3VLTHJhWHZaUDdGVlZzeU1iUFZYNUVZN3JOdExlYVdSYUMzUGFCWDJ6bGhYRjZlVXhIcXErVUY4SDdRZnU0akF0TTh5NHZibWp5VnFHWmlKRlgxT1E2MWljSHUzWFNNdHAvbGU2bjNKOHNsNU1PV2tLbjZHWjVhMjFEU2I1QVQrYWthc2I1TmJWeWNBaGxBYVZjTWxJekQ0MFhLeS9DejYrNW05a3M5dUtRQVViZXZSL2FwQ2hTOVArd01pTnlpZ3EzT1ovQVVjMy9XdldVVFJwVmNlNmE4MGlSVy9ybC9KK25vUkQ4ZXZQcmZvTHVLV3NlMHJZWC9PUWNVcUZaVmpDanRscklHNEZOVEpuR0EwQlRQck9uOXRRSzNjRTJYOC83YU1UU2JpSmhCWnRnMkdCZlp4WmFEMmJkcEpEbkszSEQ3VzBoaHlwV3J4SXJNaUk2VVdvNmtaMWRtUTBJZXkzVTJKYkd4elhWM0U0K0ovZkh0Z1RKQThHZzNuazNJUVF1dG9IU2FCUVFiMHd3a1JqUGZSK1A5ekRTOUk3cWh4UkZEeDA5aDhid2QiLCJtYWMiOiIwYTNiZmEzY2JmYjgzNzZjOTNlNjBiODUyZjljMzI5ZDQ0Y2JmMmI1MGRhNmM3NjU3NzVkNjU2OTRjY2QwYTk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665877275\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1584649700 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584649700\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-484740778 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImR4RXZXazFWVmo1dGcwQUNFTWR1TEE9PSIsInZhbHVlIjoidnd6eTFNdEhwSmd5bUhkMGRraTdWclhMU2RPb1M4Zkl6TUNMUGliUTlLS0lYOWhhaVVZalNpU0RTaXA4dkRncVp6dEtSdWp3djhybWFlQ1dXS29BK3M3WHRjemZFN2UzN2hDOGo4a1BXWmVrY2wrODdKSDZzazFzdERwNDJMUTlwc2VEMUNtQ0s0aHpyN09EZlZ4M0FZRVBuUlB6TW1KVUQ3R2xCendtcjR5Wlpld2tGTzA0Z1hzS21FeEVZLzA1bVh6VUFyTktyYzdlQk40SFlXQ1VMalNMNTJ4dDEvS2ZJSWt5UDRZQjJMbWwxeUNyWWVkbnFjcTVVQXRwS0JXcytxbXZQUkN3TVVFWml0VHRDcnpIQmE1WHpmT2xyZGw1bmRxbVBZK01LZHM1d0pOdUpya2xOYWNURWpja1ZzVEV2N1hnV1VSTkE2WjlHTjc5dWM1ZXF5NFhPZnE4Rjh6eVh0aDNYRDdUaWVVVzZSZEpEME1pWkErME1TNGFoSjJOSUpNWEtTZzFGYnZLY0RONTdmdnVuUkg2bWRrWnVLWlBMRXQ4MklZY2NPZGNKQ2cwVWFKTmtiWkdoc0oxbHVKKyt1QUhmd1BZdmVTT29OS3ppLzQzN0R4bVU4Z1NWckNWbmJ3WDhYQ094aE5ackd3ZG16ZTI1azg5enpEelZUTUUiLCJtYWMiOiIxYzI2NzU5MTU0YmVlNzMyOGM1ZDY0Zjc1MmEwZTQ5YjM5NmQwZmY3ZmE2NmViMGMxNTlhMDgxZTk2OTU4M2M0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlkxZXhLMlFNaEJOZ3VCY1h3eDRNaWc9PSIsInZhbHVlIjoibm5wcUN5WDVjdm1ETjhSZmcrcWFxVnVMZzEySU9yOFhIUzU3Q1lENHdLeng5SnlUSnhKTG1yQks5bWtObFF4WC9ncDgzY1lVbmJKNHZSSEhhZG12V1RKZGZkQW1aV3hVNHE4K0VaQ2dqaVVMMHhjMTh5b1hKVXFwY0JFR2pDaXlwVHVZN0JrZ2Mvdk9Oa2svZzdlOGlUQ05GRlh5eC9GTDArU2FGQzlCUGF3LzY2a0N2elZtUVZtc2lWQ0xGclk4cTRxQmcwYkVUblNMOTZZMERHV0RpMXYvblgwdnFEWHJpUTRKNms0RUR4MEwrTjE4K0ZQVGFLNVBLam5WUnZHUFpydkxxRFc2VWxUeDFKZ2x4RUZ2cVEzM09CZk5LUmNQdGpIVVlXdDlLUGIyMk5pbHpmdTVuWTI4Z3Znb3lxNU95THQ0SVFrMXE2UjlqTFdvSDJiU05VNWZXVHpqbVdrM1d5SmROclJDMWcyZi9OTkNuQTZJK2xTdTRyMUlyQjgwMm1RWng3czZHbW8xYjQ2L2hUaHBBanQzWWRJZ0h1WGswNXdtN00xamdxdk0vUmtZdERBbW9qZGpUYndzcWtadU1KRmpCM09NYkVrNGJZeFM1VHc3WnhpMXdGb0QxZytzWUY4bk9HeUhCWEZOSzU3ZmJnbHFwb29XcWtDRkRMTGkiLCJtYWMiOiI4NDM3M2NlZjgzNDNhNDYzYjI2OGEwNDdiZWVhNmQwMjdiMDNhYzNiZTc1OGI1NmJiODkxOWRiNGQ2MzM2NGEyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImR4RXZXazFWVmo1dGcwQUNFTWR1TEE9PSIsInZhbHVlIjoidnd6eTFNdEhwSmd5bUhkMGRraTdWclhMU2RPb1M4Zkl6TUNMUGliUTlLS0lYOWhhaVVZalNpU0RTaXA4dkRncVp6dEtSdWp3djhybWFlQ1dXS29BK3M3WHRjemZFN2UzN2hDOGo4a1BXWmVrY2wrODdKSDZzazFzdERwNDJMUTlwc2VEMUNtQ0s0aHpyN09EZlZ4M0FZRVBuUlB6TW1KVUQ3R2xCendtcjR5Wlpld2tGTzA0Z1hzS21FeEVZLzA1bVh6VUFyTktyYzdlQk40SFlXQ1VMalNMNTJ4dDEvS2ZJSWt5UDRZQjJMbWwxeUNyWWVkbnFjcTVVQXRwS0JXcytxbXZQUkN3TVVFWml0VHRDcnpIQmE1WHpmT2xyZGw1bmRxbVBZK01LZHM1d0pOdUpya2xOYWNURWpja1ZzVEV2N1hnV1VSTkE2WjlHTjc5dWM1ZXF5NFhPZnE4Rjh6eVh0aDNYRDdUaWVVVzZSZEpEME1pWkErME1TNGFoSjJOSUpNWEtTZzFGYnZLY0RONTdmdnVuUkg2bWRrWnVLWlBMRXQ4MklZY2NPZGNKQ2cwVWFKTmtiWkdoc0oxbHVKKyt1QUhmd1BZdmVTT29OS3ppLzQzN0R4bVU4Z1NWckNWbmJ3WDhYQ094aE5ackd3ZG16ZTI1azg5enpEelZUTUUiLCJtYWMiOiIxYzI2NzU5MTU0YmVlNzMyOGM1ZDY0Zjc1MmEwZTQ5YjM5NmQwZmY3ZmE2NmViMGMxNTlhMDgxZTk2OTU4M2M0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlkxZXhLMlFNaEJOZ3VCY1h3eDRNaWc9PSIsInZhbHVlIjoibm5wcUN5WDVjdm1ETjhSZmcrcWFxVnVMZzEySU9yOFhIUzU3Q1lENHdLeng5SnlUSnhKTG1yQks5bWtObFF4WC9ncDgzY1lVbmJKNHZSSEhhZG12V1RKZGZkQW1aV3hVNHE4K0VaQ2dqaVVMMHhjMTh5b1hKVXFwY0JFR2pDaXlwVHVZN0JrZ2Mvdk9Oa2svZzdlOGlUQ05GRlh5eC9GTDArU2FGQzlCUGF3LzY2a0N2elZtUVZtc2lWQ0xGclk4cTRxQmcwYkVUblNMOTZZMERHV0RpMXYvblgwdnFEWHJpUTRKNms0RUR4MEwrTjE4K0ZQVGFLNVBLam5WUnZHUFpydkxxRFc2VWxUeDFKZ2x4RUZ2cVEzM09CZk5LUmNQdGpIVVlXdDlLUGIyMk5pbHpmdTVuWTI4Z3Znb3lxNU95THQ0SVFrMXE2UjlqTFdvSDJiU05VNWZXVHpqbVdrM1d5SmROclJDMWcyZi9OTkNuQTZJK2xTdTRyMUlyQjgwMm1RWng3czZHbW8xYjQ2L2hUaHBBanQzWWRJZ0h1WGswNXdtN00xamdxdk0vUmtZdERBbW9qZGpUYndzcWtadU1KRmpCM09NYkVrNGJZeFM1VHc3WnhpMXdGb0QxZytzWUY4bk9HeUhCWEZOSzU3ZmJnbHFwb29XcWtDRkRMTGkiLCJtYWMiOiI4NDM3M2NlZjgzNDNhNDYzYjI2OGEwNDdiZWVhNmQwMjdiMDNhYzNiZTc1OGI1NmJiODkxOWRiNGQ2MzM2NGEyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484740778\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-95278057 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/proposal</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95278057\", {\"maxDepth\":0})</script>\n"}}