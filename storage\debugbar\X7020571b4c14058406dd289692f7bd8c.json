{"__meta": {"id": "X7020571b4c14058406dd289692f7bd8c", "datetime": "2025-07-14 18:34:02", "utime": **********.395289, "method": "GET", "uri": "/export/vender", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752518041.69867, "end": **********.395303, "duration": 0.6966331005096436, "duration_str": "697ms", "measures": [{"label": "Booting", "start": 1752518041.69867, "relative_start": 0, "end": **********.110307, "relative_end": **********.110307, "duration": 0.4116370677947998, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.110317, "relative_start": 0.41164708137512207, "end": **********.395304, "relative_end": 9.5367431640625e-07, "duration": 0.2849869728088379, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 73842584, "peak_usage_str": "70MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET export/vender", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\VenderController@export", "namespace": null, "prefix": "", "where": [], "as": "vender.export", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=457\" onclick=\"\">app/Http/Controllers/VenderController.php:457-463</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.005130000000000001, "accumulated_duration_str": "5.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1482239, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 52.437}, {"sql": "select * from `venders` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Exports/VenderExport.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Exports\\VenderExport.php", "line": 16}, {"index": 16, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 503}, {"index": 17, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 216}, {"index": 18, "namespace": null, "name": "vendor/maatwebsite/excel/src/Writer.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\maatwebsite\\excel\\src\\Writer.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/maatwebsite/excel/src/Excel.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\maatwebsite\\excel\\src\\Excel.php", "line": 204}], "start": **********.1904469, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "VenderExport.php:16", "source": "app/Exports/VenderExport.php:16", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FExports%2FVenderExport.php&line=16", "ajax": false, "filename": "VenderExport.php", "line": "16"}, "connection": "kdmkjkqknb", "start_percent": 52.437, "width_percent": 28.655}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\User.php", "line": 357}, {"index": 16, "namespace": null, "name": "app/Exports/VenderExport.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Exports\\VenderExport.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/maatwebsite/excel/src/Sheet.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\maatwebsite\\excel\\src\\Sheet.php", "line": 503}], "start": **********.201734, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 81.092, "width_percent": 18.908}]}, "models": {"data": {"App\\Models\\Vender": {"value": 118, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 119, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/export/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/export/vender", "status_code": "<pre class=sf-dump id=sf-dump-714406477 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-714406477\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "request_query": "<pre class=sf-dump id=sf-dump-7227750 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-7227750\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1179475161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1179475161\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1867958275 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752518033032%7C49%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlQekZ3cVZUVEowMGcxZ2pVUzlSc1E9PSIsInZhbHVlIjoiaWh0aCt5K05XLzRJSWp5ZjZnZm0vMTN1WVpZelk1MVdwUWMyTjl1dE1PVXQ4UnZpWGN0T0lldjZ3S3ZUUDlLcmhZRldzOEQ3TGU0RHRvOUc5Vmt4VU14aE1XdHp6dnJ4K1AzNVo3d2MvTjFObVlQQ3VxQ2pGT1V2MC9wYlF6czhxQ2RGdjFBMm1mRnNTZUt5aEdRbzJsaHYvZXN3QmNYTGxFMGx3ejA2OHF0cU1NL1NRSll0ZTl2Qng3MzhsNk5aNThtS2xTV1ZNK1FhbmpSdHIvQktPUnFDWjZsT2JVMWp5ekJMZFMreEt0UU9NV1VCUjBjK2RXSG1NbXBLMVZRRk5WNy9RTjJ1TVRLancvMjNtbnJ3OXJ1VFFqVyt3cktXaHJwTTgxV2ZUYXhyUGRPMGxxWW5rNGlKeUM4Y084ZmpxVSt5WDZwTEtkbE1NeHFERHBhVzk4d2JiZTloekRHaEsvaU5tRElSSWRjcjdJOC9vd2VHUWJKbVY4RloyOHF6RTRyWU44SWphQlRmNFdtRlhMaXMxYy9CNTZFQzUwUktxd3ROUHhzTVUzRGl0bnNweGh2SjFTZ0JxeXJXMFlHN1F1RjhhTUJRY2cwQlJvR1VJZk1LUm5LeXlXS05oR3EveWJ1SlZ1Nm0rdkcwOTJhcWdVV3cwc3FtK29TRGdxMUEiLCJtYWMiOiJmMTVmOTgzYjhiMjkwYTY4YjJiMjlmZWVhY2YzMGY3MzQwOTdmMDhmYjgwODFiZmI5Yzk1NGQ2ODI1MmJhZjdhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldVbnhFdFd3OTNDRmF6L1J5R28vWFE9PSIsInZhbHVlIjoiR2hNVEh0d0hkK081eHJ5Y2s5b2VvNEM2TUwrV3Z2bEZNUlAvMlhxbysxTW5lQXkvZ2lvT1pUdnh6SzYrZjBsQVpsUFZPY1l5OE1ObkI1NG90QTBsSHJ0Z3FXMUswZWVMMldkMHd2SENWMmZTNmlldHFEWUI1aitVYkxsN0xYTEl0NXhMS1A2amF0VXVMbWdrV3FSamYvTWlzOGFMUGN2TURlOU5PaC93cW5UdWhoM3NvQ1JveDFpdlJ6SVpOZHVVVGFVNGUrc3NaZmIwUjNTaEZnTGNSUEZUSDBGYldYTWJBNzYzbmpkZDZSbTVySUJLczZrSGdnSDJYeUJJMzZEUHlHMTBKTVUvOWowZDZVS0FRS0VxK0x5REFjSXdiL2NLbTM5VzRrVXNld2krUVlPdHJ3ZWNpbDBKVS9DZzNiczJvRld3SEIvcE9IL2M1cWQxSHNmWklSSU5mOUxaUnJ4OGcwVkord2JnU2xBa0lBUU1GdUdQZmxCMzZEcS9SU3VqK1V5dDFWK2xmWkxnWk5GZFF3VHpoQ1dBYllwdmdLd0IwYXVYS1dvSlpwNU45d3NmZjJxNWlDdlVOeEVEWlVHMWoxcGlvS1VwbTI5ZE9xMVdYeXpDbGpUSHZCM0JmdW9WcndkamFQck9wOFc3ZloySTBnTmFXYVVLb24rWGJ2TnMiLCJtYWMiOiIwNzE1NmEwYmZkNGI4ZDA1ODNmYjdmOWNmNjdhY2YzOTFiYjUxMWZmMWZiNmM5YmVmOTY3YmQ5Y2RiMWI4ZWFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867958275\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-83285199 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83285199\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-290116570 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:34:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:34:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-disposition</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">attachment; filename=&quot;vendor_2025-07-14 34:06:02.xlsx&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">13478</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVaMFRhbEE5RGtYYTVaN3dwMnQ0UFE9PSIsInZhbHVlIjoiSGtvaTFtWTNveERRckw4QStCNXNwLy9xbVhHYmdDVENZbmMzN1ZOemRmNGNyNDV0OFQ3eEdSN3hpTDhZa0lqRWxJMW8wOWs0Z0xHbTV6OHQ4dFQ3UWRoTmNzZEVNWGd6Zk4zQVNZYnBoQUZjNlprSTNiZ1FyLzN5SWdaT0xTM3R6bGF3bjdqcDVISmo0bVNWbWU4SzZ3RDk5L0VqYmVWWTlQZk94Z2R1MFFYZHZlUlNWNUsxY20zbmJOZHo1N0tGUGxTalVSRkwyaU00RXZIYlhlQkVURTY3ZjgrQVVtVEIwY3EwQWcxYmFxUXlkb1B2ZVpSWnEyd2NCSHh6c0xkNHFITVh2MUxvaGZpZHBSTFFvVWtNSGszMWs0WHgvR2VEUmVoQzZKWVlRRnNrcGpQWHYxdG5yQkRNOUhYK0dHT1ZSaE9maWVDMEZqK2g2dnh3RmZrSmx0OE4xSUtFQ0dCR00xaHFjT2Y4V1lPeGZjZG16Z3FwTGpiZlVtZUVPZ1B0SHgrdTVvTitwUEg3NXpUdlFlMElmcGt4UWkrdUcrOHBMdWtiWSs1bzRjV1BPam1qeXdzN1IxRE5aSkFtZDBZTlBFL0lwRlJDSUJhZitxWmV5Tlc0Ym5aRGtyREVWT3VzSHorTE5CNUUvVEdmWlpaeEZSUUgwRlZpSjJ4YkwwdWUiLCJtYWMiOiJlOGI2MWRhNTA2NGJiOWUwZjg3M2Y4MzEzNDUxZjRkNGE1MzEwNjk3MDEyZGRjNzlkYjg4MzVkNDEwNTJmMGY3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:34:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJPUnozWTNEMmlmYW5TTzJ5VlhySkE9PSIsInZhbHVlIjoiTmx3bGxUc0tpQ3F0Y0lqNkdKUmdFb1lUQ2JmNWI5aUlHZFFRSmhZN1ZOSXREbzArdUxVNkt1NWM4NG5ZSkl1a3RTdGFhT1R6UG82SEZnVjlVb3NkOTFzcHlmbFhFcXcwM3c5U2hIMFJiVEp1cUpBR0NaVktqS092WXMzSmg3ZHFKUG9uZW9rQlJkUll5dGRKL1dOanBYVEtXaVlGRWpTYi9SWDA3WEkwMUR1VnFTaGdZZGxqRkxvUW9tczVsaE9ZMTZPWGk5MkNUekxyN0NPTTJPdzVqQVNOMnhvTVhFR1hPcCsza0Q3cW9kSnM5VHJSYURsQVdoUzFxWHZ1ZkhXYzkrMEkvbDZxaGRYUlA4TGlIWHZjak40VmN5VXlxL2pZcjBKMk10bDhKWkxsSVJZTlZaYkFhVUZFcVY1VDdqb1BEcFpIbjZFTlBzSlAxSTUxWGtMdEF6UndFR2FYQ21NUy9DRjRibWxYMlF3T0xGL2UzMDlHWlNUazN5cTdpM3ZIenhHOHJ5dDFZS051Z3dlSnNhNzVKSUp3b25zbmJOSG1hWkE2R0xEUTBsbWF4dEdMWTFEMm84a1YvdTZ3LytUQ2E0NktEdmJNZFhveW5GWnEwZ24xN1gya3ZJTlhhM1doVXBINEZERWU0YWZ1OE9EZTdKbkI3cUtyNU5Pbm0wWGEiLCJtYWMiOiI0ZmQ0OWM5MDQ1OTUwODVkZjAzMmRhZGQ4Y2YxZTY4ZjIzOGUyY2E0YTI2M2ZjYTI2N2FjNzJhOTY2ODRkNjcwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:34:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVaMFRhbEE5RGtYYTVaN3dwMnQ0UFE9PSIsInZhbHVlIjoiSGtvaTFtWTNveERRckw4QStCNXNwLy9xbVhHYmdDVENZbmMzN1ZOemRmNGNyNDV0OFQ3eEdSN3hpTDhZa0lqRWxJMW8wOWs0Z0xHbTV6OHQ4dFQ3UWRoTmNzZEVNWGd6Zk4zQVNZYnBoQUZjNlprSTNiZ1FyLzN5SWdaT0xTM3R6bGF3bjdqcDVISmo0bVNWbWU4SzZ3RDk5L0VqYmVWWTlQZk94Z2R1MFFYZHZlUlNWNUsxY20zbmJOZHo1N0tGUGxTalVSRkwyaU00RXZIYlhlQkVURTY3ZjgrQVVtVEIwY3EwQWcxYmFxUXlkb1B2ZVpSWnEyd2NCSHh6c0xkNHFITVh2MUxvaGZpZHBSTFFvVWtNSGszMWs0WHgvR2VEUmVoQzZKWVlRRnNrcGpQWHYxdG5yQkRNOUhYK0dHT1ZSaE9maWVDMEZqK2g2dnh3RmZrSmx0OE4xSUtFQ0dCR00xaHFjT2Y4V1lPeGZjZG16Z3FwTGpiZlVtZUVPZ1B0SHgrdTVvTitwUEg3NXpUdlFlMElmcGt4UWkrdUcrOHBMdWtiWSs1bzRjV1BPam1qeXdzN1IxRE5aSkFtZDBZTlBFL0lwRlJDSUJhZitxWmV5Tlc0Ym5aRGtyREVWT3VzSHorTE5CNUUvVEdmWlpaeEZSUUgwRlZpSjJ4YkwwdWUiLCJtYWMiOiJlOGI2MWRhNTA2NGJiOWUwZjg3M2Y4MzEzNDUxZjRkNGE1MzEwNjk3MDEyZGRjNzlkYjg4MzVkNDEwNTJmMGY3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:34:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJPUnozWTNEMmlmYW5TTzJ5VlhySkE9PSIsInZhbHVlIjoiTmx3bGxUc0tpQ3F0Y0lqNkdKUmdFb1lUQ2JmNWI5aUlHZFFRSmhZN1ZOSXREbzArdUxVNkt1NWM4NG5ZSkl1a3RTdGFhT1R6UG82SEZnVjlVb3NkOTFzcHlmbFhFcXcwM3c5U2hIMFJiVEp1cUpBR0NaVktqS092WXMzSmg3ZHFKUG9uZW9rQlJkUll5dGRKL1dOanBYVEtXaVlGRWpTYi9SWDA3WEkwMUR1VnFTaGdZZGxqRkxvUW9tczVsaE9ZMTZPWGk5MkNUekxyN0NPTTJPdzVqQVNOMnhvTVhFR1hPcCsza0Q3cW9kSnM5VHJSYURsQVdoUzFxWHZ1ZkhXYzkrMEkvbDZxaGRYUlA4TGlIWHZjak40VmN5VXlxL2pZcjBKMk10bDhKWkxsSVJZTlZaYkFhVUZFcVY1VDdqb1BEcFpIbjZFTlBzSlAxSTUxWGtMdEF6UndFR2FYQ21NUy9DRjRibWxYMlF3T0xGL2UzMDlHWlNUazN5cTdpM3ZIenhHOHJ5dDFZS051Z3dlSnNhNzVKSUp3b25zbmJOSG1hWkE2R0xEUTBsbWF4dEdMWTFEMm84a1YvdTZ3LytUQ2E0NktEdmJNZFhveW5GWnEwZ24xN1gya3ZJTlhhM1doVXBINEZERWU0YWZ1OE9EZTdKbkI3cUtyNU5Pbm0wWGEiLCJtYWMiOiI0ZmQ0OWM5MDQ1OTUwODVkZjAzMmRhZGQ4Y2YxZTY4ZjIzOGUyY2E0YTI2M2ZjYTI2N2FjNzJhOTY2ODRkNjcwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:34:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290116570\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1561742696 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/export/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561742696\", {\"maxDepth\":0})</script>\n"}}