{"__meta": {"id": "X31cff2d6cf5363933bd4cf47f792c4c6", "datetime": "2025-07-21 01:17:35", "utime": **********.268162, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060654.758484, "end": **********.268176, "duration": 0.5096921920776367, "duration_str": "510ms", "measures": [{"label": "Booting", "start": 1753060654.758484, "relative_start": 0, "end": **********.12233, "relative_end": **********.12233, "duration": 0.3638460636138916, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.122339, "relative_start": 0.36385512351989746, "end": **********.268178, "relative_end": 1.9073486328125e-06, "duration": 0.14583897590637207, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48482296, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-223</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.042589999999999996, "accumulated_duration_str": "42.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.170742, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 4.015}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.181741, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 4.015, "width_percent": 1.151}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1962779, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 5.166, "width_percent": 1.432}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.198271, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 6.598, "width_percent": 0.634}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.22224, "duration": 0.039509999999999997, "duration_str": "39.51ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 7.232, "width_percent": 92.768}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-722129360 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722129360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.201686, "xdebug_link": null}]}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1968258122 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1968258122\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1566424546 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1566424546\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-277441791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-277441791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-849611587 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhXYW9vN1dveS9VNzFjTFZUcGNBdHc9PSIsInZhbHVlIjoiQnFkR1pqTDFmVkpFaXQvL2lHYzZ0R2NsMzFDS25xcG8rcDVnZ1pJb1dsbWJMTXp4ekxEckIyaVZLdG5FYzZjdG1UdlhTWFEzQmlQSDFqaHhqMElSWHZrbVhWSkkvMVNUTzA0UGd3M0pRZ3ZtTkU4NHNmb2p0T1RnUGhUL3hMSXFDZWJRYmMrcmk0YUdsSEVvMnZUVncrdS9OYXFtM3dJeDBMa3pwT3dvajdYbFR1QmM4UEl5MVJtc2FHVjMwekZSUGNkTFdlSlUrSVpSUGpZMVpCdHZIblJNUkZyOStsL1E3YnhrK2M4VWNRcFUxT252eWNabEV4Zk82b2hkN042Q2pEd3ZWbHQvMXZSbmlIdkwramN0ZGU1akRkVnd2ZmFnUHYxRjFZT2VXUW9aOXorYTFaa2JnWDVueWxkMzllazJzZEN0L1FzZm41UGgwV25iNGUrMXV6K0VteGZRV0poa2hMakdFUHR2UmMza2VxY3FWS0J3ZXlwOXN3czl4ZDdDQ3pjNHE0SCtzM1crWVlidHIrMlVQbkZGTDRjN0pOcmorVXE2TTc4VFdVQlV2UVhEb2ExRVM5bUkwaW9kSHphejhnUjYvYUYrSjNmQWQ3TEtVdjg4OGZnNnJ3NitVVVU2cFYvYm9BWHNaOFJLUVdpaCsvNld4ZTBsZ3NHem5EQWsiLCJtYWMiOiIyMjYzMzQwODhkZWIzMmU3ODJjZjk4ODQ5MGQxNjQxMjdkN2VlZmNjZWQwN2EwYjZiZGU3NDY0NTEyMWE4MWUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1rSEZDOUpLZk10STJ0UVBYNElyVUE9PSIsInZhbHVlIjoiZUpGb1VrLzlmUWU3Rk1VdnFLaG11SVZ5ZFpPbWVJQTA5UVFNWnFtUVhGN1Z4a3F5SDN5cEk3QSt3V3krdTZkQWdXbnFSZ0hsV1lFbGZoOHMwQzc5TkpsMkliSStOSzB2VUJhZHpZZkN5bzlGNjVKemdCckpnb204eWlzT1dRTm0wTExIZ1kyTGpZOE9lRVZXcG8xbS9XR1dmQmpOUERwSVRDdGFHV1ZOZVVXTXNtbUJjcGJCQ1BlVHcwdDMrMmx3YWtaSlAyWk03amVQM2lMdmlnNU5SajZ4QUtob1B5RndVQWpiSFljaHl5SzRocUlTM2RZTFZhelk0WUhYLzNiS3ZKRTVzaTdaeU80bUtFZWgxNlZFM01UWm9uMmw2WktuMlRIWnRQM1hMU2wrODU4bEQra0xhdWJTK090cUZqSFZtVkhwUzFpbFlGZ3R6RXk1Z24zVEdFdWdTdFdmWFAzNzE0VVNBRU54OTVnL2k1WlAyUHhVbzRjTVpBZERobXMrS2ZvaEJvazBISkhyV3FyOFdvM3d5L3doK2hsQ0RqQmJVNHpGVVNxYTR6YzVxbEdYSmMwNzN3UnNuS2R3K0pCWDhOazhvcXFjWGNzMzhWNG9tdHMzMzU1c1NQa293djhSZUYrNGdZSm4wSnVKcDZ3UDEwYkhDaWh1RnphSUMzVWUiLCJtYWMiOiJiOThiYzE0OGIxMjFmYWNmZDA4NGEwZjllMzJhZjU1YjQxODQwNWFmZGI5MTM2NGZjOWIzNzY1YTU0ZDcyOTllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849611587\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1176783765 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176783765\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1757490983 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImV6N1Q4SWRITzF4RTNyYy9iOGVjNnc9PSIsInZhbHVlIjoiTElMU0I2YVBDVkQ4dW91NmFGcXA1cjdFTE8vRHJFMHA4bXVvZ2VuL0tIWHkwN2l2SlZGMnJyZXRxbzRTS3hPM0NtaHYyZXU1UWhKYlpFaWdCaU5PVDA4MklEd0ZQbkJ3bnFwL29LZk1RZ0ljb0hsdDFFN1F2Q2xyRVVvc3FKWWdhMk5xS0QxV1luTnZqZmJqdEFjMHFqTzBIaVFOeGYvZktBZk9HQU9GOFEwZjJEaUVyTXYvdVJqUDJybk8rNUZZKzZKRUJDclZDR1FSaERVNVo3a0lqd0RqZU1WbzZ2blVZSDlWMldoc2xKL3VySDBBNk9YbmhpZWZXVlRtRk5sWG5GaDd4aHFxVnZabVFTYWd4SjR5NFIzRXlMWWxHbVNjSUlCMm8zWWpVWGJ2bHRkd0JYL0FRcE9TdVl3NzhiRDlRZGJLNTJjODgwTWh3V2hOYmxwL0UzaDJsSy9GRGFRTXRRTk5oZFNHMnREMW1oVjR2ckg0TU9qbHRKcVhtTGRPMGgzdTNpVjhka3JxaDBQOVdJM3VscTJROGhFRmFuZWpNdm05eW5VazY2UXZpaUlKNGJyRWJkck1vNlZJNHdsbVpzOEZtaStJL3ZCaHV4VFZ2eGIrWm13ZHJqWlJLaFZkdGJnbHFKOGlRVUZ2Mzh6RzZlZUhGVFZoQnN5a1Rvc0siLCJtYWMiOiI5OGNkZWY3OGU2OTY4NWI2ZDhjZTQ1ZTE2ODQzZDY2MGQ3Y2Y4ZTI1MTc5MjBkYjQ2NmY5NDFhMDE4M2I5YjE3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNTMWJFSGVDaXB1bWNpdTdMeFkxa1E9PSIsInZhbHVlIjoiWDQ1aitFQ0hKZjZTTUdTMG5Jb01GcTBWQ2JaTUZvMXFkM25RUXh4VlhlTzZWR1QvTWRqaEN2WXRCQWMxS09IRkhYelpKU0tESzVxelY4eHJoUzdIcjhhOExXRzZzMWZyUWdZMUloNjB6OUEwZFRhU01DMjBvODBENmZCc0Q1M1RWZStRcy9sbm1JR1FxM25RNmRpZkltOXArN1RGclFwa0xUWW0raE9SbEdhN2x1YjMySnptRG1JRlRPbFV4dFdHcG9FL1ptYlh1R0pxSitJRlZwZkpLRFFGU0NxYTZ6c21QYVI5VU5zSElzb0ZPSUpCZjFzdFJCdzg3MlZFd0pNYU9TSHg5cTBaMENhWmY2TGE0NS9mZWoyUVBKKzduUEJFbUVBYkVYNlZPb0ZjQ1pOclNERVRlT3hzUG9ZZWFHMWE2RTRvNk00eHJ3NVdiTWhYSS9ERi9JUy9Wdzhkajh2cXpwMXFwREhxU3g5amdCSG5ISm5vVDduQ1pIYWlQTElPWU5lM0VkZUtWa28vNmY2cXFsM1RqWHo0bVhIaDFTcEV1OUVCNE5jVmJoSWZ3TFFkRWg4THlXMm5OdmQvbXgrQ1hWTFlDZ1dpS2UrN1lYWWtLbXp0REREdE8wL2o1NXFsVVZOd3dXVldMaGtmcWg1TThCOW12MGtCL29YR0JhYSsiLCJtYWMiOiJmZGQ3MTNhZjMwZTI0N2JhODgzNzM2ZWEwZDJkMTQ4MGNlYmRiMGE0MWU1NzY3N2YzYjM1OGU0ZTkzMzJiNWNjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImV6N1Q4SWRITzF4RTNyYy9iOGVjNnc9PSIsInZhbHVlIjoiTElMU0I2YVBDVkQ4dW91NmFGcXA1cjdFTE8vRHJFMHA4bXVvZ2VuL0tIWHkwN2l2SlZGMnJyZXRxbzRTS3hPM0NtaHYyZXU1UWhKYlpFaWdCaU5PVDA4MklEd0ZQbkJ3bnFwL29LZk1RZ0ljb0hsdDFFN1F2Q2xyRVVvc3FKWWdhMk5xS0QxV1luTnZqZmJqdEFjMHFqTzBIaVFOeGYvZktBZk9HQU9GOFEwZjJEaUVyTXYvdVJqUDJybk8rNUZZKzZKRUJDclZDR1FSaERVNVo3a0lqd0RqZU1WbzZ2blVZSDlWMldoc2xKL3VySDBBNk9YbmhpZWZXVlRtRk5sWG5GaDd4aHFxVnZabVFTYWd4SjR5NFIzRXlMWWxHbVNjSUlCMm8zWWpVWGJ2bHRkd0JYL0FRcE9TdVl3NzhiRDlRZGJLNTJjODgwTWh3V2hOYmxwL0UzaDJsSy9GRGFRTXRRTk5oZFNHMnREMW1oVjR2ckg0TU9qbHRKcVhtTGRPMGgzdTNpVjhka3JxaDBQOVdJM3VscTJROGhFRmFuZWpNdm05eW5VazY2UXZpaUlKNGJyRWJkck1vNlZJNHdsbVpzOEZtaStJL3ZCaHV4VFZ2eGIrWm13ZHJqWlJLaFZkdGJnbHFKOGlRVUZ2Mzh6RzZlZUhGVFZoQnN5a1Rvc0siLCJtYWMiOiI5OGNkZWY3OGU2OTY4NWI2ZDhjZTQ1ZTE2ODQzZDY2MGQ3Y2Y4ZTI1MTc5MjBkYjQ2NmY5NDFhMDE4M2I5YjE3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNTMWJFSGVDaXB1bWNpdTdMeFkxa1E9PSIsInZhbHVlIjoiWDQ1aitFQ0hKZjZTTUdTMG5Jb01GcTBWQ2JaTUZvMXFkM25RUXh4VlhlTzZWR1QvTWRqaEN2WXRCQWMxS09IRkhYelpKU0tESzVxelY4eHJoUzdIcjhhOExXRzZzMWZyUWdZMUloNjB6OUEwZFRhU01DMjBvODBENmZCc0Q1M1RWZStRcy9sbm1JR1FxM25RNmRpZkltOXArN1RGclFwa0xUWW0raE9SbEdhN2x1YjMySnptRG1JRlRPbFV4dFdHcG9FL1ptYlh1R0pxSitJRlZwZkpLRFFGU0NxYTZ6c21QYVI5VU5zSElzb0ZPSUpCZjFzdFJCdzg3MlZFd0pNYU9TSHg5cTBaMENhWmY2TGE0NS9mZWoyUVBKKzduUEJFbUVBYkVYNlZPb0ZjQ1pOclNERVRlT3hzUG9ZZWFHMWE2RTRvNk00eHJ3NVdiTWhYSS9ERi9JUy9Wdzhkajh2cXpwMXFwREhxU3g5amdCSG5ISm5vVDduQ1pIYWlQTElPWU5lM0VkZUtWa28vNmY2cXFsM1RqWHo0bVhIaDFTcEV1OUVCNE5jVmJoSWZ3TFFkRWg4THlXMm5OdmQvbXgrQ1hWTFlDZ1dpS2UrN1lYWWtLbXp0REREdE8wL2o1NXFsVVZOd3dXVldMaGtmcWg1TThCOW12MGtCL29YR0JhYSsiLCJtYWMiOiJmZGQ3MTNhZjMwZTI0N2JhODgzNzM2ZWEwZDJkMTQ4MGNlYmRiMGE0MWU1NzY3N2YzYjM1OGU0ZTkzMzJiNWNjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757490983\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-112988724 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-112988724\", {\"maxDepth\":0})</script>\n"}}