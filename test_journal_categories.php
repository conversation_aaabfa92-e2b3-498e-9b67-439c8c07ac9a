<?php
/**
 * Test file for Journal Entry Categories functionality
 * This file tests the new category features added to Journal Entry system
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

use App\Models\User;
use App\Models\JournalEntry;
use App\Models\JournalItem;
use App\Models\ProductServiceCategory;
use App\Models\ChartOfAccount;
use Illuminate\Support\Facades\DB;

echo "<h1>🧪 اختبار نظام الفئات في Journal Entry</h1>";

try {
    // Get a test user
    $user = User::where('type', 'company')->first();
    if (!$user) {
        echo "<p style='color: red;'>❌ لم يتم العثور على مستخدم للاختبار</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ تم العثور على المستخدم: {$user->name}</p>";
    
    // Test 1: Check if categories exist
    echo "<h2>📋 اختبار 1: فحص الفئات الموجودة</h2>";
    
    $incomeCategories = ProductServiceCategory::where('created_by', $user->creatorId())
        ->where('type', 'income')
        ->get();
    
    $expenseCategories = ProductServiceCategory::where('created_by', $user->creatorId())
        ->where('type', 'expense')
        ->get();
    
    echo "<p>فئات الدخل: " . $incomeCategories->count() . "</p>";
    echo "<p>فئات المصروف: " . $expenseCategories->count() . "</p>";
    
    // Create test categories if they don't exist
    if ($incomeCategories->count() == 0) {
        echo "<p style='color: orange;'>⚠️ إنشاء فئة دخل تجريبية...</p>";
        $incomeCategory = ProductServiceCategory::create([
            'name' => 'إيرادات المبيعات',
            'type' => 'income',
            'color' => '#28a745',
            'created_by' => $user->creatorId(),
        ]);
        echo "<p style='color: green;'>✅ تم إنشاء فئة دخل: {$incomeCategory->name}</p>";
    } else {
        $incomeCategory = $incomeCategories->first();
        echo "<p style='color: green;'>✅ فئة دخل موجودة: {$incomeCategory->name}</p>";
    }
    
    if ($expenseCategories->count() == 0) {
        echo "<p style='color: orange;'>⚠️ إنشاء فئة مصروف تجريبية...</p>";
        $expenseCategory = ProductServiceCategory::create([
            'name' => 'مصاريف التشغيل',
            'type' => 'expense',
            'color' => '#dc3545',
            'created_by' => $user->creatorId(),
        ]);
        echo "<p style='color: green;'>✅ تم إنشاء فئة مصروف: {$expenseCategory->name}</p>";
    } else {
        $expenseCategory = $expenseCategories->first();
        echo "<p style='color: green;'>✅ فئة مصروف موجودة: {$expenseCategory->name}</p>";
    }
    
    // Test 2: Check Chart of Accounts
    echo "<h2>📊 اختبار 2: فحص دليل الحسابات</h2>";
    
    $accounts = ChartOfAccount::where('created_by', $user->creatorId())
        ->limit(2)
        ->get();
    
    if ($accounts->count() < 2) {
        echo "<p style='color: red;'>❌ لا توجد حسابات كافية للاختبار</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ تم العثور على حسابات للاختبار:</p>";
    foreach ($accounts as $account) {
        echo "<p>- {$account->code} - {$account->name}</p>";
    }
    
    // Test 3: Create a test journal entry with category
    echo "<h2>📝 اختبار 3: إنشاء قيد يومية مع فئة</h2>";
    
    // Get next journal number
    $latest = JournalEntry::where('created_by', $user->creatorId())->latest()->first();
    $journalId = $latest ? $latest->journal_id + 1 : 1;
    
    // Create journal entry with income category
    $journalEntry = new JournalEntry();
    $journalEntry->journal_id = $journalId;
    $journalEntry->date = now()->format('Y-m-d');
    $journalEntry->reference = 'TEST-CAT-' . time();
    $journalEntry->description = 'قيد تجريبي لاختبار نظام الفئات';
    $journalEntry->category_type = 'income';
    $journalEntry->category_id = $incomeCategory->id;
    $journalEntry->total_amount = 1000.00;
    $journalEntry->created_by = $user->creatorId();
    $journalEntry->save();
    
    echo "<p style='color: green;'>✅ تم إنشاء قيد يومية رقم: {$journalId}</p>";
    echo "<p>نوع الفئة: {$journalEntry->category_type}</p>";
    echo "<p>اسم الفئة: {$incomeCategory->name}</p>";
    echo "<p>المبلغ الإجمالي: {$journalEntry->total_amount}</p>";
    
    // Create journal items
    $journalItem1 = new JournalItem();
    $journalItem1->journal = $journalEntry->id;
    $journalItem1->account = $accounts[0]->id;
    $journalItem1->description = 'حساب مدين';
    $journalItem1->debit = 1000.00;
    $journalItem1->credit = 0.00;
    $journalItem1->save();
    
    $journalItem2 = new JournalItem();
    $journalItem2->journal = $journalEntry->id;
    $journalItem2->account = $accounts[1]->id;
    $journalItem2->description = 'حساب دائن';
    $journalItem2->debit = 0.00;
    $journalItem2->credit = 1000.00;
    $journalItem2->save();
    
    echo "<p style='color: green;'>✅ تم إنشاء بنود القيد</p>";
    echo "<p>البند الأول: مدين {$journalItem1->debit}</p>";
    echo "<p>البند الثاني: دائن {$journalItem2->credit}</p>";
    
    // Test 4: Test the relationship
    echo "<h2>🔗 اختبار 4: فحص العلاقات</h2>";
    
    $testEntry = JournalEntry::with('category', 'accounts')->find($journalEntry->id);
    
    if ($testEntry->category) {
        echo "<p style='color: green;'>✅ العلاقة مع الفئة تعمل بشكل صحيح</p>";
        echo "<p>اسم الفئة: {$testEntry->category->name}</p>";
        echo "<p>نوع الفئة: {$testEntry->category->type}</p>";
    } else {
        echo "<p style='color: red;'>❌ العلاقة مع الفئة لا تعمل</p>";
    }
    
    $totalDebit = $testEntry->accounts->sum('debit');
    $totalCredit = $testEntry->accounts->sum('credit');
    
    echo "<p>إجمالي المدين: {$totalDebit}</p>";
    echo "<p>إجمالي الدائن: {$totalCredit}</p>";
    echo "<p>المبلغ المحفوظ: {$testEntry->total_amount}</p>";
    
    if ($totalDebit == $totalCredit && $totalDebit == $testEntry->total_amount) {
        echo "<p style='color: green;'>✅ الحسابات متوازنة والمبلغ صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في التوازن أو المبلغ</p>";
    }
    
    // Test 5: Test update total amount method
    echo "<h2>🔄 اختبار 5: فحص دالة تحديث المبلغ</h2>";
    
    $testEntry->updateTotalAmount();
    $testEntry->refresh();
    
    echo "<p>المبلغ بعد التحديث: {$testEntry->total_amount}</p>";
    
    if ($testEntry->total_amount == 1000.00) {
        echo "<p style='color: green;'>✅ دالة تحديث المبلغ تعمل بشكل صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في دالة تحديث المبلغ</p>";
    }
    
    echo "<h2>🎉 انتهى الاختبار بنجاح!</h2>";
    echo "<p style='color: green;'>✅ جميع الميزات الجديدة تعمل بشكل صحيح</p>";
    echo "<p>يمكنك الآن استخدام نظام الفئات في Journal Entry</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}
?>
