{"__meta": {"id": "Xe8af619ffaa973724d3267a010a3a1d1", "datetime": "2025-07-23 18:17:46", "utime": **********.291481, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294665.670838, "end": **********.291505, "duration": 0.6206669807434082, "duration_str": "621ms", "measures": [{"label": "Booting", "start": 1753294665.670838, "relative_start": 0, "end": **********.147197, "relative_end": **********.147197, "duration": 0.47635889053344727, "duration_str": "476ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.147206, "relative_start": 0.4763679504394531, "end": **********.291508, "relative_end": 2.86102294921875e-06, "duration": 0.1443018913269043, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46535736, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00439, "accumulated_duration_str": "4.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.24785, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.364}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.262309, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.364, "width_percent": 15.262}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.271017, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 75.626, "width_percent": 24.374}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1875384623 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1875384623\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1061456831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1061456831\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-23385940 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23385940\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1188858075 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294644253%7C1%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjI1NXNJb0FVbFdpaHBYN1k1VS9GMEE9PSIsInZhbHVlIjoiRFBqWWMvN0dTcUZseWpUTTU2ZS9oZ1V1Q0I2K29MdjB2cHNsM2FGeE42dFZtajJ0NHNuODJpYURPc0VBRmdRZkRhT0YyS083VmJ5UTJQcm5jMUpYK1p6QnhuR003UHU2RDV1ZnNBdXA0OU5mWUJqYTMrMjZWSU0zVVpvc2M3OExlNUltVkVLUFAwbWorVXc1QXFudHYvUVlBQUR6MUd5RHJRcjhFTUl1Yy80R3NWQklndVdOczdJSFlUbXlCaUVJRUs0YzBKMDNZZGxYUTVkdG1QNDNIaC9ZSlBqaGZTK0tlQVEyczllU2dPUk56ZjFFR3ZTYTBuMWFMS1I1dVQza1V6eXBtZXBVT09XN0Y0ME1lNzJpcFdDdXNFZ0RBNUFqZzE2ajZCOGs4RzBjVVBOZ3lyZzJyOGkzTCtCS29qbUczZFExMmltMWJKMFRhUUVCL0hSNXR0Y0orOFJmY1RDYWplU2pxbHFmWStSSFNQdlR3WVV3aFEwUEZ0NWsvdHNZMXBHdVBvVm5tSHBOUWliQTVEaEgxQjA4Rm10OUEyR1lRTDNRVytqUmRpZUJmU1hWdmR3S0RRaktya0VEK1NEU0dEZDlHUyt5dklDWDFwd3pKbXNHd2FDei9LZ2xiQk5FMHUyRzBWMit3RDBIUnBPVlZ3WUM1MVZ3byszRFVCSU0iLCJtYWMiOiIzNDU2NzQyZTI0MWJiYWM3NzhjZmMxOTE0YmUzYjg3OTU3ZTc5ZjMxMGY1MWRlMjJhZTRkNjk2YmMxMDcyMzAyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpQNDA2cGRTTXdhVHk4S1F1YUhhSGc9PSIsInZhbHVlIjoiL3ZVTk9Bbm9iK1NnNGhIaC9udW5FNFpkVEhiSGcxS21uL2RzczgvbGZHR2tzd0RPenhrUzVSVEZUVHZTcUE1TkkySGs3c25nUDUwcXVldlY4SjEwU2k4czJhd2ZLTDA3WVVqNEFFYjduVG94N3psVlBBc1BrTC9pb2dRRkNUUTdrSE9ObCtPei9WeHhVRkp0c2dPVmh5WkZNYlc2WVhGMXgxWlVPYk1Yd055L0tYSmdBZkw1NytBZHJqU2tQMlpEMUhySU03VEYzSU5wRGhrRVQzVFpiRFNXTWlLN09FZzBMZXZ1M0NNM2pRU092OVAySDcwNks4aGZHbDBIMWE3cVV2OU93eW5xUkdsNjJHOSt3YThjdlpqOU1XeGZHT3BVUDQva3h0dmZwYzJvNHFKRGVGVlZQRWFNTUxPalcwdVBLanVuWDY3QkFwVFBnVElTWU5jMGovM3duV2ZUaC92ZVZhMzVuY1ZuZGl4TkNWanFhRy9idkM0cmZOay85ZkIyelg1MlRWZWpGTGxJTWZvQ1U2b2tiS3ZwYTJwSUpwV1hXaXZiM2dvUnZLODMyaUh0NHBrN0djeExXSktKVkVRcGlWeDJ2WE9DK2Ixa3BLTGlMdGFtNWg4cUlveStsT1c1T0Z4QlhIcmZyM1pxWDR2NWpQV3hrb2Jlb21JNVc2WEUiLCJtYWMiOiJlOWUzMThhMmJlN2E2OTlmMDIzYmQwYWY1YzI0OTU3NDQ2NTc2ZjliMDdiYjA1MzI1NWMwYzJlMzE3NTI1MmQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188858075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2130876283 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130876283\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:17:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVPdXc3dENXWXZZNzh1SDI2Sko0ZHc9PSIsInZhbHVlIjoic2lJaDE0b2RSQkFSVVFYY3dUWE56WlgxdThhOStZWVgrb081aitqNzg4d2dvV1lJWmdCWjZEUGhBemhhdFI2Q1djZWJWUVZvcldJTk1SVHdIcmNwUzk2c0RHZHRrdmsyb0VDdGlQc1JJWVo3M2dZZjJ2aE5VTFpNQmYzemVjdUpOL0NUeTJ5RlE0aVc3WnlWSGVQLzd4d0JIaHZWeWhrdGlmdE9NVldISlJ5UFJkSHlPbDFsNVgyWUJHajFZdElvOFFZUTkyUnBxRnY3SlhqS2tPelVoQjVWZmR6Um1Od0x0OVcwSkF3bzlXVFhSWWFWR3JVMU9WeUJUY1hqQWxkU29FalE1dGY5RGFEeGpReGo0enFBdmZ6YStNcGRZSVFEL0MzTWpJNENGTlNRRXc1dWNMTUhDVnhkOEx1SDN4QVU2UkUvbnVVQkdlb3JKRmxUMURuN0IzeTNzaVV2VEJpZHZ5NkFCbmhnY1NiNWh3bmNBNG9EQ1RnU3lZUVVPeXRDVDhyYVJ1UnI1T0IwMnRBMW9EYVpnSUtBZTdHL0tRanNGQXpydHA5c2pLU3FtWmlUK29GUlYzZGcyeUVoZDdhS0dleHU1ZnhjeTRhK242NFJsczNJUlArcGdkY01DR21xY1lHSHBSOEEzcWJ5aVk1T2RpemZwSG93bVF1TFFiYkoiLCJtYWMiOiI1ZGQyNWE2Nzk2NzgxN2Y2NDIxNGQxNDhhN2U1YTBiY2QyZWNmN2UyNDAzYjYwNThkZjFlN2FlN2E0ZTk0YmIzIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:17:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRGSm1iTE50SXQ4MzhvQkxmdkN3WVE9PSIsInZhbHVlIjoiRjdaM2M0S3d5VUNGTTdFdDZsZDkxUDJ0aDJxVzJwRjNzVG9zYUFJdkJGWFh5WDZYY2RBM2RDaUU4YlpFNHpLZVZIdTBJVkIvZG1CYWZrL0txUzlPbGlRc2t1VHpGUzB4RDBubjJrQ2JsSm5xT3UxbmJ2TGREQVVVN1o0UFl3MXlrUTJGQVJqVldzTVBzTnY0bjJZL3VjYmZIMmRyYk5HSDJ0WlNkc043Q3JmUHlSRGJaZjVJcUtJelBmTVJYMlFINlhmZXdSRHp6anh0LzNjV1VZSVVLM0VDRkNzZWVDeDlwN3J2V0dZYlpTRTY3YTlIWG1xM3BFWTRSQm0wdkJ6QmhXL210S2I5ME9hVW1EMkNRZEtFeWVsdTQ2UXJtbGdydFQ1My8rUzdyVHIxWVpXRFRTRnJla3cvZ2hORjBWMnBackx3TWk3ZmVJaXNlRG9xQ3FDQjlLd3UxZlB2YW9BUDdWUWFJdTBVaXhuOU0zclJ5NitScUxQVmMzRVc4dFZnM1ZrMlB6ckdDRzJYTnBhQXpreWVNNVBxZlVIU0dLNUpQL0RObVlqdnA4RlRjd3o5ZlFSYWYrSVU4VTkzeGN5blE3RlZGbEpmME9VRjNPY0MvdEoyVFRYbC9abVEzbmFSeldjUUs1cUNHSHBmcmo2cjkvc01zUVlvSDhIL1J4aHYiLCJtYWMiOiJiZTJmZTVmZDM5ZmViN2IwNDUwYzhhNDYzNzJlYTRkMTY0NDU1NzRjN2U3NjY2NDk3MDZjYWM3MGViOGExMzJhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:17:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVPdXc3dENXWXZZNzh1SDI2Sko0ZHc9PSIsInZhbHVlIjoic2lJaDE0b2RSQkFSVVFYY3dUWE56WlgxdThhOStZWVgrb081aitqNzg4d2dvV1lJWmdCWjZEUGhBemhhdFI2Q1djZWJWUVZvcldJTk1SVHdIcmNwUzk2c0RHZHRrdmsyb0VDdGlQc1JJWVo3M2dZZjJ2aE5VTFpNQmYzemVjdUpOL0NUeTJ5RlE0aVc3WnlWSGVQLzd4d0JIaHZWeWhrdGlmdE9NVldISlJ5UFJkSHlPbDFsNVgyWUJHajFZdElvOFFZUTkyUnBxRnY3SlhqS2tPelVoQjVWZmR6Um1Od0x0OVcwSkF3bzlXVFhSWWFWR3JVMU9WeUJUY1hqQWxkU29FalE1dGY5RGFEeGpReGo0enFBdmZ6YStNcGRZSVFEL0MzTWpJNENGTlNRRXc1dWNMTUhDVnhkOEx1SDN4QVU2UkUvbnVVQkdlb3JKRmxUMURuN0IzeTNzaVV2VEJpZHZ5NkFCbmhnY1NiNWh3bmNBNG9EQ1RnU3lZUVVPeXRDVDhyYVJ1UnI1T0IwMnRBMW9EYVpnSUtBZTdHL0tRanNGQXpydHA5c2pLU3FtWmlUK29GUlYzZGcyeUVoZDdhS0dleHU1ZnhjeTRhK242NFJsczNJUlArcGdkY01DR21xY1lHSHBSOEEzcWJ5aVk1T2RpemZwSG93bVF1TFFiYkoiLCJtYWMiOiI1ZGQyNWE2Nzk2NzgxN2Y2NDIxNGQxNDhhN2U1YTBiY2QyZWNmN2UyNDAzYjYwNThkZjFlN2FlN2E0ZTk0YmIzIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:17:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRGSm1iTE50SXQ4MzhvQkxmdkN3WVE9PSIsInZhbHVlIjoiRjdaM2M0S3d5VUNGTTdFdDZsZDkxUDJ0aDJxVzJwRjNzVG9zYUFJdkJGWFh5WDZYY2RBM2RDaUU4YlpFNHpLZVZIdTBJVkIvZG1CYWZrL0txUzlPbGlRc2t1VHpGUzB4RDBubjJrQ2JsSm5xT3UxbmJ2TGREQVVVN1o0UFl3MXlrUTJGQVJqVldzTVBzTnY0bjJZL3VjYmZIMmRyYk5HSDJ0WlNkc043Q3JmUHlSRGJaZjVJcUtJelBmTVJYMlFINlhmZXdSRHp6anh0LzNjV1VZSVVLM0VDRkNzZWVDeDlwN3J2V0dZYlpTRTY3YTlIWG1xM3BFWTRSQm0wdkJ6QmhXL210S2I5ME9hVW1EMkNRZEtFeWVsdTQ2UXJtbGdydFQ1My8rUzdyVHIxWVpXRFRTRnJla3cvZ2hORjBWMnBackx3TWk3ZmVJaXNlRG9xQ3FDQjlLd3UxZlB2YW9BUDdWUWFJdTBVaXhuOU0zclJ5NitScUxQVmMzRVc4dFZnM1ZrMlB6ckdDRzJYTnBhQXpreWVNNVBxZlVIU0dLNUpQL0RObVlqdnA4RlRjd3o5ZlFSYWYrSVU4VTkzeGN5blE3RlZGbEpmME9VRjNPY0MvdEoyVFRYbC9abVEzbmFSeldjUUs1cUNHSHBmcmo2cjkvc01zUVlvSDhIL1J4aHYiLCJtYWMiOiJiZTJmZTVmZDM5ZmViN2IwNDUwYzhhNDYzNzJlYTRkMTY0NDU1NzRjN2U3NjY2NDk3MDZjYWM3MGViOGExMzJhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:17:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}