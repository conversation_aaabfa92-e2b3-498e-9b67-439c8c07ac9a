{"__meta": {"id": "Xe4081d31c42cc6c04e842b679330e31c", "datetime": "2025-07-14 14:39:41", "utime": **********.858005, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.298959, "end": **********.858019, "duration": 0.5590600967407227, "duration_str": "559ms", "measures": [{"label": "Booting", "start": **********.298959, "relative_start": 0, "end": **********.772209, "relative_end": **********.772209, "duration": 0.4732499122619629, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772217, "relative_start": 0.47325801849365234, "end": **********.858021, "relative_end": 1.9073486328125e-06, "duration": 0.08580398559570312, "duration_str": "85.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45990928, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020499999999999997, "accumulated_duration_str": "20.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.812291, "duration": 0.01898, "duration_str": "18.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.585}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.84094, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.585, "width_percent": 3.073}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.849961, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.659, "width_percent": 4.341}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/journal-entry/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1988522950 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1988522950\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1066141330 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1066141330\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-730563028 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730563028\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-296208381 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503670543%7C15%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJHMFppWlhPUnRGbWQyUld1K0xBdnc9PSIsInZhbHVlIjoidmxQWGtHbUFzYmpDbVBHamgvOW5OTWRTTjNKQTBiVXQyNUZ5ZFdZdmFqbzZBZlZaVlgyVmUwU2NhZnBRQmp3L1hYRFNWMVpyRUlzTFlZYXNpelZPV21tT0VzbHQ0dExFK2x5R2ZVc3lPc0J1S1c2VFp1RTdIZWRTdTQ5YXlDdmV5NnNLM09yelVMNXpNTlhxcllnLzlnR01FNGk1Sks3NUZvbUZ4dU81eUgrMVhTMnhmMFVpdjFBeVpZSytWcXBDNTZ0S2dEdEo0Z2VKYjZqV0YvT1Q1MmN2bzJWN3JNSldRUlFxQmU4THcxNk54L2J3THhHQlRpdG5LTDFtanI2Rm5UdnJFdlBaU1N6dTBMclEzWitXWG84eEl2Y0pFR3RkZnlYdDNuVEFvdWwvWEZVNGwwdmZqVW9wNEtGZy8yZFAvQmdvd2dNeUNiU2hqTnk0ZUxEZlZzNmVTODZCbS8yRXNnOGhtVlJwV01wdUFXRDJCWGNlMXJMUmJaQ2FwOFpjUHhCQjlTZGpBVmVqVGpaZC85SmlwV2NPUHRZOTdpcnpMSzFyNXZOL2FCU2Z5MHRScm5PSklueUw3dlQwYm1qWnMxMzlaYnJha1diTThqUnZwYnlLcUk0SEZ3Uzd1Q2Z4VmxyeDZTczREdGR2UWl1RGlmVGdURWpUWXdYUHBnQVAiLCJtYWMiOiIzMWM4NDA5NjA0M2MzZGI5MmJkOTk2NzJmMDkyNTZjMzI2NzM4OGM5ZTI1ZjU4ZjJhOGE3MDNhOWNlOTZiYmIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZtTkVBNUlqc2pldDlRdUNPZW50eUE9PSIsInZhbHVlIjoidmV3ZnRhdjZRSXpYNERIMXhUbDBsVTFOK1ZVVXNLaWErS1RkR2JwL0h4clBaNFk1ZUN3OGJvYjVKYjBPRVRIaWtEaDdHeDlGbmRaazVSZFRZdzlvQm5UTzFmQy9TdXpYaWdMaDZWZExtQ29FVDdqSTNoOXFOeW9MSTZ1ZDcxZWlPLzVZWTFqWU9tUTh3Mm9ZcWFxQzBuZFhvQ1FlYzdOaktUSGVxdUZxdXcrdW1YaENIRm9NZ2JhU2NBM2dZeWMrcWJNcjZyTHpqbEJWdmVrN0t2WVJzY2RudXBZcFZ2Q01kU0FUSGFnSHcrUUt0OWR3N1VFVVBWbkl2RUZteTF2ODBVZ1I0dHcwWmZ3YndzRXc3bUZRenVsMG5xbnJiSHJjTnU1TGZhSHhWL3UvT1hNaGRORUJXWkh3dzJLbUd5WDZsZERCOEtSWmpXL3RJQXJjL0VmOENlOG9USE9YREhYOVFFa0tkTDE5S01JV29FQzFyQzJlYmtBdkVHamVoVmExYU5TZGdzSDdCQXVkY1NKRGVSVVBhVWdDL3JpMG1HMzJYTitmR0ZWMG1xaTB2NkJwSGcrQ1FMM2VqeUVydzFhc0dxNURabGFCRkwrSkxFaEpzZThyVWJvRDE3NTRpcmpHN0RDK1Nma2tIczdVYUFlZUc1eWkwUVZqNzhaNjNJcHoiLCJtYWMiOiI0MThjYTk5ZTU5OWE3NzMwNzE4MmE2ZDQ0OTNlMzZhMjdhZmQ2NzVjNDc1MTYyMmM4NzFkODQ2NTcxY2M4MzU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296208381\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1334873601 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1334873601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1415928322 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:39:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkN5dVhZeGRQc0pBQzdJN2luSEhDVEE9PSIsInZhbHVlIjoic05vTGpLK1BiOGg0N3NXVkhHdzA1aEVaTU1PRWhaSnJaVVRSNmpFMGxaREx6M1ZzWlU4SWRhKzJSNDZNb0FyVWo1bTY2bktrOWM1eXNuTG1UY3Y4NkVKcnkyc0x6QTA0Vm1YM0laZFBEenVSdWxrZS9IWGQyaXhmMXhwKzNBb0J1ckpvRndFT05zNzJWSlNmL0NrbmhHbmVDUlQwWHNKUmJoeDZDcFQvUlFPWXIyZCsrcEZXOTIxaEI5aUlQZWp4TE5MRC8rb3QwLzE5UFlIb09KL1BTZzV0SUFwMCsxWndXWUlwMm9nZ3BNTFJDOXl3WGxQTXRQREJLZ25YZDhVZWN2YXdRYnVVT3FkaHhRSytyWjlvMFVMTDJRWEJjNW1oUE9CU2x5WVNlcmZSbXNqZysrMHloTjRxekphS1hZbTZCUnZHRmh1cFFRSHZEVVl1OENPK3V2L0hJbkplQjlEdTNSSGFQN05pQU1BOFBFd0hTaTB5WXl4VUkxYnV5bEZnM0ZZOWQzcG8zUW9RTlhEQmVYZ2VIaDdCWlg3cTc0QzdsalBieWM4RVlwWVNjVG9uMVRBdjExVWlXenc0SWRZMkJyeVVhaGJNQlVQa0dMaUlwT0lLRDZwNzZmckpiQ1lLLzcxZFJtQmxRRzBpWlZUYjBscW9oYkVPWmJUczR0T0QiLCJtYWMiOiI0OWU0ZjVlNjVjODkzMWQ4NGY1OTA5NTI3N2M5ZGEyNjY5MDgzYjMzNDgyMzQ1NTI0OTliNWQzZTdlMTI5ZmM4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpyTjJZM1lLWS96c0hqWGJ6azJCekE9PSIsInZhbHVlIjoiU1NvNkZLT3ZJdjJJVnB6L0U0S3NPaFBaMmo4UVlsR2JMYklzZ25xZnZ6YW1jOGdyeWU2VFp4K0cyK1locmlaaUR3eG1mZ1N4U3RYQXNRWUpkYU9ZMFd2M0pCd2pZWU9lZWc5RHVPb3YzWHZPZ1VhNHRwWGFsODEvbGxNZzZWcThUZXFURVV6MmtIMjVCL09nZVdaNks0bEI4UjYwTkdnMXFnOXczQzU2OUM4Smw3eGdMN24yNzdLbWQ1RWxRNHM1S2RjVm9YUlM0NGpUdnNPVTlyRmU2U3M5ZUswaXdITXZ4QnZ4cVRRVXF0VzZBemFqc0RCZEVSRjd1Vit1b1FOT2ZHTU5lSlplcUhEOC9zNVVsVGxhZTJiRTlRWjNwUk9LcTBKRUlyRjBqOFJ6SUFOcEFNb09jZzJUOVQzQ1hvSi9DcGwwZjZyeHhzN2pJM3VEMzRCTEtPQTdVUUo5ZERQdG11cmRBb0poZHBXNVRiQ29uYzRsSSsrZ0svVTRHTWgxdlZlaE1RVk9qMFJNRE1iZ0UzR0VsS1liYTFPSEJiUVFRMkJzaEV0VUVodHF2LzZPK2FNbXpkUkxiYTlSRWFpdGwyNTl3SVNxLzV6dTczOG0xcGU5dU1CRHhPZTZUZzBBMi9BR0o1eS9TMU0ybjlOZk1nUFRyNkErNnVvQTVaWlUiLCJtYWMiOiI0Yzc5MDAyZWNjZDJlYjNjYzMyM2UxNzg2NDFiOTZiYjk3OWI4ZjI5ODgyZTUyYjZhYWI2MzQxMjlkNmU1YWVkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkN5dVhZeGRQc0pBQzdJN2luSEhDVEE9PSIsInZhbHVlIjoic05vTGpLK1BiOGg0N3NXVkhHdzA1aEVaTU1PRWhaSnJaVVRSNmpFMGxaREx6M1ZzWlU4SWRhKzJSNDZNb0FyVWo1bTY2bktrOWM1eXNuTG1UY3Y4NkVKcnkyc0x6QTA0Vm1YM0laZFBEenVSdWxrZS9IWGQyaXhmMXhwKzNBb0J1ckpvRndFT05zNzJWSlNmL0NrbmhHbmVDUlQwWHNKUmJoeDZDcFQvUlFPWXIyZCsrcEZXOTIxaEI5aUlQZWp4TE5MRC8rb3QwLzE5UFlIb09KL1BTZzV0SUFwMCsxWndXWUlwMm9nZ3BNTFJDOXl3WGxQTXRQREJLZ25YZDhVZWN2YXdRYnVVT3FkaHhRSytyWjlvMFVMTDJRWEJjNW1oUE9CU2x5WVNlcmZSbXNqZysrMHloTjRxekphS1hZbTZCUnZHRmh1cFFRSHZEVVl1OENPK3V2L0hJbkplQjlEdTNSSGFQN05pQU1BOFBFd0hTaTB5WXl4VUkxYnV5bEZnM0ZZOWQzcG8zUW9RTlhEQmVYZ2VIaDdCWlg3cTc0QzdsalBieWM4RVlwWVNjVG9uMVRBdjExVWlXenc0SWRZMkJyeVVhaGJNQlVQa0dMaUlwT0lLRDZwNzZmckpiQ1lLLzcxZFJtQmxRRzBpWlZUYjBscW9oYkVPWmJUczR0T0QiLCJtYWMiOiI0OWU0ZjVlNjVjODkzMWQ4NGY1OTA5NTI3N2M5ZGEyNjY5MDgzYjMzNDgyMzQ1NTI0OTliNWQzZTdlMTI5ZmM4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpyTjJZM1lLWS96c0hqWGJ6azJCekE9PSIsInZhbHVlIjoiU1NvNkZLT3ZJdjJJVnB6L0U0S3NPaFBaMmo4UVlsR2JMYklzZ25xZnZ6YW1jOGdyeWU2VFp4K0cyK1locmlaaUR3eG1mZ1N4U3RYQXNRWUpkYU9ZMFd2M0pCd2pZWU9lZWc5RHVPb3YzWHZPZ1VhNHRwWGFsODEvbGxNZzZWcThUZXFURVV6MmtIMjVCL09nZVdaNks0bEI4UjYwTkdnMXFnOXczQzU2OUM4Smw3eGdMN24yNzdLbWQ1RWxRNHM1S2RjVm9YUlM0NGpUdnNPVTlyRmU2U3M5ZUswaXdITXZ4QnZ4cVRRVXF0VzZBemFqc0RCZEVSRjd1Vit1b1FOT2ZHTU5lSlplcUhEOC9zNVVsVGxhZTJiRTlRWjNwUk9LcTBKRUlyRjBqOFJ6SUFOcEFNb09jZzJUOVQzQ1hvSi9DcGwwZjZyeHhzN2pJM3VEMzRCTEtPQTdVUUo5ZERQdG11cmRBb0poZHBXNVRiQ29uYzRsSSsrZ0svVTRHTWgxdlZlaE1RVk9qMFJNRE1iZ0UzR0VsS1liYTFPSEJiUVFRMkJzaEV0VUVodHF2LzZPK2FNbXpkUkxiYTlSRWFpdGwyNTl3SVNxLzV6dTczOG0xcGU5dU1CRHhPZTZUZzBBMi9BR0o1eS9TMU0ybjlOZk1nUFRyNkErNnVvQTVaWlUiLCJtYWMiOiI0Yzc5MDAyZWNjZDJlYjNjYzMyM2UxNzg2NDFiOTZiYjk3OWI4ZjI5ODgyZTUyYjZhYWI2MzQxMjlkNmU1YWVkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415928322\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1550399832 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550399832\", {\"maxDepth\":0})</script>\n"}}