{"__meta": {"id": "X635116627c069507e4d8529e9cb7a574", "datetime": "2025-07-23 18:21:50", "utime": **********.821032, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.368357, "end": **********.821047, "duration": 0.45269012451171875, "duration_str": "453ms", "measures": [{"label": "Booting", "start": **********.368357, "relative_start": 0, "end": **********.767458, "relative_end": **********.767458, "duration": 0.39910101890563965, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.767469, "relative_start": 0.3991119861602783, "end": **********.821049, "relative_end": 1.9073486328125e-06, "duration": 0.05358004570007324, "duration_str": "53.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46533576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00272, "accumulated_duration_str": "2.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.79623, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.824}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.806334, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.824, "width_percent": 21.324}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.811927, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.147, "width_percent": 19.853}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-317375524 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-317375524\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1080676911 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1080676911\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1349488729 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349488729\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294908018%7C20%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZKMEw5ak9XeldlWUg0a2hLVU1uMmc9PSIsInZhbHVlIjoiRElnbjI3VlpTNSsza2JYRldQeUh3YjVtY2lCWFdVOTVrREd6LzlhZUVJbUc0WXRoWCtveURPL24wOFM5UU9IL0FLZ2ZGM2o2eU1TYlBmcm1kVFFsUGhBeTFWdjcyOU03dkxrc1RSL2NMVldmNk5rSitFa012UmdJV0ptOWNDTDdrUGxsemNia3Eza1h2TTJJYUcxYVJ4R2h5TWpXRHFmdGU1L1RwMVNVb2diVFVHZlJ0VTFoOHY3ZTk5dEVKc1l6emF2Tmw4ekx4WlRkeFdIZnRjOXUrd001Tm1TdXZlb0FEaWRDYTQwUE4zWUNaaCtlS1hmZ2Z0aWhKTDQ4TzgzTXp3SnJDeEVacnAveFFCVlVMMjlpd1l4ZWxYbEoycXBuRkIxd1RGUFNBdm01Vm52NlRWYkN3SGhmR0ZPTkRaWmgzWlFmWlRPSGdWM0tqbGIzK3F2V29RRk5pUERLSWFLVG92VXoyNktRUnFQK0ZDUkZMWWZLUGVyT2FiWjhxa1lRSlYzWmNLN3U4cENDc1ZPVEFBcm96SWRLd2dRWEwyVVk4R2FqWEluSGo2TkQwOUFXV3QrQjMyUXY0djd4VmlZUDFZZGVYMWtyV1hoSVYvTjBPT2p3bjUrT2tTeWVLMC80MHVmNmtHZVRaWDE5MnlHR09KOC9NeTBNVFFQVWcyUkgiLCJtYWMiOiI1Njc2NzAxY2FmZGQ0YTcwOWE1NGNhYjc5YTJmZWJhMzZmM2ZkNWQ4YjEwY2RlOTI3YzBhZWVjNzFlYzAxNTJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVTSVoyNEplU2JCenVjK2RxOFZxMkE9PSIsInZhbHVlIjoiZlpUZGVuT2QvSWdTZXNCczdSc0pUMHRYbWNYdjl1WXp0dWRYT01aZjZPQTFhUE13TG5aWXI5bVZ3ZGVwM1N3em8vM2JvR0FtRWxwclhoZjVETEc1MW1JTzRLbm9HUURhL1dQSnRCRTdhTnZ5SW5GT1d4VlVZU1RxSUNwbjdRcHJISW9CMjhuMmtoNlVGazY5OVJWSDBEUnlvRDBxZDlsMDdnTDVCQ1dsQy9sMUppbVNBaldIclJxdFR5ZHVaZGhyN1BBM1VHYmdwVmxmbkViSENIZTdTcHcyQjlnaG1lN2JqbjRzYUxTMHpqSlp4K3JNNmN1aGhLcjJnVXZsVXhGTm9TNWJCZGVJR1phZy9HNFRnemQyc2JYY2xwdXdSQmZ4M1NGSnVKZk9uUnpUemFrYXVkVnhYMEtUNTVsNXZKZ3Vicmx2VDZEdGlJbnVTRHlEYUpIRjA1MS9IdkRqdCsxdmQxVmExeUhnVlo5N0dYM3lwcUQyWEYrbnVGOE5kK0QvMjRaOWtvWHlqR3BKV2pORElSb2dUdVFrK2NkbnFTYWYrVmd5VHdBZzRKNWYrVkhoOCtHTFNmNUdqb04ySVduTStrNUJNTFRtbmxXK056aXJGR3FkOGJ2YjYwc0tkVXIzeXl2Y1cydUtqeHpYTG5zVitBTW9jdElFZSsyT1lUSlAiLCJtYWMiOiI4MTlmODE5YThiYjlhOTkyYzhlYjVkOGYwMmU5NGZjNTJiNjRkYTg3YWQxMWFmYTdiZTE1YTFiYmZhZDcxMmNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-77825312 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77825312\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-451273974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVWaUNkRVZwVk9hemhUK2V4L3NnZEE9PSIsInZhbHVlIjoiMjRMeVRZWStwNlJyY05aZ3lOY2QrY3ZGM0pJYjd4L3FPNXFDK3NnU0QxcVJ4aFh6UEE5bTdFTnczUW9ybHcyc0ZVclRMV0hkSHBqcGpuUGZvRDBqR2hrdlk3ekVobVFkWmZFNTNlSXJ4cDVaYjEvSEEwVjFna2duZEZKUHVKNmFmZ2ttUFZVV21HY244L1ZFSFVVRHd6T2JXUXYzYXE3Q2oxdDZkSUFzVFN6WUxoQ2g2eWRFbXNBU0U3TzBiSkg3TFhtRnJFdkZ2aVhvZGtkeElocGhWdTJ5VVVxVWxvT1lVS0FSTXpseHQ5L2FHeUN0M0RVU0hzKzJnakJDMDdxdWRvalRhZllqMGxENDNtVS90NlhET0pzZXNPMDJQSkx5UHExVGxKWlU0NzlrM3VhK3dwNmNvYnJaaHp2S1lxZDhQSGYyMjVLelBwU25mYUNDeEZ0SlIwVmNrdVhWbDRXQXVzL2hzRFVCWHBtUXNRUkxsRDFtN3JLSDlNSTNKK1orQzcva1JBM3Z6cWxxaDFNY090UlA3OHFFbzRyY2tleXJuTlp2R1RWa1dQd0hPeG8xS2Z5a1FwbGZBZlpuUDBYOFhHMXN1VnFhMXNuVzVpTzVuVTlrRXpnd1VSS3R2bFZQM1VVV251Y3NsUWtzU1BSUkU5WEpDbVhZajk1OUhXZWUiLCJtYWMiOiIxYTAwMzdkY2ZmODIyYjkyMjFjMmNjYjI1MzZlN2M5ZGNjYWZiYjBiOGNkZGFkNTJmOGU4NDI5MjgwZGYzNTcxIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImViT1dPUXRudlFFUG1HNEJ4TndySFE9PSIsInZhbHVlIjoiWHhmVWZYTEpLUDRHc2x2djAvbnNJZ3N4Z1cyYW45am9lYks5UkJLWjJKRmQ5eFNjY3lMNGU1d0dyLzU3Sm5CTVluUDRScmtEaXVaZ2lqcVlkT2puQ0w0QlNQYlREaVJzV3oyemNqQnBoRmphNUZ4bWJUYlljSHFvSE9rdjNrQUdLS2xZNm0rNkcrT0k4T3hjdit0cFlsN0E0aUZlT1o3VE9lcUN0RkhkSTR3c05teEIrZlpmTnYyTnB4ZHdmUEVNeXh0ZjBhVjEzTkt2VjdtQjZ4Y1U5eFdrNG5adlhCb3MyWHUzK1JLb0luMFJGdExwQTI3VWZRQXE4OXNiYVU0NWtTSmVNYjhQQ1pHbUp5RVViVVowMTN4TWJLdTlZRFBpQzB0UzVyY2ZRcC92U3hwRlNwVFRPcmhlcVBpaXhpVHI0QlMra2pZeUNZWGdJeW5EbGdXdk9sYWZtWHF2d21DUCt5Rjg5V1pVM3hhRnJsS3lIZ3QxL2VRd1I5aGVQZzlSdE5Jbm1VbE1QTU5GMVNwWEptdUhTcktmUjR0Sllpc1NwbDFRcEpNZHBPd2MyRWt0a25WZmp6Ny9QbGlPQ0VLNEMwQ083TWpZVnY4Z1h3OHMydzhsZGxEbFRIeUJ0NWdGNVFTL0xUc3VDY0RvZWdidldFYXpmYi9KZW5nMWpsalgiLCJtYWMiOiJmMGY5NzkyY2MwYjg5NWJiMWRiMjQyNTkxMmFjOWE5YzRjYjRkZWYwOTIwNmNjNDdiMDNjY2UzZDVlY2QxMjA2IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVWaUNkRVZwVk9hemhUK2V4L3NnZEE9PSIsInZhbHVlIjoiMjRMeVRZWStwNlJyY05aZ3lOY2QrY3ZGM0pJYjd4L3FPNXFDK3NnU0QxcVJ4aFh6UEE5bTdFTnczUW9ybHcyc0ZVclRMV0hkSHBqcGpuUGZvRDBqR2hrdlk3ekVobVFkWmZFNTNlSXJ4cDVaYjEvSEEwVjFna2duZEZKUHVKNmFmZ2ttUFZVV21HY244L1ZFSFVVRHd6T2JXUXYzYXE3Q2oxdDZkSUFzVFN6WUxoQ2g2eWRFbXNBU0U3TzBiSkg3TFhtRnJFdkZ2aVhvZGtkeElocGhWdTJ5VVVxVWxvT1lVS0FSTXpseHQ5L2FHeUN0M0RVU0hzKzJnakJDMDdxdWRvalRhZllqMGxENDNtVS90NlhET0pzZXNPMDJQSkx5UHExVGxKWlU0NzlrM3VhK3dwNmNvYnJaaHp2S1lxZDhQSGYyMjVLelBwU25mYUNDeEZ0SlIwVmNrdVhWbDRXQXVzL2hzRFVCWHBtUXNRUkxsRDFtN3JLSDlNSTNKK1orQzcva1JBM3Z6cWxxaDFNY090UlA3OHFFbzRyY2tleXJuTlp2R1RWa1dQd0hPeG8xS2Z5a1FwbGZBZlpuUDBYOFhHMXN1VnFhMXNuVzVpTzVuVTlrRXpnd1VSS3R2bFZQM1VVV251Y3NsUWtzU1BSUkU5WEpDbVhZajk1OUhXZWUiLCJtYWMiOiIxYTAwMzdkY2ZmODIyYjkyMjFjMmNjYjI1MzZlN2M5ZGNjYWZiYjBiOGNkZGFkNTJmOGU4NDI5MjgwZGYzNTcxIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImViT1dPUXRudlFFUG1HNEJ4TndySFE9PSIsInZhbHVlIjoiWHhmVWZYTEpLUDRHc2x2djAvbnNJZ3N4Z1cyYW45am9lYks5UkJLWjJKRmQ5eFNjY3lMNGU1d0dyLzU3Sm5CTVluUDRScmtEaXVaZ2lqcVlkT2puQ0w0QlNQYlREaVJzV3oyemNqQnBoRmphNUZ4bWJUYlljSHFvSE9rdjNrQUdLS2xZNm0rNkcrT0k4T3hjdit0cFlsN0E0aUZlT1o3VE9lcUN0RkhkSTR3c05teEIrZlpmTnYyTnB4ZHdmUEVNeXh0ZjBhVjEzTkt2VjdtQjZ4Y1U5eFdrNG5adlhCb3MyWHUzK1JLb0luMFJGdExwQTI3VWZRQXE4OXNiYVU0NWtTSmVNYjhQQ1pHbUp5RVViVVowMTN4TWJLdTlZRFBpQzB0UzVyY2ZRcC92U3hwRlNwVFRPcmhlcVBpaXhpVHI0QlMra2pZeUNZWGdJeW5EbGdXdk9sYWZtWHF2d21DUCt5Rjg5V1pVM3hhRnJsS3lIZ3QxL2VRd1I5aGVQZzlSdE5Jbm1VbE1QTU5GMVNwWEptdUhTcktmUjR0Sllpc1NwbDFRcEpNZHBPd2MyRWt0a25WZmp6Ny9QbGlPQ0VLNEMwQ083TWpZVnY4Z1h3OHMydzhsZGxEbFRIeUJ0NWdGNVFTL0xUc3VDY0RvZWdidldFYXpmYi9KZW5nMWpsalgiLCJtYWMiOiJmMGY5NzkyY2MwYjg5NWJiMWRiMjQyNTkxMmFjOWE5YzRjYjRkZWYwOTIwNmNjNDdiMDNjY2UzZDVlY2QxMjA2IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-451273974\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1157246664 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157246664\", {\"maxDepth\":0})</script>\n"}}