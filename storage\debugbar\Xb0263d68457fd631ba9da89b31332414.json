{"__meta": {"id": "Xb0263d68457fd631ba9da89b31332414", "datetime": "2025-07-14 18:29:06", "utime": **********.635012, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.178734, "end": **********.635031, "duration": 0.4562969207763672, "duration_str": "456ms", "measures": [{"label": "Booting", "start": **********.178734, "relative_start": 0, "end": **********.56386, "relative_end": **********.56386, "duration": 0.38512587547302246, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.563869, "relative_start": 0.3851349353790283, "end": **********.635033, "relative_end": 1.9073486328125e-06, "duration": 0.07116389274597168, "duration_str": "71.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991104, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01816, "accumulated_duration_str": "18.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.592874, "duration": 0.01705, "duration_str": "17.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.888}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.619504, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.888, "width_percent": 3.084}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.627033, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.971, "width_percent": 3.029}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-813433366 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-813433366\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1956747651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1956747651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1919200546 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919200546\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-702664925 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517743031%7C29%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVrVWtYNk9yMitnMXBzUjY2eXR3QVE9PSIsInZhbHVlIjoiRHA2MjFyWmRINlBOc0pKd2RBbjBQbnlGVW5QOWlpeXNIa3ZuSGV5R1d2VXlXOHdjM1BSc1g1K29IZDVGZDFqVVR5Rmc5S3g2elNKMW80bTZRdHd3UVRSWCtBbUlMcnJWUjFuSmRzS3YzRmp2dnhna1RDNmRzUWpDWC9NNU9FM1lNeUdFeWxlZGJycGg0TlE4QmpIcUVZQmJHOFpVNnBwSDFLSklVRWpUSGYrejNDcFl2UC9BMHhDNTBlWFlmRDdhWUtyWDBtZ3gyQ2dsRWlabkw3NVRCQUlXOE9reG0zbTRVVVNobUlUd3BHMjM1RGFsSUE1MDVSWmIxS0Y3TllYNkk3UjEzVUZaVnE2VUltTURjakV1NStBUW9QeU5Sb3NKbzJGZHdMNDlpNlV1MDVFMWtJb3pMN1VUSi95K0J0bEtrdUhrN3JFQXV5ekw3VUFLWnFGa2s1Vko4SlhhWUdsalV0Z3ZLV2pVczRYaXlwWExqTFNpamhOQ1dtaUdySlFXc0plQ2JjK1ZkSVlocmNUSkhhQi9wTW0zdFVaV3k4R3d4ODhINVhKS1IwNGNkejUxcWRGYWIzdEZWYTJKbEFkejhKbzNnNjJNVzh4dm9McVhSWFJBSHFyN1g4K3Q3MXBYZ1ZTNzdFSFFMYk5jZWx6K2hINCs3dkdSbUNkcmlnSFIiLCJtYWMiOiJiNDMyYjM0ZTQ5ZGJjYzNiZjM1NmE5ZDBlNjQ2NjYwOTVlNmVjMmY2OTcyZDYwYzc0YTFjMTQ0ZjMyNmI3YmNhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZSL1lVSWdJTTZhYmU0VlRQWjBuQkE9PSIsInZhbHVlIjoiZEVNcnFCSjZTRUpKdlRsMW15UEoxSmRXYU9HUHUyaWJtamRsSVJOOGJINW9jWFMySVhlZzRNbHowWlJsS1pRM0JuMmhTV0ZxY2U3MzBoOWlJS1dmWmk5OUN6TnFNcG5OQWl5Ryt5WG90bXpTT24zNWFCdkg1QXg5RG1xRWVqUE1TYTdHMUF3bUtQTmwvOE9jNExUeXdkWkVSakhUcUtxVXZVSWt0U1phcDlUNXVmTWg5U3hrYXFVN0NTMmZuc00rOGhmZ1YwU0VIdUFRQVBjS3g1T0M0TzAzSXVrU25SRWdyYzRTek5EME9HS2xoSXRscDdmN2MvdEN4Ujd0dWhHVVF5RW1BOHdSVW5KeUZRamZUelorb3ArK0VNbDFtSzhuTDBDb09XV0lQMkpiZ1luU20wU3F4dmViL1R3ZzFUcjkyRHhMODE2bmVkdCtNTWRVMy93bE1JcS9MVXlSY2pIZXZSenFMbkhQYnR0MU1qcFMvb2daUmlEOE5TWTNNUk1oMTVCMi9XMEpUU2tVTDgweTBVTkt3dE9haHZnNGlYTWxyRmd6N2RsRTZUeUNQWDE3U1NLcER3TDZsc0ZET0xobGh1bkRqYUhEQWV3bXU3VSt6dm5pRjF3K2tUeXYwQ24weGhFRHNYMHZnUHpiWXRML1ZwK3c2U0lBdHJtUXlrRHkiLCJtYWMiOiI2NzBlYTg3ODM4MWMyMWZmNjc4ZDJkN2JiYzJlMzFlNWM2MTYzMDM1ZmFiN2I5MmY3ZDIwYWY4NDczOWFkYjg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702664925\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-896968012 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896968012\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-454404472 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVDQ2pLTzNhcXdpUHI2NjBzeGUyaGc9PSIsInZhbHVlIjoiR3dYbERrQm42YlNGVjFlNFhWSThXWmpLMHBBSk9mc2xTMTZNZFhCN3ByRlR0aTNBd3U1TEFDL3U4cW9VMy9jVWhWMkQ0SjhUMFkyL3JIbTdWWmVkTWZ0cXZDeE5lN0E2RnVEQmQva2JyUkZER2F2UHRsM08xVzduVnAvb3dtTUh0MGYrZ01IRzcyaGZBY3dxNmMrNGJwMFB5U2VTWTE1aGdQc0syN1dYaEFpMW9vMDA1T1d2RENnZFd1SXVBc2g5dTUwT2IzM1Q1Zng2Y0luUFZ2S3g5RmRaMXYydCtITTdORTFmVnVOVjZMWUlYeW0rbmo3V1NZYUdpbkNVUTRVSC9ObFJDdE9weHNXOENKMGtEYXp0RTRmVmNyczVMTVZXeXRVdWtJRE9CdlMrc2dmOTZPOEU4K2I2NmNXc1JKdExiRDFoV1A4L3N4SCs4bU5JTnJtQyt4WVE0QVNnaTF0Sk1xWCtOcTNINEhCSVcxZ2J2QTlXSmtPSUNFWGpmOG9vZ2ZqT01Fa2JNVUthQ2FlR0oyMGRTZnFqK3VSblp3VVp4cGJMTkNTdGhTYTB4NlljSDdQOHlBVStETTBXMTFWYXEzczBGOWFRdVo3SSt6RzJiQWdJMmNVZ1hjSkpZaGprRVoybjV6Y0NYdGNEcEEycWhBMGFQdk4zdmk0RkxhdFYiLCJtYWMiOiJkMWM2MGFjOGYzNTUwNjU0ZTM0NmEzMGQ5MjkyNDYyZTQ4MTFkYmNiYzg4ZGUzZGI0Mzk2YjU4YTAyYTczZTc4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkMyeDM4S1BHMkRtcVh2ZHF4V3RJMlE9PSIsInZhbHVlIjoiKzNKd2ZaZVlBemtqMDRHdGJWYnV4RGo0SmYrSFBTeTl3cFQ5SDNuSzAwSzdMWWZVNkpJNUd0N3JFTldpQ015S0czV1Q1WFlWTkpyTUoyakRyRUVLWjFiTjFTT1RGTWoxNUpXN0FPK25QVjQ5UlRxYzRQZFBTNDhWL1pzRlVvSGxGbnM1RmJ2UWVvVnNwcXhNdDkrVFcwc25JK1Vqek5HRnlCSDZvWUMrVTlTMUhGZ0dPSTB1Z2h0T0hyVnRUR0NxL3UrUUR5enRFTlFDM2prbnBJbjFOcktPZ2hMQ3BJb0VBelVKbkh0SmMwM0NkZHcyeGhSN3ZZcllSMlBEdXUwVUFLV2d2aFVieHpybzZqRmlMVFlMcW45SnVlaHZ4VUgyd0NhWjBOWmZIdG43c094cHBIbmlHMytwY0dUK1FCV1I5NTBrd1Yra3huTVUxbWhWU0MxZ29HWks0Q055Yk5udmxOVTlGY1A3TExqOFpYRHEyeVE4N09lamVnNFdWZno4V1ZlNlRIQlJWUWl0S3Q0QW1pY2I5UjNUSWJRUTNvVVBhcEpERFNBNkUzMm9GSVc5b1pMS0xpV29OUVRDL2M1M1dKdHVBV3dyVzdHZElaL21uK2h6Yk12VWJadVZXS04yc2R2bkduMGwrdjExeFRjb1dYRnV6UVZOcnVJSlExVHIiLCJtYWMiOiI4NTYyMTgwNTRmOTg2MjJlZWYyODU4ZjNjNDM4MzFkYjBlZWJkMWEwMjNjOTdhYWZhNTg1OGRjZGEwYmI4MTRhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVDQ2pLTzNhcXdpUHI2NjBzeGUyaGc9PSIsInZhbHVlIjoiR3dYbERrQm42YlNGVjFlNFhWSThXWmpLMHBBSk9mc2xTMTZNZFhCN3ByRlR0aTNBd3U1TEFDL3U4cW9VMy9jVWhWMkQ0SjhUMFkyL3JIbTdWWmVkTWZ0cXZDeE5lN0E2RnVEQmQva2JyUkZER2F2UHRsM08xVzduVnAvb3dtTUh0MGYrZ01IRzcyaGZBY3dxNmMrNGJwMFB5U2VTWTE1aGdQc0syN1dYaEFpMW9vMDA1T1d2RENnZFd1SXVBc2g5dTUwT2IzM1Q1Zng2Y0luUFZ2S3g5RmRaMXYydCtITTdORTFmVnVOVjZMWUlYeW0rbmo3V1NZYUdpbkNVUTRVSC9ObFJDdE9weHNXOENKMGtEYXp0RTRmVmNyczVMTVZXeXRVdWtJRE9CdlMrc2dmOTZPOEU4K2I2NmNXc1JKdExiRDFoV1A4L3N4SCs4bU5JTnJtQyt4WVE0QVNnaTF0Sk1xWCtOcTNINEhCSVcxZ2J2QTlXSmtPSUNFWGpmOG9vZ2ZqT01Fa2JNVUthQ2FlR0oyMGRTZnFqK3VSblp3VVp4cGJMTkNTdGhTYTB4NlljSDdQOHlBVStETTBXMTFWYXEzczBGOWFRdVo3SSt6RzJiQWdJMmNVZ1hjSkpZaGprRVoybjV6Y0NYdGNEcEEycWhBMGFQdk4zdmk0RkxhdFYiLCJtYWMiOiJkMWM2MGFjOGYzNTUwNjU0ZTM0NmEzMGQ5MjkyNDYyZTQ4MTFkYmNiYzg4ZGUzZGI0Mzk2YjU4YTAyYTczZTc4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkMyeDM4S1BHMkRtcVh2ZHF4V3RJMlE9PSIsInZhbHVlIjoiKzNKd2ZaZVlBemtqMDRHdGJWYnV4RGo0SmYrSFBTeTl3cFQ5SDNuSzAwSzdMWWZVNkpJNUd0N3JFTldpQ015S0czV1Q1WFlWTkpyTUoyakRyRUVLWjFiTjFTT1RGTWoxNUpXN0FPK25QVjQ5UlRxYzRQZFBTNDhWL1pzRlVvSGxGbnM1RmJ2UWVvVnNwcXhNdDkrVFcwc25JK1Vqek5HRnlCSDZvWUMrVTlTMUhGZ0dPSTB1Z2h0T0hyVnRUR0NxL3UrUUR5enRFTlFDM2prbnBJbjFOcktPZ2hMQ3BJb0VBelVKbkh0SmMwM0NkZHcyeGhSN3ZZcllSMlBEdXUwVUFLV2d2aFVieHpybzZqRmlMVFlMcW45SnVlaHZ4VUgyd0NhWjBOWmZIdG43c094cHBIbmlHMytwY0dUK1FCV1I5NTBrd1Yra3huTVUxbWhWU0MxZ29HWks0Q055Yk5udmxOVTlGY1A3TExqOFpYRHEyeVE4N09lamVnNFdWZno4V1ZlNlRIQlJWUWl0S3Q0QW1pY2I5UjNUSWJRUTNvVVBhcEpERFNBNkUzMm9GSVc5b1pMS0xpV29OUVRDL2M1M1dKdHVBV3dyVzdHZElaL21uK2h6Yk12VWJadVZXS04yc2R2bkduMGwrdjExeFRjb1dYRnV6UVZOcnVJSlExVHIiLCJtYWMiOiI4NTYyMTgwNTRmOTg2MjJlZWYyODU4ZjNjNDM4MzFkYjBlZWJkMWEwMjNjOTdhYWZhNTg1OGRjZGEwYmI4MTRhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454404472\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-757333896 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757333896\", {\"maxDepth\":0})</script>\n"}}