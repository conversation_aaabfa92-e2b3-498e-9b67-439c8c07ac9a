{"__meta": {"id": "X68305f3a4f3e3edaaa4dbfe75b1c92da", "datetime": "2025-07-21 01:17:52", "utime": **********.64305, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[01:17:52] LOG.info: POS Search Request {\n    \"search\": null,\n    \"type\": \"sku\",\n    \"cat_id\": \"0\",\n    \"warehouse_id\": \"8\",\n    \"session_key\": \"pos\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.636327, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.209708, "end": **********.643072, "duration": 0.4333639144897461, "duration_str": "433ms", "measures": [{"label": "Booting", "start": **********.209708, "relative_start": 0, "end": **********.568667, "relative_end": **********.568667, "duration": 0.3589589595794678, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.568678, "relative_start": 0.35896992683410645, "end": **********.643075, "relative_end": 3.0994415283203125e-06, "duration": 0.07439708709716797, "duration_str": "74.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49181856, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1271</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00303, "accumulated_duration_str": "3.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.605483, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 52.805}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.615854, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 52.805, "width_percent": 15.182}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6301801, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 67.987, "width_percent": 21.782}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.632219, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.769, "width_percent": 10.231}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-743450951 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743450951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.635615, "xdebug_link": null}]}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1964264289 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1964264289\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-786806879 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-786806879\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1307689593 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1307689593\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-386024554 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlEzNW9UYVR3cDRLcUd5ekFIcjJDRUE9PSIsInZhbHVlIjoicHN1LytCUnZHdkVsa1AzaTBkek8xTGZRQ2xheWc3ZUpNRldaWmI1T1lSZjR3dkh2eWd4M2k1NmhjdUNsMzI2N3c0YW8yVitoT2lGNmlSRlcwTDduUzVDNHI3VTI5NS9ZYWYxR1N6Y3pJV052Sko3eXZGMXlhS2pML0dESWtON1NhbDY4MTRwOFN4ckwzdUVWZHZwbUFhZFRTRzdFYmR6eXV2NTNKN1FJWVM2Y3NwVWEza1pKN3hZYWhTM0R3REtNUkN2b2xHWVhjNUhIUDlhR1czU081RTVuWFRyMEIyMDRVbFB0NGdGN1J4NmJpS0tVemZIMkxrSUNYQXUyYVcwZS9NYXNDdFZrM2UvcEdMc1YzZmR6VEQxU3k1c1hTemd4WGZVWk01RlVnNzNMQUVFS3BJSDBOTlIyNnNrZklnTUQxWlJ5N292cDhabmFHeEMzcWxHNnNhN2xJc1hJWlZoV1gvZHJnZ3hCY3oxWXhudmVnMjFqdVJ2TTVaWEhCMzdsSi9uRWs3T0RHc2gzL1c5Mk5LM3RVSVFFNGtmMVBoQUdvaVo5YmhRSDhTOTJZYVVnMjlkVDNHNzRjbmg1L2tJL0JMajRGeVZpQjg2TnB6MjdxU0dsamJPcnQ2bGNNYVI0MDFjLzlGcGU0eXpPc00rSmVGKzVsR05tbWpZUU01TUgiLCJtYWMiOiJhYjA1MzE3ODc4ZDhhYmM1OWJmMmRmZmRkMjlhYTk2NzQxNmYzYzllNGZlNTY0YTA5ODVmYmE0ODk3NjRmYmUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdTWG5MOUp3blJmaGthdzRuTkR2MlE9PSIsInZhbHVlIjoid0hEQ2ZrMHdkK2puUHFZMGxacW5tcWk5Mi9ZbDV3ckZ1M1Z1UFhrMjI2aG82bmVzc1dsU1k1SXpqNFA1VzZzYVkyVUJ2MHkwNzZ6dWZYdW42cDZVdUFLRXZUS2l2VGZpUmhlQmhQVFplZTl0TUJSc1pTVTZvdURjK3h4YVNwNVE4b3FoNW8zVXZvYUxCZWJSR21kMk1NYTFtekZRQ0llT0c4SjhHMVFqWHVQekRuVUc2clFxZ042U2JuclFqNkxIRHJ2MEVqUjNRU01SeWZQcTRGZVplNUJoc2FON2RnM0RzeWMydXhucG9wUFh3aG0rNGhTZkdZQlFWOWEzREVFbTljWFBkbjBwaUVsNUVmTnRRTXE4a2F1QVlpSytJNVJtb0lHdGNoeHk3Q25HMms0ZTd2WS9nWXBNNWRtRERFSTNOUjBqSjMyaTNBR2s2S2tidHdCSjl1YUZYeHljbk5nU0ZYL3ZmM0ViMGl1dFFMMFQrc0ZGMGxaVzJKSVo1SkVXNGkrbXpFdTh2RHk1T1U5ZlMwcjkrb0NkTVZxeGpnYlR2M0RGZ1BEdmEzZFlNZTU1N2lPNzdFRUg0REN6TlNuYTdBbCs1UnpPRkI2bHljVDk5SWkwWENod1h2Y1RSVG5iMnB3WnVFWmFJQmxUTWxZdHROQnBEYVkzRUJZeUh1a0kiLCJtYWMiOiJjNjhiYWEyMmJkOTAzYTUxM2MyNjE4ZDJmZmU5M2I0MWU0NTA1ZjQ0MGU4OTg1YWNjMzc3Y2ZkNmFjMmZiYzczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-386024554\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1260388807 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260388807\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1583635387 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9NVDlTL1hDaU50RTE4aUgzV3h0c0E9PSIsInZhbHVlIjoiNm1HUElFc0tXNThxdGFiSXYwRVVXbnpVZmZSV1J6N3dmY2M4S0ZpRnJPbFYyY3VlYTlZb0J2Yk82a3haZ3c4eEhrWWVSbXZvM3BRTm5UZHQ1dFArSWlaRzNhdGJkcHErZkJhME9WcUJYdjI1aWxWUUxIc3I0UGZDU2pvNjdZRWlsbWtyQWxsWTI3dElOOUl0aHowdWpnRmd5VjB5a3NHRGJGbmNYWkJEU1NBdm83QUNlVkF3SzRpTDByaU16NEJBSW9sV1JSeFFndjhFa2x5V0NWTEVtekJjblVYNHpkbm5SbkVmc1NkQitZNnV4b0tEY2ZIbis5NnExSzZTQ1czSVZmbm91aFQvUjZmM2VrZVAzZWJRMWJ3ZWtkcW1aczNVZWdXMWI0ZUJ5VW9aSUtFV1VwcU1McFhHc2I4eWtWckVMVVo4NGZSSE04VVRzc2JqNjVNaUxCTVVyZWVoaGNteFhZbElOaGZRZXBFOXkrS3RKbC92YjFpdlo0bzRzR0dRYVByTk1sRzg2N3Yxd1Q5QUlaQWRiTWxHa2xsaFFEa1ZPeHlXUE1XcEdHejQyVUZzb0ZTNUFrbGEydFdWSXVuR3V1cStlY05WbzUwdTNMUDFxaHdQZDZ0eXkya01QN2NsdFpyZ0JhSlBVMWRXRUhQcmNVZ29jRGw1VlhaUjZKUTkiLCJtYWMiOiI1MWFiNGI4MzA4Mjc4OGJjMTlmYWIzNmI0YTI5ZTEzZTBmMjU5MGUyM2ZkZmNkMDI2NDFkY2M2ZTE2MDQyYzE2IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFYZXIrRVBqUXRVZXFuYjFGZmZHUXc9PSIsInZhbHVlIjoicXQ0NXJPVmFCYkR0Yk1xYTVMZGxrcmtGY0w4MFJYUm9Jc2hzbEtrYS85RnRKblhybS90SzNnYmlMdGtHdTBQSzJSVjBnR0lTRTQyVzRQM1hMWTZMRDAzYndsTWdQWS9mdVhIckZROU1MNnc4bzVreVY4bU8xRnVwNWlXN2NPOU8yUmtsd3pFb1orS3BHQVpjeUlnZTlIYi9nVTlISjQxSmNBenFDN252N0xRdkg0dmRpaVdPdFI2VWNPQUhYQzJWbmx4M2JOS0xsc0dHNGIyTUt0UVQ0SlZPMHo0b0YzZkxVenhTMW0xcVJOSE5YL1YrYUttT0laVlVUblZIOVpFQlJ5V2VLT2p3R2ozckdGbHk1L3dJdW84RFRucSs1YnMzaWpPWlhzZEVaUFQ1MnlYQXJzVEtmRyt6U2paanJBSUtoRzJuQWxPSjJCRzRPOFZ6MUYrN2lnWEMzVy9qbnFWNXA5YVB6NFdWSU9NRFBVU29HczFoRmRHTyt3Q0N2MTVmNjA1K3hYMVNOV3I4TkxxbFJJVTJjQjlaTUFoNXNON0RORFdKK1BMMWVvSEpaWUVEMVhwLy8wYTdsSjlaU29VL3lUQS9ROU9zNk83eVNnT1FRSG10dnRFR09DNDRZMHc0a3lOM2x6YjJnNWRlL1hNS3R3dTZmYS9qYy9uTXFrZjQiLCJtYWMiOiI5NjI4MGNiNzEwYzViYzJhMzZkNjM2ZDgxZjIwZDExNjk0ODZmZjcyNjI0ZTU3YTBlNGI1NWUwZDI4ZDk3YTBhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9NVDlTL1hDaU50RTE4aUgzV3h0c0E9PSIsInZhbHVlIjoiNm1HUElFc0tXNThxdGFiSXYwRVVXbnpVZmZSV1J6N3dmY2M4S0ZpRnJPbFYyY3VlYTlZb0J2Yk82a3haZ3c4eEhrWWVSbXZvM3BRTm5UZHQ1dFArSWlaRzNhdGJkcHErZkJhME9WcUJYdjI1aWxWUUxIc3I0UGZDU2pvNjdZRWlsbWtyQWxsWTI3dElOOUl0aHowdWpnRmd5VjB5a3NHRGJGbmNYWkJEU1NBdm83QUNlVkF3SzRpTDByaU16NEJBSW9sV1JSeFFndjhFa2x5V0NWTEVtekJjblVYNHpkbm5SbkVmc1NkQitZNnV4b0tEY2ZIbis5NnExSzZTQ1czSVZmbm91aFQvUjZmM2VrZVAzZWJRMWJ3ZWtkcW1aczNVZWdXMWI0ZUJ5VW9aSUtFV1VwcU1McFhHc2I4eWtWckVMVVo4NGZSSE04VVRzc2JqNjVNaUxCTVVyZWVoaGNteFhZbElOaGZRZXBFOXkrS3RKbC92YjFpdlo0bzRzR0dRYVByTk1sRzg2N3Yxd1Q5QUlaQWRiTWxHa2xsaFFEa1ZPeHlXUE1XcEdHejQyVUZzb0ZTNUFrbGEydFdWSXVuR3V1cStlY05WbzUwdTNMUDFxaHdQZDZ0eXkya01QN2NsdFpyZ0JhSlBVMWRXRUhQcmNVZ29jRGw1VlhaUjZKUTkiLCJtYWMiOiI1MWFiNGI4MzA4Mjc4OGJjMTlmYWIzNmI0YTI5ZTEzZTBmMjU5MGUyM2ZkZmNkMDI2NDFkY2M2ZTE2MDQyYzE2IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFYZXIrRVBqUXRVZXFuYjFGZmZHUXc9PSIsInZhbHVlIjoicXQ0NXJPVmFCYkR0Yk1xYTVMZGxrcmtGY0w4MFJYUm9Jc2hzbEtrYS85RnRKblhybS90SzNnYmlMdGtHdTBQSzJSVjBnR0lTRTQyVzRQM1hMWTZMRDAzYndsTWdQWS9mdVhIckZROU1MNnc4bzVreVY4bU8xRnVwNWlXN2NPOU8yUmtsd3pFb1orS3BHQVpjeUlnZTlIYi9nVTlISjQxSmNBenFDN252N0xRdkg0dmRpaVdPdFI2VWNPQUhYQzJWbmx4M2JOS0xsc0dHNGIyTUt0UVQ0SlZPMHo0b0YzZkxVenhTMW0xcVJOSE5YL1YrYUttT0laVlVUblZIOVpFQlJ5V2VLT2p3R2ozckdGbHk1L3dJdW84RFRucSs1YnMzaWpPWlhzZEVaUFQ1MnlYQXJzVEtmRyt6U2paanJBSUtoRzJuQWxPSjJCRzRPOFZ6MUYrN2lnWEMzVy9qbnFWNXA5YVB6NFdWSU9NRFBVU29HczFoRmRHTyt3Q0N2MTVmNjA1K3hYMVNOV3I4TkxxbFJJVTJjQjlaTUFoNXNON0RORFdKK1BMMWVvSEpaWUVEMVhwLy8wYTdsSjlaU29VL3lUQS9ROU9zNk83eVNnT1FRSG10dnRFR09DNDRZMHc0a3lOM2x6YjJnNWRlL1hNS3R3dTZmYS9qYy9uTXFrZjQiLCJtYWMiOiI5NjI4MGNiNzEwYzViYzJhMzZkNjM2ZDgxZjIwZDExNjk0ODZmZjcyNjI0ZTU3YTBlNGI1NWUwZDI4ZDk3YTBhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583635387\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2009307128 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009307128\", {\"maxDepth\":0})</script>\n"}}