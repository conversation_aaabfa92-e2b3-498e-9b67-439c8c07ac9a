{"__meta": {"id": "X5613d7fa504270f7cc06053b388eb8a7", "datetime": "2025-07-23 18:23:10", "utime": **********.742135, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294989.992416, "end": **********.74215, "duration": 0.7497341632843018, "duration_str": "750ms", "measures": [{"label": "Booting", "start": 1753294989.992416, "relative_start": 0, "end": **********.409945, "relative_end": **********.409945, "duration": 0.4175291061401367, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409955, "relative_start": 0.417539119720459, "end": **********.742152, "relative_end": 1.9073486328125e-06, "duration": 0.3321969509124756, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52449944, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0023399999999999996, "accumulated_duration_str": "2.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 70}], "start": **********.45569, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.051}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4605079, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.051, "width_percent": 17.949}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-1141320293 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1141320293\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-905937823 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905937823\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-443347486 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-443347486\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-142147597 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294980478%7C6%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRPaENXdEhXT1VsOThIWmxMeHQwZnc9PSIsInZhbHVlIjoiaTE4OHRPMWVKWnYyVUZaRGNZU1JxNU5Bci9EVEM2TFc3VlM0azJPcndwSzlrRW9BUXppYTNpMVVNMzFFUG1CTS9FM3BNRVE3VFJPWlhodnVrVm5lL0ZYQ2VJUHRoWjlwelNvTWdWWFpvTjcxVndFbFNnSUpVbzdxNCszOFh0L21wdXNFV1R2czdJMXIwdTgyVDdnUW81bCs1MWhBaEhPODdLbk5CWXc1VGpGS3ZPV0duZnR2MWloMGpRazFkVFM0Q0h0ZC85RzdGd1RJU3FBbmVzZmRRK2x2UTJsU3dlUU1RTFpOYVdjSS9hN0wzeFV6NGNQd3MxKzNVQk1iN05Cd3lvUU5yWUVpakgwQ2QyazZ5Ukt4b1pJTm81QzFXRExXNkI3WWNLOFI3Q2pjemhkazhKMGp6SkM2TVo0RmdQdFRqNU9Kc2E3Q1ptN3FVVVpvOE1XOExXQVc1bEo1b0ludG1jSHp2dlNlWVNVWUFyMWtTWFJFdythUUJ2dWlUcS9JNE5VZHJBb2RSbkpRYVhZYlZ2YURtWFdvTkpBM281M0gxZzM4ZW13Y1ZnT0EzMXpWdVpWVXJia3ZNV1pnWlNtd0MvcTB5U0dPNnM4cloxelAzT0VsYkRCSTdCUVVkTGRwYW1OcFcxZDNnQmh5ck9KaHU2UmNqSVA5YVZ0d2dQYTQiLCJtYWMiOiJmNDg2YzM2ODdlY2FhZjYwYTRiY2RlMzFlM2FlYmVjMTQzOTgzYjhiYzRjMjFhOGIzMzRiMzUyODk3NTkwY2ZiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRjRGQ2QWgyTkJSQTF5eUtrS2lxS3c9PSIsInZhbHVlIjoianlOT2J0bWZ1ei9TUDkrOS9NUks1MEU3MmJTSTdlWlpNeCtheTIzT0dDUW1QT0pZRndyNXNGWm5JTGpPZmorU0l2bU9hSHI0dG1BNkdUZ1U1ejBXallUMzI2MHlPc2FFWFdXMFBjL09MQ0tYSTEvcHI4dzNHcXBBWGRocmpEMWxxM3M4V01TOTAvSlduRnRMSGRZalR5SVFHY0ZHdm4xRm9LZGU1akR0WHFrcE5yUEpGU21LRW5NU2dFQ016Vk85STRUZ3pCNmJpd0F3amUzK0pRVk5telZGd3hHYkhicDhzY2Frc3hhM2xDUXErL2ZGam1ORU42Rld1djVWVlNERHVVUTVOMXNua2YrSGpTR005ajYzQ0xwNjROdzFQSHJNZlJsZmF3VTdsQjVENk1OVnJhazNnR1dFVXNOYmVwaW11cC9NZlUybTh3cEFTZWRMZTZGQnc0dmxBam0zb0l2U0tkV2lsc28yZklNRjFaL0IzSUxDelUxenB6bis1b01NTnhpWlEyMFI4T2h0dnlXZnp5cGE3ME1ZdTluQVRscHoxc0tjSnhtNytiVnVnclRrMzdjUEU5MEFjZ0NCSktseG0zdnZTMWhNZGVhS1JqanhlSG4wSGczSDZCdDlyZ2dFeDJVRlppaXFGSjhrSHd4aHdZZk04UWo3dzNnQzMxc3ciLCJtYWMiOiI1MTVlNzgzZjc4ZDg3YjM4N2IxZmE3ZjRmZjcyN2E0MTg5YWU0NmFhNjlhN2I4NGJkZTVhM2YwMDI0ZTEwMjQ1IiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142147597\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1494493775 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xWPGJpNWbmDLkwLBtG0MdpEYkMbvhJtQ6DvLx5Cj</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1494493775\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-326153594 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:23:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRsMzlqWUZpTzNvdFJrcjcrYkhRQ2c9PSIsInZhbHVlIjoiU2dPazllSEFkd1pEcUZCM2p3cDNxOE01ZS9ZTGIvanMrYVp0L0dhbkZ5TitXSUdzaUloV1FTQWJwZDhXNDJpdXA2UEYrdHc3TFc5VGhjbjBUNlltcWo5cm9mVEF1enJvNWhjbW9zK1ZOY0JxeHd4cXNSdXlpdWd4QTk1SVIrWlRsNWJwYnlFckVMQUtoVFBaQWFaSGF6Nzd0Z0Q4cHVCL24zeUFaa2hXdE1ncWlZTjIyeC8wTDdLOHJyWlQ2K0dGWlo5akZySVdkZWRWUEFzK0o5VDBNR1JGZ3IzMlVCNFZaNU8xVnZuejFMeUQ1L0RxMXllalBINHJlcGhVMzJkSTE0Qjl3S2cxNnQ4SGxoN3F1UkpoTTFKMnd1SWtKY1k1d1g4YVE4VHNheERhV0VDN2V4eEl5aVVRY3BJSFZ5Ri81SnJQblJQOEZRNlZkalMzL29WQnhxQXdwc2pQTk5tNzRYWEhtdmxCeXJKNERiaDBPV3VoMDQ2aFZXc1krdnVraGhLb3JMK09wenpoS0RnZy9WN2EwT1J2MWE4cGdIMmtQUnpKQTRrWWl6azdMVk9CSGVxR01INDFvVjkyeTZRek5pdUF1eU1venRrdVNSamJ1cFFWRlI3RW5IWUkzVUhmY05vYlpiQklSNFZoT05LYVM4aDBhbS90NTdOTENVcHEiLCJtYWMiOiJlODFhYzEzMTM0NTdlNTdhYjNlZjRhMjExZTdmNTI5MjMwMmU1NmI2ZmZiM2EwYWMzN2MwMWJhNDIyMWY4NmFkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFCL3BteDc1ZVJYekRLSExkRnZoMFE9PSIsInZhbHVlIjoiWmFFN25jbWRzeFpKYytYNHhCZTNQSEhaVmovcmtTMU94VHNCeHUzT0ZqK05wcEVibmtZZnIzZzhyUTNDTC9EdkllbHdvTzdNUGp3YzBkaUlIQjFibDFibkNuZHNUMXpiYk1jbTAybVE4bVVocnFtUFI0Nk5QeHdrSGYrNHYrMU9sTVU0T3VScDNCNm5RaFFMd3dwOFdCS3g1S1ZUeXBNQ0ttU3d4d1VsNmxDS3hMejQ5M1EyTGpoeVhVUm05dDMwOXVoMHJteE04TkxucWo3by9PVW9pUVp3Wlk1TkZVMTZtOUcxTEd2dEFOb0FscGVHdlRJc1hOZ3ZTTk82L1dLbTkzT2FaY1E4TUtSZktHMFJZeHFTTlI5QnZnVFBnb3BUMjNBNk1JdXIrSElyRFRCQmRaL2FCM05nVVI0STAyNnlmZW8rNlR5ZGlLQ3hKMmNZNEFHejBiK3ZtOW5qL05sRVNVUHdLVVBtYXFUZFllMU5XcVhKTXpyNTJ0dVZjM1BlQW4wN01yVDNucTh3bHJDb1NHT1pBSm4zQ29oV29ORmtRczFwUE1abmtleGkveEExWVNZbEozaDAxajFRcER2VGU4M05XczY2WUlZUzZuK014RlJWS3kyeW1DK1JFQ2VPNTFJZnVWR1hNUGhlS3RWU0MrRkVzQy95MWNrc3hEaFgiLCJtYWMiOiI5YzhjZDIzOGE0OGJmZmRjNDE3OGI4MjU2ZTc2NWVkZjNiY2QzNThhMTM4N2M3YTNkYzYxODAyM2JmZGRhZmZkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRsMzlqWUZpTzNvdFJrcjcrYkhRQ2c9PSIsInZhbHVlIjoiU2dPazllSEFkd1pEcUZCM2p3cDNxOE01ZS9ZTGIvanMrYVp0L0dhbkZ5TitXSUdzaUloV1FTQWJwZDhXNDJpdXA2UEYrdHc3TFc5VGhjbjBUNlltcWo5cm9mVEF1enJvNWhjbW9zK1ZOY0JxeHd4cXNSdXlpdWd4QTk1SVIrWlRsNWJwYnlFckVMQUtoVFBaQWFaSGF6Nzd0Z0Q4cHVCL24zeUFaa2hXdE1ncWlZTjIyeC8wTDdLOHJyWlQ2K0dGWlo5akZySVdkZWRWUEFzK0o5VDBNR1JGZ3IzMlVCNFZaNU8xVnZuejFMeUQ1L0RxMXllalBINHJlcGhVMzJkSTE0Qjl3S2cxNnQ4SGxoN3F1UkpoTTFKMnd1SWtKY1k1d1g4YVE4VHNheERhV0VDN2V4eEl5aVVRY3BJSFZ5Ri81SnJQblJQOEZRNlZkalMzL29WQnhxQXdwc2pQTk5tNzRYWEhtdmxCeXJKNERiaDBPV3VoMDQ2aFZXc1krdnVraGhLb3JMK09wenpoS0RnZy9WN2EwT1J2MWE4cGdIMmtQUnpKQTRrWWl6azdMVk9CSGVxR01INDFvVjkyeTZRek5pdUF1eU1venRrdVNSamJ1cFFWRlI3RW5IWUkzVUhmY05vYlpiQklSNFZoT05LYVM4aDBhbS90NTdOTENVcHEiLCJtYWMiOiJlODFhYzEzMTM0NTdlNTdhYjNlZjRhMjExZTdmNTI5MjMwMmU1NmI2ZmZiM2EwYWMzN2MwMWJhNDIyMWY4NmFkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFCL3BteDc1ZVJYekRLSExkRnZoMFE9PSIsInZhbHVlIjoiWmFFN25jbWRzeFpKYytYNHhCZTNQSEhaVmovcmtTMU94VHNCeHUzT0ZqK05wcEVibmtZZnIzZzhyUTNDTC9EdkllbHdvTzdNUGp3YzBkaUlIQjFibDFibkNuZHNUMXpiYk1jbTAybVE4bVVocnFtUFI0Nk5QeHdrSGYrNHYrMU9sTVU0T3VScDNCNm5RaFFMd3dwOFdCS3g1S1ZUeXBNQ0ttU3d4d1VsNmxDS3hMejQ5M1EyTGpoeVhVUm05dDMwOXVoMHJteE04TkxucWo3by9PVW9pUVp3Wlk1TkZVMTZtOUcxTEd2dEFOb0FscGVHdlRJc1hOZ3ZTTk82L1dLbTkzT2FaY1E4TUtSZktHMFJZeHFTTlI5QnZnVFBnb3BUMjNBNk1JdXIrSElyRFRCQmRaL2FCM05nVVI0STAyNnlmZW8rNlR5ZGlLQ3hKMmNZNEFHejBiK3ZtOW5qL05sRVNVUHdLVVBtYXFUZFllMU5XcVhKTXpyNTJ0dVZjM1BlQW4wN01yVDNucTh3bHJDb1NHT1pBSm4zQ29oV29ORmtRczFwUE1abmtleGkveEExWVNZbEozaDAxajFRcER2VGU4M05XczY2WUlZUzZuK014RlJWS3kyeW1DK1JFQ2VPNTFJZnVWR1hNUGhlS3RWU0MrRkVzQy95MWNrc3hEaFgiLCJtYWMiOiI5YzhjZDIzOGE0OGJmZmRjNDE3OGI4MjU2ZTc2NWVkZjNiY2QzNThhMTM4N2M3YTNkYzYxODAyM2JmZGRhZmZkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326153594\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1168019355 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168019355\", {\"maxDepth\":0})</script>\n"}}