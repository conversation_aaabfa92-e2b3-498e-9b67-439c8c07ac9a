{"__meta": {"id": "X928f1e083ea50556ed60db2fc7f328a9", "datetime": "2025-07-14 18:29:33", "utime": **********.18917, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517772.739943, "end": **********.189185, "duration": 0.44924187660217285, "duration_str": "449ms", "measures": [{"label": "Booting", "start": 1752517772.739943, "relative_start": 0, "end": **********.131749, "relative_end": **********.131749, "duration": 0.39180588722229004, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.131759, "relative_start": 0.3918159008026123, "end": **********.189186, "relative_end": 1.1920928955078125e-06, "duration": 0.057427167892456055, "duration_str": "57.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00319, "accumulated_duration_str": "3.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.164322, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.755}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.175261, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.755, "width_percent": 27.273}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1819, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.028, "width_percent": 10.972}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-302851744 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-302851744\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1530235959 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1530235959\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-669453898 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669453898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1406310805 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517770707%7C34%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVQdE53d2FUTnl3YmZwNEJDYVVxOGc9PSIsInZhbHVlIjoiNjZlR0xnbytNZU84dWNWSUxkNW5uY2xWaldMdktxbkp1QmpGVjUwUjJSRlpvRGd1TmZURllBZm8zM0gzcGR6WldJRlg2aStPbDc0ejBBU3JxMVIvbTVPbmswMERhakU0UitTT3JsOGJCcHVZZTY0UXQ1dTNJTUNpZ25zYy9aU21uWW9ucTdmc08yTnRPU21lTk1oQ3dUb1loQkkvOWw4eHk5b1ZiWWRWQi8yM2Nja0k0TFplNEJ4a2pRbG5wcTQ4ZXpyaWl2ODlMVjdFb2hiYkZTMVc0ckdXeEFyNkhzbGN0V0M5TENkMm1aUys2WWtLMWFwUGlOSXpoOVRDcTUxVW1uK0JUOXQ0b1VpR0ZqT2cveGRvOG9Fcks3WWl3UHc1WWxQYm5ZNm1NZGFNLzl3bmZBMmxIcVNFNlNxL0hjckdqNkYrNVhVUWFTNlkrN3o2Tm5Qc2JneEhyWW9vdUpiYklKTVBjK2lSaWwzR0JNa2ZJMFpRU0xwbkV1Y2ZBRDRJOGd5ODBVRVhTMHNibVV4TUx4QzE2VmJyK3h3RC9tV1VZS213cFJaUENYTFlpSVNCcERyKzRBVnBTNzFJYWVtMG5FWXAyZFdUTFdyK3EyM3V4NVVaSUMzOWtEMG10VHg0ZlZRc2p1K1poOHFaamE5aCtFcjdHWTBuaGNCYy96VSsiLCJtYWMiOiJjMDE4YTk0OTQzMGU3MTNiMjg0ZWM3NzA4NzEyZWU3YWE1ZGFkNjUyNzVmNDhkOWZkZmJiZTZmNjg1Y2JlODEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZxbXZHcHM1dmJpaEsyK0gwRWM1S3c9PSIsInZhbHVlIjoiUC9DSGhjQWJrWnhKRGdTa1R5d3hraFhtS3RpZnlXNG53TVZ4dktxTFhJUi9ZUFJxZVFRRWIwME5ZZkJoNkFmZm5oRmR0OEEyUnBuVzBndVIzOVd3dmhRUm1GUHRHdlJDeGx5YytLcU82YjBvaFpaVExzdmpwUEhzd3NMandvbGsyR0hJS0EyRE94V3Fhdi9LTHUxNjlQc0d0N2YvWWFwQndjZU0xZExpN0wwc2diS1ZrU1UxUGFUQnFRK3BWNnpSRDZxRmtOM2NhL3hGSVUxenBIUHJDbUpkSWIrVzRwMFlVOUZJV3hncWE1R3RXSWh5SlA0MCtlQ2RIN2FvcTdMMHNzcEFKdkJuazB0S1VHWWpKaVdmRDd6YkdkZVB3bDNBYjhjWjBuajZFaFlHRW5oZXpMSkdzdndCVHhlUHdVNWVaRCtJSHlkT1BzeW5oNWgyZVN2NlprdDcyZE5lZklrMmFVeDhvWXFUekZrWmpHbEVuSXAybldDMENyZWpyWHN6STBVamU4TUg0Q0JwQXpEM1NCSzNKVlVlOXorenRrNExMeWJlQUlXYTNUaUJUN0FPRlFpYUN6RjNFUVpBdTRWeFAyVVRMRnVsZklxa3ZTMlloM1JDTi9HM1o2R05XNTV4Nk9JSjhHV0FSNWRMcW5xTGxUNHFneU14N21sc2d1VnUiLCJtYWMiOiI0NmZhOTNkMjk3MDU0OThkNmJjOTAzOGRhMDU0Njg1ZGI1MTM4NzliNzZkZGU1MTdmZmM4N2UyYzk1MGY2MTgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406310805\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1705788445 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705788445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-485043086 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9tbm53dlhaRXU3MUxoQ0diMzlvVGc9PSIsInZhbHVlIjoiNDQyK3VCeEcyTTZoNmg0ME16aWlCVVdId2h2Yk04Q1UwdGJSbVBTSWNhOTVIN0kwNDVwcGxyM1FZeFhyalNtbDRyVGF3Q0dRSDNkTFdKSzF1WWc4bThKdXF2N3FWQUNTaHp1R2JuZEJvaEFxT1RFallwckRmTlNhVXZUd09iak1lZ2lzc0pzckYrblhOb0pLbWRlYUIzUlJrZjcwWkxpZ25FaFROZ1d4S3NYeXRvR2t6SmEzUFRVdm1Nd2k2RlAycy9mRGxRRDE1VHd1T1pSK1lia0EyY1o0NkR6VTNpeDFpeVBQVTRZaWRXQlIrbE1OQ3ZxVHZZb3R2R3lvZ2Q3LzN2WGtSVU5XVXBCL3ZSQmJ5eHlFcWhVMXhWZk1sUFdvaFJzb3MvdGZFOWlPQzlkTjVrV2NmcEdYV2hCV3Jma0VINDQvZUl1dzhNdG82MGkzV05tc0FDS2gxcTdhSkc4RnNHOVJGVUIyZ0w2NnU0Z1I2Z3k2NVlKZTZlSmd0UE1QWDhFdUloVDVlc2R1U2lxZTNPMVowQUcwdjNRbUk4dUNKVDF3TURJbGNGS0RzclFqZE1GWGdVWi9BV0dremZYcHoxU05zOXpjRzlWeUtoRjcwbjhWU2xWS21qcmV3SlRmSHJTbDd2N2tCTWZSNFpsM1pRdXdVQ21DcnVqaVdvRSsiLCJtYWMiOiI5ZmJlZjRmM2Q5NjZiMzA4Zjg4YjNhMmYzMDIyYWIxYTI1NWNhMmY1ZTYyZmYxODhlMTFlNDY4YjhlOGM3NjRlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNoV3BxNjVlTS8vUzFQV2cxZzlYbVE9PSIsInZhbHVlIjoiWklqZWxFWmpQRTEwNi9RWXRxeDJOODVwZ2pJWGtHYlFCT210aVAvMDkrenJiTTZleVNCMUlycTg5ZUpNNGdQVldYNTQ0NGdldEM2MjdnSVRMbXRmdlBoVVJDN0RqYTNYYlZtckZmcSs0VFVRc2NqaHJmL3FLREdGbzd3d3NRQXVLdVQwNlo4RVFtVGR1VHhXZnFXRzcvVUhkNWdXblBUNDlEMklMeHhqb09QQ1BsVGRiR0RSRDVuQWZzOUFLVGhFVVpJU0p0a3NFUXZXK3RCZkVpRU96b2dWYjFpdmJ0elN2OWg4b3pBTElSd21SVXNKSTdpbS9SZStqcTJrcmM5OEdteXRMbFd2eTJ3Vi9Ka01XT3M1MTF3dnFJMEFpKzVnNHNxSUFZVnFidnByckh4TXJLUzdHNU1yVU93SjNiVFlOaDBCTVZsWTVVQ3BBeFUrUjFGTkVTRmZ5SWRhbVdld01nTVRxeUQvbG9tcmVGTzlWQ2tZaTVWMXZ3ekhkQ2crdXFab0RGZ3JLZ0swaElkNjZadmZZWUt4enFpM0F5SEFaYzc2OUxTYkZrWXV5TEF2ZTdKVkhJenR5eWJwWkRBMWJNelRQNldvYnJlRUFhYlE2b2VncHZyY3JyaDlrVXJ0aG1BVXBNZ3hUK2ZHRXdUaFVCVVJRZUZvbXdidnRKeTIiLCJtYWMiOiI0YmNlNWQyMzk0OTczNDc0YmY4Zjc1YjQwYTI5MWZlNDgzNDZlZmE1OTM3YjFhZDk5YjYwMmU0YWE5N2M0MDMxIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9tbm53dlhaRXU3MUxoQ0diMzlvVGc9PSIsInZhbHVlIjoiNDQyK3VCeEcyTTZoNmg0ME16aWlCVVdId2h2Yk04Q1UwdGJSbVBTSWNhOTVIN0kwNDVwcGxyM1FZeFhyalNtbDRyVGF3Q0dRSDNkTFdKSzF1WWc4bThKdXF2N3FWQUNTaHp1R2JuZEJvaEFxT1RFallwckRmTlNhVXZUd09iak1lZ2lzc0pzckYrblhOb0pLbWRlYUIzUlJrZjcwWkxpZ25FaFROZ1d4S3NYeXRvR2t6SmEzUFRVdm1Nd2k2RlAycy9mRGxRRDE1VHd1T1pSK1lia0EyY1o0NkR6VTNpeDFpeVBQVTRZaWRXQlIrbE1OQ3ZxVHZZb3R2R3lvZ2Q3LzN2WGtSVU5XVXBCL3ZSQmJ5eHlFcWhVMXhWZk1sUFdvaFJzb3MvdGZFOWlPQzlkTjVrV2NmcEdYV2hCV3Jma0VINDQvZUl1dzhNdG82MGkzV05tc0FDS2gxcTdhSkc4RnNHOVJGVUIyZ0w2NnU0Z1I2Z3k2NVlKZTZlSmd0UE1QWDhFdUloVDVlc2R1U2lxZTNPMVowQUcwdjNRbUk4dUNKVDF3TURJbGNGS0RzclFqZE1GWGdVWi9BV0dremZYcHoxU05zOXpjRzlWeUtoRjcwbjhWU2xWS21qcmV3SlRmSHJTbDd2N2tCTWZSNFpsM1pRdXdVQ21DcnVqaVdvRSsiLCJtYWMiOiI5ZmJlZjRmM2Q5NjZiMzA4Zjg4YjNhMmYzMDIyYWIxYTI1NWNhMmY1ZTYyZmYxODhlMTFlNDY4YjhlOGM3NjRlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNoV3BxNjVlTS8vUzFQV2cxZzlYbVE9PSIsInZhbHVlIjoiWklqZWxFWmpQRTEwNi9RWXRxeDJOODVwZ2pJWGtHYlFCT210aVAvMDkrenJiTTZleVNCMUlycTg5ZUpNNGdQVldYNTQ0NGdldEM2MjdnSVRMbXRmdlBoVVJDN0RqYTNYYlZtckZmcSs0VFVRc2NqaHJmL3FLREdGbzd3d3NRQXVLdVQwNlo4RVFtVGR1VHhXZnFXRzcvVUhkNWdXblBUNDlEMklMeHhqb09QQ1BsVGRiR0RSRDVuQWZzOUFLVGhFVVpJU0p0a3NFUXZXK3RCZkVpRU96b2dWYjFpdmJ0elN2OWg4b3pBTElSd21SVXNKSTdpbS9SZStqcTJrcmM5OEdteXRMbFd2eTJ3Vi9Ka01XT3M1MTF3dnFJMEFpKzVnNHNxSUFZVnFidnByckh4TXJLUzdHNU1yVU93SjNiVFlOaDBCTVZsWTVVQ3BBeFUrUjFGTkVTRmZ5SWRhbVdld01nTVRxeUQvbG9tcmVGTzlWQ2tZaTVWMXZ3ekhkQ2crdXFab0RGZ3JLZ0swaElkNjZadmZZWUt4enFpM0F5SEFaYzc2OUxTYkZrWXV5TEF2ZTdKVkhJenR5eWJwWkRBMWJNelRQNldvYnJlRUFhYlE2b2VncHZyY3JyaDlrVXJ0aG1BVXBNZ3hUK2ZHRXdUaFVCVVJRZUZvbXdidnRKeTIiLCJtYWMiOiI0YmNlNWQyMzk0OTczNDc0YmY4Zjc1YjQwYTI5MWZlNDgzNDZlZmE1OTM3YjFhZDk5YjYwMmU0YWE5N2M0MDMxIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485043086\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-364417978 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364417978\", {\"maxDepth\":0})</script>\n"}}