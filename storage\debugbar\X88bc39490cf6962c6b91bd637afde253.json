{"__meta": {"id": "X88bc39490cf6962c6b91bd637afde253", "datetime": "2025-07-23 18:24:06", "utime": **********.550292, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.088312, "end": **********.55031, "duration": 0.46199798583984375, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.088312, "relative_start": 0, "end": **********.493856, "relative_end": **********.493856, "duration": 0.4055440425872803, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.493865, "relative_start": 0.40555310249328613, "end": **********.550312, "relative_end": 2.1457672119140625e-06, "duration": 0.05644702911376953, "duration_str": "56.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46538896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027199999999999998, "accumulated_duration_str": "2.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.519721, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.706}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.52933, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.706, "width_percent": 13.603}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.535094, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.309, "width_percent": 21.691}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImIxRmNnOEtRaHl3MTJYSVBQU0ZBYWc9PSIsInZhbHVlIjoieXhZRGZpNWo1Vk1KekxDYTV5a1RNUT09IiwibWFjIjoiNmFjYjYyOTBjNTE5NjhkMGViZjdmZjAzNDVmMTY3MTk0M2FjYjg2YjU0M2QxNjUzOGE0ZDFkNmYzMWQ4ZjFkYSIsInRhZyI6IiJ9\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-934756706 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-934756706\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-545075006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-545075006\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1522219980 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522219980\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1223051344 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImIxRmNnOEtRaHl3MTJYSVBQU0ZBYWc9PSIsInZhbHVlIjoieXhZRGZpNWo1Vk1KekxDYTV5a1RNUT09IiwibWFjIjoiNmFjYjYyOTBjNTE5NjhkMGViZjdmZjAzNDVmMTY3MTk0M2FjYjg2YjU0M2QxNjUzOGE0ZDFkNmYzMWQ4ZjFkYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753295037609%7C23%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitYSG5ib1RJeTgyNTU4QVpLWkMyWkE9PSIsInZhbHVlIjoiNE55T0x5amFFQ3ZMdkloR0VaRy9XMVdXdFVhZVVZLzF1MVh1UGxYcWErL2l0SjRrTG9pSTI3L1NBd0M1KzFMMHVzeFl6VzVuMWpHeEFaWVBIZUNmOVFXSlkvV1VTVzllalc3clE1T0NLMGkxR0VpT1pSY284ZXA0dDhoQlI1STRYSi81VFZyY1FOd3RoN2lEc0ZMSUJoYkwrTm1qZ2RtaU9iSTVmbjFaYitIZEpOa256ejMyRkg3VFZ3YUMxcFYvTG9NMnhRU2loaWUwc0I2YWtNSUtVTGlvNGZnZDBOUkNzOGZYcmZ0eURJM3ovejVVYzBFaUN5YjNDK3REbi9yOU5BYUpybjBWRzJJeitmUzQrWGZTemowVHptZSttOTZNYmlKcTltWXY0MHJDUmt4RUVtN3pNQ0JBaGpLZWtWQmhLb01BOWViWnZjUmlNMStxUlZ4bjZ1THF2SGt3bUFjTnQ4MDRGNDJwQzJHSGxtdmZiUVBFc3hUS0g1SDRPcGdYOE00Vk9tUkNrWmM3QzUySnFKVnFpSGlBSjlRRFl4NVE4bW5UY0d3NkJORHk3VEFpY0RGRHVoUFVKREhQc2o3cW1jVk9ibUxIanhOSndlTUpEVjV1NlVpb3cwalBXVFA5azR4c3Rob1JieGdjUHhEWERvU1M3b3ZzaFpqMExqTVciLCJtYWMiOiIwOTkxODMwZjU4ZGU4NjgzZjA0MzcyODVkNjBkZjk0ZjM0ZWRlMDhlMWUwNzZkNzU2NDRlMTdiNjQ5OTJhNWYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5tMmE4b056TGVRM3Y5ZjNsMS9icXc9PSIsInZhbHVlIjoiN0w5NXBpVGE5WDY1Y3orTFg0QnlXaGhhU2hkSFpmWStVYUx1b0hoek9lNUcwbk9qQm1kck9FVlZpQTZtdk1aVUxvanVNc2o2R0pINFppcGFSVGp5WG9GbGNBTlZRaGhQd0hlekdadVE3bkdRRXpPYVFDdGppSEdDcVFITGRGM2pQdUdOTlcyVXROWW9xaFFjK0VuRVoyazUxN1gvOU9iWWlFL2laeXBVMmR1VlM4RE9jbEo1TVhNcDBzcTNSYXBTMkhuck9taGZZcEdsV1FmdjU3Z0FHbWJNWlRkMy96cnNObHczald0OWdwTkpxV0Vja3BQVEpaZ2k1cmc1ZDJzTFBHWDQwWnhpUFRJUFlqNTNwQ0N2cE5nSnpUYnJobE01bzR0NThTbnRzTTBYWDBiR2VjeG1tTEJCRFJqbkRZMXBNK2xvOEZ1RHRySmgzUHpuaTBYZkk1Q1lrM1E3MTFXbWFXR0YwVE1hMURoNHpvWTllZzhQS0pmcCtPakVZK1p2SzZtZThZOEt1UDhsb3pENVZ5cy92T0ltUGJyNWFzS0FTRmlielc4VEM2SU9zK2V2UlBzWWl2YWd2SnRneE4rZmZXcWkyTDNmYk0yZkI5QTE5a1hOYU9pMWlCSDV5MFJQRXBuSFgxdEdqeG5OaFYxMEFraDRmYy9xUHozaktwOXAiLCJtYWMiOiJiNjIxNTRlZGM1YTE1YTllMjc5MjUyYWIxMzQ2ZGQ3NDRhNjc3Njk5YzRiN2ViYzFjZWU3YjViYWQ2Y2ExMzcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223051344\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-793489291 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793489291\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1163932835 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:24:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5wQmoxZ2xuc3NycVdXK296dVl1eHc9PSIsInZhbHVlIjoic2RjbkJqS2VISEpWbzRzOFJYSDZNSElPR04yWEIzMmpBclgvV1g0T1hyK09XczloQi95N2szNml2ZFV2aTNSN1dsdUMxempsaW9uM2E0eVBoTkR5YmtiK1JrL3ZmWUs0YlZ2WTQ5TDFtMS9HT3U5NnhTN2dXVldiK3pxbGtXZEhjaUx4aXJJR3VzM2VqZHc0MmtaRnkwTGRIVGpESEVhVGRubmsvU1RqTzBXZllVUnJmWUVQcXVweFExR2l2QmRVT2FRdjR4VUM2eVcvam1ZTld2QlRaajlzSDltb3Z5ZnhSeDJ2NWZOMDUyVUJhQ3pJZDlsZHB2RU0xY3pOMUpsRFQweklQbXRMV1d0TEtPVnBCY0Jrais5M1BDRW8yTjJzck1oenc5N3Z3K05nK3htMWJMSGpOakNNWlRPM2o4ZVJNNVJTdmJTZUQzR1RtSjJYT3lzYnpUOW5Dc3Y4WmlXQTNxekMvdm80WlBGOW1mQ1FtWE5QQ1VoZFZjN3MxdGJyTGk3RXc2TG9ZYVVHTnhiNWMzd1Z0SnlBbTN5WlRNQWRpYjJNZ21EK1RVMU15bUF2TlF1OWZGSXpzN0x1bVNnWURFdVhzRExNTENvNkNlSUpEZEpzSitiVXM0YWVQamFLcEtkRmlxQlBrWGxYK2U0RzdFTVo0ZTErcy8zVWFnYmoiLCJtYWMiOiJjNmI4MDIxZjY3NWIwMjNiMjM5NzRkOTRmZTUxNDI0YjllY2Q3YzFmYjM3ZTljYmI4N2ZjZDBjZTY3NDc2Njc3IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:24:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5ESERUMFZvMDR1OGRIWjM0bTNtV2c9PSIsInZhbHVlIjoiQTBXZVByOFd3ajE3MnFIR29CcGlEaGFJa3d4NlM4RVZ1TXkwaGtqbDZTZi9GWHd5bE8yK2ZacGk3RWFhTm5TNTZHNTA5NEs0M0c5K1RxMmxVMWYzdVdQMWtRcko0Y0tDZzU1QXFWSzJEYVVOcXIzV0Zib2NYS2VCRGZvRUxsUWNPWXVmM2tOYk5aYndzckF4MjNWTG9CR3JUTjhiT1lCVmdjTk0vbkhsbzFuTnpzTlYxbjdTWUtrT2draytCRTNJaDFqcWFNS2h2SlFIcGZPN1JMOC80MlBzcVJvNXpPcTNxcDZRRzRVTmhuUVpWSWNKY0VYSU1HdmJLK0JqNm9sR3ZPby9CTVN3eloxeWlkNkxLRnVDZkdxL3dGN0JCMHBWUlg2MjhuN1J0RTBtQ2FVNlQrRHJLeGJJK1ZycThLOFMzdTk0N0w0K20rSHhqM2NyU2UyRVRkTUFMaHovZVN2OGZkejhremNLOVhtbytDdWdqNHh3RTNpakVSSVFwZG12cE9JQ0xsWGJETWZtdnlQTjloNUZSY0tFVStsclZzays1a2FJRUpyNjRLNTREM1NYSVpML0JINWxtc2UzWnpPUXlmUUdCWWRJMCtkTjdySlphN3dzbitYVk9GTm8zYVNoTHNmclFxVytLUG0xZkc3ZXJpdUd2UWJ6TERoZjlXS2MiLCJtYWMiOiIzZjlhZThlMjdkNDY2NzEzZjI4YjBiODMxMjgxYWE0OGU3YmQ5YjIzMmI3MjE5ZjhkNTdiNzYzZWRiZTczNTZmIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:24:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5wQmoxZ2xuc3NycVdXK296dVl1eHc9PSIsInZhbHVlIjoic2RjbkJqS2VISEpWbzRzOFJYSDZNSElPR04yWEIzMmpBclgvV1g0T1hyK09XczloQi95N2szNml2ZFV2aTNSN1dsdUMxempsaW9uM2E0eVBoTkR5YmtiK1JrL3ZmWUs0YlZ2WTQ5TDFtMS9HT3U5NnhTN2dXVldiK3pxbGtXZEhjaUx4aXJJR3VzM2VqZHc0MmtaRnkwTGRIVGpESEVhVGRubmsvU1RqTzBXZllVUnJmWUVQcXVweFExR2l2QmRVT2FRdjR4VUM2eVcvam1ZTld2QlRaajlzSDltb3Z5ZnhSeDJ2NWZOMDUyVUJhQ3pJZDlsZHB2RU0xY3pOMUpsRFQweklQbXRMV1d0TEtPVnBCY0Jrais5M1BDRW8yTjJzck1oenc5N3Z3K05nK3htMWJMSGpOakNNWlRPM2o4ZVJNNVJTdmJTZUQzR1RtSjJYT3lzYnpUOW5Dc3Y4WmlXQTNxekMvdm80WlBGOW1mQ1FtWE5QQ1VoZFZjN3MxdGJyTGk3RXc2TG9ZYVVHTnhiNWMzd1Z0SnlBbTN5WlRNQWRpYjJNZ21EK1RVMU15bUF2TlF1OWZGSXpzN0x1bVNnWURFdVhzRExNTENvNkNlSUpEZEpzSitiVXM0YWVQamFLcEtkRmlxQlBrWGxYK2U0RzdFTVo0ZTErcy8zVWFnYmoiLCJtYWMiOiJjNmI4MDIxZjY3NWIwMjNiMjM5NzRkOTRmZTUxNDI0YjllY2Q3YzFmYjM3ZTljYmI4N2ZjZDBjZTY3NDc2Njc3IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:24:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5ESERUMFZvMDR1OGRIWjM0bTNtV2c9PSIsInZhbHVlIjoiQTBXZVByOFd3ajE3MnFIR29CcGlEaGFJa3d4NlM4RVZ1TXkwaGtqbDZTZi9GWHd5bE8yK2ZacGk3RWFhTm5TNTZHNTA5NEs0M0c5K1RxMmxVMWYzdVdQMWtRcko0Y0tDZzU1QXFWSzJEYVVOcXIzV0Zib2NYS2VCRGZvRUxsUWNPWXVmM2tOYk5aYndzckF4MjNWTG9CR3JUTjhiT1lCVmdjTk0vbkhsbzFuTnpzTlYxbjdTWUtrT2draytCRTNJaDFqcWFNS2h2SlFIcGZPN1JMOC80MlBzcVJvNXpPcTNxcDZRRzRVTmhuUVpWSWNKY0VYSU1HdmJLK0JqNm9sR3ZPby9CTVN3eloxeWlkNkxLRnVDZkdxL3dGN0JCMHBWUlg2MjhuN1J0RTBtQ2FVNlQrRHJLeGJJK1ZycThLOFMzdTk0N0w0K20rSHhqM2NyU2UyRVRkTUFMaHovZVN2OGZkejhremNLOVhtbytDdWdqNHh3RTNpakVSSVFwZG12cE9JQ0xsWGJETWZtdnlQTjloNUZSY0tFVStsclZzays1a2FJRUpyNjRLNTREM1NYSVpML0JINWxtc2UzWnpPUXlmUUdCWWRJMCtkTjdySlphN3dzbitYVk9GTm8zYVNoTHNmclFxVytLUG0xZkc3ZXJpdUd2UWJ6TERoZjlXS2MiLCJtYWMiOiIzZjlhZThlMjdkNDY2NzEzZjI4YjBiODMxMjgxYWE0OGU3YmQ5YjIzMmI3MjE5ZjhkNTdiNzYzZWRiZTczNTZmIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:24:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163932835\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1323205899 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImIxRmNnOEtRaHl3MTJYSVBQU0ZBYWc9PSIsInZhbHVlIjoieXhZRGZpNWo1Vk1KekxDYTV5a1RNUT09IiwibWFjIjoiNmFjYjYyOTBjNTE5NjhkMGViZjdmZjAzNDVmMTY3MTk0M2FjYjg2YjU0M2QxNjUzOGE0ZDFkNmYzMWQ4ZjFkYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323205899\", {\"maxDepth\":0})</script>\n"}}