{"__meta": {"id": "Xee789ccbdb5b582cce64653c9854acda", "datetime": "2025-07-14 17:58:36", "utime": **********.262079, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515915.78935, "end": **********.262098, "duration": 0.4727480411529541, "duration_str": "473ms", "measures": [{"label": "Booting", "start": 1752515915.78935, "relative_start": 0, "end": **********.187741, "relative_end": **********.187741, "duration": 0.3983910083770752, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.18775, "relative_start": 0.39840006828308105, "end": **********.2621, "relative_end": 1.9073486328125e-06, "duration": 0.07434988021850586, "duration_str": "74.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991128, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022170000000000002, "accumulated_duration_str": "22.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2154949, "duration": 0.02092, "duration_str": "20.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.362}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2469409, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.362, "width_percent": 2.661}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.253344, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.023, "width_percent": 2.977}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-196013529 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515911303%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFjYUlVT200NDVQZmNabEZLMG9ETmc9PSIsInZhbHVlIjoiZFBRWjdqYzYrN0RwellLU3VRNzlqYU5jdGprT3BhL3JuRk1PZ0FIcVcxa3UrUFRXcjlDREtxOWx1eTltazdpdStsVzhUWlU0Y0FDOS83MXM0RXoxdHRXbHo5eitoMEJzVW4zZEtYb3FLT0E0VDBmbUtJM2RiRlJYMExZdGREa0JMMlpLcGFmMTFVcldBWExYV0YxcThWWk9hTVE2cnd3dzFtOFRINUI3RGNQVS9IVzFlU0U4c0tiZGM5MDdXVTdxcXJTZkRNcTBCQjZVckM0NzFDWE9EVDVpNE9ZeTdma3ZSbjN5QVZ1cG8reXJHc3Q0TkR6cnhWZFVVVzBEUncrcWZKRENMQ3ZvZUdXdExNYWo1YnBoeWZ2V1RiZXZLQ3JsTEZMb2xJOUtYZ0xodS93ZHVWY21zUHFMSVRJZU9UYXZ6N044UjZGSkhBVWgrWFRTTXNGTXVvWURyRWRVUG10b0pyNHdmQVppUDBWWkhYZU9DemJUY3RsaXUrS2tCSnozOHlRQU8vS21La243SEtEZFpSWG4zalhLcEZZZytHODRIL0FPcXR5c2ZSNWwvT01WRUsvOVVKT0JqZHRKbkpSR2pNNDBpVVJvUE45R0FWZ3oyVjhFaHVaNkVaVXFnWDgvQ01pOHpyb1JBS3NuVjlMeXBSbUhtb2UrUStSOVg5aGoiLCJtYWMiOiJiYTE3NjAyNmE5NDNjMzllYjQxM2E5NjNmYjFkODNkZDVhNDM2ZWZjZDA2ODZiNmMzNzhhYmExMGZhNzlkOTk3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IndQeEJPZnlzaDhMd3Z4NGdJSlRNQUE9PSIsInZhbHVlIjoiWnJxK2djVEtIeUkrVCs0Vzk1WW1QSEpUQnRVT1B4Yk9QZlg2VXA2S2hsMEgwaVkxcTlkdStSZ293VEN3ZXBNUkUwVG5ZQ2pkTVppM3A0MExGMHlGWVdEMTIycllZK2pMRXFLUGdING5TK1M5ZWlIeUpMbVh4bjF3U25FVWk1cU9GWkNaRjVmR1RyNkxNeUZyM2RMTUM4UjBzeFBLRHJBMGhqR1h2cnY3UnRDanFwWlZINmNyL1BIM05MMU56YzFIdVkzSVVLcjNmcVlYUUc4UTJpKzlObkVJSWVLVU1LaUJvekhsY090dXJNcWhETWxyeHFIcVV5N0JwMDlmYzFCbjk2VlhTaDBIOURCUktpS09pc1A1MGFoN1gvaUZVdjRtcXhDZitoQmpjWGJNMlFMQUJ3eERjbG4xTHptRHlCVXpYUldrZGYrazlPWThBZVRyeVhNanB5UEM2L0lWSHBRTy9DTUNaV2pCUEVHanpndkM4a0V5V1l0aHRQMG4wTFhBUFdRMThEMmNnWHE1bGxPampGT2o4MGo4MFBLVUlBQzlFdHBaS1BzcmlDdm9jNXdBeElkNHFxT2lybHYvcGpENmVnSFc4QWN4d3hEbVJsaDlkeWw4LzJtNVJRQ2IxVEYzak5WYkV1NnNkTForUERNendBb0FYN1BXTW1mdDZoN0UiLCJtYWMiOiJmODc1NTRkNWRhYzA4MDM4OGY5NWU0NzBhZDVjZjMzZGYyYjZjZmRhYWM2OTQ0YmZmZWE5NTVlNjA5OGZlZmExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196013529\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-913009142 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913009142\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-70360064 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBpUVRwdnhhdHRBKzRDMDJ0dGJaSEE9PSIsInZhbHVlIjoiR2RPM3l4bkhoeTg2ZFZ2aEY4SEorSjhCNGEyS0ZjLzU3VWhyd2dJL0d6Vzc3dkNDWW9KVklRYkxyVGtydWVvYzJiUjVyY3FHOVIzc0I5WmJTRTRKQ3UyUkIwME1nczE4and3eFVsU3hKWDVvdllBeU1IYm1RamdXK1YvUlNWZ2p2QUk0Y0NNWXU5UnFmNkgwOGFjVWEyMlVmRzFFZjIwN1RWMUtwOURpWEE4dWZ0N25LUlNMV2V6blduejBpVFF5dGh3ZVlyQUVPalFnczlDUzZqdS8xZ3h5TE9obEpVenlNbVpWNGJVelplUXVHYlVqQzNnN3BvU09Dd1NzRnk2dXkxU2tzUldnT3QyUWVzdFBBTU40LzEycU9Ta3BxRzlLOGtmZmJwaEZ6ZXIxWFhuTDhVZFQ5bXZmN2Y0SktuTUdFZ0N5U0lDOUNJVmRwQjgydjhhK2lyc3d2Y081K3REVzYxeEcyYmx0OVoxZU5WbFJJRUVJcGFRVWVQUzFDK3V6UEJvdmxOUFVNcHJML1JrU0h6MHd3blgvY0UrZUQ4cGxBaFN2SkpUNlNoMk56SXl3aVVmSTdsT2ZvbUszYXFpeFZmRFFDQVZhazBJSDlpaWFwSXZkV1l0R0ZKYzU5OFdWSGVoYlYzbmluSkhMK1BlYVQ3T3FEbXhNOWY0a3hUQVIiLCJtYWMiOiJlNzM3YWYzODM4NjYzZGUyZTYxMWE3YmM5ZjIwM2UzYTVkMDE1OGIwZDVjYmI1YmUyZjA0Nzc1NDI2NmJkNmRjIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilk2elM4bFVHYVFDTXFJSTBhNG1wVGc9PSIsInZhbHVlIjoibktndG5XbmwyYnA3dldTenFHMjZFdEo3dFRHR2l1WmpMU2xpSW0xZzhRUzM1R2tYQWlWVndtNk13Qk9FVGdCU1hVT3FIVVY1WlhwdTJQUk1RS05LZzM5Z3p5bDRaMS9tZ1pqNU5iMm16K28wNFlqTkliSjlVYVZDeXdwMEJ3dlZrRndrMkd6QkN2aUgzOGVQZVVSaFpUVG83ZWxBNTU3RDB6TDluQnU1d0VGNHhSNlZkUXVsYzAzUXFmUUVyL1ZzbTMyU0hsdDJOS3dxRVNvZzQ0WnU2UmhxQU9sQTFEdk1mazYwUlA1c09FbEdHZ01VK2JGWlQyT2ozQk1DMmtuUTBHb2EwNE1Lbm94VE5OejZONmVyYjdaSUpUNFhCWmZRQmJPSEkybDdab0JpTkZIWFpNMHkzcnIrdmhaUXJwaUJKTFp6RDVZK2x2WTNyb3lpUUtST3NOQ3pKSGRsUkhBWjFWRG9YVml6NStENGFTK3NiTERDRzRRbkkwSzhrWmlZVko1dVVBUm9SYXZXMm1MbDR4QmI1V1FhM3RMR2F6Wkdjd2hMSUY2MVZoL1VNWEFWSW5JTGYzVjgyZW1OckZuZGlLeC9TNUV0aXM0Y2NkeWxseGRuTjErMnk1cDNXOXJjZ3FPWVJGdDdWczM5VzZXSld3eUFzWEhhSm03TFl1OG0iLCJtYWMiOiIyZWZiZTZmMDZlMDlmNjY1NzgzY2FlNDExYTNhZjBhOGM4ZDA5ZjBlNzU0MWZhNGQxNTU2YjlkNzM2ZjU0MTg3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBpUVRwdnhhdHRBKzRDMDJ0dGJaSEE9PSIsInZhbHVlIjoiR2RPM3l4bkhoeTg2ZFZ2aEY4SEorSjhCNGEyS0ZjLzU3VWhyd2dJL0d6Vzc3dkNDWW9KVklRYkxyVGtydWVvYzJiUjVyY3FHOVIzc0I5WmJTRTRKQ3UyUkIwME1nczE4and3eFVsU3hKWDVvdllBeU1IYm1RamdXK1YvUlNWZ2p2QUk0Y0NNWXU5UnFmNkgwOGFjVWEyMlVmRzFFZjIwN1RWMUtwOURpWEE4dWZ0N25LUlNMV2V6blduejBpVFF5dGh3ZVlyQUVPalFnczlDUzZqdS8xZ3h5TE9obEpVenlNbVpWNGJVelplUXVHYlVqQzNnN3BvU09Dd1NzRnk2dXkxU2tzUldnT3QyUWVzdFBBTU40LzEycU9Ta3BxRzlLOGtmZmJwaEZ6ZXIxWFhuTDhVZFQ5bXZmN2Y0SktuTUdFZ0N5U0lDOUNJVmRwQjgydjhhK2lyc3d2Y081K3REVzYxeEcyYmx0OVoxZU5WbFJJRUVJcGFRVWVQUzFDK3V6UEJvdmxOUFVNcHJML1JrU0h6MHd3blgvY0UrZUQ4cGxBaFN2SkpUNlNoMk56SXl3aVVmSTdsT2ZvbUszYXFpeFZmRFFDQVZhazBJSDlpaWFwSXZkV1l0R0ZKYzU5OFdWSGVoYlYzbmluSkhMK1BlYVQ3T3FEbXhNOWY0a3hUQVIiLCJtYWMiOiJlNzM3YWYzODM4NjYzZGUyZTYxMWE3YmM5ZjIwM2UzYTVkMDE1OGIwZDVjYmI1YmUyZjA0Nzc1NDI2NmJkNmRjIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilk2elM4bFVHYVFDTXFJSTBhNG1wVGc9PSIsInZhbHVlIjoibktndG5XbmwyYnA3dldTenFHMjZFdEo3dFRHR2l1WmpMU2xpSW0xZzhRUzM1R2tYQWlWVndtNk13Qk9FVGdCU1hVT3FIVVY1WlhwdTJQUk1RS05LZzM5Z3p5bDRaMS9tZ1pqNU5iMm16K28wNFlqTkliSjlVYVZDeXdwMEJ3dlZrRndrMkd6QkN2aUgzOGVQZVVSaFpUVG83ZWxBNTU3RDB6TDluQnU1d0VGNHhSNlZkUXVsYzAzUXFmUUVyL1ZzbTMyU0hsdDJOS3dxRVNvZzQ0WnU2UmhxQU9sQTFEdk1mazYwUlA1c09FbEdHZ01VK2JGWlQyT2ozQk1DMmtuUTBHb2EwNE1Lbm94VE5OejZONmVyYjdaSUpUNFhCWmZRQmJPSEkybDdab0JpTkZIWFpNMHkzcnIrdmhaUXJwaUJKTFp6RDVZK2x2WTNyb3lpUUtST3NOQ3pKSGRsUkhBWjFWRG9YVml6NStENGFTK3NiTERDRzRRbkkwSzhrWmlZVko1dVVBUm9SYXZXMm1MbDR4QmI1V1FhM3RMR2F6Wkdjd2hMSUY2MVZoL1VNWEFWSW5JTGYzVjgyZW1OckZuZGlLeC9TNUV0aXM0Y2NkeWxseGRuTjErMnk1cDNXOXJjZ3FPWVJGdDdWczM5VzZXSld3eUFzWEhhSm03TFl1OG0iLCJtYWMiOiIyZWZiZTZmMDZlMDlmNjY1NzgzY2FlNDExYTNhZjBhOGM4ZDA5ZjBlNzU0MWZhNGQxNTU2YjlkNzM2ZjU0MTg3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70360064\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}