# تقرير شامل عن نظام الفواتير (Invoice System)

## نظرة عامة
نظام الفواتير في النظام المحاسبي هو نظام متكامل يدير دورة حياة الفواتير من الإنشاء إلى التحصيل، ويتضمن إدارة العملاء والمنتجات والمدفوعات والضرائب.

## 1. النماذج (Models)

### 1.1 نموذج الفاتورة الرئيسي (Invoice Model)
**الملف:** `app/Models/Invoice.php`

**الحقول المسموحة (Fillable):**
- `invoice_id` - رقم الفاتورة
- `customer_id` - معرف العميل
- `issue_date` - تاريخ الإصدار
- `due_date` - تاريخ الاستحقاق
- `ref_number` - الرقم المرجعي
- `status` - حالة الفاتورة
- `category_id` - معرف الفئة
- `created_by` - منشئ الفاتورة

**حالات الفاتورة:**
- `0` - Draft (مسودة)
- `1` - Sent (مرسلة)
- `2` - Unpaid (غير مدفوعة)
- `3` - Partially Paid (مدفوعة جزئياً)
- `4` - Paid (مدفوعة)

**العلاقات:**
- `items()` - منتجات الفاتورة (hasMany InvoiceProduct)
- `payments()` - مدفوعات الفاتورة (hasMany InvoicePayment)
- `bankPayments()` - التحويلات البنكية (hasMany InvoiceBankTransfer)
- `creditNote()` - إشعارات الائتمان (hasMany CreditNote)
- `customer()` - العميل (belongsTo Customer)
- `taxes()` - الضرائب (hasOne Tax)
- `attachments()` - المرفقات (hasMany InvoiceAttachment)

### 1.2 نموذج منتجات الفاتورة (InvoiceProduct Model)
**الملف:** `app/Models/InvoiceProduct.php`

**الحقول:**
- `product_id` - معرف المنتج (nullable)
- `product_name` - اسم المنتج (يدوي)
- `invoice_id` - معرف الفاتورة
- `quantity` - الكمية
- `tax` - الضريبة
- `discount` - الخصم
- `price` - السعر
- `total` - الإجمالي
- `description` - الوصف

### 1.3 نموذج مدفوعات الفاتورة (InvoicePayment Model)
**الملف:** `app/Models/InvoicePayment.php`

**الحقول:**
- `invoice_id` - معرف الفاتورة
- `date` - تاريخ الدفع
- `amount` - المبلغ
- `account_id` - معرف الحساب البنكي
- `payment_method` - طريقة الدفع
- `payment_type` - نوع الدفع
- `reference` - المرجع
- `description` - الوصف

### 1.4 نموذج إشعارات الائتمان (CreditNote Model)
**الملف:** `app/Models/CreditNote.php`

**الحقول:**
- `invoice` - معرف الفاتورة
- `customer` - معرف العميل
- `amount` - المبلغ
- `date` - التاريخ
- `description` - الوصف

### 1.5 نموذج التحويلات البنكية (InvoiceBankTransfer Model)
**الملف:** `app/Models/InvoiceBankTransfer.php`

**الحقول:**
- `invoice_id` - معرف الفاتورة
- `order_id` - معرف الطلب
- `amount` - المبلغ
- `status` - الحالة
- `date` - التاريخ
- `receipt` - الإيصال

### 1.6 نموذج مرفقات الفاتورة (InvoiceAttachment Model)
**الجدول:** `invoice_attachments`

**الحقول:**
- `invoice_id` - معرف الفاتورة
- `original_name` - الاسم الأصلي للملف
- `file_path` - مسار الملف
- `file_size` - حجم الملف
- `file_type` - نوع الملف
- `description` - الوصف
- `uploaded_by` - رافع الملف

## 2. وحدة التحكم (Controller)

### 2.1 InvoiceController
**الملف:** `app/Http/Controllers/InvoiceController.php`

**الدوال الرئيسية:**

#### دالة الفهرسة (index)
- **الغرض:** عرض قائمة الفواتير مع إمكانية التصفية
- **المعاملات:** تاريخ الإصدار، العميل، الحالة
- **المخرجات:** عرض `invoice.index`

#### دالة الإنشاء (create)
- **الغرض:** عرض نموذج إنشاء فاتورة جديدة
- **البيانات المطلوبة:** العملاء، الفئات، المنتجات
- **المخرجات:** عرض `invoice.create`

#### دالة الحفظ (store)
- **الغرض:** حفظ فاتورة جديدة في قاعدة البيانات
- **التحقق:** وجود منتج واحد على الأقل
- **العمليات:** حفظ الفاتورة، حفظ المنتجات، تحديث المخزون

#### دالة العرض (show)
- **الغرض:** عرض تفاصيل فاتورة محددة
- **الأمان:** تشفير معرف الفاتورة
- **البيانات:** الفاتورة مع العلاقات (المدفوعات، المنتجات، المرفقات)

#### دالة التعديل (edit)
- **الغرض:** عرض نموذج تعديل الفاتورة
- **القيود:** إمكانية التعديل حسب الحالة

#### دالة التحديث (update)
- **الغرض:** تحديث بيانات الفاتورة
- **العمليات:** تحديث الفاتورة، تحديث المنتجات

#### دالة الحذف (destroy)
- **الغرض:** حذف الفاتورة ومكوناتها
- **العمليات:**
  - حذف المدفوعات وتحديث أرصدة الحسابات
  - حذف إشعارات الائتمان
  - حذف منتجات الفاتورة
  - حذف سجلات المعاملات
  - تحديث رصيد العميل

## 3. الواجهات (Views)

### 3.1 واجهة قائمة الفواتير
**الملف:** `resources/views/invoice/index.blade.php`

**المكونات:**
- فلاتر البحث (تاريخ الإصدار، العميل، الحالة)
- جدول الفواتير مع الأعمدة:
  - رقم الفاتورة
  - تاريخ الإصدار
  - تاريخ الاستحقاق
  - المبلغ المستحق
  - الحالة
  - الإجراءات (عرض، تعديل، حذف، نسخ، تكرار)

### 3.2 واجهة إنشاء الفاتورة
**الملف:** `resources/views/invoice/create.blade.php`

**الأقسام:**
- معلومات العميل والفاتورة
- جدول المنتجات التفاعلي
- حساب الضرائب والخصومات
- الإجمالي النهائي

### 3.3 واجهة تعديل الفاتورة
**الملف:** `resources/views/invoice/edit.blade.php`

**المميزات:**
- نفس مكونات الإنشاء مع البيانات المحملة مسبقاً
- إمكانية تعديل المنتجات الموجودة

### 3.4 واجهة عرض الفاتورة
**الملف:** `resources/views/invoice/view.blade.php`

**المحتويات:**
- تفاصيل الفاتورة كاملة
- سجل المدفوعات
- إشعارات الائتمان
- المرفقات
- خط زمني للفاتورة

### 3.5 واجهة الدفع
**الملف:** `resources/views/invoice/payment.blade.php`

**الحقول:**
- تاريخ الدفع
- المبلغ
- الحساب البنكي
- المرجع
- الوصف

## 4. الجداول (Database Tables)

### 4.1 جدول الفواتير الرئيسي
**الجدول:** `invoices`
**الملف:** `database/migrations/2019_11_13_055026_create_invoices_table.php`

**الحقول:**
- `id` - المعرف الأساسي
- `invoice_id` - رقم الفاتورة
- `customer_id` - معرف العميل
- `issue_date` - تاريخ الإصدار
- `due_date` - تاريخ الاستحقاق
- `send_date` - تاريخ الإرسال
- `category_id` - معرف الفئة
- `ref_number` - الرقم المرجعي
- `status` - الحالة (افتراضي: 0)
- `shipping_display` - عرض الشحن (افتراضي: 1)
- `discount_apply` - تطبيق الخصم (افتراضي: 0)
- `created_by` - منشئ الفاتورة
- `created_at`, `updated_at` - طوابع زمنية

### 4.2 جدول منتجات الفاتورة
**الجدول:** `invoice_products`
**الملف:** `database/migrations/2020_01_13_072608_create_invoice_products_table.php`

**الحقول:**
- `id` - المعرف الأساسي
- `invoice_id` - معرف الفاتورة
- `product_id` - معرف المنتج (nullable)
- `product_name` - اسم المنتج (يدوي)
- `quantity` - الكمية
- `tax` - الضريبة (varchar 50)
- `discount` - الخصم (افتراضي: 0.00)
- `price` - السعر (decimal 16,2)
- `description` - الوصف
- `created_at`, `updated_at` - طوابع زمنية

### 4.3 جدول مدفوعات الفاتورة
**الجدول:** `invoice_payments`
**الملف:** `database/migrations/2020_01_18_051650_create_invoice_payments_table.php`

**الحقول:**
- `id` - المعرف الأساسي
- `invoice_id` - معرف الفاتورة
- `date` - تاريخ الدفع
- `amount` - المبلغ (decimal 16,2)
- `account_id` - معرف الحساب البنكي
- `payment_method` - طريقة الدفع
- `order_id` - معرف الطلب
- `currency` - العملة
- `txn_id` - معرف المعاملة
- `payment_type` - نوع الدفع (افتراضي: Manually)
- `receipt` - الإيصال
- `reference` - المرجع
- `description` - الوصف
- `created_at`, `updated_at` - طوابع زمنية

### 4.4 جدول إشعارات الائتمان
**الجدول:** `credit_notes`
**الملف:** `database/migrations/2020_02_25_052356_create_credit_notes_table.php`

**الحقول:**
- `id` - المعرف الأساسي
- `invoice` - معرف الفاتورة (افتراضي: 0)
- `customer` - معرف العميل (افتراضي: 0)
- `amount` - المبلغ (decimal 15,2)
- `date` - التاريخ
- `description` - الوصف
- `created_at`, `updated_at` - طوابع زمنية

### 4.5 جدول التحويلات البنكية
**الجدول:** `invoice_bank_transfers`
**الملف:** `database/migrations/2023_05_29_063149_create_invoice_bank_transfer_table.php`

**الحقول:**
- `id` - المعرف الأساسي
- `invoice_id` - معرف الفاتورة
- `order_id` - معرف الطلب
- `amount` - المبلغ (decimal 15,2)
- `status` - الحالة
- `date` - التاريخ
- `receipt` - الإيصال
- `created_by` - المنشئ
- `created_at`, `updated_at` - طوابع زمنية

### 4.6 جدول مرفقات الفاتورة
**الجدول:** `invoice_attachments`
**الملف:** `database/migrations/2025_06_04_230120_create_invoice_attachments_table.php`

**الحقول:**
- `id` - المعرف الأساسي
- `invoice_id` - معرف الفاتورة (مفتاح خارجي)
- `original_name` - الاسم الأصلي
- `file_path` - مسار الملف
- `file_size` - حجم الملف
- `file_type` - نوع الملف
- `description` - الوصف
- `uploaded_by` - رافع الملف (مفتاح خارجي)
- `created_at`, `updated_at` - طوابع زمنية

**المفاتيح الخارجية:**
- `invoice_id` → `invoices.id` (CASCADE DELETE)
- `uploaded_by` → `users.id` (CASCADE DELETE)

## 5. الجداول المساعدة

### 5.1 جدول العملاء
**الجدول:** `customers`
**النموذج:** `app/Models/Customer.php`

### 5.2 جدول الضرائب
**الجدول:** `taxes`
**الملف:** `database/migrations/2019_11_13_051828_create_taxes_table.php`
**النموذج:** `app/Models/Tax.php`

**الحقول:**
- `id` - المعرف الأساسي
- `name` - اسم الضريبة
- `rate` - معدل الضريبة
- `created_by` - المنشئ

### 5.3 جدول الحسابات البنكية
**الجدول:** `bank_accounts`
**الملف:** `database/migrations/2020_01_09_113852_create_bank_accounts_table.php`
**النموذج:** `app/Models/BankAccount.php`

**الحقول:**
- `id` - المعرف الأساسي
- `holder_name` - اسم صاحب الحساب
- `bank_name` - اسم البنك
- `account_number` - رقم الحساب
- `chart_account_id` - معرف حساب الدليل المحاسبي
- `opening_balance` - الرصيد الافتتاحي
- `contact_number` - رقم الاتصال
- `bank_address` - عنوان البنك
- `created_by` - المنشئ

## 6. عمليات CRUD

### 6.1 الإنشاء (Create)
**المسار:** `/invoice/create`
**الطريقة:** GET/POST
**الوظائف:**
- عرض نموذج الإنشاء
- التحقق من صحة البيانات
- حفظ الفاتورة والمنتجات
- تحديث المخزون

### 6.2 القراءة (Read)
**المسارات:**
- `/invoice` - قائمة الفواتير
- `/invoice/{id}` - عرض فاتورة محددة

**المميزات:**
- تصفية حسب العميل والتاريخ والحالة
- عرض تفاصيل شاملة
- تشفير المعرفات للأمان

### 6.3 التحديث (Update)
**المسار:** `/invoice/{id}/edit`
**الطريقة:** GET/PUT
**القيود:**
- إمكانية التعديل حسب الحالة
- تحديث المنتجات والكميات
- إعادة حساب الإجماليات

### 6.4 الحذف (Delete)
**المسار:** `/invoice/{id}`
**الطريقة:** DELETE
**العمليات:**
- حذف المدفوعات المرتبطة
- تحديث أرصدة الحسابات
- حذف إشعارات الائتمان
- حذف منتجات الفاتورة
- حذف سجلات المعاملات
- تحديث رصيد العميل

## 7. المميزات الإضافية

### 7.1 إدارة المدفوعات
- تسجيل مدفوعات متعددة
- دعم طرق دفع مختلفة
- تتبع المبالغ المستحقة

### 7.2 إشعارات الائتمان
- إنشاء إشعارات ائتمان
- ربطها بالفواتير
- تحديث أرصدة العملاء

### 7.3 المرفقات
- رفع ملفات مرفقة
- تتبع معلومات الملفات
- إدارة الأذونات

### 7.4 التقارير والتصدير
- تصدير الفواتير
- تقارير مالية
- إحصائيات المبيعات

## 8. الأمان والصلاحيات

### 8.1 الصلاحيات المطلوبة
- `manage invoice` - إدارة الفواتير
- `create invoice` - إنشاء فاتورة
- `edit invoice` - تعديل فاتورة
- `delete invoice` - حذف فاتورة
- `show invoice` - عرض فاتورة
- `duplicate invoice` - تكرار فاتورة
- `copy invoice` - نسخ رابط الفاتورة

### 8.2 تشفير البيانات
- تشفير معرفات الفواتير في الروابط
- حماية من الوصول غير المصرح

## 9. التوصيات للتطوير

### 9.1 تحسينات الأداء
- إضافة فهارس للجداول
- تحسين استعلامات قاعدة البيانات
- تخزين مؤقت للبيانات المتكررة

### 9.2 مميزات إضافية
- إشعارات تلقائية للاستحقاق
- تكامل مع أنظمة دفع خارجية
- قوالب فواتير متعددة
- دعم عملات متعددة

### 9.3 تحسينات واجهة المستخدم
- واجهة أكثر تفاعلية
- معاينة فورية للفاتورة
- تحسين تجربة المستخدم على الهواتف

---

**تاريخ التقرير:** 2025-07-14
**إعداد:** نظام Augment Agent
**الحالة:** مكتمل
