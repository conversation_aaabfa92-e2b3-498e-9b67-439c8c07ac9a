{"__meta": {"id": "X77e5d1616b13de9e990b834307bc533d", "datetime": "2025-07-23 18:21:20", "utime": **********.226509, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294879.77446, "end": **********.226523, "duration": 0.45206284523010254, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1753294879.77446, "relative_start": 0, "end": **********.146345, "relative_end": **********.146345, "duration": 0.371884822845459, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.146356, "relative_start": 0.37189602851867676, "end": **********.226525, "relative_end": 2.1457672119140625e-06, "duration": 0.0801689624786377, "duration_str": "80.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46536176, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027329999999999997, "accumulated_duration_str": "27.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1760721, "duration": 0.026109999999999998, "duration_str": "26.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.536}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.211673, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.536, "width_percent": 1.903}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.217539, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.439, "width_percent": 2.561}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-922759663 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-922759663\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1762062422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1762062422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1194087618 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194087618\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1781957069 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294877783%7C14%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM0NEF5aENJRTA5L21BaURUS0hGZFE9PSIsInZhbHVlIjoiT0FlUlNuUjZQSVdXNzRVVDNmUEY3MzhiY2pVZ3pLYmFBWDBYRmFmZmVWWnRFSTZERHRxemlVZU1NZWVOdEFvOWNaMzBpQ1dFdmh6dWlkenZYMVpCVWlidkdnQnc2UE5mT0JhNEh2K1I5VEFBSm0rNDQ3MnFBM0w5cVA4WEMzNmMzTHJUcjhlTEFzWlJuTXZmWDZXVFVTdXUrRTBzVkNWdWJCbGdZTDgycGZYVWdjcXlLYUNCZG5HckZrdWxQdFQvZXhLTWx0SXhQZWNGM1F0S1pLRGlqTjRaeEVRZFYzNzViV1NYM0Q5VHVLWFliVWpmdzA5TVZsaVpDN0NjbkpMRHBRZVJkU28wWnUvTnFVZnJvWVpNOVc2TC9mcyswNmd2YzRIbHFZazZzUEpJdU5lYlBaMVRHUHJLYkdpSVhLeFlGUldmT00wNnMyN1owVEczNm1GUlRML3ZzbDFoQm9YNjMxRlNBVWdySC9HOEh6RllKbHFvTlNkMTVNQTdDNzYxTjdrRExnUzNxRkpKdFZ4MmVwME16Nyt0c0U5cmptVm91a3RwcVBGZE14SnRDd2EzZkZWTzVXMEJGcWhrb0RFQ3lLVzhFcVpvYWlpZ2oyZ0xQdFRCSmQzYWpyUzFOQzJxTGszK3pXZWpqakhPRlcyQzJBTUFGenNKSFZpUEI2T0YiLCJtYWMiOiJkZTU5N2Y1NGVmOWMwYmFhNTkzNzkzMDZiOTExN2M1YTYwZGU2ZGFjZDA3MTY5NjJjMTQxMmVlNDczZDdhMDllIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtyUGlpQVIzcHNkZDFSSm95dHRocWc9PSIsInZhbHVlIjoiTDM1SmVKNGx2VVVCdFpBRVd1SitCNkp5ZVpsM2NHTUVPNytITU52ai9DYTIxSXduTGdlaS94Lzd1SW44N0k2dGxocVVFRkhZNmMvbVV0ak5XWkpJc1E2Q3YvTU1OSmRyYmt3SkF1OS9RTWZwcmhnSVBFOHRQQ05EeTU4TGpsUnJ5c0o4aU0rc25BcVBZMjdvaGRhTHJnL0drQ0xCS3A1QkFsZHVWZWlBc05KTllSR0wxampqQ0xOQnlSRElkWEoyYytHN05BdE1mazc2enZLNVRWajBkOU1hWlNvVnVsVG9tSVhSZVd6MldZcG5vTUhuZS9KWkthcHBkeDdDaC9ETG83M2JIYVo4VndwZlZWU3F6SjRkOUdNaGJtT0RjR2NtUVA1RnU5ZVErbEhFRFYvM0grejZiUjZlL1VCQzBaM2F4MHozbVZQVTRhSFhXMVkrdWFkbWNrWkg4dEJqb0MyZmdDNzZxVmFKcFVERmZzOXppVC92UFZBZk5hWXV5RkVSK3hyd1I4aGhZNVluME9hQVFHbkhrbGp0WE56L3VBT0xSMU9lWEx1b2U4MFRwald0ZnM0d2ZBOUliek81SktwUk5nZlVDdW94L3F5Z2FMcWFyV1hUY2lEVWp5NWVmMmdzRWFpSi9wby9sMjN1Y2dKaTZIWDVyWWhKSVJFQmdvc2wiLCJtYWMiOiJhZGI0MWYzYWQyMWM4YWJkMjBmOWIzNzE2NjlmZGIwMzc5ODgyZTI2NTEyMzQwNGQ0ZjExNjg2NTdmYmJkYjk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781957069\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-619128750 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619128750\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1598123194 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImkrVTZhVEZKVTFKMUd6WDkrMG81aWc9PSIsInZhbHVlIjoiWjhkOHhBaHJRSE4zbTBPUjhpSjJCTGJYSWV0ZGVYYitNa0JybEh5U1JYbTdzS28wQk5uK2RTTXRSc0Y3NHVlSjhoYkM4OGVReUZveXZ6RCtYTVp4MTJRWThmdXJFNDFTRGFHT2xEdUhxcGtlV2x3S1FsRVlmM2FKeWdFTm1jL0YxZ3JxMHRGaVhGd3B3aGJLVisvL3NqdDNwQTFtWS8wMDNVNXBYQWpHQkszRk4xajNNMlNHeWF0V2p2SHNuejB2dEpFWWcyY1pWeHh3WnAvcU12c3BBSUNtd1IySS9XbTFMblpwR3NhNTFvd0MrZmcwZGYrajUvNzE0eVZ5Y0dEKzNQMFFlYmptcUwyclJFL0w1dWUyQ0plMzZabzd3UjRVZWdnNUwxVHVEVTcvYjZXeCtlblRSdVNrSUNXUGkvek1TaDJkMys2VnVHZzczZGltNVU2VHNkemdxUE55MVU4VkRwbVNyT3dVYnJDbmZ5djNwUHdnMld3cGduMEJ2QTBQOG1KUW5nNm5JbGxLMXdBODBXVHpjVHdiQUlaYTNoSjVyV094Wkc3OG1SQkZyc1hIbjVLL2hBaWkrOEFKd2ZDcXdGZE52Qmx4cFZIK3Avak1aL09tMlhLZzFkd3YxMmxORndOQ0lCY3ZEanhhUjF6YmVTb2ZUWUQ1VEkwWStjM2ciLCJtYWMiOiJiOGQxOTJmNDFkMDUyYzg0ZjM5NTdlYzNhZjQyZjlmYTgxODk1ZGJmZTE5N2I0MzE3YThhNzViNWRkMWU1MjczIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhTZWlZWlF5RTVjT3pITG42VDBXTUE9PSIsInZhbHVlIjoiMi9QYXJjZDZYSFB5WGsxaXRlaDNDVTBQaTBXRUJMMlQxK1NTbFVlRzUwdUh1QkpxaUgzc0lwOWpaUWJQdEM2Wm94eHhtMEF6S0UwNnIrOGFKcFZ5V1ZYNEhKQVFqUkpPWnpnZ2NEUEIxNzZwWjF0QmhZNnlCWlRaT3g3V2dNOWpGdTVKNlZaa3dTYXFWR2JZWE0xSnBuczdENEVYY0JNWUNsMHhOOHhXdHJmOG8vR0RSQWI4SFphTVVOaVoyYi9ibDZwbHBCbG1hWHorUTRVR29OK1FEbkJDakJLRGhTT25ITWowbGZDcWV6QTdtRW5jTWZ4dHhtYUhZNERYSldrUnNXb3NETGo2ditTNTFiOCtsK3RmWGxYUTh5czg2VVhPaFVta3B6eUlYaUxmQ1pPdlh0ejUxT0h3V3VscGoxV1F2ckYxOEhtVFVpTjBKMEdMWWlHeUF4UzB5aDFIZS9WR3pwcmlyUFlpZjZjNFQvckhQRHd6WUZYNWxBbGNiWDB1QStIQmtzQUkydzNDWVBFZElRWEhySWpEVTZ4M1FIVTVaNC96cnRHNnpYNC9tSHFlNXIvclowaklIeFB3NXVsRzNRTkovZ0JodUJ5anN3VllYaklKcjA0OTBIbVNQS0l1NE1KV01JL1VHQXI1RHY3R3puQm1vci9OZGJaT2trS2IiLCJtYWMiOiI1MjgxNWI2MmI0NjFiZWY1MGI2MTY3ZTE2OGM4NjNlNzNhMmVmOTNhMDg0ZjAwZDYyMDVhNDRjMTIxMGIyMDVhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImkrVTZhVEZKVTFKMUd6WDkrMG81aWc9PSIsInZhbHVlIjoiWjhkOHhBaHJRSE4zbTBPUjhpSjJCTGJYSWV0ZGVYYitNa0JybEh5U1JYbTdzS28wQk5uK2RTTXRSc0Y3NHVlSjhoYkM4OGVReUZveXZ6RCtYTVp4MTJRWThmdXJFNDFTRGFHT2xEdUhxcGtlV2x3S1FsRVlmM2FKeWdFTm1jL0YxZ3JxMHRGaVhGd3B3aGJLVisvL3NqdDNwQTFtWS8wMDNVNXBYQWpHQkszRk4xajNNMlNHeWF0V2p2SHNuejB2dEpFWWcyY1pWeHh3WnAvcU12c3BBSUNtd1IySS9XbTFMblpwR3NhNTFvd0MrZmcwZGYrajUvNzE0eVZ5Y0dEKzNQMFFlYmptcUwyclJFL0w1dWUyQ0plMzZabzd3UjRVZWdnNUwxVHVEVTcvYjZXeCtlblRSdVNrSUNXUGkvek1TaDJkMys2VnVHZzczZGltNVU2VHNkemdxUE55MVU4VkRwbVNyT3dVYnJDbmZ5djNwUHdnMld3cGduMEJ2QTBQOG1KUW5nNm5JbGxLMXdBODBXVHpjVHdiQUlaYTNoSjVyV094Wkc3OG1SQkZyc1hIbjVLL2hBaWkrOEFKd2ZDcXdGZE52Qmx4cFZIK3Avak1aL09tMlhLZzFkd3YxMmxORndOQ0lCY3ZEanhhUjF6YmVTb2ZUWUQ1VEkwWStjM2ciLCJtYWMiOiJiOGQxOTJmNDFkMDUyYzg0ZjM5NTdlYzNhZjQyZjlmYTgxODk1ZGJmZTE5N2I0MzE3YThhNzViNWRkMWU1MjczIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhTZWlZWlF5RTVjT3pITG42VDBXTUE9PSIsInZhbHVlIjoiMi9QYXJjZDZYSFB5WGsxaXRlaDNDVTBQaTBXRUJMMlQxK1NTbFVlRzUwdUh1QkpxaUgzc0lwOWpaUWJQdEM2Wm94eHhtMEF6S0UwNnIrOGFKcFZ5V1ZYNEhKQVFqUkpPWnpnZ2NEUEIxNzZwWjF0QmhZNnlCWlRaT3g3V2dNOWpGdTVKNlZaa3dTYXFWR2JZWE0xSnBuczdENEVYY0JNWUNsMHhOOHhXdHJmOG8vR0RSQWI4SFphTVVOaVoyYi9ibDZwbHBCbG1hWHorUTRVR29OK1FEbkJDakJLRGhTT25ITWowbGZDcWV6QTdtRW5jTWZ4dHhtYUhZNERYSldrUnNXb3NETGo2ditTNTFiOCtsK3RmWGxYUTh5czg2VVhPaFVta3B6eUlYaUxmQ1pPdlh0ejUxT0h3V3VscGoxV1F2ckYxOEhtVFVpTjBKMEdMWWlHeUF4UzB5aDFIZS9WR3pwcmlyUFlpZjZjNFQvckhQRHd6WUZYNWxBbGNiWDB1QStIQmtzQUkydzNDWVBFZElRWEhySWpEVTZ4M1FIVTVaNC96cnRHNnpYNC9tSHFlNXIvclowaklIeFB3NXVsRzNRTkovZ0JodUJ5anN3VllYaklKcjA0OTBIbVNQS0l1NE1KV01JL1VHQXI1RHY3R3puQm1vci9OZGJaT2trS2IiLCJtYWMiOiI1MjgxNWI2MmI0NjFiZWY1MGI2MTY3ZTE2OGM4NjNlNzNhMmVmOTNhMDg0ZjAwZDYyMDVhNDRjMTIxMGIyMDVhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598123194\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-154969000 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154969000\", {\"maxDepth\":0})</script>\n"}}