{"__meta": {"id": "X9fce4274e03ec59942ba379b92f585bc", "datetime": "2025-07-23 18:21:08", "utime": **********.521249, "method": "POST", "uri": "/receipt-voucher", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294867.998883, "end": **********.521268, "duration": 0.5223848819732666, "duration_str": "522ms", "measures": [{"label": "Booting", "start": 1753294867.998883, "relative_start": 0, "end": **********.405726, "relative_end": **********.405726, "duration": 0.4068429470062256, "duration_str": "407ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.405736, "relative_start": 0.40685296058654785, "end": **********.521269, "relative_end": 1.1920928955078125e-06, "duration": 0.11553311347961426, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46723160, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST receipt-voucher", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptVoucherController@store", "namespace": null, "prefix": "", "where": [], "as": "receipt.voucher.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=64\" onclick=\"\">app/Http/Controllers/ReceiptVoucherController.php:64-84</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03303, "accumulated_duration_str": "33.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4370081, "duration": 0.0245, "duration_str": "24.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.175}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.481314, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.175, "width_percent": 3.966}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.487647, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:69", "source": "app/Http/Controllers/ReceiptVoucherController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=69", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "69"}, "connection": "kdmkjkqknb", "start_percent": 78.141, "width_percent": 3.058}, {"sql": "select * from `warehouses` where `warehouses`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.491662, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:70", "source": "app/Http/Controllers/ReceiptVoucherController.php:70", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=70", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "70"}, "connection": "kdmkjkqknb", "start_percent": 81.199, "width_percent": 3.179}, {"sql": "select count(*) as aggregate from `voucher_receipts`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.494093, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:72", "source": "app/Http/Controllers/ReceiptVoucherController.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=72", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "72"}, "connection": "kdmkjkqknb", "start_percent": 84.378, "width_percent": 3.209}, {"sql": "insert into `voucher_receipts` (`date`, `payment_amount`, `receipt_from_user_id`, `purpose`, `payment_method`, `created_by`, `custome_id`, `warehouse_id`, `shift_id`, `updated_at`, `created_at`) values ('2025-07-23', '1000', '22', 'ي', 'cash', 22, 'S-المستودع الرئيسي-55', 8, 122, '2025-07-23 18:21:08', '2025-07-23 18:21:08')", "type": "query", "params": [], "bindings": ["2025-07-23", "1000", "22", "ي", "cash", "22", "S-المستودع الرئيسي-55", "8", "122", "2025-07-23 18:21:08", "2025-07-23 18:21:08"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 81}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.496625, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:81", "source": "app/Http/Controllers/ReceiptVoucherController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=81", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "81"}, "connection": "kdmkjkqknb", "start_percent": 87.587, "width_percent": 12.413}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "Receipt Voucher has been Created successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-voucher", "status_code": "<pre class=sf-dump id=sf-dump-1785918706 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1785918706\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-547409932 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-547409932\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1278611798 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-23</span>\"\n  \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>receipt_from_user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>purpose</span>\" => \"<span class=sf-dump-str>&#1610;</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>custome_id</span>\" => \"<span class=sf-dump-str title=\"21 characters\">S-&#1575;&#1604;&#1605;&#1587;&#1578;&#1608;&#1583;&#1593; &#1575;&#1604;&#1585;&#1574;&#1610;&#1587;&#1610;-55</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>shift_id</span>\" => <span class=sf-dump-num>122</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278611798\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1692617846 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">142</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; XSRF-TOKEN=eyJpdiI6IkRxcmk4RW9tazhIY1djb2R2YStmQ2c9PSIsInZhbHVlIjoiSytzUFcrdUdwd2VoQVdFUE4rRHp2UnF4RlU2TVI3RitKVE5yZ01DNDhBb01FZWJnZkZScU5MWVEvZy9PT1lkMFA4RmE4Nk9rd3NVV1RTZDc3Mlh3RGhFbFF1NWhOK3p4ZFRNaWx0WGovanpYOFFLSURhQTJlQ21rQVRuZDVGUEh4L2xmWitOVlRvbEU1SnB1VFpnU2xUUk9VQTFXQ2pRdGZSKytvanFNeUgrWWdBM2E4TVBzOXNMRDc0ejhvRUVJejR5ZDZnRVoxMzdQOHNORW5ObklTVllSVEZXSy81T2h5TmhYYWYxMFNicWVzQXpaOE1rSGF3enpVVllWa0VJZ3hKQ04yLzErcWoyaHEvSXVRNng0Z2xmTy9DQUVwTmNSTUpHb01Vclh3V2pQL2N1NlQ0WGJyc3QvSXBkTk4xTTZDYSs3dlNnQlhzbE5lRFpDeE91SWkreGRYR0w0enFKQm5rZUFkVTFXeUVmZEgweEVDMDdPdENhYnJldUpCVlhYOVowSGhSSlNqVThPSjVCQjRvNm1ndHV4YXZCR3VRU1A1Y08wM1dIZUo1cGlPNm1FTHA3eEFQUktQMjREYjNNZzhNWVpiTlIrdEVOTlI0cHdSQjRaaWxqUVVTMXpTUFpobklMWlY0ZkNJV0FiazF4YVFCd2JqQWlOcndHdXpPSjgiLCJtYWMiOiIwNjcxZmM0NWE3YTFmZWFjMTZjOTg2OGY5YWI0NTM3OThlZDQwMzZmMTVlNjAyZmEzZDM2NDkzNjY3MDZkNmY4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImUwYlBUaGNKcWJkSERJT0p0OWJFU2c9PSIsInZhbHVlIjoiVzh4Q2JwMSsvYzdGL212elFyaEg0UlpKR3lTLzVtZGtxUDA2Uk5lSVltQkRpaEtZRnlKZ2svelF6VlFWb0hadmdRRWs4VG8wTCt4S2h2d1ZPMDZDQU9RVW54NFNwcGg2OFZ4WVVFdEtrSllnbm5MYkoxVkV1Q2RWdEJKWS8vOFl4ZUNGM2dVeDVXaUhLZkpLUGVDejJMcGZLeDBIZTgrcXdQREJ2eFZuVGFhK0xSZXhWY01yZDU5dUYrRkx3RC85ZFROTWZxQWpZZ1IrVVlSZ0ZUVkdKVTZjSkxRQXBONkMvM2lYNG1zem1QVDJJRmdWY2JRTGtXTEV1YU4rcWVsZm1ONjkwNmxNbmRIRkdjcEhWV2J1OWxJcllDZzhGWWtqZUdSU2gwVHJKN0hUUkVPdlVlSzhCK2hiYzZVdHFQQ3lPK0dEZGFXUHhHZUl4V2FzdzllemNzdzJUWlZ1V2pudkxPZzZ0Nll1dWhObWtJd3dMOTJsL0U1emZOZHF5b3ZPZ1FjM0JKUFgrM1FJUEgxL1N3SlcrVEY2ckJ1Y3VVMElMM3ZJdkVqcXJQZHhlSzloNlRJVk5EUWl6NUpkVzY4YmF3YWlqekRmaWRzT1cycmV1eldubmNLNzJOV1RTVnlPemVKZ1RGTzdidzlQWTRyRysySlFqY2NrSW1oR3phMFYiLCJtYWMiOiJjZDc2ZmY5OTQxYWQ3Y2Q3YTVhZTRiZjYwNzg3YzBhZDM2MWQwMWYwMTU3MDdkYmNkYzc1OTZjZjAwZjYyODBjIiwidGFnIjoiIn0%3D; _clsk=c6ed4t%7C1753294850069%7C10%7C1%7Ca.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692617846\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1926855078 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926855078\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImllSUsyejd5NlNqOXF5N0ZtQll4Z3c9PSIsInZhbHVlIjoiM1RlZ3g0c2szd1JEYVQxSEJsL25RZjRNT2xHN1JBZ2o5ckd4QU1GZGJ1VEFEQ1NkTUJibTQ4cUwxQnlsc0JhQ21Xa1o0WmkvK0ltOUhTZGZOWGM1TzZmaG5qSTVrWnJoc3h5VDliNE5ETnIyQjc5Qzk1WjZ5WmxFOWhyVmxCOG8rbS8rU1VhTlFsUEc1eTVqYS8rd1lnR01UajIydDRYeEJ6YkllNFZPT1JuQVZCM1ltUjE1UGlFNXdpV1dHV202MU5YS0Z0MVJaQUNVUS9GNmFDQTR0YnVMb1pvUVRNU0Z0VkZURjBEbXVmUHNiSEF5T01zSEVhdDFsdlk2WnZ6TENUcHBQaWxWSVJzM2lIMjRKNjc1SkN0S3Vwb3dySWhvZ1ZVd3g4MVY3VzIxMjdiTXc3MWFucVN6WVdVSDMwT2ZPMDNxTjFvS1lhMDE1bnVHZzVCK1l1MStwcDdoNEZNWGdIT2RXUkg3NUhHVFNvaUczNFpuWlVPY254NCtkd045ZVV1dklHc2ZTOGdFY2tGN3Rmc3o1eVozS1VUZ3VRVG5kWWFzbVp1bDB6MDdRTGMwTGNDNGN1VlFYNDNBbG9EWmRHekhJOFNRZ3lYSUw1QWorN3haR08zSFRJYzdQMHY2QlFmOG4rWnF3NmxhbXA2ajJraGdaY0w0SWYrSzJDTjMiLCJtYWMiOiI0ZGVjNTgyZDI1MmIxZGQwZGY1NDUzNDNlOWQzYzk1NTcyMWNlMWMxMDc1ZTJlMzBkNWYxYmVkZWNkZjFkNTFjIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRYQjlmVVZ5eDZwN2txd0JQbXhFYUE9PSIsInZhbHVlIjoiY2dCTHhha2kwUDBxZngyd2VFRTA3TXdkcXF6RVVQVmc3M1NxcEgyRi8vczFJQ1ROZ2VwelBiODgvZnFRcHpVekVRaFRlV05XWDNJdWd4Q1VOUW1nQ3NGOWRiNjJKQ0J2RFBTU3oxY0ljcWJ5WGdnRHI3bWJhRUlYQ3VkNGVQaWpsUHJCdmdwTXJkQjhpaXJabVlDajBKZEkzSjVJZzJnYWlmNmxlRzRJUENtcTdEdVhQeEppcXh6SjJZQ3hVVmI0OTh5eVhVYTdOdkE3MUNYR2lCeE9NN2VsVk5RZGNEdTd2RDFyNXdwbnZ0NFczZGxoT0s5QnBsV3hDVXc2MmcrbjhPRUZXRWlXT0JKNDlULzhRbC9mVFM2VFhOY0h1MVZMV3hjb1diYi8zQ2JTM1EyUFlvVFp0UlZMbjloWU9kK2haQ0pwZS9Ed0JEbWQ2b0U3MjF1bTl6Nkw5S2ZPRW4yK0FzNDVCK2hwR3hibGdSWldkR3Mycys2Rm5CYkFjbmYvQXFhN0MxTGErL3Z4VWQ3d0FqQWlxNkF6R1dqSTR2N2FKYzV0UjQrM2xKYmZRVjRDd3RpK0llSWMvWEhMOE5IYVV4a1JwRFlwc1l1d2gxb0dPNWpkWW5MR3VRR0hYMTA3Z2NyMUYxaU9qQjlOVlFDbEY4b1NhZEtwQk81TUJVblEiLCJtYWMiOiJkZDAxMmZiMTI0YzUzOTA0NDNiMTNiZTg5ODU1YzhkNjQ3NGY0NzNkMmVjMTU5NWI3MDVkMzBmN2EwMDIyMWZmIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImllSUsyejd5NlNqOXF5N0ZtQll4Z3c9PSIsInZhbHVlIjoiM1RlZ3g0c2szd1JEYVQxSEJsL25RZjRNT2xHN1JBZ2o5ckd4QU1GZGJ1VEFEQ1NkTUJibTQ4cUwxQnlsc0JhQ21Xa1o0WmkvK0ltOUhTZGZOWGM1TzZmaG5qSTVrWnJoc3h5VDliNE5ETnIyQjc5Qzk1WjZ5WmxFOWhyVmxCOG8rbS8rU1VhTlFsUEc1eTVqYS8rd1lnR01UajIydDRYeEJ6YkllNFZPT1JuQVZCM1ltUjE1UGlFNXdpV1dHV202MU5YS0Z0MVJaQUNVUS9GNmFDQTR0YnVMb1pvUVRNU0Z0VkZURjBEbXVmUHNiSEF5T01zSEVhdDFsdlk2WnZ6TENUcHBQaWxWSVJzM2lIMjRKNjc1SkN0S3Vwb3dySWhvZ1ZVd3g4MVY3VzIxMjdiTXc3MWFucVN6WVdVSDMwT2ZPMDNxTjFvS1lhMDE1bnVHZzVCK1l1MStwcDdoNEZNWGdIT2RXUkg3NUhHVFNvaUczNFpuWlVPY254NCtkd045ZVV1dklHc2ZTOGdFY2tGN3Rmc3o1eVozS1VUZ3VRVG5kWWFzbVp1bDB6MDdRTGMwTGNDNGN1VlFYNDNBbG9EWmRHekhJOFNRZ3lYSUw1QWorN3haR08zSFRJYzdQMHY2QlFmOG4rWnF3NmxhbXA2ajJraGdaY0w0SWYrSzJDTjMiLCJtYWMiOiI0ZGVjNTgyZDI1MmIxZGQwZGY1NDUzNDNlOWQzYzk1NTcyMWNlMWMxMDc1ZTJlMzBkNWYxYmVkZWNkZjFkNTFjIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRYQjlmVVZ5eDZwN2txd0JQbXhFYUE9PSIsInZhbHVlIjoiY2dCTHhha2kwUDBxZngyd2VFRTA3TXdkcXF6RVVQVmc3M1NxcEgyRi8vczFJQ1ROZ2VwelBiODgvZnFRcHpVekVRaFRlV05XWDNJdWd4Q1VOUW1nQ3NGOWRiNjJKQ0J2RFBTU3oxY0ljcWJ5WGdnRHI3bWJhRUlYQ3VkNGVQaWpsUHJCdmdwTXJkQjhpaXJabVlDajBKZEkzSjVJZzJnYWlmNmxlRzRJUENtcTdEdVhQeEppcXh6SjJZQ3hVVmI0OTh5eVhVYTdOdkE3MUNYR2lCeE9NN2VsVk5RZGNEdTd2RDFyNXdwbnZ0NFczZGxoT0s5QnBsV3hDVXc2MmcrbjhPRUZXRWlXT0JKNDlULzhRbC9mVFM2VFhOY0h1MVZMV3hjb1diYi8zQ2JTM1EyUFlvVFp0UlZMbjloWU9kK2haQ0pwZS9Ed0JEbWQ2b0U3MjF1bTl6Nkw5S2ZPRW4yK0FzNDVCK2hwR3hibGdSWldkR3Mycys2Rm5CYkFjbmYvQXFhN0MxTGErL3Z4VWQ3d0FqQWlxNkF6R1dqSTR2N2FKYzV0UjQrM2xKYmZRVjRDd3RpK0llSWMvWEhMOE5IYVV4a1JwRFlwc1l1d2gxb0dPNWpkWW5MR3VRR0hYMTA3Z2NyMUYxaU9qQjlOVlFDbEY4b1NhZEtwQk81TUJVblEiLCJtYWMiOiJkZDAxMmZiMTI0YzUzOTA0NDNiMTNiZTg5ODU1YzhkNjQ3NGY0NzNkMmVjMTU5NWI3MDVkMzBmN2EwMDIyMWZmIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1883264499 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Receipt Voucher has been Created successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883264499\", {\"maxDepth\":0})</script>\n"}}