{"__meta": {"id": "Xa9cdc9d5b4a0eee0a235e539baef8564", "datetime": "2025-07-21 01:18:02", "utime": **********.36572, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060681.919171, "end": **********.365734, "duration": 0.4465630054473877, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1753060681.919171, "relative_start": 0, "end": **********.293084, "relative_end": **********.293084, "duration": 0.3739128112792969, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.293094, "relative_start": 0.37392282485961914, "end": **********.365736, "relative_end": 1.9073486328125e-06, "duration": 0.07264208793640137, "duration_str": "72.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46328456, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021750000000000002, "accumulated_duration_str": "21.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.325893, "duration": 0.02057, "duration_str": "20.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.575}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.355181, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.575, "width_percent": 2.989}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.359218, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.563, "width_percent": 2.437}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-8019812 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-8019812\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1367571711 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060674456%7C6%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktOU0loV2pSUTcyTEQ0MzNSUkxNNWc9PSIsInZhbHVlIjoiM2YySm05Z1VYVXdyUzNrcm1mU1R1ZkVDMi9URGdadzdxR0l3VkJLZnBCcnd4RDh1MHI2S29tYWN6Q0VnemQyOElld0xxQ2hKVlVrdTRGemQyZ2xSeHNqaFMraW84K0FPQllVcThUYmszS3E5OHFZZzdSY2FOUUZJaTF6aE9UWVhBRnpIUTlRTXVsQW5NVnBvLzVDYk1KdCtpMy91bW8vRDk1TWJrNFhEUzlMdEs3V0wzVDlERE56ek1ZbEhYcGdWNm1xVlBvTEcwa1M4RVZMVFdyRTRLK1hPYU4xNUN1ZHNmeU1La3RBZkpOMEJFcURRQXplelNMMzVIU2g2YzVyWFdIZU1Nd2Y2ZVZMWVkwQ3NTZUxNSkp2ajlvNVRndjBiUHI5dU1UdlpXNlNZZklmQWpxY3FvRGprSy9td3Y2WlpWaWNhUWlXWlVVczFQcXhWTEwvSXVnTW03MVJueXBqa2pnOVBYWHUremIycllXcXQxRlYvNEg4TS9uWFVLbnVGWjZCcUd3WlJwV2NKa3RvZTdEbzJlRW9ZaWxWZkE4N3FQUnVwK2QvYU9OVnVwVWsyUlY2T1pDWTV6a3BRWlhrUER4L2J1a1RIbHJMTVMvNXRYTVF0V2M0Z2ZxakVmVDJUQThjU2JqdGZybXphM2MwdEprZGZGTGtnelVKQnE3VTkiLCJtYWMiOiIxODFkNmFhODYyNGIwYTFlYjcwMzI5Yzc5ODEzNjFmOGJiOWZhNjJjODNlOGIwYWU2MzBjM2Q5ODc0Nzk1YTVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldJNHVDdVFqbGxRNHlqSml6TnNpYlE9PSIsInZhbHVlIjoiQmpUT01tcytBQThIV0lPamJKbG5rT0hQTCt4Z2M5ZTJURXBROFBUc3VFN3dRd0FlUUN6a1ZSbkF0OUgyYnh2MTBLR2NoZzFNcGp5SmJmUEVvZysyb3Rsb3E0Y3hQQytXQjZVdVJmenlPNFE4SHRXUHNpKzZ5dUtod1NlSENuSWVsUmRvaFpSUWRzQUNqNFZoeFZyRGtOREkvZWNZbUpUeFlLRjVjMS9ycGg2U1NMbkJQTE9OWVdrWHFZYmI0eDB2b29QczlWT1k5WjFrUGQrbmtzR1hpeWp5M3cvZXZHcnVjc3p2NzFycDZMSS9RNmNFQlh4Y3ZkYnIwck1sTnZnaFUzR1FUZmxZSU10M0lUV1N4Uys5dm5qc21lT0JBbXhhL0ZlTWkzWWhjQ3d4NEozc3VNRkZpNmREMHo5ajBxSG1GNDVFVy9zMldPclYwalkvM2FVckU3NFR2bnYyS3g0dlFXSVh1ekhHaFR4aEVtZTJkbUxyR0U2MWJIY0dsNUE3UHZBcHNxQzdUdjdsYTB3RDZoU014WkFFS3B1Y3hkcTMyR0JGRkV6WDEyV29EQVdKZ3JPSGg3N2Q0djF3L1dKZkFyZ1dWMkRTM0NibU16MGQxU1ZBVjdxQXdTSEMzaVc2c09KQVhaaGQzcDRMc3hUdlVWUXZaTnNOZHFBOXE2aUIiLCJtYWMiOiI5YjQ5NGViZjEyMDBjM2ZmMjU0NjhlZjgyN2ZlMTcwMzVjYTVjMmNmMTY0NTU0YzNmZTRmNmQxOGNmYmZiZWY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367571711\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-876359017 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876359017\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-775845188 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:18:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh6Sno1UzhmNzJHeDdlK08wNmxXdHc9PSIsInZhbHVlIjoiaGpUckVNUVFaYlNFdFFXd1lpT1Z1T1JSQm96ZVlJWmw2NHVpTkZjR2l1djlyM2xyV21uMFprOXFaTjF6di96UGdINlVxazE2ZDZtMWJqZW5lcjRVNDJBbS9BT0hENnBFc1ZxbU9LTXBZS3pnN2hLQVBxK3dkYkUvVFZXWlR5VXBqbnhuMVlvaFNaWVJqZzVzdDNpUWsxcTYycE8xWU14VW1nSmM4ZWRqbUhBb09oWWpUOGlNanVmWklqQ1ZicEhUUVcwNkYxcnAzUjRjalkwb05OcVRtd0FSSUk5a0w3L05kZFR5Skh1MkVsNHZMYnZvRncrYlNpTktWQ1E1L1U1RDlqTjM2RGwxQW5Rc08vM0Z1OTlrbk1MRkJsbFVQQ1VPdFB6K0JrWDZ6enZXVGxMU280cTFSM09Na01SQUpZalpHM3Z3YnlLdy9EUGM1TFZTSjNWMndjZDlDWlVTQ3ZqZ0dGYnNDSHJCT0l3WitOVjNVa1FQbkk2RTI5S21XOG5QT091U3dJdElHWmhKT2x0S214bUJ5T1RNN0ZhMU56NDBCbno4UlluVmF2SmM1d2V1OThsRjFQK2RiTXNtV2Mzdk8ydnRPZG5TSE9CM3YxQkRreDdqNXBrVFNRU3A0VHZISVRLVVd3TXpHc2JyY3UwcDdBbEc2SlplWS82eWtjTlUiLCJtYWMiOiJiOGI3ZDE1Y2FmYjlhMzljZGYyNjI4MzdhZTk5NTIxZmY2NTkyZDQyZThhMTZmNDY2M2UyOGIyMzI2NjNkYzU3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im51dG5lYXJYeDRSeHc0dERCRVVBS2c9PSIsInZhbHVlIjoiSmxrZmh2d2cyZDlDZjBtV1kxWWE2S3YzTk4zcU94SW02aHJDWTNPRlJqbDJGdWVnYzRuWndCYUY3Nk9GRkp1Y2lBQVpoRU5FUHFpREJoNWxNMVRSL05qcFpHekJyb2VnWm05Qzh5eVNOaU40QTNZbDlMTEZlcjJBYk4vdjFEeHh5RHAza0hnMHVwV2l3RzNNQU9pdzJkbHdwTjhsVm1aR0tYQXRuaHloYUhPWGRWNjNPRGpiZzRtTVF1ME5leFYrSkp0d3g4cGVNd2hHY2MwYlpIdHBrRHNEL1pSNzNyRjVodDRaWm1JRjI5TlZhbzhGcnZOMXRheXhzT3NBcmZWa3dkZ1A0SWZmWHpMUVRpRHU2OUJpK0pMc3BvUGpVS2t2REdQMEc1UHhvaUpDTHFKSHBoRStMYzMzbmtkalJCL0lXSzFTem9lVjEyOW5vN0FxaGVLVm1hRjkyeVViWmxOZWFVVjdxRjNXWXQvWFF0VWtGYTNaQkxVQXdoS2p0Z241WmdDVW1XL3JOdGtQL2U3M1VwMW5uOUFTQVdYUUg1eHQxYk9ESzZ2aXhYNTNMaEJBL2RPOEFIaVE2VDYxYWE2dzJvTmppMEg2Q0NvNGYzTmtrSHhwY1hhWm96eEwrbFdkVHJVc0NtbkxwNTRRMjVuU1NKbEpZeDJFMjBDNGtBbGgiLCJtYWMiOiIxY2RiNDE4NjRjYWM2Y2RlMTkwYjQ4OTliNzM0YWY3NTQzN2YyOTBiMTYyNDdhMDBhM2E2ZGRhODQ2OTIxZjBkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh6Sno1UzhmNzJHeDdlK08wNmxXdHc9PSIsInZhbHVlIjoiaGpUckVNUVFaYlNFdFFXd1lpT1Z1T1JSQm96ZVlJWmw2NHVpTkZjR2l1djlyM2xyV21uMFprOXFaTjF6di96UGdINlVxazE2ZDZtMWJqZW5lcjRVNDJBbS9BT0hENnBFc1ZxbU9LTXBZS3pnN2hLQVBxK3dkYkUvVFZXWlR5VXBqbnhuMVlvaFNaWVJqZzVzdDNpUWsxcTYycE8xWU14VW1nSmM4ZWRqbUhBb09oWWpUOGlNanVmWklqQ1ZicEhUUVcwNkYxcnAzUjRjalkwb05OcVRtd0FSSUk5a0w3L05kZFR5Skh1MkVsNHZMYnZvRncrYlNpTktWQ1E1L1U1RDlqTjM2RGwxQW5Rc08vM0Z1OTlrbk1MRkJsbFVQQ1VPdFB6K0JrWDZ6enZXVGxMU280cTFSM09Na01SQUpZalpHM3Z3YnlLdy9EUGM1TFZTSjNWMndjZDlDWlVTQ3ZqZ0dGYnNDSHJCT0l3WitOVjNVa1FQbkk2RTI5S21XOG5QT091U3dJdElHWmhKT2x0S214bUJ5T1RNN0ZhMU56NDBCbno4UlluVmF2SmM1d2V1OThsRjFQK2RiTXNtV2Mzdk8ydnRPZG5TSE9CM3YxQkRreDdqNXBrVFNRU3A0VHZISVRLVVd3TXpHc2JyY3UwcDdBbEc2SlplWS82eWtjTlUiLCJtYWMiOiJiOGI3ZDE1Y2FmYjlhMzljZGYyNjI4MzdhZTk5NTIxZmY2NTkyZDQyZThhMTZmNDY2M2UyOGIyMzI2NjNkYzU3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im51dG5lYXJYeDRSeHc0dERCRVVBS2c9PSIsInZhbHVlIjoiSmxrZmh2d2cyZDlDZjBtV1kxWWE2S3YzTk4zcU94SW02aHJDWTNPRlJqbDJGdWVnYzRuWndCYUY3Nk9GRkp1Y2lBQVpoRU5FUHFpREJoNWxNMVRSL05qcFpHekJyb2VnWm05Qzh5eVNOaU40QTNZbDlMTEZlcjJBYk4vdjFEeHh5RHAza0hnMHVwV2l3RzNNQU9pdzJkbHdwTjhsVm1aR0tYQXRuaHloYUhPWGRWNjNPRGpiZzRtTVF1ME5leFYrSkp0d3g4cGVNd2hHY2MwYlpIdHBrRHNEL1pSNzNyRjVodDRaWm1JRjI5TlZhbzhGcnZOMXRheXhzT3NBcmZWa3dkZ1A0SWZmWHpMUVRpRHU2OUJpK0pMc3BvUGpVS2t2REdQMEc1UHhvaUpDTHFKSHBoRStMYzMzbmtkalJCL0lXSzFTem9lVjEyOW5vN0FxaGVLVm1hRjkyeVViWmxOZWFVVjdxRjNXWXQvWFF0VWtGYTNaQkxVQXdoS2p0Z241WmdDVW1XL3JOdGtQL2U3M1VwMW5uOUFTQVdYUUg1eHQxYk9ESzZ2aXhYNTNMaEJBL2RPOEFIaVE2VDYxYWE2dzJvTmppMEg2Q0NvNGYzTmtrSHhwY1hhWm96eEwrbFdkVHJVc0NtbkxwNTRRMjVuU1NKbEpZeDJFMjBDNGtBbGgiLCJtYWMiOiIxY2RiNDE4NjRjYWM2Y2RlMTkwYjQ4OTliNzM0YWY3NTQzN2YyOTBiMTYyNDdhMDBhM2E2ZGRhODQ2OTIxZjBkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775845188\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-13******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13********\", {\"maxDepth\":0})</script>\n"}}