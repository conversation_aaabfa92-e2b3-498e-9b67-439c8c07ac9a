{"__meta": {"id": "Xa121faf40173e02d664d36d06a6bf6b4", "datetime": "2025-07-21 01:20:01", "utime": **********.342627, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060800.899117, "end": **********.34264, "duration": 0.44352293014526367, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1753060800.899117, "relative_start": 0, "end": **********.273023, "relative_end": **********.273023, "duration": 0.37390589714050293, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.273031, "relative_start": 0.3739140033721924, "end": **********.342641, "relative_end": 1.1920928955078125e-06, "duration": 0.0696101188659668, "duration_str": "69.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006224, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02157, "accumulated_duration_str": "21.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3010972, "duration": 0.02063, "duration_str": "20.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.642}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.329661, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.642, "width_percent": 1.762}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.335356, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.404, "width_percent": 2.596}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-69865218 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-69865218\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1056874589 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1056874589\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-780066122 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780066122\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-504289897 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753060789457%7C6%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImE4anpkZ202ejMzVDdWQkcyaGdwUGc9PSIsInZhbHVlIjoia1N1NEFHT2hmbWVLd29aUVNvWjlkU2gzOHlvRXZuZ3JwaC8zRmxCcHZOWmN0NGVac1BlUHJLaU52L3BsenZVOUpNdzFIN1R4ek5BMllhckQ5a1ZocXFTV095NDAvOWZRM0YwTXdBWWdsQmtwa1RNeXo2TWFFRVZwN2NqcjNhSDkwamxaTVlMLzVnY0p3dGM0dkptVHYzcVVDU0ZkVzBTZEQ0MksxRnBZOWZTVW9aQTdTUWVnMEo1RHgyYkhvZmQzM2ExNjVZOHBNQ0lyZVdER2E4aEZLcEVJaXhka1hiNEdpVXkyUzNudlA4MnVjYTVwMVpYMEdmWnk0bEtxRXp1clJxY3hLZEVlTGh3Y2Z2VWRLM24rbVZEV0RIZG5CeXd6RkxsNFRqTnN3Yk9tbGVKQldPOG9renVLRkNxc0NONmpTTTlzL1EvZkd5dU9OdHp6SlYxNnJBUGFiYVJ6cWhQZ1Z4NWhVMi9ZdFk1aGlNVlh6dHR0SVNkeXhhZEQxTml1QUNPRG5JSWFpcHQrRWZNUlhIQTRUVitsV283U0ZlTkJFMHBLR2twTURtNlFXOUQ5STlacEdmeDRUY25aUVJtTVRpdCtBSWFaUzFndkp6bnY0UFY4cUxtOE5UUWtjSUZCYW1aaWhiY1JoTWNOYWJ6cGVkL1hkc0NLeTZ6WWl6TVYiLCJtYWMiOiI2MmQwNmJkZjVjNTczN2ZkYzEwNjNjNDg4MDZhMjYxNzMzMzI1MGYwOThmOWYwMjI4MTY1YWE1MTc2YWRkNDI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVBVExsTnFRZldTcnNZSWRydER0SkE9PSIsInZhbHVlIjoiN1NNMElMNUVkZDI0Rm0zaDRRY29TaHRmQjRzUml1N1FrRDRtS1BGcGpDQjFOWVYwUUVIVVZPSUFIV2F3eE05bEVudFBObVVTeFVmQkFMcWIrRjJ1U3JEMU1XZlFESXUwWHV0MFpVNE9yYlR5V0lGalpVNUZQdUduY1FIbFFmRWVnZkY3WVc4Tlo4UFNoai9JNUFxT3l5eTNBdjExK0piVGFPNXR4b2c2TitEbVJ6Y1ZSczcrdVpJMmhkZE03MXlNM2tmb0R4RkFiRUlhUVdueVZXSEF0cS8zMUVrMU5qYjN2MUFUd3hqR1h2bll0YllvYlc5ZHBTRzIzOUhGbU1oUE10eFpPcEJ2NzdWaFgwTi8vV1NoMkZoNSs0dkplK256K0c1WWZON0VmRVFKUWdxSGVXL3hoVFJCVmVGMkpMeXRjMGE5WDZEd3k1L0xBeVJDa2RXM2dqc2RxRlhOSWJUVW9MUWo5ZEJJZUV5Q0RCeDdwVFYzekEyVnQwNmlrY2M0eHNLc01pUkp1SGJ5VGk3dndXNDBObnE2a3hMSVFrZlB1YkZlZjVGWXJ4OCthRnN0S2k0d0JPMGtVYTVWZjdVRjNzQXN0b3YySHBqSDBpSitXZkdyNEVrbms4Sys2YWdJU05LeDJnL3RCemV3MjN5ekovQzh2Q0Fsbjl5TGtRYUIiLCJtYWMiOiJiMGE1ZDA4NDRhMjVjYjc1ZGRhM2JkNmEyZjk3MmNjNjkwMDUzZjdkN2QxN2M3MmY5YTVkZTNmZDhjNDIxODc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504289897\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-307966443 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307966443\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1553970189 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:20:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdMUnFicUFDZGMrNHk4RDJ3SHlHK0E9PSIsInZhbHVlIjoieUFBdG5lMGF1YjVtVnRzZm91VnM1Z04zVGhneFQwL2N5Y0xGaTEyRmxKYmwrKzNSVU1TOGlQdC8rMUxqZ0tNTGtMNkw0aU81R1hlV1BrSG9lUTdlemx6bjhnZnpCd21tUUt2SkdZYkdXenV2My8xemMwak9PS0VoVXAyTWcvWEdFWUtvRHdhdWFuajlCV2RLYzdaK1Rzdi9NY3FqNHZMSlh6dzV1YUY5SU5BdHdkazZMbW1mSHF2NWNjSVBrWWJVQkk2YndUVWNDczZhNTU3SU1nNkltTy9rejh4akhOVG5xQUE1UXdJUStrY2I5b2N4bGZIaS9aK2I0VG5IZWg1V3Q1VTBuMVRBZ25idjd5RVJMVHk2WjF1RmRoUGhOelRHVVhZMzVncDFhYTZHNTJjN094V0VJRHoySGNmMEFpZkhwcjBMNms4OUxzVXdXc0xQSWR1bkJOQVZoR2VBTDJyTzZjWUJabUs2aTdBSmMxeG0wT0VUQ3ZvRE5ybmJ3bTc1dHJBajd5cG00OCtobEZZMk9QOUlBVm9zTFpPQ0c3dHNIZUpscmN6WVNHaGpaRFM4ejc2L0dROXdHVGxkTGgyNDdlc2YzOFBwQS9obXZVRHUvRlNjME5nVlNoZ1NyamVUWm5FVWttS1MrTFB5NnFYT1VmTjdvOGFULzlFMGtLWDgiLCJtYWMiOiIyNDE0Y2VkY2JkZmI5NmNmMTliZTExODVmYzNkNWJlYmZlZTgyYTI1ZmRlNTI2MDZlZmU1MGRmMzBiYmI3MWIzIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:20:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik04dVBIbkIvKzZUQjE2ZXIxTjZWUmc9PSIsInZhbHVlIjoiT2pNUVZuMFhHSk8xdlEzUThremcvVURMWlBTTjAzc2R4R2k1R2tRZ1oxc2VUbnp4ditIVlVzc2hHMm5OWWdsTjlyT2VmVzBhSU95bzEyUTgrN3RTTlExSWxNbVYzQmpWMStKa2p6WEhvd08zRnMwZGcvYmN0d0FLTGtrTXF0d0dCMWg4cEVod2I1MGs4V2dRM29pN0pEVU5USm92N2FiVEtCZlVBUGtBdWx3N1c4VmoyZWhSUUgvNHBtOU9OVWkyeU9yd3YvYk9BN3ZwRGRyNGdDTmRYZzZ1SUcvakp5N0UzZFYzd0YxWGk3WWhUMkhtMlpYL1ROemxER0VmNjlKbkhGYnVVVlFzektQUHRLV2dlYnpRbU10WXRwNy9ScFY4UCtZQXlvaGxwL0QzaDNEa05adVZ3TVVYM2I5d3ZuZ2RjSUpJenVVRnA0TTJaeUx1SlcwTEQ4NGxXVVA1dVQzWWE1V1locThPZ3NHaE9PckdjbmphSDhMTW1XZU1nYnVyWVdiclNnMWh0S3ZuQ2xURmVWUzgzR2YzOHIrS1VxcEhiU28rQU5WREZxQzhwQVB3UVFCTHJ1S1JQeWg4UXA2VUpaWkJxZnJBWUZlamNWQmw2MUczYTdoMTdhU0JHd0pCM1lhNjhTcjc1L090UWNMSWRrNzAvSlBYMFZFS2t2L3kiLCJtYWMiOiI2MTI3MjE2ZTAxMTkyM2RhYmQ1Nzc0OGUyNzk0ZDA1MGEzOThmYzBlOTljOWM3ZjE2MjhmMzRhZDEyYzdiNWI1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:20:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdMUnFicUFDZGMrNHk4RDJ3SHlHK0E9PSIsInZhbHVlIjoieUFBdG5lMGF1YjVtVnRzZm91VnM1Z04zVGhneFQwL2N5Y0xGaTEyRmxKYmwrKzNSVU1TOGlQdC8rMUxqZ0tNTGtMNkw0aU81R1hlV1BrSG9lUTdlemx6bjhnZnpCd21tUUt2SkdZYkdXenV2My8xemMwak9PS0VoVXAyTWcvWEdFWUtvRHdhdWFuajlCV2RLYzdaK1Rzdi9NY3FqNHZMSlh6dzV1YUY5SU5BdHdkazZMbW1mSHF2NWNjSVBrWWJVQkk2YndUVWNDczZhNTU3SU1nNkltTy9rejh4akhOVG5xQUE1UXdJUStrY2I5b2N4bGZIaS9aK2I0VG5IZWg1V3Q1VTBuMVRBZ25idjd5RVJMVHk2WjF1RmRoUGhOelRHVVhZMzVncDFhYTZHNTJjN094V0VJRHoySGNmMEFpZkhwcjBMNms4OUxzVXdXc0xQSWR1bkJOQVZoR2VBTDJyTzZjWUJabUs2aTdBSmMxeG0wT0VUQ3ZvRE5ybmJ3bTc1dHJBajd5cG00OCtobEZZMk9QOUlBVm9zTFpPQ0c3dHNIZUpscmN6WVNHaGpaRFM4ejc2L0dROXdHVGxkTGgyNDdlc2YzOFBwQS9obXZVRHUvRlNjME5nVlNoZ1NyamVUWm5FVWttS1MrTFB5NnFYT1VmTjdvOGFULzlFMGtLWDgiLCJtYWMiOiIyNDE0Y2VkY2JkZmI5NmNmMTliZTExODVmYzNkNWJlYmZlZTgyYTI1ZmRlNTI2MDZlZmU1MGRmMzBiYmI3MWIzIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:20:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik04dVBIbkIvKzZUQjE2ZXIxTjZWUmc9PSIsInZhbHVlIjoiT2pNUVZuMFhHSk8xdlEzUThremcvVURMWlBTTjAzc2R4R2k1R2tRZ1oxc2VUbnp4ditIVlVzc2hHMm5OWWdsTjlyT2VmVzBhSU95bzEyUTgrN3RTTlExSWxNbVYzQmpWMStKa2p6WEhvd08zRnMwZGcvYmN0d0FLTGtrTXF0d0dCMWg4cEVod2I1MGs4V2dRM29pN0pEVU5USm92N2FiVEtCZlVBUGtBdWx3N1c4VmoyZWhSUUgvNHBtOU9OVWkyeU9yd3YvYk9BN3ZwRGRyNGdDTmRYZzZ1SUcvakp5N0UzZFYzd0YxWGk3WWhUMkhtMlpYL1ROemxER0VmNjlKbkhGYnVVVlFzektQUHRLV2dlYnpRbU10WXRwNy9ScFY4UCtZQXlvaGxwL0QzaDNEa05adVZ3TVVYM2I5d3ZuZ2RjSUpJenVVRnA0TTJaeUx1SlcwTEQ4NGxXVVA1dVQzWWE1V1locThPZ3NHaE9PckdjbmphSDhMTW1XZU1nYnVyWVdiclNnMWh0S3ZuQ2xURmVWUzgzR2YzOHIrS1VxcEhiU28rQU5WREZxQzhwQVB3UVFCTHJ1S1JQeWg4UXA2VUpaWkJxZnJBWUZlamNWQmw2MUczYTdoMTdhU0JHd0pCM1lhNjhTcjc1L090UWNMSWRrNzAvSlBYMFZFS2t2L3kiLCJtYWMiOiI2MTI3MjE2ZTAxMTkyM2RhYmQ1Nzc0OGUyNzk0ZDA1MGEzOThmYzBlOTljOWM3ZjE2MjhmMzRhZDEyYzdiNWI1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:20:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553970189\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"123 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}