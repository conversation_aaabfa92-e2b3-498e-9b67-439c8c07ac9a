{"__meta": {"id": "X9d629897c87a5f52487d4d3575b4e901", "datetime": "2025-07-14 18:33:53", "utime": **********.127526, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752518032.652859, "end": **********.12755, "duration": 0.47469091415405273, "duration_str": "475ms", "measures": [{"label": "Booting", "start": 1752518032.652859, "relative_start": 0, "end": **********.06651, "relative_end": **********.06651, "duration": 0.4136509895324707, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.066521, "relative_start": 0.4136619567871094, "end": **********.127553, "relative_end": 3.0994415283203125e-06, "duration": 0.06103205680847168, "duration_str": "61.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46005792, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0046500000000000005, "accumulated_duration_str": "4.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.097892, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.925}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.109452, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.925, "width_percent": 11.398}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1172462, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 70.323, "width_percent": 29.677}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1714705112 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1714705112\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1506189659 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1506189659\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-201158625 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201158625\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1902716176 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752518029710%7C48%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9UQ0N5cmQ1N0VRdmpqRlMwRDdhbEE9PSIsInZhbHVlIjoiVnM1aEZXSHhzYkRZeTRndHE0YlRRaUloSXRDT1liNWFXWGt5cldRY3VCUGkrMytiaHQ2bnNEYVUrOG9NdHJhTjE2MHk2TTlrc2xLNEVtRjRtaXc1bG1OVDBOWnFrdUpSZ2V6dzduOUFPNXZSZm9WcEhsVUtaYVBVKzlhUm5SdG42ZXU2UzNKelNWWTJrLzNldUlYQVcybmN3c0RyWng4K2pWY1FialdBMSs0RHJ3dmNWcDFEd1BuN1ZlSSt1RFJqNVhXcm1RTmRBNkJ4RW5vV2Q3OWhGWTFud3dOWDVzZEgwWWNyaXR1TlRxckJnc1IvVkZaL3FBTWVZdjRRUDI2KytGUk5yanlETmQvQk5hV2VrSXQrbDQrd25mVURMSnY4WHdaYjY3WFViNGNuVHo0RnRhanEzbTZqckk2NFpWUHE1b1YycHVwRzU3dDYwNENmOWpHcTVjUEZKREhJTmxlcnJDbk5SWFdrMWFRc0hKa0UzM0dXVk5OV0hEbVRxZEhOaXJMU0FYN1FhblpFVG5OQ1laSGxxTDV2eUNIa0JDQ29uQjBrNGF6K0FIMU93cXFiUUROTEI2K05YaWVjSGRkS25PWXBNTVJSbytxNzMvNVo3M3Q5RTFrVlo1czlmb2UyQ2VKcWNVRk9aRW1WSXBsTnBxUFdMN0xZcXBUOHVKOWMiLCJtYWMiOiJlZTVjNjU4ZWIyODljYzk4ZGE5YmQyOTUxMjAxNzBmZmJhMDg1MGFjOWJkZDc0NjA5MmJmY2Q1ZjcyMDQxMTI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlQ0ZzE0NldpSXpFaVJ1MzRDcXltRUE9PSIsInZhbHVlIjoiOTZoY2NaMk8zR0dVM2NlOUtjV29JZDhMYWtFbEl0Sk40Y21LZ3c5UVR2Z3poZ1hQZmRpczZXTmp5UVF4TmZkMGp1Z0ptOS90bnhvNXNIajNab1Z1OVQxOUJwTHgyRzVBZDJGZnJ3VnZCbmFKbUd1d3hoazdoRlF2UkpWZUZTVW1WMGdJdmZaOGhSU0wxaWpHV1h3SXdlZndEUCtscXl3bGlmQ09HY2NBYVp6OGErWklDeC9DcEVHSFN3RUZMMVNKeGF0dnFGREU2TTRwT2U0ZDVESUFMNVNPenROR2R4bTdHNDBCV0d0eUJ4TzM4Y3lLOE5NSG1KbnFNdGh3ODQ3MmpQV1I1eFRQK0s5eHJLSHZJaVhZc3NnTDhrY0hpYVFLMVJNWVJGUi9yNWV0cDJRMjhob1RpNEdDeWNVZ2hXdFJvUk9VdFdqZGNhNzg2b01VTTQ2Rk9KcGJ2QUdSVHAyb2duVGRPV0tJMWc1UkRtNlR1blRQTlJYWUlJL2dadTE2eXJEWmZVRkpSTTVlMXZoWTMvUzAxcmJTNFFESU5KNjZsV1lRd1IzNVhqWTd4R1N4cll0SXdGUzdaWkpJQk50ZlhoK2VPMlg0ejZLNHMwMzUvWGRldld6SEdSczQ2Z3l1aFp6K2Y2aW1zaTJuM2Y5NkNJTmFVMjZhR1B1RVFXa0giLCJtYWMiOiI3OGZmYTA3MjEwOGI2ZTAzYzI0OTU3ZmQzN2E5NzQ2Y2EzZWM2YWQ4NGZmZDYyMGIzMzU1ZWI2ZDRjN2IyYzFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902716176\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1984484022 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984484022\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-503570145 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:33:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxOdC8xR2tqWUpUamFnYlE0MnlDNEE9PSIsInZhbHVlIjoiRlhFTlNHSkNyVEZkRWJQaDI2eGIxaWNaVFpJRFYvSGo5aHNpYmd4MGxpcksvQ003N2NGd3lJRmFyVCtBNFlPK3Axc1RqTitocWRGcGo5RXZsUDI3NFgzSkUyUzVaQVNMbmJRR24vOUVkRHphWEsxN2hOclEvSVR3S0ZhbXJOQnZCSnRoRmNWSjB4WDlwSkJ0RUthbHFhekpLTEJ0ODg2UzBqNEkxUS80MmZsWnFiVHp5WDBZSXhmYmNOUmRMd1AzMGw1OWRVTlpFWVFueEJsUlhhT3VRRFVxYTM0VCtYQ2hXdEJtQnJGWUdUUnl6MUpzUzVaVE5yRXlkdWlkMS9aQ3lJSGRtcXhMblZCdURuR1pRTjN2S0FYaUMvd2J4c3YyV3QyWnZYcFBQeFV6L0dQQmFiS3diM2FkRndPc1JWaU95Y3JPbk5jclVmdEY5VytSUHlrdzArMGQ2N1JsdzNtL0lVdWhVNW9EREtObERQRDVEbWJ6dUNVM1cxVTNnaVNEVmY5QkIvNHJGZDZsSnhuNGFFNFMyUGNNTnF3U1d0YkpzS3Vwd2hMb3RIcW1jMGRnQ256NjdQeU9lWUtYZTBGTnRPUmZET082SmM5RjJlVi9jMUdkL205WVorN2N1ek1UbEFieUFSaFFnM0hXMUpBTGNMS2diQXJYYlU3MUlJaVoiLCJtYWMiOiIyMDhkNTEyMWM3YzU5ZDcwNDIwZDdmMzgzM2JkYWMwNDNjMThhYWI1MzQxMmE1NDk0ZjU0NmRhZDM5Mzk4NDcyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:33:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxRVUhwdHRSUkFiT3FlT1R1NkI5TWc9PSIsInZhbHVlIjoiZ3F2S25vVHVWamFwVHRhYTUrRUlPQ3ZTM01vUmp1Yi9qai9HMjNQVGM4cnlKOE1KYzRtWllkTEljNFJHMmtEZDRZbXhzUnU5VG9LS0lEVVAyNWxyejdxSGc0cnlMaHpUUnZ6OGg0U0J6REk5cDMrYzVsaWxhZ2ovWHhOWU8xd2lXeTJTY09FVkFyUjdvTkVhTHUrQWJPOWZWYlVybXUxdVpsN2J0UUtKRmQ5MXViNjlXWmFqTWZzdHFGNDBiWTB1TU5MRU1JU3Flc2ZDWUJ6RVR3LzN3NytNOGJTRHFtdzBqTDdyc040VEtrdzBtdnk2L2dJVDJncEEwbUxSZjJKRzVSVVYrT2JVUTZRREliRDVRc0I1dnRXQkNzWGdtUGJCV2poZm5jQjlONmgzUDFNRVk1Q01TN1I2WXJnb1cxdkwzOEJZcFVja05BRldybEs5OXFoektodkxKeHpRZy84NnYvQWwzSEx5SDFrMU4xaWxCWWFqZTlFejFzUmphV1FjTC9VVVJQY3ZHRTJyaGtvQ1U1TEcvdzUwOXEvZ09pdDNGK0JIQlM1cWhrSk0vVzJZbzR0MjB1cENNVzA1bTdEN1J1eTZvZU1CYXRWdFIrQW82aXF2ZW9qajU3SDBKVUxid3Zzdm1mOE5WTFoxNG1oOXVGWkRlQ2xxTnlWR1ZNSGMiLCJtYWMiOiJmZDNlYjA1M2FiYTkyN2Q5MTIyMzZhZmRkMDA5YzlkZmNjZGE4YTUyMDE2YmI0NjQ5NWJiNzM1OGZjM2QyOTk3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:33:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxOdC8xR2tqWUpUamFnYlE0MnlDNEE9PSIsInZhbHVlIjoiRlhFTlNHSkNyVEZkRWJQaDI2eGIxaWNaVFpJRFYvSGo5aHNpYmd4MGxpcksvQ003N2NGd3lJRmFyVCtBNFlPK3Axc1RqTitocWRGcGo5RXZsUDI3NFgzSkUyUzVaQVNMbmJRR24vOUVkRHphWEsxN2hOclEvSVR3S0ZhbXJOQnZCSnRoRmNWSjB4WDlwSkJ0RUthbHFhekpLTEJ0ODg2UzBqNEkxUS80MmZsWnFiVHp5WDBZSXhmYmNOUmRMd1AzMGw1OWRVTlpFWVFueEJsUlhhT3VRRFVxYTM0VCtYQ2hXdEJtQnJGWUdUUnl6MUpzUzVaVE5yRXlkdWlkMS9aQ3lJSGRtcXhMblZCdURuR1pRTjN2S0FYaUMvd2J4c3YyV3QyWnZYcFBQeFV6L0dQQmFiS3diM2FkRndPc1JWaU95Y3JPbk5jclVmdEY5VytSUHlrdzArMGQ2N1JsdzNtL0lVdWhVNW9EREtObERQRDVEbWJ6dUNVM1cxVTNnaVNEVmY5QkIvNHJGZDZsSnhuNGFFNFMyUGNNTnF3U1d0YkpzS3Vwd2hMb3RIcW1jMGRnQ256NjdQeU9lWUtYZTBGTnRPUmZET082SmM5RjJlVi9jMUdkL205WVorN2N1ek1UbEFieUFSaFFnM0hXMUpBTGNMS2diQXJYYlU3MUlJaVoiLCJtYWMiOiIyMDhkNTEyMWM3YzU5ZDcwNDIwZDdmMzgzM2JkYWMwNDNjMThhYWI1MzQxMmE1NDk0ZjU0NmRhZDM5Mzk4NDcyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:33:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxRVUhwdHRSUkFiT3FlT1R1NkI5TWc9PSIsInZhbHVlIjoiZ3F2S25vVHVWamFwVHRhYTUrRUlPQ3ZTM01vUmp1Yi9qai9HMjNQVGM4cnlKOE1KYzRtWllkTEljNFJHMmtEZDRZbXhzUnU5VG9LS0lEVVAyNWxyejdxSGc0cnlMaHpUUnZ6OGg0U0J6REk5cDMrYzVsaWxhZ2ovWHhOWU8xd2lXeTJTY09FVkFyUjdvTkVhTHUrQWJPOWZWYlVybXUxdVpsN2J0UUtKRmQ5MXViNjlXWmFqTWZzdHFGNDBiWTB1TU5MRU1JU3Flc2ZDWUJ6RVR3LzN3NytNOGJTRHFtdzBqTDdyc040VEtrdzBtdnk2L2dJVDJncEEwbUxSZjJKRzVSVVYrT2JVUTZRREliRDVRc0I1dnRXQkNzWGdtUGJCV2poZm5jQjlONmgzUDFNRVk1Q01TN1I2WXJnb1cxdkwzOEJZcFVja05BRldybEs5OXFoektodkxKeHpRZy84NnYvQWwzSEx5SDFrMU4xaWxCWWFqZTlFejFzUmphV1FjTC9VVVJQY3ZHRTJyaGtvQ1U1TEcvdzUwOXEvZ09pdDNGK0JIQlM1cWhrSk0vVzJZbzR0MjB1cENNVzA1bTdEN1J1eTZvZU1CYXRWdFIrQW82aXF2ZW9qajU3SDBKVUxid3Zzdm1mOE5WTFoxNG1oOXVGWkRlQ2xxTnlWR1ZNSGMiLCJtYWMiOiJmZDNlYjA1M2FiYTkyN2Q5MTIyMzZhZmRkMDA5YzlkZmNjZGE4YTUyMDE2YmI0NjQ5NWJiNzM1OGZjM2QyOTk3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:33:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503570145\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1609004462 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609004462\", {\"maxDepth\":0})</script>\n"}}