{"__meta": {"id": "Xca9bc07e0585265d3fe29be5eee961aa", "datetime": "2025-07-21 01:36:01", "utime": **********.70181, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.21168, "end": **********.701826, "duration": 0.4901461601257324, "duration_str": "490ms", "measures": [{"label": "Booting", "start": **********.21168, "relative_start": 0, "end": **********.632766, "relative_end": **********.632766, "duration": 0.42108607292175293, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.632774, "relative_start": 0.4210941791534424, "end": **********.701829, "relative_end": 2.86102294921875e-06, "duration": 0.06905484199523926, "duration_str": "69.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013109999999999998, "accumulated_duration_str": "13.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.664656, "duration": 0.012199999999999999, "duration_str": "12.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.059}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.686242, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.059, "width_percent": 3.661}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.693383, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.72, "width_percent": 3.28}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=&customer_id=&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-934989396 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-934989396\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-798430191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-798430191\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1152319098 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152319098\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1800533072 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=&amp;creator_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061744622%7C19%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcwYWFkaUk5UE9tVGlTR2xSaXg3RGc9PSIsInZhbHVlIjoiZ2YxSHZnMmRjODlNYUo5R2h0QnF3Z0ZkZDhjSkhneU5lQVkyTVBPU2tWQ1drYUYzd2lDN2g4ZEZwUXMwOXNoNklZc0I2eHdESW5ha0FPN1ZHSzUwYytqSW5kMHlJQStRaThFNlBwZ2FWaFhXNW9hS2lMbWxwN0lVdDZFUnJ6bHJtNVNNaThmdFdBWkd2bjBMMUtJbkdjMjdMc1dXQTdpSjNXMXlRZk5mY00rY2RNbkRMc1VFVFdlNkJiRSs2cWplMjVRZG5jTWN4Qld0aEgrQ3BlREtsdnNNbmdYZC9yOGk5VFF1M0hiNzZ6NDMwRm1hTkFOTlVRR1RJcGdkdUk0akZxc0JmRXdFeS9JSnpjcEVHTFFydzVpeURtaTZvay9YZy9PZVBDNmZlY0VKS1Vuelk5a2RjV1lhckN3Tml5R2EydU9Na1pWaU1kVlFRWEZqeVZBc01yaHpBVnlXc2VJNUhodkw1WExCb3psd1NzYVowWlhPU0lyWU55WEREWkZzUnM3WlJsa2pXR0ZBMFBrdHFtUkV2Um5CdFViYUxpbEEwVCtXcEVOTlhTTXNFTWE2Q0o0ZVFzNDF2cEZPZytwTUFZVGo0cGtqTElEUGxQQzAwMG1qQWwrbWF6WGQ4YzZ0Y3BUdDI0NlB1dzB4YkxuVHVDMDBGWVZPTGlva1BScisiLCJtYWMiOiIyMmM2YWZkMWVkYTYwYjliZTEwNmYxYjZkY2QxNjNlMTkzZmNhNDI2YzU0ZDdjMzg5ZmM4YjE0MDhlYTBjMGRkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkNvejRuVXo1RlM1YS9UY0g0Ri9kOHc9PSIsInZhbHVlIjoiVkNXZUIzaTN0NDJVU2w0alg5WXpEOHJ2Q0NKRGM0b2JYaEg1VmJoWithZHZmSHNPbnltZzVUY0w3REdNaXpaOTBhUCtBbDMrczhUM2xPNWlHazN0NjdYZWZvM3BEd1hjVkxwVUVhMDUyQTVGT3hRcTZaY0dhdnYrMit5aEdRaXJLdWo0WlZwTGJqUE1oN1hWdGxQRG0vaTdDdU1VK1NJc3NkeVpiY3FNREl0aG5MRnVzcWNuTG9yNTBIMFJzMlMvSEE3NFlvdW03aHREOXd0S0lVdGlOU1o3ZWM0MTZETXQ1YjZHTUdobCs5bFdmSXpscnVYaE5od1NWbVNYSGVOcjNTd3Bhbk9hRGkvSjhIMC82eXhBK2Y5eUROZzhpcEtJajY2QTZhWkx2bWpMMnpvS0NLUk5LaDdha0hibVgyTlZ2SjF3bU5ka3JYakcwdlNUNm92SXp3N1Q2RWNkK3BXbFczTVVoWUxtek12K1hTcncxc0tydllORi9zQkNkN3hkUmFBMmc2dUxmNkhZQjBVbW5KMXNwV2FyZnJnMnhkZUxDWjVWalkwN1JQdWJTTFVxNHovOVg5YVBkbTFJeUNDYjFoSU1zZXpxTDllTnRjTzNaeUMwOWU5S1RlcXVzU2tOYkhpYlh3RlQ4STM1U0w5NWRUeW1KeVlFTkRHRU5jR3AiLCJtYWMiOiIzMWUwZmI0N2FjYmNmNjgwNzE4ODMxZjg4MTNhNTM4ZjM3OTkwZjhiZWNkMmMwYTZlNjNlNTI3YTk5ZDUxNDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800533072\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-117876471 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117876471\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-475299940 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:36:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRORU1QNGRWMHFmUDRRNWIyRmlkYUE9PSIsInZhbHVlIjoiTy94cFhpYU0vaGtaOEZMNGQ3T3FhdmlhbUN3dDJtRUxqNmtXdmVSTVpZdjVRNnpadGtkcDZrWERPb1ZKZUxaamwwRU56NGE3TkdZRVlKYTRhVkRoclU0NWhRWEdHS3B1d0JvMk1XRUk3cFBjS2VVditScW94Z0FMN3lYSXcxenNLWWZ3VGY2aUlsMGN0WDVsSnNqZGR4TlJrdVh4UGJDQ2tCSTdiZDg0dngyUC8wNnZZSzU4TjQvRFROdzg4a2RiT2k0eFM1bnh6V2tRWVdqcnVjTHUxNmVoSVFRR0tKL1JsdlUrNVF2ckZVcFVpQ05vNjEySW92SnNTdHp5dnBBZ1k1WUUwVytnL3FlZTZhNXhRRlpTZnpjakxyckhWYVJPQmR0ZEJUQWNTSUo1Wm1ERFpjOGJOZFE5Y0NNUXdwVVI4WUFwdDVpbm9wb1JsdmtkZVozL0JjckgrbzkvbTkyZzdkZWo1NTFZQ3BHbkV6ZG1YMU1ScXdJRjN5THpOL2gwVkdYbkJiajhvL1FxdHROWkpSL2RmVTdzVjRkVHU1eEV4TU1rU0JuMHFkU21SUzIxTDAzdDBuSGdkeVBCazRxT3pUazNIekdLSHB5bVBTeTRHRk5YT0c4ZGhVaFZGVmdITTl5MU5DOEcwWk0rRnhFQmRKRHFXRllGNzhSRThHNG0iLCJtYWMiOiI2ZTBiYjA2ODNhYWJlNzY1YjIzN2UzNTNjNmJjMDRhZGMxODEzMmJiNzQyOGI3YzY0ODEyZTY5YzY4ZmFmYWQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:36:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZ2RUNEYktpQ29paGxUODc2ZUpNWGc9PSIsInZhbHVlIjoieHR0djl5NXdZdXFFZHhPMjhvZmJieVU2Z2w4RkkvM2FSU2NhOU9BalFtS2ZpaFhQcHByZ3FxSUs4bnFZRmxDQURhc2ViaHlNVm5CWE9xT2xxUmprOVpDQXBKdzdVdVZVaEpXTjlRQkxhS3UxZFhIMlBSTDJFSHpiNXdWT21YL2pRMjZNRHVUSk9WU1V2WG4rU0hsbWtiWVV3SnRUdWR1QWFUNHVRdXdZNjFoZ3Q1d1cyc0FaOE5yK3gyNVBHSlh4cWdUYTZjK3dyVnpDa0s1K2tBaFQzSUR3OCs1YTlTSGdxUEhpQStIQ3RVanlObGRFcGdsUWxTWFhSM2tuanhuVE9KWFkvRFI5NXB4Z3A2R1VJckVXQmM0VW1Sc3VTQ0hlUTY2bHVWQU9rbzdnVmozeUd4ek9wMi82K3BVWkpyL3FOVC9JaUp4ZllMaDRZR3huQ2wxRGhQQU0veGdtVUgvS05ZMnp1dzJpRVlIYi9BU3N5Q0Z0Ym5xdVU3ckFPM01EUC81bjRVMlFOVjgwNkNpcHdJdUFpV3U1cnpaU05aVXFWeUVBbm1hbzF3RDJTNHVwT1kwcnJ0VCtQZ0wwalQ1ZmhYU1lUcUNxUXV6cWpqeVJscm4xWkk3aFhEeTExdzBJQ3dqSEk4QTAyQTNaaENab3puNExwU09QamN2TUVzK2UiLCJtYWMiOiIzZmZhNTE1NWZmN2VkZjEyYzIwNjQ0MmYzN2I0NTg4MmVkZDYzZjM4ZGM4YzBlODViMzlhZDM4YjNlMzQ1ODdmIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:36:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRORU1QNGRWMHFmUDRRNWIyRmlkYUE9PSIsInZhbHVlIjoiTy94cFhpYU0vaGtaOEZMNGQ3T3FhdmlhbUN3dDJtRUxqNmtXdmVSTVpZdjVRNnpadGtkcDZrWERPb1ZKZUxaamwwRU56NGE3TkdZRVlKYTRhVkRoclU0NWhRWEdHS3B1d0JvMk1XRUk3cFBjS2VVditScW94Z0FMN3lYSXcxenNLWWZ3VGY2aUlsMGN0WDVsSnNqZGR4TlJrdVh4UGJDQ2tCSTdiZDg0dngyUC8wNnZZSzU4TjQvRFROdzg4a2RiT2k0eFM1bnh6V2tRWVdqcnVjTHUxNmVoSVFRR0tKL1JsdlUrNVF2ckZVcFVpQ05vNjEySW92SnNTdHp5dnBBZ1k1WUUwVytnL3FlZTZhNXhRRlpTZnpjakxyckhWYVJPQmR0ZEJUQWNTSUo1Wm1ERFpjOGJOZFE5Y0NNUXdwVVI4WUFwdDVpbm9wb1JsdmtkZVozL0JjckgrbzkvbTkyZzdkZWo1NTFZQ3BHbkV6ZG1YMU1ScXdJRjN5THpOL2gwVkdYbkJiajhvL1FxdHROWkpSL2RmVTdzVjRkVHU1eEV4TU1rU0JuMHFkU21SUzIxTDAzdDBuSGdkeVBCazRxT3pUazNIekdLSHB5bVBTeTRHRk5YT0c4ZGhVaFZGVmdITTl5MU5DOEcwWk0rRnhFQmRKRHFXRllGNzhSRThHNG0iLCJtYWMiOiI2ZTBiYjA2ODNhYWJlNzY1YjIzN2UzNTNjNmJjMDRhZGMxODEzMmJiNzQyOGI3YzY0ODEyZTY5YzY4ZmFmYWQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:36:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZ2RUNEYktpQ29paGxUODc2ZUpNWGc9PSIsInZhbHVlIjoieHR0djl5NXdZdXFFZHhPMjhvZmJieVU2Z2w4RkkvM2FSU2NhOU9BalFtS2ZpaFhQcHByZ3FxSUs4bnFZRmxDQURhc2ViaHlNVm5CWE9xT2xxUmprOVpDQXBKdzdVdVZVaEpXTjlRQkxhS3UxZFhIMlBSTDJFSHpiNXdWT21YL2pRMjZNRHVUSk9WU1V2WG4rU0hsbWtiWVV3SnRUdWR1QWFUNHVRdXdZNjFoZ3Q1d1cyc0FaOE5yK3gyNVBHSlh4cWdUYTZjK3dyVnpDa0s1K2tBaFQzSUR3OCs1YTlTSGdxUEhpQStIQ3RVanlObGRFcGdsUWxTWFhSM2tuanhuVE9KWFkvRFI5NXB4Z3A2R1VJckVXQmM0VW1Sc3VTQ0hlUTY2bHVWQU9rbzdnVmozeUd4ek9wMi82K3BVWkpyL3FOVC9JaUp4ZllMaDRZR3huQ2wxRGhQQU0veGdtVUgvS05ZMnp1dzJpRVlIYi9BU3N5Q0Z0Ym5xdVU3ckFPM01EUC81bjRVMlFOVjgwNkNpcHdJdUFpV3U1cnpaU05aVXFWeUVBbm1hbzF3RDJTNHVwT1kwcnJ0VCtQZ0wwalQ1ZmhYU1lUcUNxUXV6cWpqeVJscm4xWkk3aFhEeTExdzBJQ3dqSEk4QTAyQTNaaENab3puNExwU09QamN2TUVzK2UiLCJtYWMiOiIzZmZhNTE1NWZmN2VkZjEyYzIwNjQ0MmYzN2I0NTg4MmVkZDYzZjM4ZGM4YzBlODViMzlhZDM4YjNlMzQ1ODdmIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:36:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475299940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1973312218 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"148 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=&amp;customer_id=&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973312218\", {\"maxDepth\":0})</script>\n"}}