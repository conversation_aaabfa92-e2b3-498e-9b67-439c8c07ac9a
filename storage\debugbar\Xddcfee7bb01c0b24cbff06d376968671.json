{"__meta": {"id": "Xddcfee7bb01c0b24cbff06d376968671", "datetime": "2025-07-14 17:58:33", "utime": **********.865778, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.431802, "end": **********.865799, "duration": 0.43399691581726074, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.431802, "relative_start": 0, "end": **********.795336, "relative_end": **********.795336, "duration": 0.36353397369384766, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.795351, "relative_start": 0.36354899406433105, "end": **********.865801, "relative_end": 2.1457672119140625e-06, "duration": 0.0704500675201416, "duration_str": "70.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44724336, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015340000000000001, "accumulated_duration_str": "15.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.827724, "duration": 0.0148, "duration_str": "14.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.48}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.845795, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 96.48, "width_percent": 3.52}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1222433640 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1222433640\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1326076866 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515911303%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk1SU9CbFAxVWlGZnZ4SU1aUU1kMFE9PSIsInZhbHVlIjoiMy9iMVNuUU5CemYvYjJ3bmEvRGxLQlZvTENMUHo4V3FNODM0N3Rxc2NPbDJwY3hBWldORys5eVpQMmpZSU4xaHNLaThUdWVvNlJ0T1Q5bXBONERCSmh1SHVxc1IvdVRVcmMrRVk4RGd1ZktmQ3c2WGpzdktlL04yK2lmc1dNaG8xdWRSeGYrc3FyMHU3enBnSlByY0lpYndnZHpNOXpYeG4yRE5SUmYxaGVHcElrTVg2VDY4RytDQlQ4Slp4MEpOMDExZno5NmQ4aG9KS1YxdzhkTVlac2ZhMndBSjIxUGhEaHZPZkFEV3R1UkVoZHNsTlRrOEhleE5xYmliN0FMMUtZQWJFTHR4OEZuWUtzTU5MYWtZQURTY2hyOXFKK3BWOGxDVHpLMVBlb3lZSkZGZFZXUFRZcnBhL0tXZlpkdzI5VXVZOU5WeWRlMWJpbFNGcFA4aStkZ1d0NE5Nd2Vheml5NFdFWWw0N1pYV0tsZUNObU1HMFVtS1d3L1VRT0JodjFNSGlia1Z6L2Z0UWVNSFVPb0ViakxWUFhoNzJLMTgwRldISDU5VHpkYndlSzRYSzFsdzQrcUkxRCs2L3dpMGh3cWVCWEZLRitzeVZUcnRIMVN2eUMyQWZ4WExkYnhwR1ErQU5QWktlZkUwUlB2THJ5NTVjaDdpVHFURlB1ZjQiLCJtYWMiOiJjNmVmNzE3ZGZjYzJiN2ZjMDA1OTJmZGY4NjI3ZTg2ODg2MDg0YTJhZWE5MWU2MTQ2Y2NiOTA1YzBjNTcwYmU2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9IWlZDVldhKy9FZUlRNm13bGxkblE9PSIsInZhbHVlIjoiYXNIQnRJck85ZFhPc084SUV6UW9XR2NyRmNnZDJQYlFUMEFLRlZ2R1BrWGFobGQvV3RtcXowUWZyRGNtMXErU09ZUmQrS0h3c0czNkNWc0JhTEh3OUd3ekkwYzV5cG9mQWJsVXlIcHMwSUc5aVJpWGl3SWtOZXBWQ0ttUlZXdnJmNWl0Vm54amMySlpXVVhxOHQyN1NrOEsyc1VkYVhoOU1PMGFLblVqcUpRd2N1YkgzWDRwWkQyUWtyUGxhZit6elFZeGw1Sk5OUVVLVUVMS0E5S0NZTER0UzB3Q1hJUjg2ay95VStkdlFERkwwSCt4SE1FQWpBWVpFa0RGU0d1QVZ5bXY0MWZrTGVVOHJ3VmN5R0cyKzNJcndBcTRhWmtiVjAxZzZWSTc3MXQ0Nk5ISC9qZVFhNjN1RHBtZnVQVDRkTlh3RSszZU1oY2F0Sk9Wb2Ixa0l2M2JDaHhSWXhXYWJLczhDQWhxdGRLM3Bzb2RyZ1ZHWi9OMUdrUGNWMEhRZzBqdUVQK2xoamQvZmV2QnZxYW9UZFp6RnJmMHh4MUF4L01LVEpub0ZqTGJ5L1RUdndRUEZhQmMzSUo0ejFvRHJ2Yk91blFIVk5teWRwaW9YdHpUTGRjMStMMkJndDhFalNIVmVrbW4reWxrRjhqeXU4dTR5VGhkSGszWm9RZHYiLCJtYWMiOiJiNmZjOGYzZGUxNTU4ODYxYjA2ODE5YmFiMGQyN2YyMjA5NDhkMGYzOTI1MWM0ZGIwOTQxZGNmZGNiNjBjMTUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326076866\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1799878697 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799878697\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1398168355 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlN2STFYK1BieVNTcTdCSGpRaXZ5Tmc9PSIsInZhbHVlIjoiWUdGNzhZTzdreDREeGx2Z3RQdi8yM0JlQm9pWFc0MXhaMGdVR2VYdWVnQy8zQTVSTVc0N2ZVblppM2pleG5YOGVUL3VyRU81WG5zVWFEd1Vva2U4NEtyMVZIdG00WEFJTXB2SGNRZm9YZXlIdWRRQUNGK0pLV1plKzgveUZFdUdjckVNRW9semNubXhENzQzWFZzaS9LdUZodit4SUwvZ0phaFpmT1ExOHl4Rkl2YkhXT2dXQ2pxYmhpRFYyS2paWnorQktIU1hYVWFtM3N0MWIvWkU0RXRoYjJzLzU3S3c2d1ZJb1JSQWtNSnFwWGhlV0dIOGxEc3FGaE84dXh4bmE3cG0waWNPekhmbW1ML2t5NXQxTVF0aEZldEo3ZTFGYTBCOEphMEV2bmFpTjdpVWdrQ0cycVloN2tsWCsvY25sNW5yaDNnWnd0SmR4dHNiY2FISlpjWCtOVG5yK0RkTEZlaEpkR0F0NWl2c1BFUk1yTzN4U0lEazFLeWE1eVZUUDBhQlltclBKUUlOZEhZVHdjcExHRUFCZ3A4S1dLbk9jVFhaVzVXNzdhWHpmSkJCMms4WmZIbXJMTHI3c3ZlOWpmM2lmWmtkZ3JjejRseHhOdzBnRWYwdlZiWTNzSXl1aU9WTEJ3MngyL0d3YWduMy8vaytwMTV5Sk55bW9uZ2kiLCJtYWMiOiI2NWRmODI1YmFjOGEwZTFkZmU3NmZmY2ZkMTczZjY2MjIxZjkwMWM5OTBmOTUxYWI4MjZiMDkwMjExMTFmNGY3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IllIY3NYNjI0dzRQa1ZKM1RxRkd5cHc9PSIsInZhbHVlIjoiaXJyS3pqc3NPeGF0WU1FeERmdHR3L1dnTGFLREpSVzl5bDljV2FnaVFuRGx0WkhZZ1dmR3BoUDVNWGU5VzlCYVBsTDEwNGtoYXZ5QnhENVgwa3BMVUtvdzV0QWhQbzBiZGg4aldKak1zLzQ0bng2ZC9yRCtlRnVSMWxZSTlONXdJYlhpZkw4ekpCK2NnQlNYYm1GUkxDblhhaExvSWNBejl3NFI0Z2Z0Q2NLdGd4d1ZRb240aE1CWnBuMEN2VXZ1QmYyeStWZDE5dDJ5WFltUlpWSEFnbE1LbU1LKzk2T0E0UzZQRnN2WU5vMHBFYk0zaldFZ2Z5Uit0VDdCSGk3dkJsVzlqMzNWeGcrOFBUc3U3YWNnNTQvNElOaGFPbGJNSkMraGJKWFpCaDlWNXNoeFlwOWN0dk5Nc0tKWVNzdnV1aEJyaGtVK1grbURrRmZ4aUl0VjBMZHJRbmNWdXF3SFlScS9EV01KUHZUZm5zeXcvaExnbVd3UC9FeFlzMWhIQ2M3MzF6TFpsY1J4RFkvZGo2OUQrcUNvTW0xMy9Kdk5McmFMOSsyYlcwdVYyWEFlRzlmTlpIWlU2UHRPQVJMeUZ5d1d1VDhkQ2hSQ1BLRXZnMFd0Q0gyM0Jtc3JiOVBxZTYrSU9vMks1WldSU1lmQWIzZW0xTmVCZVFXbEt2TTkiLCJtYWMiOiI4MmY2YTgxMmRlZDFjMGYyZGZiZTM2N2UwZDI0ZGM3Mjc4OThhZGY2ZGFiZGFlNDg0MzdmNjNiODMwMzIzYTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlN2STFYK1BieVNTcTdCSGpRaXZ5Tmc9PSIsInZhbHVlIjoiWUdGNzhZTzdreDREeGx2Z3RQdi8yM0JlQm9pWFc0MXhaMGdVR2VYdWVnQy8zQTVSTVc0N2ZVblppM2pleG5YOGVUL3VyRU81WG5zVWFEd1Vva2U4NEtyMVZIdG00WEFJTXB2SGNRZm9YZXlIdWRRQUNGK0pLV1plKzgveUZFdUdjckVNRW9semNubXhENzQzWFZzaS9LdUZodit4SUwvZ0phaFpmT1ExOHl4Rkl2YkhXT2dXQ2pxYmhpRFYyS2paWnorQktIU1hYVWFtM3N0MWIvWkU0RXRoYjJzLzU3S3c2d1ZJb1JSQWtNSnFwWGhlV0dIOGxEc3FGaE84dXh4bmE3cG0waWNPekhmbW1ML2t5NXQxTVF0aEZldEo3ZTFGYTBCOEphMEV2bmFpTjdpVWdrQ0cycVloN2tsWCsvY25sNW5yaDNnWnd0SmR4dHNiY2FISlpjWCtOVG5yK0RkTEZlaEpkR0F0NWl2c1BFUk1yTzN4U0lEazFLeWE1eVZUUDBhQlltclBKUUlOZEhZVHdjcExHRUFCZ3A4S1dLbk9jVFhaVzVXNzdhWHpmSkJCMms4WmZIbXJMTHI3c3ZlOWpmM2lmWmtkZ3JjejRseHhOdzBnRWYwdlZiWTNzSXl1aU9WTEJ3MngyL0d3YWduMy8vaytwMTV5Sk55bW9uZ2kiLCJtYWMiOiI2NWRmODI1YmFjOGEwZTFkZmU3NmZmY2ZkMTczZjY2MjIxZjkwMWM5OTBmOTUxYWI4MjZiMDkwMjExMTFmNGY3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IllIY3NYNjI0dzRQa1ZKM1RxRkd5cHc9PSIsInZhbHVlIjoiaXJyS3pqc3NPeGF0WU1FeERmdHR3L1dnTGFLREpSVzl5bDljV2FnaVFuRGx0WkhZZ1dmR3BoUDVNWGU5VzlCYVBsTDEwNGtoYXZ5QnhENVgwa3BMVUtvdzV0QWhQbzBiZGg4aldKak1zLzQ0bng2ZC9yRCtlRnVSMWxZSTlONXdJYlhpZkw4ekpCK2NnQlNYYm1GUkxDblhhaExvSWNBejl3NFI0Z2Z0Q2NLdGd4d1ZRb240aE1CWnBuMEN2VXZ1QmYyeStWZDE5dDJ5WFltUlpWSEFnbE1LbU1LKzk2T0E0UzZQRnN2WU5vMHBFYk0zaldFZ2Z5Uit0VDdCSGk3dkJsVzlqMzNWeGcrOFBUc3U3YWNnNTQvNElOaGFPbGJNSkMraGJKWFpCaDlWNXNoeFlwOWN0dk5Nc0tKWVNzdnV1aEJyaGtVK1grbURrRmZ4aUl0VjBMZHJRbmNWdXF3SFlScS9EV01KUHZUZm5zeXcvaExnbVd3UC9FeFlzMWhIQ2M3MzF6TFpsY1J4RFkvZGo2OUQrcUNvTW0xMy9Kdk5McmFMOSsyYlcwdVYyWEFlRzlmTlpIWlU2UHRPQVJMeUZ5d1d1VDhkQ2hSQ1BLRXZnMFd0Q0gyM0Jtc3JiOVBxZTYrSU9vMks1WldSU1lmQWIzZW0xTmVCZVFXbEt2TTkiLCJtYWMiOiI4MmY2YTgxMmRlZDFjMGYyZGZiZTM2N2UwZDI0ZGM3Mjc4OThhZGY2ZGFiZGFlNDg0MzdmNjNiODMwMzIzYTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398168355\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}