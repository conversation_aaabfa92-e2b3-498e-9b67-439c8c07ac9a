{"__meta": {"id": "X824fcb93ddee8660c76b4cf44995b510", "datetime": "2025-07-23 18:22:59", "utime": **********.899741, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.480744, "end": **********.899761, "duration": 0.41901707649230957, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.480744, "relative_start": 0, "end": **********.843619, "relative_end": **********.843619, "duration": 0.36287522315979004, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.843628, "relative_start": 0.3628840446472168, "end": **********.899763, "relative_end": 2.1457672119140625e-06, "duration": 0.05613517761230469, "duration_str": "56.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46009712, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030299999999999997, "accumulated_duration_str": "3.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8728728, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.957}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.88468, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.957, "width_percent": 19.472}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8914711, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.429, "width_percent": 9.571}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1696430272 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1696430272\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-554478325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-554478325\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1222149608 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222149608\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1042532808 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294975464%7C5%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAyYW9TOG05ZEw0MTJsNGo4NVFNNGc9PSIsInZhbHVlIjoicWR5Z2xsbXR6RHljV3c1Q0dYbmNsQXVRSnErMWNFSXJSOThlWVBWVUdURWx1WHAyTkJTUkNrOHgxZGFuVFNzMk1kYUpKeWhCdFRHUzVaSncyMkgvQTc1UzJTZ2U5aTdKTmlvd2JFMzRubnBxbFhXdDFnL2RLa0U1ZlArbUcxalpoOXAyNGhjNUJhR2hmdDJubkdDM3NQemNIS1ZLb25CcnNwUnQrOWdlc3RPdkY3R3ZTeFRsZk5NNGVITHhrREErN0NCWktwUnlQeEhtNnZuUzRpN1BhSld6TzVNbEtOcmRYV3lnWTJCY0RrOTFhUGNvS21jOUVaWTlscUIrUzFhUjl3c0llenN6UlJwWHpCdi9ackxQazc4SUJieFpHVVp6TEJLOTN0R3phYS9yTzRLODg2NlZvYm03bHRrSmRYeTZBeU9VczZ4NDVSanRYelBKcjNJaTZtNU9jUWhQdXRRSnFGUjIrYkhGQXIwU3lyTXZDWk9lYjlSZU04NUFqZlh1dS9ScDVsaWNrVDB2Y0E3V1ZTN0tpWkQ0OFFTYUdBVjAyU29rU2hOTVRVZi9sOXYva0M5WTVZY0RLUEpJajhxeXRleXcvYjRNZTVRSmRoLzVwL29xNW9mL2FFUUU5MnMzakl0VWpTcnVWM3Bnb094OVc1NnJpZDNnNk5BREtGMUkiLCJtYWMiOiJlN2MyZDgzOWRmYTg3Yjk5OGU3Zjc5MGM2NTBjZDUwYTNmM2UzYTk1NjE3MTM3N2Q3ZDAxMmEzYzgyYTRmMmZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldEbDhVT3RYTmpqUWNlS1VzbWJiZ0E9PSIsInZhbHVlIjoiMWRaL2JVdC9KSUljR1VFQ01uS1dIcllxL2NKMXp3Vk1QTDcvK2dWeDVoSExYOEdEeXE2SkdjNTVldTdTbkdXYjduYjJadDBzYVo3QmFscUdEaG1lTWY5T2pJMkhkS1pyUlYwOFg4ZUkxbFM1M21NN3ZHUnZGNDF1TThVNHVDVE9aakoweHNkMmlLcFh4VERxbnoyRFlVWkpJeEhNVFFDTE1Za0FpK1NYOHp2ZnhWOEZoSjRNcTcvYkc4ckNDa1RWSXNpYWFkOGtjTTBobEc2SHBoU05XTzZ4R1o5emw0SjhNYzNLbnJjVHZ1MlhzdjZSak81UVJBY29pNXFNS2Z5U2tpTU91ZjNkOHJGMkk3QnEzc3NuaHk4eEJsbk9yUnkrVnFnUU1xOHVlU1Vlb2F3SWRjTUJZeEF2NlZnSDZ3TVVVR0s0clMrSTNob0pKR01rRWNOWU1scXMzb20rUEdiS2w5WWNZREhBMHJ3eHBXVS91aWtOVjNYNkpwbG9raUttNDNPY2JDMStpRkcvQzNtTXlJOWsydGtjYkg4d2lzNFE1cWc1R3BFSXJRbmV5TWtRb3lqZXlJbEtvYnVUdlZyY0lBOWFNNnBwMlMxbEdxRmg3d294MTFBRmtzeHdUK1lmbkRZaXdDbmpJUkdObnd4dnk2WkVHMEk1djZ5aVpzTWQiLCJtYWMiOiI3NDUxNTEwYjMwNzUxNWFkOTFkNjFkNTY2YzAwNjU1NmE1ZjdlN2VhMTMwZDdhZmJkOTcxYzFiMzBhOGJhYzI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1042532808\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1058132175 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xWPGJpNWbmDLkwLBtG0MdpEYkMbvhJtQ6DvLx5Cj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058132175\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-599293531 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlKS0oybGVYTXNJOUhza1phZVhNQ1E9PSIsInZhbHVlIjoicmxLcDdUQVkxMFl6MmE3U21zRnhCazZhRmd0QlMwaHROd09oRGh0bnVOLzVtQ01lL0wzcG1lYndkaUlobWN1N2UrbjNDZU1PYXl2NHMwUytNRDhvY0Y0cHdxWkswUHJxRUs4NXFsanplN1BOM0NGUnl4NHg4N3l0RjVjdGQ4aEx6aEVvbTJnTDFBQkxnKzRxdEN2UTFKRnlPaGxUZTd4OGlnQ083SFo4N2ZGaXdBayt1aEc3SEM4N1pZV0lPcWRPNjlYY0NGRkRnL3NkT1p1UXVDNUo0QzVzUjA5NTR6eTMyL2tGb1NPQTVodWQrb2VWTGF0R3JqdmUxQ2t6VTlmdERHTFhXaU9LbGRLdkRxcDZKMzFKNXVpekRmTC9VMGhMUVliemhNbEgxdHZwL2xFTTAzcG9RUE4vNmZFRTBFVGxUZU5nUS9yYnBmZnlqUUJwbXdDWEl4VkduWkJyTG1BNXBmTEIvWmY1WnJXSTUxL3FrV1lPUEdweGRGR0lqVmpQb0ZteHZOcGZra0prZHl0OEo4a2k5djd1R3FVVmhxZ1BMMFV3aGZVajNScDlXaWI5OU1MdlRrY1NYdThWb2xCVFYxM05KMWVtTW8xTEhLVHlhTUV3UDU2SXd4ZVR0VzlsUkdCT1Ruam81cjBCaUZzSGkyN000RWxMSUR6b2FSRGQiLCJtYWMiOiIwYjYzN2JjZWZhOTEzNDBhNmI0NTljYjMzYzA3MTMwNjNiOGExY2IwYTI2NWJjYWZiYWVhZjI1YTVlMTg2Y2JjIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpTSHRYVVV5NFlOZkdrOHhrS3lSNEE9PSIsInZhbHVlIjoiU3oxL2JXWUtrRjdSRVlRY0NHaUF2eVcvU0VLcGZKbUJJZUw5SkhKQlVXWkRNeG9WS2pwZXd3K2xYTFR5SDhNSFhwSGNjWHhYNmltK1l5M3lvTGF0T1dXS1hyR0pIREl6WEZDUzhtbjJ4STlPUllyV1pqV3VsVnBFYXl5RFB5UExBcGNNUEpIRjFlK3dqby9sUjRSZHRZb0YvejY3N2k4aUN0clRQeUhWek5OUEQ5SkJkUFVPVWVZN0FUOHo2S3RFQ2cxL0ZyYnJ5K29KV3VSZUVkWTdWZnBBUDFINC9lMXN1OTFaMGhwY1dsNEZnTmdNZ28rOUNOZ2FqVWZ6cnhWbDVmSGFNT3NtQXI1QXQzTlVWd2FmNFRkdTZTYzgzaDFJcDdmRGNVbDNKOGdKaUhnQUwvdTRLb29qR1Fmb2h4ZWtLU29mYW5IbnhqSHlQRHRJdlF6MUI0eE5DWVZmeXBBQmh3TzJDODk3ZFlmSktiZFh3WlZTcUNLSk5ONkttdGM2eVh5ajZzZXFmRU01ZStMeHFRWXJ0N0pYOGhIYjM4eTJsWkl4VG1UVVpLZ3Nwb2lyZzBncElicndrNC8zS2ZDdnUrL2FuTXgyQ3lzOUdvdDlKczQxR1NISTdVOFdPdFg4UDJLaTQzWlpjUnF1TStwRmw2NE5lcUJlOHhaM20zUkkiLCJtYWMiOiJjMjI0NzZjNzFkNjRhYzJjZWRmYmY2OTlhZDdjYjkyNWIyNTlhMDlhMmM0OWE1ZmE0NjZiMWFmMjU0MGQzMWNiIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlKS0oybGVYTXNJOUhza1phZVhNQ1E9PSIsInZhbHVlIjoicmxLcDdUQVkxMFl6MmE3U21zRnhCazZhRmd0QlMwaHROd09oRGh0bnVOLzVtQ01lL0wzcG1lYndkaUlobWN1N2UrbjNDZU1PYXl2NHMwUytNRDhvY0Y0cHdxWkswUHJxRUs4NXFsanplN1BOM0NGUnl4NHg4N3l0RjVjdGQ4aEx6aEVvbTJnTDFBQkxnKzRxdEN2UTFKRnlPaGxUZTd4OGlnQ083SFo4N2ZGaXdBayt1aEc3SEM4N1pZV0lPcWRPNjlYY0NGRkRnL3NkT1p1UXVDNUo0QzVzUjA5NTR6eTMyL2tGb1NPQTVodWQrb2VWTGF0R3JqdmUxQ2t6VTlmdERHTFhXaU9LbGRLdkRxcDZKMzFKNXVpekRmTC9VMGhMUVliemhNbEgxdHZwL2xFTTAzcG9RUE4vNmZFRTBFVGxUZU5nUS9yYnBmZnlqUUJwbXdDWEl4VkduWkJyTG1BNXBmTEIvWmY1WnJXSTUxL3FrV1lPUEdweGRGR0lqVmpQb0ZteHZOcGZra0prZHl0OEo4a2k5djd1R3FVVmhxZ1BMMFV3aGZVajNScDlXaWI5OU1MdlRrY1NYdThWb2xCVFYxM05KMWVtTW8xTEhLVHlhTUV3UDU2SXd4ZVR0VzlsUkdCT1Ruam81cjBCaUZzSGkyN000RWxMSUR6b2FSRGQiLCJtYWMiOiIwYjYzN2JjZWZhOTEzNDBhNmI0NTljYjMzYzA3MTMwNjNiOGExY2IwYTI2NWJjYWZiYWVhZjI1YTVlMTg2Y2JjIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpTSHRYVVV5NFlOZkdrOHhrS3lSNEE9PSIsInZhbHVlIjoiU3oxL2JXWUtrRjdSRVlRY0NHaUF2eVcvU0VLcGZKbUJJZUw5SkhKQlVXWkRNeG9WS2pwZXd3K2xYTFR5SDhNSFhwSGNjWHhYNmltK1l5M3lvTGF0T1dXS1hyR0pIREl6WEZDUzhtbjJ4STlPUllyV1pqV3VsVnBFYXl5RFB5UExBcGNNUEpIRjFlK3dqby9sUjRSZHRZb0YvejY3N2k4aUN0clRQeUhWek5OUEQ5SkJkUFVPVWVZN0FUOHo2S3RFQ2cxL0ZyYnJ5K29KV3VSZUVkWTdWZnBBUDFINC9lMXN1OTFaMGhwY1dsNEZnTmdNZ28rOUNOZ2FqVWZ6cnhWbDVmSGFNT3NtQXI1QXQzTlVWd2FmNFRkdTZTYzgzaDFJcDdmRGNVbDNKOGdKaUhnQUwvdTRLb29qR1Fmb2h4ZWtLU29mYW5IbnhqSHlQRHRJdlF6MUI0eE5DWVZmeXBBQmh3TzJDODk3ZFlmSktiZFh3WlZTcUNLSk5ONkttdGM2eVh5ajZzZXFmRU01ZStMeHFRWXJ0N0pYOGhIYjM4eTJsWkl4VG1UVVpLZ3Nwb2lyZzBncElicndrNC8zS2ZDdnUrL2FuTXgyQ3lzOUdvdDlKczQxR1NISTdVOFdPdFg4UDJLaTQzWlpjUnF1TStwRmw2NE5lcUJlOHhaM20zUkkiLCJtYWMiOiJjMjI0NzZjNzFkNjRhYzJjZWRmYmY2OTlhZDdjYjkyNWIyNTlhMDlhMmM0OWE1ZmE0NjZiMWFmMjU0MGQzMWNiIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599293531\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-244342194 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244342194\", {\"maxDepth\":0})</script>\n"}}