{"__meta": {"id": "X456151f857274b386ab8500eee846e18", "datetime": "2025-07-23 18:21:30", "utime": **********.211153, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294889.739175, "end": **********.211166, "duration": 0.47199082374572754, "duration_str": "472ms", "measures": [{"label": "Booting", "start": 1753294889.739175, "relative_start": 0, "end": **********.131604, "relative_end": **********.131604, "duration": 0.3924288749694824, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.131613, "relative_start": 0.3924379348754883, "end": **********.211168, "relative_end": 2.1457672119140625e-06, "duration": 0.07955503463745117, "duration_str": "79.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02357, "accumulated_duration_str": "23.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.161308, "duration": 0.02248, "duration_str": "22.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.375}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1929028, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.375, "width_percent": 1.782}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2008631, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.157, "width_percent": 2.843}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1636617045 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1636617045\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1171680992 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1171680992\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2094164678 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094164678\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294883484%7C16%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpBU1NXdmRFMGtKNDNQM1lXOUdyUGc9PSIsInZhbHVlIjoiemxMNUZGVmIyUG05VldsME5qYzJ5dkl2OHl3QzFXZENjTGVmTEZpSE1CZnplbUJveXVsZ29uVmpKRVBDSS9hZEVhdVFNRjdUZ3hYWEhBMXhjN3N4YldVYW5XK1ZCTFlDL3c5b0UySm52MkYxdUMrVCt6WlVxTGFLM2s4R3pIMlcvZzdUTE10MnBIdFMrdVdEU09PeFlaZ01kQ3FXQk9jVUVhTUpsNG1tNGt2R3ZFRmRlTGRXejluakhCMnFDUmM1cHhxRmdyWUthSzRnQVlWV21zbkgzd3lCNWxibEx2U2ZrRFppQzhWOXphcFBWTjRQMGV4bVc0ME53eUdPaVFQeHNtczQ0QVpJR2x1dDQwdDVRTlpzNHJQaTRUcmU5REYzYzlpTWZnUEJlVlpLVXoyTXE1elAyeCtobnhTcjdodW9ERTlyMUpGMWhCME9nMGxKMVgyOURrdTNrMWt1c2E1WHNSbHZMck91aVo0bU1VUUUybVlZQVpqYndjSkJQUWp4OVZpRFVDOXRGQ3cvaEpQTzlUbzRCRXE3ZmU3VzVZT085SkdxRnVQbzlrUU9sR2RPcG1WMlh6RjRqdm9CNWlHWmx2RFZhR255dDRwZlVzbjBwendCa1lKWGdnTVg4azF4TVFZazdSQmNzdVVEd3FxMUVKOTg1dFQrRlJiZlR1MEkiLCJtYWMiOiJjZTNjMTI3ZjQ0MWI5ZWJmNjQ0NGI2ZDc2NjExYWRlMGYzZTdiN2U2YjVhZTczMDI5NjdlNTI5YWZhNjEwOWVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFIdjhMV0hhYkdpUWJ2b01WZGZVelE9PSIsInZhbHVlIjoibDlBd21KL1NLRjB3eE95SjZ5OHZvaWc4QU9uOExyUE15Z3kwVzQvRWwyb0FaZjBPRG9HS3ZvTmMvNEd1QmZiY09kUkVrNW5QT0NJSW9kWW95TTFzRjdqejFza2hxdE9yUXlXcFhjNFIreGFjNmlJRG96WHhnM0hyTXVIelRmY3F6L1IwNWdSb3lyeGtsVTdmejJMajhqNkVGSTgzNEdrY1hBTkFPMVU3MElsK1FhMjl1V1NMNmdpM1dxalVZT0lMeUxTVFM1NkQ2MDhYbU5ESjhyQWdVL3c1V01RYzdXSEE2RlVPUkpSVGpwUG9pTlBBOUIwMkFoL2VGaUFkWWhjVVNKSzB0ZHFFUXZORjQrZWZUdTJhYnNKWFhwdWwzWnB2dldOVEZUZnRaYWRmUU4rTjFHNThhRWk2eXZtMUpXaVdTQkhsSmFwb1ZMM0g2a29BUXRIZVdpamRBZlM3NW1vVkhuUUdSNzFJdnNmb0NmREV5K3VtM2hGTVNQVUY0QzMxeHhpUU84d3VIYjdna0J3WUNrMjJmekZOVk5jb3pyQjBWTzhObzhBeXNrK2JMYWh1S3JHRjdYcVYwYkg2M1UvaTFiQkNNUHArR05TODYzMkVZUUJOU0R3ZVVRbUFHR29vYzV1UHNJSi8xejErbUF3bWJLaWd1OUJyQUdYRzJKTmIiLCJtYWMiOiJiODk1MjViNzQwZWFlOWU5ZGFlNzUyNGE0ODQyZDllMjFkNzhjZTAxZTYwOWIzNjliNjBkNzFlMjBjOGM0ZGVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1245191301 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245191301\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1024841036 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjYxYy9BNi9iMlFwSVFPeGJzZWkrOVE9PSIsInZhbHVlIjoiY05OYlBEUjNudDZhSTZCYjdGSS9pNzJXeVRFTlJsdU9LYTZ2aXowQUNRNzBhcmJ4S0FxS3gyemhXN1RIeDFWaTZKR3ZlTUZiUzJUV1FIOW9xWHhOY3NmdFg3ZGVWUXpOVlZSU0hlYTlESVNveUZ4MUtzWUx0akRCeUxJOHllb2ViV05OSzZzWE14ZDZ6UktHOVVmTnBsR0tXYzJCL3FlOUJYczQ1UTFEMUdKTWxFTEdCNmRSVTNXU2hkRUpQN0JqbTIwTmJ0eWJJNzlZbGVZcmRXVXhSK1BXSysveWc1OTRzMDY2NVVuNkp1NXFadEtWbkkyMEJQT1lFK1kzVUdHNGRUaW1aNTF4eGJaKzJMMTdNK2doaWZiakp4RFRPdkJMTlFLWnNIZ3NXdU9UQXdYV3lIaVl1MlFyQTlMdGlLby9jNW9oR290a0ZjN0FTbUIvMjNuS0JxOWhBVzhhRlVYeUlhRThld09YZktzM1lUakdnbE9HRU96a2Q3bmQzT0hsZUFseGdLZThDU2RzaE1UQVppbUcxNGxEeTNGZnpPQ0RDSVA5czVCQzFFUTNBdlNuQ2ZYS1BidTRwNk9FQnBPbUZwQVhCMndtbk84MllyZ21mUkh2T0VhcTk1cWhrZFhEazFlb25qYUNyQ1FJN1JGY2hYVFpsbzNkTXlQdEJDaGUiLCJtYWMiOiIyNTExMmEwNGQ3MTNjYjhiYWMwYzI1NzIwODRjZmQ5ZDMwMmUzNzgyMzQwNDMyZWVkYzA4N2QyNTE0ZmQ3NDIwIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpERW9lbStSU3AxMUlKempWby8yNHc9PSIsInZhbHVlIjoiYmd5dUdBaE9GUVlZY0RsaHFFeDJBZDlZVHQxQ2JkcDV6WURlZ0hDRngvV3dQRkdzT1pLcmhYM2pST0xwRVBRRTloMFlMdHBpNEdCWndFaDVyWjk0MUR1RmJKZUFSZkRoVStNNi9GQ1I3UG9KNXRncTNpK001Y1I4WGc0eHFDL1dHYzRVaWt0dnVQWWpNWk9Vb2hGS0dXVmd3anlZcmxRMGJHeEQ2a0xvWTFGZzh2QTFtRStaSzlUWWc0cm5xNDBUWlprUzNqaVhwQ25XbDNqYzVSTm1aNlRaOER3NnhpMm1lNXFndzR5SGZRWVpaUlc0WUhXeU9GdVhRNzFRNFcvbEEwUFNVWmhvSGs4VkVVTTdTTmQ1alBaY2pNazlIRkVyM2dNTy9KbklyVEE1YjE2dlk2QmprTTFxQzNWeURzOWZzNVhrUGpGMW1SQXRsL1JDN0FqU0JMOWhsb3lFeHp6QnkyRmNUekFTOWkvVmE5ak54L0dlbmp3VnpRL2RkZEJ1ZGVWMUtXdWYrWGlNNXBGMU1hYW1IazRCSmI0NTQ3cFR2SXVNWkRaaWFhdGxNQVh0YTFpL2ZvTnpmYk9VNTQ0S2piaEgzd3NXNUlWYTBhNTVqYTZKY3hkODFtZUV2VlB2ZjNXSTZCeHl6TFlxbWhuZ3JwZUllR1dsZ3pFMlNJWDAiLCJtYWMiOiJkZWJkY2NiNTM5Zjg3NTU3NTljNzQ1YTFhNmRkY2JjYTJlZTU0ZGQ1NDc1ZWI2NWIzMGIxNDAwZTE3MGQyNGNlIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjYxYy9BNi9iMlFwSVFPeGJzZWkrOVE9PSIsInZhbHVlIjoiY05OYlBEUjNudDZhSTZCYjdGSS9pNzJXeVRFTlJsdU9LYTZ2aXowQUNRNzBhcmJ4S0FxS3gyemhXN1RIeDFWaTZKR3ZlTUZiUzJUV1FIOW9xWHhOY3NmdFg3ZGVWUXpOVlZSU0hlYTlESVNveUZ4MUtzWUx0akRCeUxJOHllb2ViV05OSzZzWE14ZDZ6UktHOVVmTnBsR0tXYzJCL3FlOUJYczQ1UTFEMUdKTWxFTEdCNmRSVTNXU2hkRUpQN0JqbTIwTmJ0eWJJNzlZbGVZcmRXVXhSK1BXSysveWc1OTRzMDY2NVVuNkp1NXFadEtWbkkyMEJQT1lFK1kzVUdHNGRUaW1aNTF4eGJaKzJMMTdNK2doaWZiakp4RFRPdkJMTlFLWnNIZ3NXdU9UQXdYV3lIaVl1MlFyQTlMdGlLby9jNW9oR290a0ZjN0FTbUIvMjNuS0JxOWhBVzhhRlVYeUlhRThld09YZktzM1lUakdnbE9HRU96a2Q3bmQzT0hsZUFseGdLZThDU2RzaE1UQVppbUcxNGxEeTNGZnpPQ0RDSVA5czVCQzFFUTNBdlNuQ2ZYS1BidTRwNk9FQnBPbUZwQVhCMndtbk84MllyZ21mUkh2T0VhcTk1cWhrZFhEazFlb25qYUNyQ1FJN1JGY2hYVFpsbzNkTXlQdEJDaGUiLCJtYWMiOiIyNTExMmEwNGQ3MTNjYjhiYWMwYzI1NzIwODRjZmQ5ZDMwMmUzNzgyMzQwNDMyZWVkYzA4N2QyNTE0ZmQ3NDIwIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpERW9lbStSU3AxMUlKempWby8yNHc9PSIsInZhbHVlIjoiYmd5dUdBaE9GUVlZY0RsaHFFeDJBZDlZVHQxQ2JkcDV6WURlZ0hDRngvV3dQRkdzT1pLcmhYM2pST0xwRVBRRTloMFlMdHBpNEdCWndFaDVyWjk0MUR1RmJKZUFSZkRoVStNNi9GQ1I3UG9KNXRncTNpK001Y1I4WGc0eHFDL1dHYzRVaWt0dnVQWWpNWk9Vb2hGS0dXVmd3anlZcmxRMGJHeEQ2a0xvWTFGZzh2QTFtRStaSzlUWWc0cm5xNDBUWlprUzNqaVhwQ25XbDNqYzVSTm1aNlRaOER3NnhpMm1lNXFndzR5SGZRWVpaUlc0WUhXeU9GdVhRNzFRNFcvbEEwUFNVWmhvSGs4VkVVTTdTTmQ1alBaY2pNazlIRkVyM2dNTy9KbklyVEE1YjE2dlk2QmprTTFxQzNWeURzOWZzNVhrUGpGMW1SQXRsL1JDN0FqU0JMOWhsb3lFeHp6QnkyRmNUekFTOWkvVmE5ak54L0dlbmp3VnpRL2RkZEJ1ZGVWMUtXdWYrWGlNNXBGMU1hYW1IazRCSmI0NTQ3cFR2SXVNWkRaaWFhdGxNQVh0YTFpL2ZvTnpmYk9VNTQ0S2piaEgzd3NXNUlWYTBhNTVqYTZKY3hkODFtZUV2VlB2ZjNXSTZCeHl6TFlxbWhuZ3JwZUllR1dsZ3pFMlNJWDAiLCJtYWMiOiJkZWJkY2NiNTM5Zjg3NTU3NTljNzQ1YTFhNmRkY2JjYTJlZTU0ZGQ1NDc1ZWI2NWIzMGIxNDAwZTE3MGQyNGNlIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024841036\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-150263438 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150263438\", {\"maxDepth\":0})</script>\n"}}