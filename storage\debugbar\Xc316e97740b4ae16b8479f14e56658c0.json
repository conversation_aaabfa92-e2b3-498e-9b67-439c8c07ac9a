{"__meta": {"id": "Xc316e97740b4ae16b8479f14e56658c0", "datetime": "2025-07-14 17:58:23", "utime": **********.910272, "method": "GET", "uri": "/invoice", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.396271, "end": **********.910298, "duration": 0.5140271186828613, "duration_str": "514ms", "measures": [{"label": "Booting", "start": **********.396271, "relative_start": 0, "end": **********.792579, "relative_end": **********.792579, "duration": 0.39630794525146484, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.792588, "relative_start": 0.3963170051574707, "end": **********.910302, "relative_end": 3.814697265625e-06, "duration": 0.11771392822265625, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48436032, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice", "middleware": "web, verified, auth, XSS, revalidate", "as": "invoice.index", "controller": "App\\Http\\Controllers\\InvoiceController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=35\" onclick=\"\">app/Http/Controllers/InvoiceController.php:35-62</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0070999999999999995, "accumulated_duration_str": "7.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8268392, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 28.592}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.839755, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 28.592, "width_percent": 7.042}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8556628, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 35.634, "width_percent": 20.563}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.884168, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 56.197, "width_percent": 30.423}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8880389, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.62, "width_percent": 13.38}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage invoice, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1048779197 data-indent-pad=\"  \"><span class=sf-dump-note>manage invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048779197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.893896, "xdebug_link": null}]}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission Denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/invoice", "status_code": "<pre class=sf-dump id=sf-dump-203109937 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-203109937\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-501574793 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-501574793\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2008056670 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2008056670\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-969140402 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515898579%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxhY0hYVDNhTnkrUGh2ay90eDBsd2c9PSIsInZhbHVlIjoiWlk4ZTB4VCt1WERaZmpNLy9HTk9jYjJZVkFxelpYYzlWcU9saVlFbndMbUhzdkZzUS9hSElUYmVhR2I1Y2VadHJqOFBLYlo5Z0RqdTc3d1RYY1UwaHRDNHFtTTg5WER3YzBhYytyeHpMY1hEOTROK0IwS08vcjg3dzA4U0tiOC80NUdNODhOSlRSdG91VUZFbmQzd0EwQkxoeXBIVWlZK2dsckxXZDRudnpRRE5MTDBDSFQxQkUrNUZYK1dOV0lxR2drcjNQaXFBV2hEa2IwdnBRcHRPRFZxdkNtZ3Rla1hXditFcHUrZHhrRytyNHdFdmFMazFyNUI4bkE2T3dZbllpTDdRcC96N1VKVEdzT0lJeXRrcEk1TmJEL0p2bGEveTR2T1Jic3dpM1ROZURLVHdNT0tuaWUrRkVOK2owYmxtbXRCTU8zSXZLZDZHQ3IrUEtqUzBqb00wbjRwdlBYdjRrcDF4ZnZWc2lYOUQreGpWa3QyQWdvcXV3cWRvZ2kzaWN6My84VEJJczhFZzdIY0dEbHNqcUNPTEZESEgvVEltMk15RVp5RzFadnZVQUptYWhoSmg2SHhzVnp4eHRwVnNZT2ttUjRUTUZtS2plcXB0Y3EvZm9hakFjUnpPWUYyRnRzV24yQXE1S2x3M2c5Z2RBUjZJaFBPNDA3eXY0SzYiLCJtYWMiOiIxOTNjNTA0ZjA3YWFlMDI4NWJjNjMxM2FkOGY3MjMwYTQ2ZDBiZWZmYTdkYmRhZGFhZTU4OGVjZjgyNGY1NTk3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNNdzVpdEttNzRzeDI2Tnd4bHgvM3c9PSIsInZhbHVlIjoiWWtGNmNxQXR2azdnenZpYlJiUUVNQWIvdlBBTllQUHpvaEppR3JUUjVGeU16ZXZwUndqSytaRm85dFFibzJTNjFXbVJMTlc0M0RCSjZBNGVSaVBDZFp0ektvMkdXa1dTNTFKK09ubHRjU2h6ZHlZWWFyK3JYU1QxcXU2d1pSMkpieE1FYzkwRGU5UE1iSk1sSHFBYUJCRnBtOWUwSnBqcGlqUGhHZHZlU25kdFc4bmVtTHUzS2NSSlZjUWhBdS9lL2lyakY0OWYyRGlhRGU4NG03SklpTWZVcGpCRXFNc3hqenBUZzhTNDBWR0lySHNaN1pOVDg3ZHcya21CSjBKVzdHSUNhZnR0cENhM0IvV0ZROThxTFhOTHN0NkxYeEpVRUlBZ0RlTEFzd2M3K2VkQ2gweWJqR1IzbnNtU3R4UlRZNjNBOWsxS3JnSk96N2dNNWo4TFdOWi9mNWZwQXlVMlZkRjM0OCtOUW11TzIrTWduMWFWVjhkMUJaQ1BBSVo0RVVzdlc1dXZialFFSlVFcDVSZGZmNnFvRDFYM2QyMnN4b3YwVzdNSkZQY3M0bTh0T01mR3ZrVVFPdVVIeUs3TThwZ0VNb3pJTWlqM3VXbGVuWm9zb3c2bXZ0VlV1Z0lQMHZORER3RU9TZ1RHdk4vUC9TcnE1M0lmdGMybWtDSlMiLCJtYWMiOiJjM2YxNzE4YzQ4MTlmNjgwNzdmYTljOWEzM2E3MThmZTRmMTI5NDQ4N2M1M2I4ZWQ5Y2NjNjZhOTVhM2M1MjY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969140402\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1985368943 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985368943\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-265018178 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd1c3ZIMDFOM1lxNFU3dGxBZVhhYVE9PSIsInZhbHVlIjoiQldncFN4cEJvZnY5WEtpS1FyeVM2OEtXU3NQeUtMakpIcDdKWmNYa05qVm54TXl1cTJQQlBxRVdoZlROeU1wa0U1Z1lhUDFBZjhGaVplcHhHNVo5SnE5UFZTcTNCbmtOaDV3cGNWSkhIN3NQN0k5UmlzN3UwZlFlMlNRVjhOcGRadHVWOXNEcE5zZERoZXpZdHEvKzAvaEgybkRqZUlib2RlVDNCbG5ndDlCZzZZYksrelFqTmVUUnZNTFg0dnNkbTZibUVxclZMUDlNZDQwWFJsYUVNOUlEQ2M1V2pONUhCM3dRYnp2Sk01a0trTXh2SkxqdTg1WVpiODhmRW1xZjJIWjJId0FtQ1RMbHc2UXBTcDRVSHpVQWNMNUhGcUFvSnlzbTNEMzZPamNPZlI3NjRkQnBmeGtSVmQ2aFhGTUtmQ3FEZ3VML0prczI3QjRXUzFPaHlhNTVmcGlYNElOdnZyOWdPVmFYemw0YjBVbzlOQ0F5MHJrazRTajVJOXo3Z3lyTlIydFh3MHlTNUk5M3M5UlR1T3ZHdTNYSUgyVFdZTXIzYy9wZ1NrVzc5Zk16S2MwVHZZOUw5OFJEalo1VnJIa1Y2eTg2bE9XdU5jbFdKZ3ZybmMxRFFCRmpRN01pS0hOSE8yd3ZEdkViTkQzN2x5Q0ZqNS95emVRVUxUUGYiLCJtYWMiOiIyOTU4NDc0ZjA4ODQ4Yjc5ZTBiOGY0ZTljYjRkZmQyMDczMWFlMmI4OTkxYjcwMWVjNWVjOWE0YmM5NGM4YzM4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktKWnludExCSXh2Vk1odktaZTg0Z2c9PSIsInZhbHVlIjoidmZXcjhYZzNkQkk3d2xoK21qOGtWQjlZNjdNQnFPd1ZmQTVFYTZ0NmRONVBRdWJYTW15NFdpWlViK2U4aTJKN2tsc2E0WEplUS9ldVZQWVN0UmxiczRSWW9zZ1M2TkxuZzZCNWk0a1V1R0tORnh5cVZsOXBpRjVlUVdSdVh3VGNIT1dtc2QxamNwS1RKOWlvdy95TUlxZndGNnpHSnNHMGdEUldBTFZScy9kK01aRlhscHExelRQOERtN01iWUt3ZDRrOVBsYk80dk9aT0RWVWMrdHRiWW96dFNoaG9YTStOcUM0NmhIL0xHc29NdGE3ZkN2Q0g2enc4d0FiSnU4YWp0MUNKTnAwOGJHM1dFYVBLR05zM2U4VlhMNHZIV2RNRkZmc2JVc1MyMjJPLy9aM0ZWY0RuQ2w0ODVOVitaUWl0eUowM1BBcVVXaTNSTFRmVHBMUnlSaTAwcktjWlZSTTBrUmhGcmRqM2lrcE4yc2plOFVUZHg0VjM0RDRtWDVBM0xoYnBvUCtOOXdCcHJRYTZ1UTJ2MXFJdEZ5K0FvRnVQdjJqb0R4eUVzRk84UTMrWHRXRCt1cDhYempEU01CRFlsMXViRktUVlE4cFB0dEZsOFh0aE1QTjZLczluOFlEQ0tDelJzZWRsZTIyTnV4VkxlUjZaSG5wSXgwV0FzUEgiLCJtYWMiOiJlNzRjZTFlYjk1MzUyMDIyOWEwODlkOGExOTFjYmNlZTU4Njk0ODM0NGM5Yjk5Yzg1YzVmYWIwOGYwYjAwYjM3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd1c3ZIMDFOM1lxNFU3dGxBZVhhYVE9PSIsInZhbHVlIjoiQldncFN4cEJvZnY5WEtpS1FyeVM2OEtXU3NQeUtMakpIcDdKWmNYa05qVm54TXl1cTJQQlBxRVdoZlROeU1wa0U1Z1lhUDFBZjhGaVplcHhHNVo5SnE5UFZTcTNCbmtOaDV3cGNWSkhIN3NQN0k5UmlzN3UwZlFlMlNRVjhOcGRadHVWOXNEcE5zZERoZXpZdHEvKzAvaEgybkRqZUlib2RlVDNCbG5ndDlCZzZZYksrelFqTmVUUnZNTFg0dnNkbTZibUVxclZMUDlNZDQwWFJsYUVNOUlEQ2M1V2pONUhCM3dRYnp2Sk01a0trTXh2SkxqdTg1WVpiODhmRW1xZjJIWjJId0FtQ1RMbHc2UXBTcDRVSHpVQWNMNUhGcUFvSnlzbTNEMzZPamNPZlI3NjRkQnBmeGtSVmQ2aFhGTUtmQ3FEZ3VML0prczI3QjRXUzFPaHlhNTVmcGlYNElOdnZyOWdPVmFYemw0YjBVbzlOQ0F5MHJrazRTajVJOXo3Z3lyTlIydFh3MHlTNUk5M3M5UlR1T3ZHdTNYSUgyVFdZTXIzYy9wZ1NrVzc5Zk16S2MwVHZZOUw5OFJEalo1VnJIa1Y2eTg2bE9XdU5jbFdKZ3ZybmMxRFFCRmpRN01pS0hOSE8yd3ZEdkViTkQzN2x5Q0ZqNS95emVRVUxUUGYiLCJtYWMiOiIyOTU4NDc0ZjA4ODQ4Yjc5ZTBiOGY0ZTljYjRkZmQyMDczMWFlMmI4OTkxYjcwMWVjNWVjOWE0YmM5NGM4YzM4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktKWnludExCSXh2Vk1odktaZTg0Z2c9PSIsInZhbHVlIjoidmZXcjhYZzNkQkk3d2xoK21qOGtWQjlZNjdNQnFPd1ZmQTVFYTZ0NmRONVBRdWJYTW15NFdpWlViK2U4aTJKN2tsc2E0WEplUS9ldVZQWVN0UmxiczRSWW9zZ1M2TkxuZzZCNWk0a1V1R0tORnh5cVZsOXBpRjVlUVdSdVh3VGNIT1dtc2QxamNwS1RKOWlvdy95TUlxZndGNnpHSnNHMGdEUldBTFZScy9kK01aRlhscHExelRQOERtN01iWUt3ZDRrOVBsYk80dk9aT0RWVWMrdHRiWW96dFNoaG9YTStOcUM0NmhIL0xHc29NdGE3ZkN2Q0g2enc4d0FiSnU4YWp0MUNKTnAwOGJHM1dFYVBLR05zM2U4VlhMNHZIV2RNRkZmc2JVc1MyMjJPLy9aM0ZWY0RuQ2w0ODVOVitaUWl0eUowM1BBcVVXaTNSTFRmVHBMUnlSaTAwcktjWlZSTTBrUmhGcmRqM2lrcE4yc2plOFVUZHg0VjM0RDRtWDVBM0xoYnBvUCtOOXdCcHJRYTZ1UTJ2MXFJdEZ5K0FvRnVQdjJqb0R4eUVzRk84UTMrWHRXRCt1cDhYempEU01CRFlsMXViRktUVlE4cFB0dEZsOFh0aE1QTjZLczluOFlEQ0tDelJzZWRsZTIyTnV4VkxlUjZaSG5wSXgwV0FzUEgiLCJtYWMiOiJlNzRjZTFlYjk1MzUyMDIyOWEwODlkOGExOTFjYmNlZTU4Njk0ODM0NGM5Yjk5Yzg1YzVmYWIwOGYwYjAwYjM3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265018178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission Denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}