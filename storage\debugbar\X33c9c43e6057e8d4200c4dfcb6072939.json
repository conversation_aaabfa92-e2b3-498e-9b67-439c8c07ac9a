{"__meta": {"id": "X33c9c43e6057e8d4200c4dfcb6072939", "datetime": "2025-07-14 18:33:42", "utime": **********.740272, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.236345, "end": **********.740288, "duration": 0.5039429664611816, "duration_str": "504ms", "measures": [{"label": "Booting", "start": **********.236345, "relative_start": 0, "end": **********.670734, "relative_end": **********.670734, "duration": 0.4343888759613037, "duration_str": "434ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.670743, "relative_start": 0.43439793586730957, "end": **********.74029, "relative_end": 1.9073486328125e-06, "duration": 0.06954693794250488, "duration_str": "69.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991104, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00396, "accumulated_duration_str": "3.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.705279, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.414}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.720913, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.414, "width_percent": 18.434}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.730023, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.848, "width_percent": 15.152}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-338418464 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-338418464\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-504206495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-504206495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1362477068 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362477068\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-964261067 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517896149%7C46%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imo3ajNZdUhUaUx6ck4vV1dsbVcvRUE9PSIsInZhbHVlIjoicktFZGlZZkx5aEdJemEyck1sNVhHYWY3MjRHbHNzYWtEak1HWkpkaXp4WTJMamp0cFRJdWM5enBzYzhyMGozeUduTUJpck9sRmlKSGZmZkdhaTNoWEtBaDBRVjNoay9CdnNNWVExZGQ2R0pXaXFzMkxpZHQ4VGtkZGxMcWJFK0o1RUh6bklPSGtOZVB2THpOMFJmVmxaUWNQRDkzeFc0cHcvRTQrTThCUldtL0k2YVRXcEZwTHFJa1VmVnAramZOWTNTc2s1RWhPTFBIT0lWUTdrRmFqdVlUVkdCMG9KM1UvTDhGcVFjM08yeVZhK3VDNVkxakFraUE2NXlLbUhLKysxNm9pYk53Vk0vRk9jUW1oZUkwemRJTVpkajFUTGhVekUyUFRTK2ZEU3cvRDc1cTEwRVBWTkMzWGRyc3dOak5UUTc1SitiRkRKY1djM2xHeCtEc0hvTGc3dTRyQXZZaDRwTDAxN3F2M1hBazlVaFpTUGQ5WFM3ZGhYUEU4TUhvdnptYjI2MHJaQlYwVlhSZVhJSkFYNlFpejQ4eFVHa0hMOE96UXQyajJEaHdOOEIzS1pTWFZRa0ZzZUx4eXUrR0dFY01raG1zSkVKbEV4M0dacnAzVDh2Y2pUQVNyakptT1E0Z3hqZVhwbWtYRExQUDNqNkNpSjlwN1Z2YTNETW0iLCJtYWMiOiI0YjYzZTg1ZmVkNTlkNTQwNTE2YzQ1ZmNhOWE5ZmI1ODE3MmYwN2Q0MTFlNzVmNmVmZDFiNTY1NzdkZTVkNWU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InlOc1phMERIMGdlek0rUHBPcDRMaWc9PSIsInZhbHVlIjoibGxyWjZLczhuVnAyTXJteHZHNHhOaW1qREpITkJ2azJ5QmM0QUQ0Z3hkL24wdUNaZDBtUGhjVktvbEFYVU9pSXRCVnluZE5aU2JKUG1Oa2xrMk9iQVVwWHpoWU1lQVV5RjhwK1M0clo2SElPMkV4TVRlQkx3VFNOOWhCQ0l5b3RNRjh6b0RyZWlaam1ZVDNrc0h2TEZFdngxeklkdU1nNnprdHErZ1Z0Y2diUXFFb2wyZzFZamFjbXZVdTJSMldNMEJyb1Rmc2ZUTkR5SG1LWDVJcHhSRUdDMkVacXYyK01LbUhtL1FwOXkxSUlXSDFLVmFmTFFoZVY1czlVSE1hMkxJcHJ0MTVEbFJSZG9KWmYwTno5Y3pwWU9kdmhoVVJyaGZ4bGV3ZkVsN2hOY1RTeVFEbU0wZVliNHZtTzdFQ1hFMzArNlR5QW1aUHczU3YzcnFPbEMyaTM2SG9ZMmVWV2Yya2w1bDFWTkRpTWZMTmdlUDMvV1UxaXU4bEVKMnBHenlFM3Z3ZzNlNGlvU0lhSkhvd1FHTmliaGhvOWRwb25YRXFwOXVIU0tzcjFiVUdJMUQwcjVoOTNhS2pTZmMvRWM4OEZPR2R3NkNJUkcvY0pCQjBWS29vSVF1ZWJtRnRDaVBUYW1UMnJvdDRmdE9kTjZidEpLTWRCUWhzR1hKeXYiLCJtYWMiOiI3NjNmMjk3NDI1N2M1YjRjZmNiMjk3MWU3ZWEzMDA2MDdhMGZmY2Q2YmE4ZWFjYzk1MjUxYTE2ZmI2YmEwMTcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964261067\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-120426382 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120426382\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1073442669 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:33:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFnNkVmU3QveDE3TTBOeW14c3JoT2c9PSIsInZhbHVlIjoiQmxEWkxIRFBBWkt4dVBUOEttMFoyM2U3YVQ1OG5vQVI3M3ByOGh6Mi9JaGhhM3Z6a3Jqb3l5S0tZU2RJQ1J0OE9XanhYMjU3UEdUTWRWcHRCT3h5U3BaWjdKaDR5OGF2aUJCOHI5QnM2dDRHTTZhZ0I3SldDTkpCYVg4ZW5TY2pqS0gzYktORWJuLytJU3M5OWJYd1BXWTJjU0RER3QzVUhJTC9nUUJzcVREZ0ptNjJpQkFEL2RMTXBNU3ZXeEhUWmRFN2RFRnhDYVlPMG56VWVrbnRSOTkxRDBWbGtuek5JVnQxR2pyZ3hLRTlneHdQZmIrQ2treXd6aWxSb1B2cEdvZWQ1MDFjbTVmdDhKelN2R0FKbW9KT20rcytYb2I4UTBFM3BRZFd5ZDlmbVdlTmQ3NVVCUDlYWGgvVWgxb2ppeFNOWW45SzJFZ3VjOS9mcm96ZFZLb3MwcS9tbjRQMzFydi9GbXp1MHBVek8ra1NSaGRYVSszbE5nc2tIcnFIWnA4R1VuTGtnOFE1cEdBRlNvOXVBQ3o3aHlPWVAybjVkUlF1dXlEOVZHaFhNYms5SWt3RmhTem1zNnAxZ0dQbHJaYk1LV3dyTTVObHh5cnQ5WHBSQTZ5V1pTUkhqWmhHdUhpc0kzWThrYkoyUGN3d1dLMnhKSXZHeHUrUGs1TUYiLCJtYWMiOiJkYmYzODFiOTcyYjc5MzY2Y2U1MmY1MDllZThhYTM5NjU4MTZjOWM1Njc2ZmFkYTM2OWFmODkyZjA1YWM0ZDk2IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:33:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpaT2M5SnFuVWg2MWZmSGlCRmh0MkE9PSIsInZhbHVlIjoibzdSd1I4d3N0OERNbDNiT2txUjJ2aG9ITEJ0aGpGYlYxSWl0WTkzTDNnSytRcEZlak5YYUNjUUNUc0pnWW0wYmg4NkFmL2J6ZjYwTFp2T284Z05oZ1p0Y0Z1eUpLOVZSZG9CZllmMXRXZHkvVXhBZVU4VGtwWTRHR3RlckgwcDh6Nk8rV2dBa2xwYUg4WVljM1ZsSTlaSjNGWlhSWXJ5TE1WWUFzYlhUa1A3R0ZzTk5ta3A1R0dXbE10dnUzVzZOMTMreHBmUU9TWlY5aXZJN2xlMXNxYnpjTkhwOVcwU3BYVkpNZ2ZsR0J2UitqVUZnVFFiN1oxbE83UUtHSkEwYlU1amJqeUdRNXAzOWZvZjIvVkFDWkhBK0UvVnZhZmZ0VVJlMjJncXJEbTYrV044TnNZMjhiWEMyVHNCd0ovaXlxczF4dVJLdys0RzN0Nm9UZElTa1lvWG45MG5ob0grcWZKdGVZbk4rUDk3NFR2WFI4NUlHN0Jodm1zMUg3YVllck1hNFNsUGtXeWxlMFEvblYrcnRwc2lFeVQ5Yi9aRk9xNUhucmFXeUtKWG15UTM2Q3ZCaEJCYitDZklmWXRiaHluNVFSZ0hoQVVKZy81MndLV1Z3M2syZlA5WURJRm5BYXpveHY2Q3hnTCtMYWd4OTFtL3Yzd2JCV2x5d2tiQ1QiLCJtYWMiOiIwMWUwOTI5MGU4ZWU0YTE0NmVkYTBjZThlN2IwYjM2YTM3Njg0ZWZjZjVhOTZkZWE2YzY1ODRlYWVlMDljMGZhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:33:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFnNkVmU3QveDE3TTBOeW14c3JoT2c9PSIsInZhbHVlIjoiQmxEWkxIRFBBWkt4dVBUOEttMFoyM2U3YVQ1OG5vQVI3M3ByOGh6Mi9JaGhhM3Z6a3Jqb3l5S0tZU2RJQ1J0OE9XanhYMjU3UEdUTWRWcHRCT3h5U3BaWjdKaDR5OGF2aUJCOHI5QnM2dDRHTTZhZ0I3SldDTkpCYVg4ZW5TY2pqS0gzYktORWJuLytJU3M5OWJYd1BXWTJjU0RER3QzVUhJTC9nUUJzcVREZ0ptNjJpQkFEL2RMTXBNU3ZXeEhUWmRFN2RFRnhDYVlPMG56VWVrbnRSOTkxRDBWbGtuek5JVnQxR2pyZ3hLRTlneHdQZmIrQ2treXd6aWxSb1B2cEdvZWQ1MDFjbTVmdDhKelN2R0FKbW9KT20rcytYb2I4UTBFM3BRZFd5ZDlmbVdlTmQ3NVVCUDlYWGgvVWgxb2ppeFNOWW45SzJFZ3VjOS9mcm96ZFZLb3MwcS9tbjRQMzFydi9GbXp1MHBVek8ra1NSaGRYVSszbE5nc2tIcnFIWnA4R1VuTGtnOFE1cEdBRlNvOXVBQ3o3aHlPWVAybjVkUlF1dXlEOVZHaFhNYms5SWt3RmhTem1zNnAxZ0dQbHJaYk1LV3dyTTVObHh5cnQ5WHBSQTZ5V1pTUkhqWmhHdUhpc0kzWThrYkoyUGN3d1dLMnhKSXZHeHUrUGs1TUYiLCJtYWMiOiJkYmYzODFiOTcyYjc5MzY2Y2U1MmY1MDllZThhYTM5NjU4MTZjOWM1Njc2ZmFkYTM2OWFmODkyZjA1YWM0ZDk2IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:33:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpaT2M5SnFuVWg2MWZmSGlCRmh0MkE9PSIsInZhbHVlIjoibzdSd1I4d3N0OERNbDNiT2txUjJ2aG9ITEJ0aGpGYlYxSWl0WTkzTDNnSytRcEZlak5YYUNjUUNUc0pnWW0wYmg4NkFmL2J6ZjYwTFp2T284Z05oZ1p0Y0Z1eUpLOVZSZG9CZllmMXRXZHkvVXhBZVU4VGtwWTRHR3RlckgwcDh6Nk8rV2dBa2xwYUg4WVljM1ZsSTlaSjNGWlhSWXJ5TE1WWUFzYlhUa1A3R0ZzTk5ta3A1R0dXbE10dnUzVzZOMTMreHBmUU9TWlY5aXZJN2xlMXNxYnpjTkhwOVcwU3BYVkpNZ2ZsR0J2UitqVUZnVFFiN1oxbE83UUtHSkEwYlU1amJqeUdRNXAzOWZvZjIvVkFDWkhBK0UvVnZhZmZ0VVJlMjJncXJEbTYrV044TnNZMjhiWEMyVHNCd0ovaXlxczF4dVJLdys0RzN0Nm9UZElTa1lvWG45MG5ob0grcWZKdGVZbk4rUDk3NFR2WFI4NUlHN0Jodm1zMUg3YVllck1hNFNsUGtXeWxlMFEvblYrcnRwc2lFeVQ5Yi9aRk9xNUhucmFXeUtKWG15UTM2Q3ZCaEJCYitDZklmWXRiaHluNVFSZ0hoQVVKZy81MndLV1Z3M2syZlA5WURJRm5BYXpveHY2Q3hnTCtMYWd4OTFtL3Yzd2JCV2x5d2tiQ1QiLCJtYWMiOiIwMWUwOTI5MGU4ZWU0YTE0NmVkYTBjZThlN2IwYjM2YTM3Njg0ZWZjZjVhOTZkZWE2YzY1ODRlYWVlMDljMGZhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:33:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073442669\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1779351863 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779351863\", {\"maxDepth\":0})</script>\n"}}