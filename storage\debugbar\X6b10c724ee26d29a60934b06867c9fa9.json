{"__meta": {"id": "X6b10c724ee26d29a60934b06867c9fa9", "datetime": "2025-07-14 14:40:12", "utime": **********.456571, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752504011.979126, "end": **********.456585, "duration": 0.4774589538574219, "duration_str": "477ms", "measures": [{"label": "Booting", "start": 1752504011.979126, "relative_start": 0, "end": **********.405537, "relative_end": **********.405537, "duration": 0.4264109134674072, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.405545, "relative_start": 0.4264190196990967, "end": **********.456587, "relative_end": 2.1457672119140625e-06, "duration": 0.05104207992553711, "duration_str": "51.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991504, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026, "accumulated_duration_str": "2.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4338338, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.443461, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.846, "width_percent": 19.615}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.449596, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.462, "width_percent": 16.538}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2128627849 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2128627849\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-659703136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-659703136\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1769745091 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769745091\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; XSRF-TOKEN=eyJpdiI6IlluczdDOG5zQzVMenVibEhrNmZ0UlE9PSIsInZhbHVlIjoibzBkOVB0N3NHSmVZVWZpYXJrd3VxVmVtQXpqZ1RQWjVVUWl4RjkyaDdnbWtEQ0dwZzUwK0FvVDdxSkpCZGQ4WUltcFRJeUE5a1NYbEhYWGdCSGdWZGx0bjh0WHI4dStpam5WVEt3YUdRV0hTK0NZN3hqakJ5clA1Tk54VDF0M0tZVkJmWnpkRjVKeFp3NzhWSWx2NXlsb3lEeDArSXNNYVRIR1I4cWZLNzhxakM0YjFlaEp6UUgzRjRKWjB5NVMwRnZKUXg1UkFYOS80eHprQm1TNkU4TlljZlF6K0w4RE1iSTdUQVpjbUQzdU82RnhRMFNCVVd4L2oyT0YwQ1N2RG1WQ2t6dkxycC9JZkFKTVh5RUZoMytVeE40QjBRTnVMNHEwVlYrdGRoQjdWUytweEprUFdzdFh2L0lybDNIN2lDZk0xQlA5OUtxQTVOMkw5SFpmRys1TkM0TzB5bjdNZDYraTE3WWFlNDh2ZFpzUzdSTXdVMjV0T3lRWGtZNlJONkZ4QnlOQlJ4dmJVMkd1bEJOTllpYnAxN05KdFpPcWxJWkg4L2lmV3dWZnM0dkp6OU02d3JJbkNVMWYwUVZtaVVkVjZ6czJ2RGkwVTBRcHBGRFU0TWN3bHI4MEw4OGFBVnFVaWEyaW5FSmNoUVNpWGZzQzQzNUMvL29LdkROWUIiLCJtYWMiOiJjYjgyZGExY2MxMzQwMGMzOWZkOGFmMTg4ZjkyZTRiMGNlN2Y5OTZhY2EwMjkwMzc4ZTA1YmZiYTFlODJmMzVlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InQ1d2NRTjV5Yi9RelF4VnpLTFJmSEE9PSIsInZhbHVlIjoiQjJQZzl5aHZTTk1RWlZVd3lRVEtRenZIN1UzVXB2dVYremRWN1pnRXFLQ1lDbW1ZNWVDVkkxRDNROXVCSThoSVpkMHJ4VXhBcWpzUi9NS2RkSzlUUFR6WkFNOVJ1MWNSdUZFdkVqOWhVWFlsbktwWWUyTW53WmpqVWpaUHdHWGZkeFZVaEQ0TUhIUU5KaUZOcGVxbEFCaFo1VERzZ3BjRWRLTEVJVTAxZ1pyc3prU2JuS1U1enlhZ1BZUHYwU0habHZOekdNTHVMVUQ2ems3Q3ZhTVQ4WjB0YktqYnJ4WW04OGlualJjT2lLTERDczFQQ2E0SDBRbGd2MlNxaXIxcndVUDh2SFMwVys0YUxUZ29oS001SWU0VzVadFJBV0pHNVZkY1RyYkRMTnFpUG5SWHNpNnFtVndkZlFTVElqdjVVckdXbFNOaXIwT25JS3Bub2JTZnJTZU9iYWFWcDBCT3VJRGV4YW5BRGFwMHA1b1hwUlNCQjVGK3BIVVI1Vmx2bTQ2ZXZaTHdzZWlmUFFxSGUvdzZNYUd0WWRsZDdtTnhzWkhCelJwK0xYeEczSzhISUVlZm9GV0ZtNmI4UU8yYTNocGhyNVljamZCck5SSHJFcnpvSFBNQWYzMmN0M2VTQ0tScFpDQ005TVpmcUNnb1ZqQ2UrWEVKNG5Nb1lHVUUiLCJtYWMiOiIwOWIxNzM2NTI2MjU1OWY5NjFkNjAxOTczMWU5NTI5ZTZmY2ZjNDY1ZDMxNTY4MjBmYzhkNzZkMzY5MmNiNDFiIiwidGFnIjoiIn0%3D; _clsk=1g7ulh6%7C1752504011682%7C21%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1698998210 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1698998210\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1833874473 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:40:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNvL3R3OEM3NUpLM0tNMTh2elpHbVE9PSIsInZhbHVlIjoiRFhPbUN4cEJEM0pFMjdQZ0NOeFV2dk9kZDAvaEVJNlp6cVY1cnVOK2toTyt4UHdFR1A2ZWtnVkl1cGFvaldtNUc0MWJrV0JtcDZSQ040TUtaWW81SkswRlhqSE5WeHlhTFZjTnN3cTdjbUxNTVRHS0JWbFN4VFpKQ2s1ZjQwV29jL2d6L0dXbjkyeDlUcGl3U0RuTEV4TzdrTVFrUHBUaXF4NUF6SlJlT1JsMlp4YkhYT1RlaDdYNTA4NlVtR09wQVNOWU9jVm9QQmdrRkE4aVY5cm5yV3ZUYXI0eFNNZEdsTnJ0cldNU3EwRG1rWmEzRHlTSlpKMjFPZXBKRHN0dDByRDdkdkxWSmdRM1d6SHZXcExZcjlPUjdhUGV0VGJkUVUvZWtiaUFUMzI3WSt2WDB5YUZoU3VodzlMK3RFU1VWaXZFZ1lhV1BXTmh6TGtWd2p6RFFja2VnYTN2alpiUFZVYVl1Y1BxbEtlYzBiZlVOam5kY3hFQVIvRmcxdlFSZUJyNUtQdGVRYVY4TmZDeXhEdVBmS1dOR2ZPanRsMzA1dHBIbFVYSHZCQ2ZkVXRBMFFVcVBuQ0FIdmRtZnVDUGxLa2hRRGVuNFRVSEQwU2hoQXB1TWdvc0wxbEhtQVd2OHZlT1lYVVMwRmZtRG00b3RlL1VPWHU3UDBJNzBSNVAiLCJtYWMiOiI3NjU0YzFiOTFlMDhhNDU1YmYyODMzMjAzM2MxNWY1OGIwMTM5ZmY5NzU0MTg5YjhmMWQ1NmJiMmUyMmJjMjIyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImprRWY4QzNlNG0xM3N6RE5ZQW5lMEE9PSIsInZhbHVlIjoiQjhyY044d0JjTUhoTkdJandYOXlZNzBrT25RaTJqNnduS21IYU1ZS3VCZzA5M1RvN1ZUMC80Qk9DTUVRSGVaakNrU21pNnZQS2wvNm82TGZxWWtjV0NzNS9KQ3FDT08yK1MzOXhGQitkZlNFYlNiK3pvVG9DV1B2TCs4clhQUkZvelZkUXMyL0wyMU1UMkt6SFV1OU40TGFIUGtMS1VmODBmWXhub3FrVUN6RWpYaC8xa2hiOS9BQ3JadzN2M0FqOXhnYmQxcUlIQ3BoUTFHMWdRWjRyR081NXd0aTM4ZWlvREF5UDlVdTE4a1k3cTE1M2lrRmtwM1J1RW1RcVk5SmRFazBveWhFRSthazVqNlFGV0tqQ2dYZWQ4Nlk2WWZQYzhUQnRNUXozalZid2ZWYUNiOXZOQzVSQlFWRXhHdVBrdnZnWmtyYzljSm9PZjdjenc5b2VidzhLS3lKSU5TVEdwK3NpR1VxdFIrc2tIYlpzTTlQb0xCdEYybWJ6MGVjVmhIN2F4TEJtUWZMeUNqMmdLcGZ1d1JDSDVHUDBzTW5EMk1iOWVUd1FQQXBqZG1maVQwd1ZoRHZpS2N4NDgvMXJqZnkrVFJ3d0RrUG5lZ3lLeFovS1lVMHJHRWtUai9uVzNxRHBlWEpjcTBEeGFpQWNSbWY1ZktmbFFWbU42NysiLCJtYWMiOiI5Y2RjMjBjNTU3ZWNjN2QwOTk2YTE2Mzk0ODMyNmIxMDlkOTY1NDYyNzk5MDY2MzlmY2MzZTJlZmM5Mzg5YWEyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNvL3R3OEM3NUpLM0tNMTh2elpHbVE9PSIsInZhbHVlIjoiRFhPbUN4cEJEM0pFMjdQZ0NOeFV2dk9kZDAvaEVJNlp6cVY1cnVOK2toTyt4UHdFR1A2ZWtnVkl1cGFvaldtNUc0MWJrV0JtcDZSQ040TUtaWW81SkswRlhqSE5WeHlhTFZjTnN3cTdjbUxNTVRHS0JWbFN4VFpKQ2s1ZjQwV29jL2d6L0dXbjkyeDlUcGl3U0RuTEV4TzdrTVFrUHBUaXF4NUF6SlJlT1JsMlp4YkhYT1RlaDdYNTA4NlVtR09wQVNOWU9jVm9QQmdrRkE4aVY5cm5yV3ZUYXI0eFNNZEdsTnJ0cldNU3EwRG1rWmEzRHlTSlpKMjFPZXBKRHN0dDByRDdkdkxWSmdRM1d6SHZXcExZcjlPUjdhUGV0VGJkUVUvZWtiaUFUMzI3WSt2WDB5YUZoU3VodzlMK3RFU1VWaXZFZ1lhV1BXTmh6TGtWd2p6RFFja2VnYTN2alpiUFZVYVl1Y1BxbEtlYzBiZlVOam5kY3hFQVIvRmcxdlFSZUJyNUtQdGVRYVY4TmZDeXhEdVBmS1dOR2ZPanRsMzA1dHBIbFVYSHZCQ2ZkVXRBMFFVcVBuQ0FIdmRtZnVDUGxLa2hRRGVuNFRVSEQwU2hoQXB1TWdvc0wxbEhtQVd2OHZlT1lYVVMwRmZtRG00b3RlL1VPWHU3UDBJNzBSNVAiLCJtYWMiOiI3NjU0YzFiOTFlMDhhNDU1YmYyODMzMjAzM2MxNWY1OGIwMTM5ZmY5NzU0MTg5YjhmMWQ1NmJiMmUyMmJjMjIyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImprRWY4QzNlNG0xM3N6RE5ZQW5lMEE9PSIsInZhbHVlIjoiQjhyY044d0JjTUhoTkdJandYOXlZNzBrT25RaTJqNnduS21IYU1ZS3VCZzA5M1RvN1ZUMC80Qk9DTUVRSGVaakNrU21pNnZQS2wvNm82TGZxWWtjV0NzNS9KQ3FDT08yK1MzOXhGQitkZlNFYlNiK3pvVG9DV1B2TCs4clhQUkZvelZkUXMyL0wyMU1UMkt6SFV1OU40TGFIUGtMS1VmODBmWXhub3FrVUN6RWpYaC8xa2hiOS9BQ3JadzN2M0FqOXhnYmQxcUlIQ3BoUTFHMWdRWjRyR081NXd0aTM4ZWlvREF5UDlVdTE4a1k3cTE1M2lrRmtwM1J1RW1RcVk5SmRFazBveWhFRSthazVqNlFGV0tqQ2dYZWQ4Nlk2WWZQYzhUQnRNUXozalZid2ZWYUNiOXZOQzVSQlFWRXhHdVBrdnZnWmtyYzljSm9PZjdjenc5b2VidzhLS3lKSU5TVEdwK3NpR1VxdFIrc2tIYlpzTTlQb0xCdEYybWJ6MGVjVmhIN2F4TEJtUWZMeUNqMmdLcGZ1d1JDSDVHUDBzTW5EMk1iOWVUd1FQQXBqZG1maVQwd1ZoRHZpS2N4NDgvMXJqZnkrVFJ3d0RrUG5lZ3lLeFovS1lVMHJHRWtUai9uVzNxRHBlWEpjcTBEeGFpQWNSbWY1ZktmbFFWbU42NysiLCJtYWMiOiI5Y2RjMjBjNTU3ZWNjN2QwOTk2YTE2Mzk0ODMyNmIxMDlkOTY1NDYyNzk5MDY2MzlmY2MzZTJlZmM5Mzg5YWEyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833874473\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1309612438 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309612438\", {\"maxDepth\":0})</script>\n"}}