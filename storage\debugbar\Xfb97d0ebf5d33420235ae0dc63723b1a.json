{"__meta": {"id": "Xfb97d0ebf5d33420235ae0dc63723b1a", "datetime": "2025-07-14 18:28:56", "utime": **********.412223, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517735.964571, "end": **********.412237, "duration": 0.4476659297943115, "duration_str": "448ms", "measures": [{"label": "Booting", "start": 1752517735.964571, "relative_start": 0, "end": **********.340493, "relative_end": **********.340493, "duration": 0.37592196464538574, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.340502, "relative_start": 0.3759310245513916, "end": **********.412239, "relative_end": 2.1457672119140625e-06, "duration": 0.07173705101013184, "duration_str": "71.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0193, "accumulated_duration_str": "19.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.370004, "duration": 0.01844, "duration_str": "18.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.544}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.397615, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.544, "width_percent": 2.642}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4037051, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.187, "width_percent": 1.813}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjVjVWVLRjNpUTByQm91ekJDUnpnR2c9PSIsInZhbHVlIjoiaHZqYkFSNnJhbVVkanhtWVE5c09oUT09IiwibWFjIjoiMTA0NmU2OTlmMmM0YzE2MjM1ODhlYjFlNDk3ZTkwZjE5MGM1YTgzNzEyMWI4MDhiNGUzOTliYzU2M2MwMjMxMyIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1919952511 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1919952511\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-936638322 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-936638322\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1117898078 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117898078\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1252862105 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjVjVWVLRjNpUTByQm91ekJDUnpnR2c9PSIsInZhbHVlIjoiaHZqYkFSNnJhbVVkanhtWVE5c09oUT09IiwibWFjIjoiMTA0NmU2OTlmMmM0YzE2MjM1ODhlYjFlNDk3ZTkwZjE5MGM1YTgzNzEyMWI4MDhiNGUzOTliYzU2M2MwMjMxMyIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517731773%7C27%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNJTUZGNzhsV2g5M3RiL3Uxei85RHc9PSIsInZhbHVlIjoiL1E0bVFYNDI3TWtFMU45MWdCQ3ZHUEFjRWxiZ2lGQjVLd3A0QXh2dUE4cHdlR2t2ckljK3ZLZjlVS3J5RGpEcHNobmU5bzlpMUx1NGtSc0FNMVlLQWdFMnVMZnlVa0E5M0YrclFrZWhQc3E1SHZETTd1TDMvUm1LNCs0MVdzb3VUU25iY0M4UWVHdG01cGZZakt4bkNhMFdjY1FQeGFDQ1ltaURFd0FNNEwyK0dsaXRucGxEdFhZRjFYS3h6YVlQaDFDMGJkODB2a2g1TVJrZ04rTW9RZ3NYSGZ4TWdxTG5NT21SbjZGQXBudCtoTmJHZlk3REcrTlc5ZWpKdVNuQkhDWWhWN2NTL1B5dWxUODJpUllnemZlaVNFaFdLaUdJcXhUNlhWckJwaEltWlVpWG9pNU02RnVxU3Fmbkl3dHNRTERMZVVqZE5KS25FQlFxQjRVdWdQdE5xUU9YQ1pOYXViRlVIQldSMllTcVBld0l6d1pQMklWSzJpUEc5a1RUTzBldUN5SVc5MlVPNlBBMldQTk9maVlLM3dTTlAwT3ROck5OcENvbkV2M2ZKN3E0Q05KbEc5NUE0aGNVaVlSVzdWZ3hISDdGdnFKaVRXcUFJaXp1TkozaEpmMkRnd3BFeEs0SHY4MERYV0hOQklMYzZ2cVlIbGNiQ1BwL3RlMy8iLCJtYWMiOiI5ZTEwNjA3MTRiNzFkNjdhYmJmODJjMTMzNDYyMmMzNTg5M2UwODlhY2VjMWU5MWZlZTdmNDJlZmEwOTNjMWJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhGZ1NCclJPa3NmSEtuRHYyUHBpWGc9PSIsInZhbHVlIjoiTnQ4NzZyVGtrSW1GUzRNdWNIQXM4dkNVa0liQy9SazhUd2pwdzc5TW9VWWVNVTNtd1lPOU5vdTc4NERONS85cC9zWlVveDc4bWJhRG9QZ0crTmRzeW0ydUxTOXJNM1ljNlVQYUwvTmhPZmVtZmpaRXZsVk14QnV4MTlteWlXVU96YzJDWnU2L1hqbFpPM0xFa0RsYzdEOHlKU2F3SGN1b21jMFhNTTZ5cHBSeGhBU0Q0T25yME11bE1WRGNUV3kycTZ1WUZVc2VSeE5EQWxiMCtCbmwwem14OEpMVlBLUXF3dndRQ1pydm5uQjNWcXgreE1JM0QzZzBMc29VSXh1a2lTekVkL09rUUJ1UlVwZ2ZXNkYwaDNqQzhCV0M5aGxsS2FFS3JtZGJCR2RMcGZSeDJnZnBPdkZYa1RZNk1kU0xyS2lXMHZaYmE2MzhSR2NkNEVZSk9zWmpUQUF6RVU0eVVhWEFKUnBEK211QUJJMlIvb3F4b2JSV0JQeWY4VnN5Sy9JY0IyMmlNSEVQakNndmZDd1BmOHdoNi9EbzZJWTFvc3FWQUpNUUxWWmFGeFFGOTVVelJEVG5tUWxaVGxaS25pRDBzMFVGVmtMejB4Ykg4azJaUE1RcjNZbWIxb2xCNkpqOWRrcUh1emdpOTFORDVuOGsxa21YbkpnNW9lNWUiLCJtYWMiOiI1ODdkNTU5YWViOTIwYTJjODAyN2JkYTYwN2U2OTMzNzVlNDAxZGNiZjlkZDUwMjIwMDA1Y2Q4OWFkNmNiM2U5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252862105\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-396254814 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396254814\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-108041234 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:28:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllnL09iTWdpYVNGWVBrenJHaWN1OFE9PSIsInZhbHVlIjoiZm5PNTZrY3NNNm9sVTBDR1crdjZFd29qdUxQaXk1TWRScVUxZU1aUWVoNUcyeDVWZGdyd0M5M1FKdWdNeGIwQkNmZHBqb1hiVU5DV2N3dlRzOE41WVZuWGhMa2pvRzZuYm5ENERpSlFXbXNnMXFaS3dHOXdIc1lsZm1HVGQ5aVRHMGZKZUVzelhlS3Q3c0dHcUV6RkdnOGlRUmRSR3hDRy9GOUNGRDQrRUtqeUxaTm9MWk5UR3F6ZmowaGFmR2RIaXNxc1RvV3dUZUtHeis1RUVxeWwyc1NucjhHMEJzcmZHNm5GMlZ3M0MzVGVMZlUybDl0V1ZuYXZoQzhqQklyaGZab2l0ajR2bTU3SC95UDJxUnJtL2EwTWdFUFQ4bUNnd2RXbE81ZXBRam1mV0xSWHNoNzd2NFJrREFLNjBtZFV0UXVJSGk2WEp3TUZJK3pOY0pnWW8vOXpBNUNMQTcwL1IwalNrZElCT1BHb3N2c2c4YkFVekdOYzRTV0ZzcFdtTHltdXBSdVE2OHpWQnRENWRoZkk2bTdRcmcraWp3clgwNkJQSHh3SlJBV01JNDMyY2h5eGxyY2plTnd5eURhT3lJemdwbUZyenNZZ1BGUTFjTSt6Qmo0Y3FHbkNtaCtIV0hmRi91VUJJUTkvYTRhVDNjTmxsdUdUUzA1NEFlTFMiLCJtYWMiOiJkYjFjOTlkZWVjODYxMjFhYTZiNGI1YjgzM2ZiMTI2Y2MwYzRhZDZjOTU0MmRjYzc1OGJjZmE2MWQ5MzZlODk2IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9IR1pyMDB5SlJqbit3V0R4bDF4S3c9PSIsInZhbHVlIjoiZmJRVXFNbjkxWUk1YWdzVSsvc2hHRGpzVWNkc1Uybm05V2pOM2lDZVZKRS82bkU2b3puMC9keGxVSS9pQkwyT2hUcVhSbEx3bFoxMXBrZXBwKzdmRXhmeTZRdDBsNmRHb1d5Vzh2SnpaM0V1cTdGZllHbGRYTlAzRTZkdjVtTUpoOUE1bWJQTFVERTJNK1JqdW9ac1NmQXVEdWRWWDVKajRhSDRiRkxDenN5Z1RQTlp2WFJVR1NubzB5K0FEOFVUM3NEVHFBWGdOUUN1OEtTclEzRyswVnhmU0VuQ0VUVzVSNnpoK2V0RmxocTVsWW1iaFN5dlhZWHlrd0cydkZFTVlIbGFOcWhLSnloZW9KRWozN0JGc0xwWDlSeG9vdVlEWm4zWWIzWjMyMGduWDFDcWl1ZmxOZi9oUkRWS2pBTm52SHg2a21wTjV3YnRpemF2dlV5d1oyeEFwNnFQQ3ZoYUMwTzArUkRmdE5KclhmWUJLNkwrdndVSjZKd0V5NXZIcGZyUWI1U0x2cDdWeEZVQTlOL1lvWTY0ZEhDcW5PQnRRNDgvZU00TXFwS1RwYTRVMUZGWHlMRHlrL3Arem95TWdZYW1iNmxaTzBNK1M0OWx4L2pMVGNleDNLSHJjYUFCR1Q1Sk9pL0VSRVp6VllyUmFBc1Y4TlJlVXJySzZZWVUiLCJtYWMiOiIwYzE3OTk3NDBhZDBhOTNiOTQ3YjAyNGVkYmRhN2UzMWRkZGM1Y2QyZjExNzJkNzk2YWM3NjNmM2RjZWJiZGU4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllnL09iTWdpYVNGWVBrenJHaWN1OFE9PSIsInZhbHVlIjoiZm5PNTZrY3NNNm9sVTBDR1crdjZFd29qdUxQaXk1TWRScVUxZU1aUWVoNUcyeDVWZGdyd0M5M1FKdWdNeGIwQkNmZHBqb1hiVU5DV2N3dlRzOE41WVZuWGhMa2pvRzZuYm5ENERpSlFXbXNnMXFaS3dHOXdIc1lsZm1HVGQ5aVRHMGZKZUVzelhlS3Q3c0dHcUV6RkdnOGlRUmRSR3hDRy9GOUNGRDQrRUtqeUxaTm9MWk5UR3F6ZmowaGFmR2RIaXNxc1RvV3dUZUtHeis1RUVxeWwyc1NucjhHMEJzcmZHNm5GMlZ3M0MzVGVMZlUybDl0V1ZuYXZoQzhqQklyaGZab2l0ajR2bTU3SC95UDJxUnJtL2EwTWdFUFQ4bUNnd2RXbE81ZXBRam1mV0xSWHNoNzd2NFJrREFLNjBtZFV0UXVJSGk2WEp3TUZJK3pOY0pnWW8vOXpBNUNMQTcwL1IwalNrZElCT1BHb3N2c2c4YkFVekdOYzRTV0ZzcFdtTHltdXBSdVE2OHpWQnRENWRoZkk2bTdRcmcraWp3clgwNkJQSHh3SlJBV01JNDMyY2h5eGxyY2plTnd5eURhT3lJemdwbUZyenNZZ1BGUTFjTSt6Qmo0Y3FHbkNtaCtIV0hmRi91VUJJUTkvYTRhVDNjTmxsdUdUUzA1NEFlTFMiLCJtYWMiOiJkYjFjOTlkZWVjODYxMjFhYTZiNGI1YjgzM2ZiMTI2Y2MwYzRhZDZjOTU0MmRjYzc1OGJjZmE2MWQ5MzZlODk2IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9IR1pyMDB5SlJqbit3V0R4bDF4S3c9PSIsInZhbHVlIjoiZmJRVXFNbjkxWUk1YWdzVSsvc2hHRGpzVWNkc1Uybm05V2pOM2lDZVZKRS82bkU2b3puMC9keGxVSS9pQkwyT2hUcVhSbEx3bFoxMXBrZXBwKzdmRXhmeTZRdDBsNmRHb1d5Vzh2SnpaM0V1cTdGZllHbGRYTlAzRTZkdjVtTUpoOUE1bWJQTFVERTJNK1JqdW9ac1NmQXVEdWRWWDVKajRhSDRiRkxDenN5Z1RQTlp2WFJVR1NubzB5K0FEOFVUM3NEVHFBWGdOUUN1OEtTclEzRyswVnhmU0VuQ0VUVzVSNnpoK2V0RmxocTVsWW1iaFN5dlhZWHlrd0cydkZFTVlIbGFOcWhLSnloZW9KRWozN0JGc0xwWDlSeG9vdVlEWm4zWWIzWjMyMGduWDFDcWl1ZmxOZi9oUkRWS2pBTm52SHg2a21wTjV3YnRpemF2dlV5d1oyeEFwNnFQQ3ZoYUMwTzArUkRmdE5KclhmWUJLNkwrdndVSjZKd0V5NXZIcGZyUWI1U0x2cDdWeEZVQTlOL1lvWTY0ZEhDcW5PQnRRNDgvZU00TXFwS1RwYTRVMUZGWHlMRHlrL3Arem95TWdZYW1iNmxaTzBNK1M0OWx4L2pMVGNleDNLSHJjYUFCR1Q1Sk9pL0VSRVp6VllyUmFBc1Y4TlJlVXJySzZZWVUiLCJtYWMiOiIwYzE3OTk3NDBhZDBhOTNiOTQ3YjAyNGVkYmRhN2UzMWRkZGM1Y2QyZjExNzJkNzk2YWM3NjNmM2RjZWJiZGU4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108041234\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-82522510 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjVjVWVLRjNpUTByQm91ekJDUnpnR2c9PSIsInZhbHVlIjoiaHZqYkFSNnJhbVVkanhtWVE5c09oUT09IiwibWFjIjoiMTA0NmU2OTlmMmM0YzE2MjM1ODhlYjFlNDk3ZTkwZjE5MGM1YTgzNzEyMWI4MDhiNGUzOTliYzU2M2MwMjMxMyIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82522510\", {\"maxDepth\":0})</script>\n"}}