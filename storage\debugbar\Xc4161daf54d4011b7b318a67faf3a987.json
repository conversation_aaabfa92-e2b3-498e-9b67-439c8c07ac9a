{"__meta": {"id": "Xc4161daf54d4011b7b318a67faf3a987", "datetime": "2025-07-23 18:21:13", "utime": **********.553563, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.087829, "end": **********.553582, "duration": 0.46575284004211426, "duration_str": "466ms", "measures": [{"label": "Booting", "start": **********.087829, "relative_start": 0, "end": **********.469661, "relative_end": **********.469661, "duration": 0.3818318843841553, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.46967, "relative_start": 0.38184094429016113, "end": **********.553584, "relative_end": 2.1457672119140625e-06, "duration": 0.08391404151916504, "duration_str": "83.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023110000000000002, "accumulated_duration_str": "23.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.497351, "duration": 0.02214, "duration_str": "22.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.803}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.527997, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.803, "width_percent": 2.164}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.53461, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.966, "width_percent": 2.034}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-voucher/55\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-498497310 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-498497310\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-980040725 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-980040725\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1583102037 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583102037\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1516881160 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/receipt-voucher/55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294869709%7C11%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZtRU5QSjg3MUc0dFR4QWN0SmF2b0E9PSIsInZhbHVlIjoiWDFOeVBTVXhlYVJqbXhBaVFFbTByUHNYTXZlaTB2NEptR3YwS0o2VUxjbEJ2RE5YMEF0VzZXOE01OVozL1RtNTR6dDVlSmt0cVg1UkpvTW5tZThYRGxJNUp5bnNRcFJGVlNsSjQwMVhVc21MNE01Y3Jrd2dSRi84RzVYQ3JIcVQrSVI2ZW8rTGwwOTFWOUJGcFJXQi9uOWhpWTByai9ILzZHMkkzV1crRWQ1dlFiZWtQNlAwRzFyZ2E1UjFVZUxReUM0ME5LZDJtY2Rpd0RsT042aVd6bEZleWNNdDBGQ1VVak14bkczVVhySjV4YmgrR2tPR0xlc2s0Z2dKY3l1aGwyNmYvWU1oZnJPN1JqSHpmcDR1Tk1iTVlhS1dsak5wdXNJWHl6aS96MnA5NEJ3RWREWFl2Mys2S01yRUtwLzhHb2dKbFhpK1N3Q05aNlRBRzd4WnBtSVh1K3JMTE1memgyWWpYQzFjaHNLSWxkVXFkY05PRXIzWm9DcVdZODRWYTNYM1VDWGhrVnlSTTRqckxhWlZJNDA5bGpXL1dxT0Vpb1RNc1d3VTJHMkp2OFBIWWRmbzRLOTNDaXJGSlZWYk1kb1htMHI1WFFGWDdXZUVSQ29mNjBVWEV0U3MxS0Myc3o2UVRCdE82d09ZOExGWHAxcTFKWkEzM0E5a1pISjMiLCJtYWMiOiIxMTVhMTczNWQ1NTgyNjU0M2E2ZDNhN2IyMWMwNGM4MzI5Y2E0YTQ5NWZiOTYxNDRiZTBlNGUyZGRmZTViOThiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRvbm9PQlQrai9BTnNUbFBZcWYwT3c9PSIsInZhbHVlIjoiazVCTTBodmYyUmYwUUp0dXEvcS9nYVNmR2YyUEVUQVFySXVTM21VaHYvWkx0ekx2Wm9LcmRNVzNsdlhUUVhTVkx4TEFMRUM2RjJYaXlOejNZc2RjU1doeldzYk9JODhYRmhmMUtQZlN0Q0FTalZNOXdTeldoRUltWTZpTGc3OTZhS1dwOVVjQUxSOTZGNEp2M3Z2NFBrZERWUU1GbDVZMzZaNVpqZ0s5YTByWW9iNFNzSGlPYXluUUlpNGlOejhCL3dtczExRHlzcEtyNlJ0MjNlbXlKeDNzWDh4UndhTWZDck8waUNZYXNuMHFhbWlKL3duY3liMS9XaTltTkUwMEJLWmx3SDZacC94ZGVDQ1UxWFplcjVwQUVkQjZhSlNTalpmUFJZMFpPRDREcXF6ZGdJNjBjNjc1U2NGVU9hWm4yZzZ0SktXajMzdnkyTlFlRUgrazMyOXI5VnYxMzByZG1mWUcwYkhlTytjY3k4RDhoRitCeFNaTUM5eVZjcXlCMExjZzNSNkEwK2k1Z0RVWG5DT1RwWTJoeEhtMENnanppbE55YVgwNzhTSjRHbWU3R0VoYzhpRllhNG8vaXpXWjRNV29UY1VMS0FTcldXMGZCOFFLd1JsM2RxUDZqOEtSTm92Z0VvYnkzOCt2VWdpcVFiWVJFZ1VUbDc2KzRrWmwiLCJtYWMiOiI3YjQwMjg2Nzk1NzlkOWRjZTcxZDliYTllMDRjZjk1ODYzMTI3ZGM3YzA0YjIyMjc1MGVjMjk2NDE1NzIyMjIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516881160\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2003021211 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003021211\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1810742457 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBFeTJteDhLanRyZ1FGRXAwbXFVbEE9PSIsInZhbHVlIjoia1FReEpYcU54RURFdnV6cnZud2tlYTFlZm5za1Exd2ZlMmtHQndLT3NaeXJIUWh6VmVWUEgxRVczZFJyS1RUdTZxNDAyaTgyKytTVWlLZGpIYUxiQ0EvUnhwa3JNaHEraHBnN1l1Y3hDdFlVdEdObVBkbmlPeHNXL0ZwbU5vcUtUVWdybnZncEp6N2NVZ2RMQ3IycjlEaXcwYWFxT1F0OUU4dm9IYnpEbmdrT2FFc2dPR3d2UzV3a2xBMkw5YnlOWUNhM0VyaGlJVndXaEdvenBGZWd1VG1qTmo1Q1JudVRIU3h1c29NOVdyK0FBcEEwYXYxVjRoYnRkb3BsNUdtRUJlYStnRFRQU3BwYnVhSXkrRzViRlFwS3hFSTltYzBtR3psSFF1cU8zVEJVQ2Q3cXBERzhFZjF6NXlDNGg3WWFvZnBqeWRWb1dsTVk4bmRlVVBmMGNOQmpmN2xIVUVpYi9yQWJKOTJscUg1bmZWOFNSY0dzTDBjUTBaRFVmRngzK1J4aEM4b3NYOTV5YnErWFJMTGNkK0dzOFRXUlV6RmkrZzh0aHlxTkJVMWlyQ1ZLdWhVb2tRRndoOTQ0TlhMeE0yMy95dm5tYTlqaTRxTnNPdHpnOVdYeFVPQUxRU1hwa1JUdHZRNnk1Z2VUeXNocThHT015dmkzc3ZBdVVGZkYiLCJtYWMiOiI0MWIyYjhiMTIzZTA2YzMzN2FiODU0MzlmZWQwODYyNGRiZTlhZWQwZTViMGIxMDUwNWYxOTdjYjZiOTZiNzRkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVQakRvY1o2TG1kR0xaS28vbkZZUmc9PSIsInZhbHVlIjoia2g1Q1UwQm9oZHFlbnowMXRlUWl2ekl4UnlJeXJweEdPdDI5ZTZzeTVJcjMzNU9CcHhJOTFNQ0ZsaW4ra1dNbERWMmhERk5zZkdDQlhxQmFiN3pqZjNEaG1RQXdKSHN1bzd4UnRGNGsvQzBYcjBlOU9ZWWNoT3FEQnJxYzlhczFIUW9LTUNHei9iTUEzSjBkaEVoS3lhamIxemF2TDd3ZGdaYmVxbDM4QWs3V1dzSDFab1czc0VDQlY4QXkwYWVFRUUrNzR1VzVhaW1xc0pid21YQU5xK2kwZytsNGtBajZPZXpURlhpWk4vVjVTOUJPRkRuVE44Ulg3S0ZIeVJoeElvZE82UkdYTmlsQVNlcFArdjNaWHY3S0ZmRnBITVA3VTViRVMzalNVR0F2NzUveXVDNDd4VHMycE9rUlZmVmRVQzVDOEZld0VKRUFuZXVoNFRYQTB3Y3JTS3ZFUDlkUmJNZjlJOTIvazgxZkVqRC9sa3Y4V1JuVk9lSHFDQW9jWXZZZ0Q4RCtZMEJ0K05qZmNIRUx5Q0c5czkyc3dHSWxZYjExUlVZNmdicmNNcUR6WHEzbkZrSUI3THBEQjQ0OXc3REYxVFFwaXZ4alJNcWlnS2pYUUJMdlQzVUNLT2M4MHJpTUxXVE9IU2FkR1BZdDA0K1B1Tnhab0FrSExiM1IiLCJtYWMiOiI0NTBmYjQyOTE2OTEzNjJkNzFiZGE2ZjE2NzFmYjA0NTZjMDFkNGZmY2I1MjdlYjczMjFlMDk5NGU1Y2M5NjJkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBFeTJteDhLanRyZ1FGRXAwbXFVbEE9PSIsInZhbHVlIjoia1FReEpYcU54RURFdnV6cnZud2tlYTFlZm5za1Exd2ZlMmtHQndLT3NaeXJIUWh6VmVWUEgxRVczZFJyS1RUdTZxNDAyaTgyKytTVWlLZGpIYUxiQ0EvUnhwa3JNaHEraHBnN1l1Y3hDdFlVdEdObVBkbmlPeHNXL0ZwbU5vcUtUVWdybnZncEp6N2NVZ2RMQ3IycjlEaXcwYWFxT1F0OUU4dm9IYnpEbmdrT2FFc2dPR3d2UzV3a2xBMkw5YnlOWUNhM0VyaGlJVndXaEdvenBGZWd1VG1qTmo1Q1JudVRIU3h1c29NOVdyK0FBcEEwYXYxVjRoYnRkb3BsNUdtRUJlYStnRFRQU3BwYnVhSXkrRzViRlFwS3hFSTltYzBtR3psSFF1cU8zVEJVQ2Q3cXBERzhFZjF6NXlDNGg3WWFvZnBqeWRWb1dsTVk4bmRlVVBmMGNOQmpmN2xIVUVpYi9yQWJKOTJscUg1bmZWOFNSY0dzTDBjUTBaRFVmRngzK1J4aEM4b3NYOTV5YnErWFJMTGNkK0dzOFRXUlV6RmkrZzh0aHlxTkJVMWlyQ1ZLdWhVb2tRRndoOTQ0TlhMeE0yMy95dm5tYTlqaTRxTnNPdHpnOVdYeFVPQUxRU1hwa1JUdHZRNnk1Z2VUeXNocThHT015dmkzc3ZBdVVGZkYiLCJtYWMiOiI0MWIyYjhiMTIzZTA2YzMzN2FiODU0MzlmZWQwODYyNGRiZTlhZWQwZTViMGIxMDUwNWYxOTdjYjZiOTZiNzRkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVQakRvY1o2TG1kR0xaS28vbkZZUmc9PSIsInZhbHVlIjoia2g1Q1UwQm9oZHFlbnowMXRlUWl2ekl4UnlJeXJweEdPdDI5ZTZzeTVJcjMzNU9CcHhJOTFNQ0ZsaW4ra1dNbERWMmhERk5zZkdDQlhxQmFiN3pqZjNEaG1RQXdKSHN1bzd4UnRGNGsvQzBYcjBlOU9ZWWNoT3FEQnJxYzlhczFIUW9LTUNHei9iTUEzSjBkaEVoS3lhamIxemF2TDd3ZGdaYmVxbDM4QWs3V1dzSDFab1czc0VDQlY4QXkwYWVFRUUrNzR1VzVhaW1xc0pid21YQU5xK2kwZytsNGtBajZPZXpURlhpWk4vVjVTOUJPRkRuVE44Ulg3S0ZIeVJoeElvZE82UkdYTmlsQVNlcFArdjNaWHY3S0ZmRnBITVA3VTViRVMzalNVR0F2NzUveXVDNDd4VHMycE9rUlZmVmRVQzVDOEZld0VKRUFuZXVoNFRYQTB3Y3JTS3ZFUDlkUmJNZjlJOTIvazgxZkVqRC9sa3Y4V1JuVk9lSHFDQW9jWXZZZ0Q4RCtZMEJ0K05qZmNIRUx5Q0c5czkyc3dHSWxZYjExUlVZNmdicmNNcUR6WHEzbkZrSUI3THBEQjQ0OXc3REYxVFFwaXZ4alJNcWlnS2pYUUJMdlQzVUNLT2M4MHJpTUxXVE9IU2FkR1BZdDA0K1B1Tnhab0FrSExiM1IiLCJtYWMiOiI0NTBmYjQyOTE2OTEzNjJkNzFiZGE2ZjE2NzFmYjA0NTZjMDFkNGZmY2I1MjdlYjczMjFlMDk5NGU1Y2M5NjJkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810742457\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1917897708 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/receipt-voucher/55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917897708\", {\"maxDepth\":0})</script>\n"}}