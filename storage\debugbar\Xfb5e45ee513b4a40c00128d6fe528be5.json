{"__meta": {"id": "Xfb5e45ee513b4a40c00128d6fe528be5", "datetime": "2025-07-21 01:17:57", "utime": **********.191194, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060676.738026, "end": **********.191217, "duration": 0.45319104194641113, "duration_str": "453ms", "measures": [{"label": "Booting", "start": 1753060676.738026, "relative_start": 0, "end": **********.108311, "relative_end": **********.108311, "duration": 0.3702850341796875, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.10833, "relative_start": 0.3703041076660156, "end": **********.191219, "relative_end": 2.1457672119140625e-06, "duration": 0.08288908004760742, "duration_str": "82.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48482296, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-223</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00628, "accumulated_duration_str": "6.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.147189, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 29.299}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.157562, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 29.299, "width_percent": 10.191}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.173481, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 39.49, "width_percent": 8.28}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.176074, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 47.771, "width_percent": 9.236}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1815908, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 57.006, "width_percent": 42.994}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-511073653 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511073653\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.18047, "xdebug_link": null}]}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1848604270 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1848604270\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1481147457 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1481147457\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1305811695 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1305811695\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1143631693 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060674456%7C6%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1LeVZqUFdIaDNKbGo5K2NQbkh3YlE9PSIsInZhbHVlIjoiQzNKSWt3cDIycHRRUS9uWGs5ZE9abXYyY1NiaEhLTkNwdVBZU0NjS1B5VFIza1gzdG5BbUtXakNoOWJYL1JSalVxS0ZvNkhWZ3MwRDdLZG1DT2duVGdaSGVBMFlIamtSV0hBSjhnNzc4eXpkNkF0Y0FxWmVtb2dOaXIrMWFqck9jTzlBaEJralhFLzQwc3dJRXpra2JvaTk0T01IdEFvOGlZamMzUHF3amtIMUdTdkdwT3hWYlJZL3BkMUZLYk9CK1VhemtwVVpFcTdKQ0xGa1JjYXM0N1BaNW1BN0lETmh2L2dVUVYrRTA2bDlTZDBiMTA1WWU3bmkrMFkzY3MxZzZzVUVBb0hoMTQzRkN0TXdqNHZPREJqS044Y0dIRDhPN3RjZVZNRXRsb0RMM3dGYVQ3Qjg5QmFMdVg2akw2LzZKbmc1T0s0ZnEzMlpNYmN2V0M0bHBZRGNHbm9aUXZHTUpSTEdud2drQjZhWTNwVVNjQkh5N3BPOXM3Z0s5QWdqOG5CN3pRaGs5a2lzWTJmM2xkV05kdjVVOTZYU0VRRTFWb04rcXdhek1nRDNsUlBvR3ZXT2JRNGdzRTV6dVVpMGk3cWlCMVRhSFQyZkpycm5FY2JzYlE0WXQ4TXVTclAxWXhveWRDdkhCaDBSRFkrNENHS3lBMExWSlY4R0gvYkUiLCJtYWMiOiJjNmU1YThlY2ZlOTkxZjg3M2I5ODkxNGM1MDk2OWEyZGU2NmI5YmNiZTI5YTk5YzFkN2VjNWNlMGRkYWRhZWU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhNNVFKa1hsTkdJQlIxVE5zNU5RYUE9PSIsInZhbHVlIjoiTlVGV0RRUlB0QjdxL2tMRHFmNXdhKzNxczZ0amczYURTWHk3Mmpwenp4M0VpY2F3UDh6cm5LaGVzWXZwbmZuakI5eWJYU1dkb2V4eGNZTUxTRENaaWZ1U3BsL2RMbUIzcm9RZDhFbzhweVUwNWFacUI5TzR6RUd1SUFSb0NSQWM5Mk5FOENIUWFTaHJZSVc3TDRBbTFFaXJjSHpkV2ZpTVBwdzZSYmFMa1ZJbi9ScFNZY0FNY1ArTHlPdVIvb0RGdmVRRVk1MElZdG1LeEpxUU9RM012bXo3b2lwbjc5MGJIMHFlMnVuUkZQTHozd0R4Z3lWQlpOY3A2dUtNQU1FdllqNTNsWjNwa203UlpHSStGVzVXSWFmQTV4aTNzNTh2ZUZMNUk5QklIVnI1T1NpOTg2MDVhamt3aG1aM3p1eVR1SEhQRGZpNHJndEFRNHpWeHQwK1g5L2wwUHhORW1rU0ZxU2dnOXk3dEtyamxpNkd1QXpWRWFmWmxNRkdjZUZsNDJIWlJxVXB2QTBNWWRXb3JvU2FCeFZLU2dSL2ZKcDQwRXR2Y2tRK1oweFY4T2UxaW8yWkhGendkS1JGaVVKdkNZZkh6amZ2UjhML3BCcmcrbTk0cVdqcVB6WjVyVTh6eTVGMGNZQUFEcWgrUUlvTjhYb1BuYzJyTG5zVjVBS3giLCJtYWMiOiJjZjdhODFhNWZiYzA1MTFkYjhkZDA1ZWQxMzVkOTA2NTljOGNmNzVkNTgyNTk5ZTc5YmY5ZjY1MTU4OWY5NmI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143631693\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-730251658 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730251658\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1936515468 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkgrUE5HQmJudWVmOS9zbHNOdDhCZXc9PSIsInZhbHVlIjoiRm1hNWpCcE5rSkpBbWxndm9QamdUVFdFYzdZOC9MKzJUZUJXcVFJSmFMbHRybXUyTEJDT2JnR3hLak5HbnA5ZTg0QzM1RkJTdGhUUGFhZTBucGNtcTlpNWVuRVpiRXJNa0tIL05iaWxHNUFjS2dtVkVmU1VWbEluTTVKNFpEaHh1Z0hrYmUwSjJubSsvQmo4dXQzMHVRNDVDZmF5R2UzUEROV3FrMXN2K2RsWWVQTXB0NUFIMSt0dkkwZkl5ZU9DQ2hpc0Q3NVU1L1VGYmtlczZaR1ppV3RNUkZZUzNLZnp0VEhPK2NScWM3aXNlM0M4Vlo3T3FvOUtlR3JyY0IxL3QxeExNdkJJb1lWanE5bW5WOEpHTkNoYytmS2hnSWhaUUJRSlRiUFVjbGNJeGt5NjVuU2syOFIvbmlubnl1bmVSY0xodFVjVDFEY2pUUytBdlQ0MUt4NXZSemxZUjZHSXJ4cFByeFNjOTR5QnJCa2JoYkRNdEdFTjhKUUhDZDdvd1daWXJ3dTdqanZzZFh3RHdpeCtwbE5DNm05dXBXTy9QdmJxbDdkOTF5MFBwM1ZTMVF6NmxXUkI4cURVWi9aeUFESjRTV01kMkxkd3IvRW5hNFo2U3lLd2xVVldhbG52VXFBMlZMNXFVMHFOZW03QStWWklSTEtHU2g3SWczY20iLCJtYWMiOiJkNDI2NmRjNjE3M2RiZDI2Nzg5NDJhYTE3NGZkZWMyZTliNDE4ODg1MGRhMTk4NjM3MzE1ZWQzZDIwYTU3MTQ3IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRYdmYwZFpKUXM4RlBiYlNvQkM0eEE9PSIsInZhbHVlIjoialprRHp6VlZXRDluM3dobXNJOFlJbDJhbm5xajR6UWs2d2FwdWtOcVdLS2MrV1llTVZ3bWhMbzY4aFZOWU5SVWVQSnNZb1phQXlYMU9zcUxQQmc3Ynk2QXpMNVY4T3hPWjhuc3RlN0hrdldoYWVHYysxTzI1T284bW5xTlNpMWM1eTR2WGloc29JbEZ5RG9pNUdSN3R4TXlOL1h1MnZoUFNTd3B4UEtWcVRLM3Y5dlRnVG5Ybk5XTlpZTUhvK2gyb25aZnFsMVM1Y1JPdkN3cHpmSy9IbjEvUUNka05CTUl1ZkVOaHkrS09OcnFHaFhiempNcmpTa05pUlpSYzNoQUpaemtFL1cxQ1d0YzdRSkxsMkZVeHRsZkVTYTF4WXd0bXN4ZzI5KytZU0QxWHhDV1AzeEJhdzhrU28reUE1bGJMbXZubldCOVAweENoSHE1RXIyc3czRUUvclRWbmdzUXc4cWtiWTF4Z0dHS0NvczN1azNUbGVxbzMzS1pRYys5Q0dOdzZNWlp5d2xNeG0reWI4a2I0M2p3QzRsMHRPOEM0ZFhldk5QRUp6c2dmS0M5N0puL1g5K2M0T2lhbDdpWm52ZnNVZlRZcVdWRDEzYTY0VkVlcGUvWEZSSWJaWVVrT2ZCWFBuNlA4MVdVVkJhK0s3bjlGTlpBUVlBbUpLcFIiLCJtYWMiOiI4N2Q2ZDM5MGM5MmRmNzI0MjZjMWFiYmVkN2FiZTM5OGNmNjA3NTkwMDFlOWEzYjQ0MDg5NTc5MTgyYmEwMDljIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkgrUE5HQmJudWVmOS9zbHNOdDhCZXc9PSIsInZhbHVlIjoiRm1hNWpCcE5rSkpBbWxndm9QamdUVFdFYzdZOC9MKzJUZUJXcVFJSmFMbHRybXUyTEJDT2JnR3hLak5HbnA5ZTg0QzM1RkJTdGhUUGFhZTBucGNtcTlpNWVuRVpiRXJNa0tIL05iaWxHNUFjS2dtVkVmU1VWbEluTTVKNFpEaHh1Z0hrYmUwSjJubSsvQmo4dXQzMHVRNDVDZmF5R2UzUEROV3FrMXN2K2RsWWVQTXB0NUFIMSt0dkkwZkl5ZU9DQ2hpc0Q3NVU1L1VGYmtlczZaR1ppV3RNUkZZUzNLZnp0VEhPK2NScWM3aXNlM0M4Vlo3T3FvOUtlR3JyY0IxL3QxeExNdkJJb1lWanE5bW5WOEpHTkNoYytmS2hnSWhaUUJRSlRiUFVjbGNJeGt5NjVuU2syOFIvbmlubnl1bmVSY0xodFVjVDFEY2pUUytBdlQ0MUt4NXZSemxZUjZHSXJ4cFByeFNjOTR5QnJCa2JoYkRNdEdFTjhKUUhDZDdvd1daWXJ3dTdqanZzZFh3RHdpeCtwbE5DNm05dXBXTy9QdmJxbDdkOTF5MFBwM1ZTMVF6NmxXUkI4cURVWi9aeUFESjRTV01kMkxkd3IvRW5hNFo2U3lLd2xVVldhbG52VXFBMlZMNXFVMHFOZW03QStWWklSTEtHU2g3SWczY20iLCJtYWMiOiJkNDI2NmRjNjE3M2RiZDI2Nzg5NDJhYTE3NGZkZWMyZTliNDE4ODg1MGRhMTk4NjM3MzE1ZWQzZDIwYTU3MTQ3IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRYdmYwZFpKUXM4RlBiYlNvQkM0eEE9PSIsInZhbHVlIjoialprRHp6VlZXRDluM3dobXNJOFlJbDJhbm5xajR6UWs2d2FwdWtOcVdLS2MrV1llTVZ3bWhMbzY4aFZOWU5SVWVQSnNZb1phQXlYMU9zcUxQQmc3Ynk2QXpMNVY4T3hPWjhuc3RlN0hrdldoYWVHYysxTzI1T284bW5xTlNpMWM1eTR2WGloc29JbEZ5RG9pNUdSN3R4TXlOL1h1MnZoUFNTd3B4UEtWcVRLM3Y5dlRnVG5Ybk5XTlpZTUhvK2gyb25aZnFsMVM1Y1JPdkN3cHpmSy9IbjEvUUNka05CTUl1ZkVOaHkrS09OcnFHaFhiempNcmpTa05pUlpSYzNoQUpaemtFL1cxQ1d0YzdRSkxsMkZVeHRsZkVTYTF4WXd0bXN4ZzI5KytZU0QxWHhDV1AzeEJhdzhrU28reUE1bGJMbXZubldCOVAweENoSHE1RXIyc3czRUUvclRWbmdzUXc4cWtiWTF4Z0dHS0NvczN1azNUbGVxbzMzS1pRYys5Q0dOdzZNWlp5d2xNeG0reWI4a2I0M2p3QzRsMHRPOEM0ZFhldk5QRUp6c2dmS0M5N0puL1g5K2M0T2lhbDdpWm52ZnNVZlRZcVdWRDEzYTY0VkVlcGUvWEZSSWJaWVVrT2ZCWFBuNlA4MVdVVkJhK0s3bjlGTlpBUVlBbUpLcFIiLCJtYWMiOiI4N2Q2ZDM5MGM5MmRmNzI0MjZjMWFiYmVkN2FiZTM5OGNmNjA3NTkwMDFlOWEzYjQ0MDg5NTc5MTgyYmEwMDljIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936515468\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1494077437 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1494077437\", {\"maxDepth\":0})</script>\n"}}