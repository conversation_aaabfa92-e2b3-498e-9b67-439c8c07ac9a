{"__meta": {"id": "X15adf1efe4a2364d0d3d27fa0eff7803", "datetime": "2025-07-23 18:21:16", "utime": **********.78122, "method": "POST", "uri": "/receipt-voucher/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.326149, "end": **********.781239, "duration": 0.4550900459289551, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.326149, "relative_start": 0, "end": **********.683097, "relative_end": **********.683097, "duration": 0.3569478988647461, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.683105, "relative_start": 0.35695600509643555, "end": **********.78124, "relative_end": 9.5367431640625e-07, "duration": 0.09813499450683594, "duration_str": "98.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46713984, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST receipt-voucher/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptVoucherController@confirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "receipt.voucher.confirm", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=146\" onclick=\"\">app/Http/Controllers/ReceiptVoucherController.php:146-172</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.010320000000000001, "accumulated_duration_str": "10.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7252672, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.023}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.741545, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 18.023, "width_percent": 4.942}, {"sql": "select * from `voucher_receipts` where `id` = '55' limit 1", "type": "query", "params": [], "bindings": ["55"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 151}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.744241, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:151", "source": "app/Http/Controllers/ReceiptVoucherController.php:151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=151", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "151"}, "connection": "kdmkjkqknb", "start_percent": 22.965, "width_percent": 3.198}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7468638, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:151", "source": "app/Http/Controllers/ReceiptVoucherController.php:151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=151", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "151"}, "connection": "kdmkjkqknb", "start_percent": 26.163, "width_percent": 2.229}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7482638, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:151", "source": "app/Http/Controllers/ReceiptVoucherController.php:151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=151", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "151"}, "connection": "kdmkjkqknb", "start_percent": 28.391, "width_percent": 2.519}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 346}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7507992, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:346", "source": "app/Services/FinancialRecordService.php:346", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=346", "ajax": false, "filename": "FinancialRecordService.php", "line": "346"}, "connection": "kdmkjkqknb", "start_percent": 30.911, "width_percent": 4.264}, {"sql": "select * from `financial_records` where `shift_id` = 122 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["122"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 352}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.753665, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:352", "source": "app/Services/FinancialRecordService.php:352", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=352", "ajax": false, "filename": "FinancialRecordService.php", "line": "352"}, "connection": "kdmkjkqknb", "start_percent": 35.174, "width_percent": 5.233}, {"sql": "select * from `financial_records` where (`id` = 122) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["122"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 364}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.755517, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:364", "source": "app/Services/FinancialRecordService.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=364", "ajax": false, "filename": "FinancialRecordService.php", "line": "364"}, "connection": "kdmkjkqknb", "start_percent": 40.407, "width_percent": 2.81}, {"sql": "update `financial_records` set `current_cash` = 1004, `total_cash` = 1104, `financial_records`.`updated_at` = '2025-07-23 18:21:16' where `id` = 122", "type": "query", "params": [], "bindings": ["1004", "1104", "2025-07-23 18:21:16", "122"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 364}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.757013, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:364", "source": "app/Services/FinancialRecordService.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=364", "ajax": false, "filename": "FinancialRecordService.php", "line": "364"}, "connection": "kdmkjkqknb", "start_percent": 43.217, "width_percent": 28.585}, {"sql": "update `voucher_receipts` set `status` = 'accepted', `approved_at` = '2025-07-23 18:21:16', `voucher_receipts`.`updated_at` = '2025-07-23 18:21:16' where `id` = '55'", "type": "query", "params": [], "bindings": ["accepted", "2025-07-23 18:21:16", "2025-07-23 18:21:16", "55"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7625122, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:162", "source": "app/Http/Controllers/ReceiptVoucherController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=162", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 71.802, "width_percent": 28.198}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\ReceiptVoucher": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FReceiptVoucher.php&line=1", "ajax": false, "filename": "ReceiptVoucher.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-voucher/55\"\n]", "success": "Receipt Voucher has been Accepted successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-voucher/confirm", "status_code": "<pre class=sf-dump id=sf-dump-1397256796 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1397256796\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1409343714 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1409343714\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-460154322 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">55</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460154322\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-875126478 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/receipt-voucher/55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; XSRF-TOKEN=eyJpdiI6ImpQdVVHTkp4ZnhQMTZKL2QyNU9Janc9PSIsInZhbHVlIjoiU0JWcUdpMjBKNURTeDlRMUFPK2tNQStrKzBWMVluN3M0dTNXQlJvVFFZSS9RSThMTVNNaVRGc05QL3hoYUd4WmRTaVhMVEZ0V2FqOHRCemp4VTJhUURwTDFKMm13NEhlUTM0NldDL0dYMzNxNlI1dUtYSzdYazVMUWVXQnEybmFUVzFsUzVMMTJpc3JGK1A0b2xPK2kvOWVsZDg0ZElMa1lXYnpOb05Ya3hYMDlPVWtPazlvM3NwV0J0WTZKa0VPanY0dmllUjlrR1N0RThmSDRINUMvTkxleDBNekdMYVZ5UlNvWW5zTWRhZE1RRG1RZ3Z5TXRSbC9Sd1djS0hlbnBLN2lhVTJjbXc4NnVob24rbjJOSHBYNmRXQmh5VmVjYkdCL3JrSWRtNmI3NTRDNEVCNzNKUng5UlBTSEM0ckxodTI5QU5lajRvMTJ6VlZSVkc1MGNIcEZMWWV3dThYZUFXUlVMaGZBa0tmdlo2bWpra2w3VVVldTNjeWNYbGRwY3ZrUDJNYjJJb0JudktVekJ6Z1VCdFZuMkkySUk2OURIVUpqblcrWDlUdFREN3h5bFRySTV3TlgvOHZKeTJLV25PTDM3RVAwTGd3eWhtSDBJU3FDVHhWbnFXZXU2aUVpNk8yaEVONVRlcFRsUkg4amVEWER5TjNNV29tcmZaTGwiLCJtYWMiOiJkODhlNWE3ZTJmMjMzODM0ZWJhM2Q1MTNmNjYxY2Y1MmE4MGVhMGZhNjU1YzUzMTg4ZDdkYWFiZjc3Y2QyYjBiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlVWFpjMzRRR2ovY3NqTjlWbkVtZ1E9PSIsInZhbHVlIjoiV1JycVlHSnQ5NmZpdnFRMkxTNHlRRmtEMnZRcWpwZmZuRTVwalBIMkVYTFpTTmZENjZOZEdoeGJkeHFOYmszOGZMTjhuQ29WV0FtaW1WajZwb2N6ZGF4a1N0VHVHNFJoYlBsL2U4R0owYlpLcmV6dWZFVVJXOHNucEsycFJGc3pUTG9GR3hlY3dnV0x2SE5aOHM3eUw1ZFNjallmRTE0SFpBamlKckRqK2dJZVplZHFRdXNPL1Z1R0ZxcElEZ3lKWHU3VjRqRWFWUzRXajNmQ0lBak9lNkFnTG45bGQvNVd5anZqZlB0UGNIaHFuWGtOaU9hQnNKOVRsWXBOMGF2MGFoc3Z5VUczVFFEWmJvMlBlYlFXaTFxVHBtVGtvcU90TldNQXZxSzVDMFhha0hYV2NOS0FJQkJoZE5LbmF3YnF5d1JjWEQxTkNVUkY2VjF2K2V2ODNFZXJ2dnJHUzVWZUhFdkdsQ1h1KzJsb3RrbEhPS3h5ZjNWMVkxODZBMUdKVzJ0T0M3Mzd3L1pQbWk1aFE2UmdrQkVQQk1DMVVkSEEvV2xvbU05UzVzRTNsQ2dHWjRTMFhpM29aSVRGTVFhb25NRk1kWUNkdHN4MkxIY2RQN0U2TXZyK1pNZkZTZFhERFhJZmJjY1dtdDkrMWVCbkxBQm5jcVB2aFY0bWtQRlQiLCJtYWMiOiJjOTg1NDQ1OTNiMDliNDliOWI2NGRjYmY1YjkyYTk3NzNhNmRiNzA3NTVkMWFlZTA2ODQwYWM0YWY0MmMzZjZjIiwidGFnIjoiIn0%3D; _clsk=c6ed4t%7C1753294875191%7C13%7C1%7Ca.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875126478\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-897025412 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897025412\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1322326835 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/receipt-voucher/55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkIvMUJIdTBpOWFxYUhFZXdYWk11S3c9PSIsInZhbHVlIjoiZHZLZk5OY3BhQ0cyMzFMeS9aZnNrOTQ0d05sQXowSjhkSklSQzRsN21jdTdBc3Z5OFF0N0ZLUjJNMVV6RGhFTnFGQUNFeTRzbjVpYW9SRVFiZGkrWW90NWQzeHpLWWpobnRkRnA2QWFmRFcxQ3kzRmJWK2pOT01Nem10K2w3eW9TcFpZZ1djMDB6N0hHN2ptdWIzUzlaNmhjSjJMbDYzaldRWENpOUZFUHNocHhwcmdoK3hzK3FsMjJLNEZqZnh0TWN5YTJiM2lha0NpSVFZa0UwTDByNVJjdXJVaXhrVzhYOVhIUlZhK3hsYThpcUdmdkVnVkZ0a1phVko4R1pmMWpXT1ZhZ01NL3pEL0pEb1VCNWJVY0tUendtRFpzQ09MV2laUjhha2V5VlZiY2g3TXE5Wkg4L2s0bTJUWGlYY0d3ajVCc1dtQndaRVc4bnh3WDFLQy8raVZJTXY4YmVKeVBGWFJURko4S3NIdEt1YU9KQktPYTFlbHdheFEyUC9tMElaNVZZOSs3QktHNmpkdlNJcnVFdmxreWJOQzY5cHJyYUF4anFId0cybFRDNmVCeS9qUTlNeTEzL0l5b3FGd2JldDl4aURGakJaUERUQUJIaU1XWm1TUExGYkY1SEE4UWRoNVJZVCs0Q3F6c2lwUFhBVUtVTWZTZkE4ai9GQW8iLCJtYWMiOiI5NTczZmY4YmQ1MTRlN2I0NjJkM2Q5YTFkMTViNDhkMWY5Y2FhNzAwNDcyNmFhOTkyNDA3MzU1NDQxNzhhYTdhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikg1Y1NZa3AxVlRlMzhmZmRraGtUTEE9PSIsInZhbHVlIjoiUnN6clpxYWhUKzJmZC9rSnlmelQ1V0YyV1F4SERRTzkvR3F6TVltUzRQTkE4VW81ZHhEYVY5S0xVeXNBUzRUUk1hcUpmT0xqUHUvRFBJbjBCTGI0bjFKeXlIamNkTjBGQmcwaWt2OU1ISlpFcG84Y2dXeHgyY09kdmhOSWdoREFvY3FURDRzbmNsNWZYVUpWTzNFRnZlYXJZSjhjSVMwN0xQclg1M2FUR1V0dTF2VUQvK2tyekNuSmNaTnVJbllSTTBqd0VxQWRsOEYrUHU2MkZtYVRYUHlyVDRrKzF2Y2VnbFZYcDdPaWxrNkIvZlhnT3EzOTl0NXFrMHJhQXlGVjgwTWJLZVpNY3RNUnRjS0tXVEpWY1RpSDIwRHVUU2IwYUdjWGxPT3p3eDY4Z1BQOGtKMmVEdUpYOEVyVUNRNyt2NGY4TFE0RHc0Wnp5OFJpeWhERzhoOEZjRmxNaHZRS2g1RTF5aVhSSi90bGQ5bktDTXRCTnhwS2p5QUgveUdaT0xTTmNVOGdQdktPZEN2czh5TmlldmtkWG9SeklXV1Rqbkx5VEYxYXdiTDVVUGIyai9xVm9BTFArZGFhOUtxSjhGMTNkTjRTeDdjVjZGeS9nK205WENaZmVWU0FEV2o1WTlqVllROFMxaGlGTWZVMkdTbCtHTVZjU3RjWTk1SVUiLCJtYWMiOiIxZjQ1M2E4ZGVmNjM0ZWRmZTNmMmJmOTgwZDg1MjYxMmNhM2E5NzhiMzU2NTIyMGVjNWI1YjNjOTE4MmQ2OWQ5IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkIvMUJIdTBpOWFxYUhFZXdYWk11S3c9PSIsInZhbHVlIjoiZHZLZk5OY3BhQ0cyMzFMeS9aZnNrOTQ0d05sQXowSjhkSklSQzRsN21jdTdBc3Z5OFF0N0ZLUjJNMVV6RGhFTnFGQUNFeTRzbjVpYW9SRVFiZGkrWW90NWQzeHpLWWpobnRkRnA2QWFmRFcxQ3kzRmJWK2pOT01Nem10K2w3eW9TcFpZZ1djMDB6N0hHN2ptdWIzUzlaNmhjSjJMbDYzaldRWENpOUZFUHNocHhwcmdoK3hzK3FsMjJLNEZqZnh0TWN5YTJiM2lha0NpSVFZa0UwTDByNVJjdXJVaXhrVzhYOVhIUlZhK3hsYThpcUdmdkVnVkZ0a1phVko4R1pmMWpXT1ZhZ01NL3pEL0pEb1VCNWJVY0tUendtRFpzQ09MV2laUjhha2V5VlZiY2g3TXE5Wkg4L2s0bTJUWGlYY0d3ajVCc1dtQndaRVc4bnh3WDFLQy8raVZJTXY4YmVKeVBGWFJURko4S3NIdEt1YU9KQktPYTFlbHdheFEyUC9tMElaNVZZOSs3QktHNmpkdlNJcnVFdmxreWJOQzY5cHJyYUF4anFId0cybFRDNmVCeS9qUTlNeTEzL0l5b3FGd2JldDl4aURGakJaUERUQUJIaU1XWm1TUExGYkY1SEE4UWRoNVJZVCs0Q3F6c2lwUFhBVUtVTWZTZkE4ai9GQW8iLCJtYWMiOiI5NTczZmY4YmQ1MTRlN2I0NjJkM2Q5YTFkMTViNDhkMWY5Y2FhNzAwNDcyNmFhOTkyNDA3MzU1NDQxNzhhYTdhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikg1Y1NZa3AxVlRlMzhmZmRraGtUTEE9PSIsInZhbHVlIjoiUnN6clpxYWhUKzJmZC9rSnlmelQ1V0YyV1F4SERRTzkvR3F6TVltUzRQTkE4VW81ZHhEYVY5S0xVeXNBUzRUUk1hcUpmT0xqUHUvRFBJbjBCTGI0bjFKeXlIamNkTjBGQmcwaWt2OU1ISlpFcG84Y2dXeHgyY09kdmhOSWdoREFvY3FURDRzbmNsNWZYVUpWTzNFRnZlYXJZSjhjSVMwN0xQclg1M2FUR1V0dTF2VUQvK2tyekNuSmNaTnVJbllSTTBqd0VxQWRsOEYrUHU2MkZtYVRYUHlyVDRrKzF2Y2VnbFZYcDdPaWxrNkIvZlhnT3EzOTl0NXFrMHJhQXlGVjgwTWJLZVpNY3RNUnRjS0tXVEpWY1RpSDIwRHVUU2IwYUdjWGxPT3p3eDY4Z1BQOGtKMmVEdUpYOEVyVUNRNyt2NGY4TFE0RHc0Wnp5OFJpeWhERzhoOEZjRmxNaHZRS2g1RTF5aVhSSi90bGQ5bktDTXRCTnhwS2p5QUgveUdaT0xTTmNVOGdQdktPZEN2czh5TmlldmtkWG9SeklXV1Rqbkx5VEYxYXdiTDVVUGIyai9xVm9BTFArZGFhOUtxSjhGMTNkTjRTeDdjVjZGeS9nK205WENaZmVWU0FEV2o1WTlqVllROFMxaGlGTWZVMkdTbCtHTVZjU3RjWTk1SVUiLCJtYWMiOiIxZjQ1M2E4ZGVmNjM0ZWRmZTNmMmJmOTgwZDg1MjYxMmNhM2E5NzhiMzU2NTIyMGVjNWI1YjNjOTE4MmQ2OWQ5IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322326835\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784098088 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/receipt-voucher/55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Receipt Voucher has been Accepted successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784098088\", {\"maxDepth\":0})</script>\n"}}