{"__meta": {"id": "X1aa22b914b5007caff7f8a57494da717", "datetime": "2025-07-21 01:18:35", "utime": **********.202418, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060714.756026, "end": **********.202434, "duration": 0.4464080333709717, "duration_str": "446ms", "measures": [{"label": "Booting", "start": 1753060714.756026, "relative_start": 0, "end": **********.136421, "relative_end": **********.136421, "duration": 0.38039493560791016, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.136428, "relative_start": 0.3804020881652832, "end": **********.202436, "relative_end": 1.9073486328125e-06, "duration": 0.06600785255432129, "duration_str": "66.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46518696, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01591, "accumulated_duration_str": "15.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.163907, "duration": 0.01491, "duration_str": "14.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.715}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1869, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.715, "width_percent": 3.394}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.193468, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.109, "width_percent": 2.891}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-561945614 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-561945614\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1362270065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1362270065\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1176703189 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176703189\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-498597183 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060712344%7C11%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRwMWhuMHQ4bVhmZjMrdGxVVTR0WkE9PSIsInZhbHVlIjoieHlpQnJycmxvMkZYM2FnQU9SYkZZcjVKWGxnU3loRjF4NHUwZW5SWGQ3QkxxUU9pcTd6OU5KSmRaZW1Db3VvaE5aV3pHQmIzV2JKZnI3VVZBbzdwY0dQOEZ5TmRqUU5XNGFxNEhjeC9OQ3lEbm92eGtjbnlSZFZzcHNrTi9ETkVlNUd3ZlJJdVZMVHJ2UTlJclhPRGVPa05OcXhHZXZCQy8zYXh4dUFVQzFDaDhsYlNpV2t2cytBendRUGdvVDRpeGQ2NVM3N0diVTRoMHFYdEJsUW0wTEFla0JCVWVYc1A5S0xMK3dNZGRHcndseEdlUzJZZmdEWFBlRzJaZzNpVzRoUnV4elJ2VXB1ay8wMFhOdTNob3pVUFBkRTAxYytsQkxVSHZjcGlRK2xMelVqNXh6V2RkN0lDbStzMFg1bTBUN3AxTmZlTEFlSXVtSFkvU2JkTzhhNXYvQTVhR2Z4cHVqL1d1c3pHejV0bEdBQ1RFSTlMcVI5VG1zLzFmTTZRRDBXUkRsR1VaaDVYeHcwdjRhWlJ1ZnFncWlURnRra3pHcTBPV2RPRlJ1cS82LzA3UUpUNTl4SVlYUEpkYzMwdjYweGxNeXhBU0kwNThJTHlLbVJ1TlVkN0xab3hRZ0NuRC9KN2tyUTRFeERsYUFYME1IQWp3a09ZaHk3VDRzNnAiLCJtYWMiOiIyZjY2Zjc1NjJmODEwNWNiMzZmZmYzMWNkMWIzYzc5NGIzMTQzMDczNjlhZTQ1MGRkMDE5NTViNGE2NTc4OWZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNPN0FFYkY5ZjQrbjBKZ2pTREh4Mnc9PSIsInZhbHVlIjoicTF5bGRqN0UwUnlQNm1KWFNFcGcwZk9ZR3A0MnNzcWcxZWpXaHdZSlZqSUwwTS84QjQwOHZNeitOcXhwUFFmNlZJVVRxSUFkMFgxeFhsRlZCYVlTVFMwcnJPQSs0N3ZrM1FTUzZXZmgwZllycEsySXNBSG91bjIzSGNRVDBtZUpjc0lEYTlaRE13ZkNydFlBVDJkdDFMYktTdFNSWTIxblBGZmZqVnJaVGdNR1RCZ3B0eFhjWUlGV3RISTYyM05NUmZBMlRpa3RUNzlmeUxOd2tYZDdCUWFDeE54c0F3WFhmYW1KeU41VmZuaHZBZUtsUzREZmlNSjl1d2dNR1JwQURyUGN2bkc5ZkNIVGpIOXNFM2ZPQ3I3TGdQNk8rb3VzWU4vRStqNmwvQkYxZFhTNnlIRHVVQVlNQVRHQ1JzS1lJZ2tDN3pwYnl6NVIyTXNnclh6R1VxbkMrVkM2VTh6aE5HZDcxeGo2enZaRUV4K296d2MzTzluM2NMejdydjVBaURpYnRXS2Q4a01zak5VRXVzbGpWbUVLdWEwZEFORVN2VTNqdkdZZlR0K2paSlRJTmo4VWdOU2xDZWtiNVgycHhHc0cwUVY3WkwyWVVUOGpvazY1N3pZc2JRNTNoRlVUSUxhaVEzcmNtSFNNWjloTS9udk1yTTVRTzg2b1NRRGkiLCJtYWMiOiI1ZmUzY2VhMTZjYjc1ZGZjZDUzNTQwYWQyYzAxODc0YTY1YzZiYzFiNzdlMTU0NDRkYzAyNWE1Mjk5M2NkNDg5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498597183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1634246867 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634246867\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1545742522 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:18:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZ4VHlGSXB4eFNkTXk3RU5hL3k4Nnc9PSIsInZhbHVlIjoiSTVKVEFSOXoyR21wa3daRm8wcFZuVFVtcnBzeWNvVVFURHdqaUFhWWx2bExaZGViNUpIZmthM1QwOUp2ZDBnWUtzQ2VqVHZiZWZVU1lndWZWWlZtckRpVlE2dklQYmI3eU5weXVBM0l6VFJwY0djdWU1Y1Q0NGdQU2FKU2VCdzVNa0hDa085OEZjWmhlV1FyakcwRk1oQ0FaMTFtVUlVS0xDdlJEeVVyN1FFVDNNUWJhRktJQlhZRWxRZVNkWXdoYlFkSUlJcXlESzhHNUZhN0o1SStjRFI2SHJSVXlGUFBtZkdzenl0SDdzNEFQRnY4MVVRd0RBNk5EcVBEWVNxa2JDeGE5cGZ0VGFWaHJNV0VHRDF1TnVobTNQQmFsNU9lZEFFMjJWM3RaOFIyM3Y3WllXdzRkYnZtYktQWFc3c3JjVHptTUd6TVorSGhvNXgxMjlESi9WRldCT0RLOHQ0ZEt1aCt6dXJCYzIxMmg4ZEtIQ0NUZGgrYUY5YmxDVjFRcFhpV0pFQTdqOHl5TnVROVo0WnAzN3dCLzNNS1U0MUYxKzlQaUxqbnVCVUY2SWM0ZW9qZnkxTHhYNzNMSEUxWGtJMWl4T2JsWmNuYkhiZEZBK3h4eGtBRzNzZkxhYWVBeHVOeCtVYUNZSW95ZFMreU81dlBoRTZkbHdSdWRvaGMiLCJtYWMiOiJlNmQ2ZDljZTFkYzczMjZmN2Q5YjkzYTgyZTdiMjNkMWY3NjA2ZDI1ZmI0YjcyYWVhNzRlZmI4N2I5ZTNlZTUxIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjQycEE3QmIrZkRITXFGWm1LbFZCWkE9PSIsInZhbHVlIjoiQzZVR0w2dXZBMktYQlJjOTM0OHBPblI1aVJtd1F6UmVsMGppaDROL1JsODByQ0N5aDFVR3RnVyttZXlzSW91eVRwU2JwUjViNlhZSytDb2luMXJOM28xY292OEhDeldjY3g1MWhSTUhZeTUzL2tZVjFycGt6RFl3VU5jVGNST00zaEdOWGpMckhKYm9QMDYrSlJIZnJoYzZSTExhVlBpRDExcC9uUExtNTF4RWtNVWZ3SUhUWUtQVVd1SUIvNTJPakVmMkhxOUVXTFVKNWEzbkR1eTMrcjg1SUtxUGJ6MW5oVmYzNzRFamdPVDc5RFJWR0Q1UHFIN1BjTC9DZFMzakxEQ0tiWTIzSzFMeXpvM28wMC9CQ1NuNVVWUkVaVjdaNE1RVnlLU3RHVDFETWRralB2NDMrYlhIbHYyYlJWdnVpZWl1UlFBVnE0b2dwUkRmcWFRTjVwVXRuczlLVk9ESFRRMVg0SXhpR1B5RUgwM0J4c2Z2U2RMYTdiTWhDY0FObkR5LzhlSmNrdlFmcnJidXRCeGdYMVhJZEFXTFZ3alpkajkwYmpMbXRadEd6b2UzaEMxSWxwSGdFV1ZzOHhzZERKV3pWZmNrbzRjejFZS3hWWHo4NXJOS0hPK3FzTXRDQ0Y0NDhpSWZtN2FlTVBDTVFUdTVraG5aRWdvb2pKUysiLCJtYWMiOiI1NmI1YzIxNGY3ODNmMTU5OGM3NzdjOTk5NWZlY2JjM2VmZTkwYWFjYThiYmNmMDY3ZDNlYjI5ODUwZGYwZjgyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZ4VHlGSXB4eFNkTXk3RU5hL3k4Nnc9PSIsInZhbHVlIjoiSTVKVEFSOXoyR21wa3daRm8wcFZuVFVtcnBzeWNvVVFURHdqaUFhWWx2bExaZGViNUpIZmthM1QwOUp2ZDBnWUtzQ2VqVHZiZWZVU1lndWZWWlZtckRpVlE2dklQYmI3eU5weXVBM0l6VFJwY0djdWU1Y1Q0NGdQU2FKU2VCdzVNa0hDa085OEZjWmhlV1FyakcwRk1oQ0FaMTFtVUlVS0xDdlJEeVVyN1FFVDNNUWJhRktJQlhZRWxRZVNkWXdoYlFkSUlJcXlESzhHNUZhN0o1SStjRFI2SHJSVXlGUFBtZkdzenl0SDdzNEFQRnY4MVVRd0RBNk5EcVBEWVNxa2JDeGE5cGZ0VGFWaHJNV0VHRDF1TnVobTNQQmFsNU9lZEFFMjJWM3RaOFIyM3Y3WllXdzRkYnZtYktQWFc3c3JjVHptTUd6TVorSGhvNXgxMjlESi9WRldCT0RLOHQ0ZEt1aCt6dXJCYzIxMmg4ZEtIQ0NUZGgrYUY5YmxDVjFRcFhpV0pFQTdqOHl5TnVROVo0WnAzN3dCLzNNS1U0MUYxKzlQaUxqbnVCVUY2SWM0ZW9qZnkxTHhYNzNMSEUxWGtJMWl4T2JsWmNuYkhiZEZBK3h4eGtBRzNzZkxhYWVBeHVOeCtVYUNZSW95ZFMreU81dlBoRTZkbHdSdWRvaGMiLCJtYWMiOiJlNmQ2ZDljZTFkYzczMjZmN2Q5YjkzYTgyZTdiMjNkMWY3NjA2ZDI1ZmI0YjcyYWVhNzRlZmI4N2I5ZTNlZTUxIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjQycEE3QmIrZkRITXFGWm1LbFZCWkE9PSIsInZhbHVlIjoiQzZVR0w2dXZBMktYQlJjOTM0OHBPblI1aVJtd1F6UmVsMGppaDROL1JsODByQ0N5aDFVR3RnVyttZXlzSW91eVRwU2JwUjViNlhZSytDb2luMXJOM28xY292OEhDeldjY3g1MWhSTUhZeTUzL2tZVjFycGt6RFl3VU5jVGNST00zaEdOWGpMckhKYm9QMDYrSlJIZnJoYzZSTExhVlBpRDExcC9uUExtNTF4RWtNVWZ3SUhUWUtQVVd1SUIvNTJPakVmMkhxOUVXTFVKNWEzbkR1eTMrcjg1SUtxUGJ6MW5oVmYzNzRFamdPVDc5RFJWR0Q1UHFIN1BjTC9DZFMzakxEQ0tiWTIzSzFMeXpvM28wMC9CQ1NuNVVWUkVaVjdaNE1RVnlLU3RHVDFETWRralB2NDMrYlhIbHYyYlJWdnVpZWl1UlFBVnE0b2dwUkRmcWFRTjVwVXRuczlLVk9ESFRRMVg0SXhpR1B5RUgwM0J4c2Z2U2RMYTdiTWhDY0FObkR5LzhlSmNrdlFmcnJidXRCeGdYMVhJZEFXTFZ3alpkajkwYmpMbXRadEd6b2UzaEMxSWxwSGdFV1ZzOHhzZERKV3pWZmNrbzRjejFZS3hWWHo4NXJOS0hPK3FzTXRDQ0Y0NDhpSWZtN2FlTVBDTVFUdTVraG5aRWdvb2pKUysiLCJtYWMiOiI1NmI1YzIxNGY3ODNmMTU5OGM3NzdjOTk5NWZlY2JjM2VmZTkwYWFjYThiYmNmMDY3ZDNlYjI5ODUwZGYwZjgyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545742522\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-265370362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265370362\", {\"maxDepth\":0})</script>\n"}}