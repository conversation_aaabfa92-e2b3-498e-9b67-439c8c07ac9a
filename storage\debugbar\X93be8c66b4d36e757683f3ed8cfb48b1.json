{"__meta": {"id": "X93be8c66b4d36e757683f3ed8cfb48b1", "datetime": "2025-07-14 18:31:35", "utime": **********.819638, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.366974, "end": **********.819653, "duration": 0.452678918838501, "duration_str": "453ms", "measures": [{"label": "Booting", "start": **********.366974, "relative_start": 0, "end": **********.745245, "relative_end": **********.745245, "duration": 0.37827086448669434, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.745254, "relative_start": 0.3782799243927002, "end": **********.819654, "relative_end": 9.5367431640625e-07, "duration": 0.07439994812011719, "duration_str": "74.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022169999999999995, "accumulated_duration_str": "22.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.774411, "duration": 0.02124, "duration_str": "21.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.805}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.804816, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.805, "width_percent": 1.714}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.811078, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.519, "width_percent": 2.481}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1336671194 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1336671194\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1980297807 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1980297807\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-539247296 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539247296\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-940809180 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517839891%7C45%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijd3ODh3eXJNUzcrVTNqWitneXYxQ2c9PSIsInZhbHVlIjoiWS9kcFhPT295cFY2cmhEa1VVUEx1WmJrNldGNk5Fc1BlUm5oMWlSZWhFZzZ5SDhvUmhaNGpGQVVSYXFyYjVvNzJwZVpwdkVsOW12cUtzbHI1M0I2RlY4YmZEdjNGYnNzNGdib3hhaDd3YkxtREVadjI4TWYrVzFxQlE1RkxDSkxZeGYvYzNxMi9KTHF1a1lXYnBYV0swaTBBZHV1b0RsOS9xYmlqZ1NjdFhuOTFGdTdva2JmZC9aSTdtdG95SjZ2ZkwwOFhHWW4ralkwakpWdHVMY0xDRis0RDc5Qml6dGNLVG5OZ0VpRFgxTzc3NGhNMDJZTTYzSGkwYVM5OE5CSk5wR0VlZmVUV0lIRkt2Y0dhOS9ySDZ4Wk9kRFZEMXpkaHdzS0pNM2p2S3NYbkRoaURjY0xxZTlrY3JYeTZ0a3QrVGI3ZDZ4d3hJUWVpa1lhQWk5NHVDUVdpek01eFp5N2VZd2VsczZSZEtKMDNoQmhiOGRsdEwwbS9ESHhNZlYrZlBFLzEvS1YrL2JsbFkzN0JNRExzcnFZbC80Q2kyaVIydDNIV3pHcTlDd2VGNzBjQVNHRmk4cTJCMDBvNDhuTi9QdWFXSFNqLzhYa3I4ZldzVndNVFNXb0dIWlUyTEZIRnVka3QyeWFFTzlqU0loVE00NHd0S3hpYm4vMndOSGkiLCJtYWMiOiI2ZjZmMmY0YjUzMDkyMDI4NGRmMjFjY2VhNDRlODhjMWJkZWMwNjRiNGM3YTFlYmNhNTU2YzdmNTZlM2ZiZThkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktyUEFUVDJFdkN3ZExyT1VsbFBUWFE9PSIsInZhbHVlIjoiV1JJR1VtWDFLUlNPQmI2ZVhDVW91bTZtSHpaOU0zTXJUR2x0NlByMnZXejJQWlZEc1h3MExXUncySmxsak85TmFqSEluRWRScDZKQnBYQXFQNW9mYXVTTmorS3hDOUdGWVpuM3FNRFV2TzZIb1RsUmoxV0gvbmdVaE90UnFCczRhMDU5NXZwV2g2QllSVEUyS2d4V3V1eENyajFsNE5iVkZuQXA3eXdIU1l0VzZSa1I5R2xQTndVK0c1Wjl4L1FqaWNyK0s3OC8rdVlSL1lMQ3YyNEVzYVVMbWRxS28xMWdLeDJHTm0wZk4xM3ZWNFh0THFzNG9PN1VPTnJQR0JVYlQzRXZwclM5WnE4U2k3WEw5SE55OUcwcDBKLzhaSys4RUxONjNFQWVZZ0xUM25sWDliOUZIS2NldFlHbWFhOG9RSll6NzlzM0NZaFNZVmRtdzR5cy9nc3NqWEVZT3RoZmk3K2Uwc2pTdEhCL3RqbFBrZW80cS8xRjBPb2xoam5tNFZLTEEwbzQ3RXlUTGtYcW15VzRKMEFxNDRiejcvN1RobzZRYW5uK1c0SkpuYkMwaDROcjRrdjBLYWpBNlhvQ0lRclA0UnAvZGV3L3FhL2ZFcWpKTGRnOWVZR2wrOUxYdXJ0ek5uN1plKzIySDh5MHJrcFcwYzJ6b0xnOXRvR2siLCJtYWMiOiIyMmFlNGI4YmZlMDE1OTMxZjUyNDAyY2U4ZTlmMmMwZGI3OGZlYWQ1YzRmYWE3NTI4Y2NkMGI0YWY0NDdlMjAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940809180\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-532308369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532308369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2036652997 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:31:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNiTjVJV1U0SmU3OE92aUF2MnNUUXc9PSIsInZhbHVlIjoieEd0b3dnNExuRFZOenF1aHlRUW5yNkI3Yys2NVlPSzJvUTVhNkpDeU83WGJtKzJDWlYzUU05aTR5QmlUdG1lV09Ua2MvUHFMZnN0ekYvS1dKZk54TFVBZUIxL3g5OTJ1MnhIcXloNjQ3dlpMbVd1Q1lPUFl3Zy8zOHBwSFVYVCs2RmtpU2NQOTBzTWFPSDhFRnNyVU5MR2tiMUVnUGNJZG11N2FTQWlCSG9WcUJBWEN5T2p1RHhKeWgrcWVTTGIwbnd4dmYzOTVQVHVuYWRlRVRKMTQzOUExODVsN3Z2L0hVODYzWVMzK3pvM1ZrYmdaSGtCQ3NJOWtqOGg0bllEcVh4NnFueUxiM0tUaHJMUXZua1hoSzJURlBLaGFrYlhXM2xrelVLbW43bzlyRXVwYzRBSEt4emdmNjc2NDZVTVVEY01kTjFOZTBYL2QwY0dUdEl1YSswSGhhWFl0c3M2THMxYmZJNzQvV1l4N3pVcnNoTE5nN01yOU16QVRSbUFUTlRla3d0Vkd1K2R2T2M2MDRGRGRJUkYySUlQTExOa2dJd0wrMkJvekFwczhndHpCeG9MMGNtWnNnWERZWVVSaU5Rc2Z4TmZzYVdvS3JJRE1iOWg5N1Y3QWNiZ20rVndCN3dkeWo0TFZTK3dUQUNIS1dIQjY1SmdiUitSanNiWXYiLCJtYWMiOiIyOGQyOWZlOWNkYTI0YTY0MjJjNTkzZTFlN2Q0YzRjMzRkNjk2NzZjYWM1ODUxNDZkZjc5MGI1ODczMGI4MWJjIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:31:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IklpVFBXVlh2WHphQlF4WGh0SWVnTmc9PSIsInZhbHVlIjoiL3RBd1RDTm1LQkM3R1htK3RYdG5xSkF1YmxsbkZiZkF0a0Y0R2o0QnZCeWkrMFBPTTRvbysrTm5oNXFkYUZITFVDajB5WWRSTGVUMHlmSWI0Qm8yS3JrTnJoUTh3Y3J0RFBiWktod3dTYy9EY0xQUzIyL3VXOWVtelJkMnBXZjZkUEJhcHo0ek1tbXlKL2R4YTVPN3AwVEIzQW1SSmwreTdRLzJ0eWFYc042Y1FyQVFSMjVoUFBOTFFoL1ZrTnN4dEI4NmN4RERkTlJJV2U5ZDUvcWMwMnFITVJPbG9VVnA3Ym5vOWEwa0xvZHg1SVlkdDdkMWpnelkzKzV6MGVxcTdqQ0luYytRcHFBVDRqNElUeWt1T01NczBxcXpPS3pnNTEyaGxNelpERWFwVGtJc044VXBJTkZ6eTM5L0JZUjN1Z3V1TWZ1NHJucnFkTXRCNngrOUErT0JIU2VZZFIvdTAxT2ptS1lJZHBnUDlpMXpNRk4zU1FMZEVKTE5LY1RYYmU5Q3VLUytyQXZUVWNLKzN5aWwzTWxBVEh3aDJ1cnpQclhYazdTYjZTK2hIODFmUmc2OWxvOWRlV1FzMG9VbHQyVlp5ZzFUY1ZWWGY1eTlpTTVPNjJUb2ZGWWNwZ0ovb3Z0M29PNDczWjEycFJLdjlUbU5LajZDVTdteDhQQnQiLCJtYWMiOiI2NjRmMzk3MzdiZmRiY2M5NjdjMDc2NmJhNGM1ZTA1ZTM2NzExNDIzYWQwNjcxZTk3YjA2NzFmZjBjM2MxYWYwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:31:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNiTjVJV1U0SmU3OE92aUF2MnNUUXc9PSIsInZhbHVlIjoieEd0b3dnNExuRFZOenF1aHlRUW5yNkI3Yys2NVlPSzJvUTVhNkpDeU83WGJtKzJDWlYzUU05aTR5QmlUdG1lV09Ua2MvUHFMZnN0ekYvS1dKZk54TFVBZUIxL3g5OTJ1MnhIcXloNjQ3dlpMbVd1Q1lPUFl3Zy8zOHBwSFVYVCs2RmtpU2NQOTBzTWFPSDhFRnNyVU5MR2tiMUVnUGNJZG11N2FTQWlCSG9WcUJBWEN5T2p1RHhKeWgrcWVTTGIwbnd4dmYzOTVQVHVuYWRlRVRKMTQzOUExODVsN3Z2L0hVODYzWVMzK3pvM1ZrYmdaSGtCQ3NJOWtqOGg0bllEcVh4NnFueUxiM0tUaHJMUXZua1hoSzJURlBLaGFrYlhXM2xrelVLbW43bzlyRXVwYzRBSEt4emdmNjc2NDZVTVVEY01kTjFOZTBYL2QwY0dUdEl1YSswSGhhWFl0c3M2THMxYmZJNzQvV1l4N3pVcnNoTE5nN01yOU16QVRSbUFUTlRla3d0Vkd1K2R2T2M2MDRGRGRJUkYySUlQTExOa2dJd0wrMkJvekFwczhndHpCeG9MMGNtWnNnWERZWVVSaU5Rc2Z4TmZzYVdvS3JJRE1iOWg5N1Y3QWNiZ20rVndCN3dkeWo0TFZTK3dUQUNIS1dIQjY1SmdiUitSanNiWXYiLCJtYWMiOiIyOGQyOWZlOWNkYTI0YTY0MjJjNTkzZTFlN2Q0YzRjMzRkNjk2NzZjYWM1ODUxNDZkZjc5MGI1ODczMGI4MWJjIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:31:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IklpVFBXVlh2WHphQlF4WGh0SWVnTmc9PSIsInZhbHVlIjoiL3RBd1RDTm1LQkM3R1htK3RYdG5xSkF1YmxsbkZiZkF0a0Y0R2o0QnZCeWkrMFBPTTRvbysrTm5oNXFkYUZITFVDajB5WWRSTGVUMHlmSWI0Qm8yS3JrTnJoUTh3Y3J0RFBiWktod3dTYy9EY0xQUzIyL3VXOWVtelJkMnBXZjZkUEJhcHo0ek1tbXlKL2R4YTVPN3AwVEIzQW1SSmwreTdRLzJ0eWFYc042Y1FyQVFSMjVoUFBOTFFoL1ZrTnN4dEI4NmN4RERkTlJJV2U5ZDUvcWMwMnFITVJPbG9VVnA3Ym5vOWEwa0xvZHg1SVlkdDdkMWpnelkzKzV6MGVxcTdqQ0luYytRcHFBVDRqNElUeWt1T01NczBxcXpPS3pnNTEyaGxNelpERWFwVGtJc044VXBJTkZ6eTM5L0JZUjN1Z3V1TWZ1NHJucnFkTXRCNngrOUErT0JIU2VZZFIvdTAxT2ptS1lJZHBnUDlpMXpNRk4zU1FMZEVKTE5LY1RYYmU5Q3VLUytyQXZUVWNLKzN5aWwzTWxBVEh3aDJ1cnpQclhYazdTYjZTK2hIODFmUmc2OWxvOWRlV1FzMG9VbHQyVlp5ZzFUY1ZWWGY1eTlpTTVPNjJUb2ZGWWNwZ0ovb3Z0M29PNDczWjEycFJLdjlUbU5LajZDVTdteDhQQnQiLCJtYWMiOiI2NjRmMzk3MzdiZmRiY2M5NjdjMDc2NmJhNGM1ZTA1ZTM2NzExNDIzYWQwNjcxZTk3YjA2NzFmZjBjM2MxYWYwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:31:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036652997\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1059460666 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059460666\", {\"maxDepth\":0})</script>\n"}}