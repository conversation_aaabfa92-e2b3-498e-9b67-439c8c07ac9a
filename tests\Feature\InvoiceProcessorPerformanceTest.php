<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Pos;
use App\Models\Customer;
use App\Models\warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class InvoiceProcessorPerformanceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $warehouse;
    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();
        
        // إنشاء مستخدم للاختبار
        $this->user = User::factory()->create([
            'type' => 'company',
            'lang' => 'ar',
        ]);
        
        // إنشاء مستودع
        $this->warehouse = warehouse::create([
            'name' => 'مستودع اختبار',
            'created_by' => $this->user->id,
        ]);
        
        // إنشاء عميل
        $this->customer = Customer::create([
            'name' => 'عميل اختبار',
            'created_by' => $this->user->id,
        ]);
    }

    /** @test */
    public function it_loads_today_data_by_default()
    {
        // إنشاء فواتير لتواريخ مختلفة
        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');
        
        // فاتورة اليوم
        Pos::create([
            'pos_id' => 1,
            'customer_id' => $this->customer->id,
            'warehouse_id' => $this->warehouse->id,
            'pos_date' => $today,
            'created_by' => $this->user->id,
        ]);
        
        // فاتورة الأمس
        Pos::create([
            'pos_id' => 2,
            'customer_id' => $this->customer->id,
            'warehouse_id' => $this->warehouse->id,
            'pos_date' => $yesterday,
            'created_by' => $this->user->id,
        ]);

        // تسجيل الدخول
        $this->actingAs($this->user);

        // طلب الصفحة بدون فلاتر
        $response = $this->get(route('invoice.processing.invoice.processor'));

        $response->assertStatus(200);
        
        // التحقق من أن البيانات المعروضة هي لليوم الحالي فقط
        $response->assertSee('إجمالي النتائج');
        $response->assertSee('اليوم الحالي');
        $response->assertSee($today);
    }

    /** @test */
    public function it_applies_custom_date_filter()
    {
        $yesterday = now()->subDay()->format('Y-m-d');
        
        // إنشاء فاتورة للأمس
        Pos::create([
            'pos_id' => 1,
            'customer_id' => $this->customer->id,
            'warehouse_id' => $this->warehouse->id,
            'pos_date' => $yesterday,
            'created_by' => $this->user->id,
        ]);

        // تسجيل الدخول
        $this->actingAs($this->user);

        // طلب الصفحة مع فلتر تاريخ مخصص
        $response = $this->get(route('invoice.processing.invoice.processor', [
            'date_from' => $yesterday,
            'date_to' => $yesterday
        ]));

        $response->assertStatus(200);
        $response->assertSee('مفلتر');
        $response->assertSee($yesterday);
    }

    /** @test */
    public function it_shows_loading_indicator_elements()
    {
        // تسجيل الدخول
        $this->actingAs($this->user);

        $response = $this->get(route('invoice.processing.invoice.processor'));

        $response->assertStatus(200);
        
        // التحقق من وجود عناصر مؤشر التحميل في الجافا سكريبت
        $response->assertSee('showLoading');
        $response->assertSee('جاري تحميل البيانات');
        $response->assertSee('loadingOverlay');
    }

    /** @test */
    public function it_has_quick_filter_today_button()
    {
        // تسجيل الدخول
        $this->actingAs($this->user);

        $response = $this->get(route('invoice.processing.invoice.processor'));

        $response->assertStatus(200);
        $response->assertSee('quickFilterToday');
        $response->assertSee('اليوم');
    }

    /** @test */
    public function it_has_customer_search_field()
    {
        // تسجيل الدخول
        $this->actingAs($this->user);

        $response = $this->get(route('invoice.processing.invoice.processor'));

        $response->assertStatus(200);
        $response->assertSee('customer_search');
        $response->assertSee('العميل');
        $response->assertSee('ابحث عن العميل بالاسم');
    }

    /** @test */
    public function it_has_creator_search_field()
    {
        // تسجيل الدخول
        $this->actingAs($this->user);

        $response = $this->get(route('invoice.processing.invoice.processor'));

        $response->assertStatus(200);
        $response->assertSee('creator_search');
        $response->assertSee('منشئ الفاتورة');
        $response->assertSee('ابحث عن منشئ الفاتورة بالاسم');
    }

    /** @test */
    public function it_filters_by_customer_name()
    {
        // إنشاء عميل آخر
        $customer2 = Customer::create([
            'name' => 'عميل آخر',
            'created_by' => $this->user->id,
        ]);

        // إنشاء فواتير لعملاء مختلفين
        Pos::create([
            'pos_id' => 1,
            'customer_id' => $this->customer->id,
            'warehouse_id' => $this->warehouse->id,
            'pos_date' => now()->format('Y-m-d'),
            'created_by' => $this->user->id,
        ]);

        Pos::create([
            'pos_id' => 2,
            'customer_id' => $customer2->id,
            'warehouse_id' => $this->warehouse->id,
            'pos_date' => now()->format('Y-m-d'),
            'created_by' => $this->user->id,
        ]);

        // تسجيل الدخول
        $this->actingAs($this->user);

        // البحث عن العميل الأول
        $response = $this->get(route('invoice.processing.invoice.processor', [
            'customer_search' => 'عميل اختبار'
        ]));

        $response->assertStatus(200);
        $response->assertSee('مفلتر');
        $response->assertSee('عميل اختبار');
    }

    /** @test */
    public function it_shows_time_instead_of_user()
    {
        // إنشاء فاتورة
        Pos::create([
            'pos_id' => 1,
            'customer_id' => $this->customer->id,
            'warehouse_id' => $this->warehouse->id,
            'pos_date' => now()->format('Y-m-d'),
            'created_by' => $this->user->id,
        ]);

        // تسجيل الدخول
        $this->actingAs($this->user);

        $response = $this->get(route('invoice.processing.invoice.processor'));

        $response->assertStatus(200);
        $response->assertSee('الوقت');
        $response->assertSee('ti-calendar-time');
        $response->assertSee('ti-clock');
    }
}
