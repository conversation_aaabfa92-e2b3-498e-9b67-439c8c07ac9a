{"__meta": {"id": "X1d9730fb56dde05973bd6598faa34793", "datetime": "2025-07-21 01:57:31", "utime": 1753063051.020851, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.611824, "end": 1753063051.020867, "duration": 0.4090430736541748, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.611824, "relative_start": 0, "end": **********.967884, "relative_end": **********.967884, "duration": 0.3560600280761719, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.967893, "relative_start": 0.35606884956359863, "end": 1753063051.020868, "relative_end": 9.5367431640625e-07, "duration": 0.05297517776489258, "duration_str": "52.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00298, "accumulated_duration_str": "2.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.996068, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.456}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1753063051.007056, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.456, "width_percent": 16.443}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1753063051.013148, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.899, "width_percent": 15.101}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_search=Afrooz%20Ala&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2041240231 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2041240231\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1429375781 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1429375781\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-307910044 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307910044\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-937026978 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"163 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_search=Afrooz+Ala</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753063045728%7C28%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1oWVBTTUp1ZWE3Z0RtT1QzeFFsZlE9PSIsInZhbHVlIjoiNjNxeEd6RytCVnVrcU1iWTUyTWs4RURpcTVJRjNKbU5XdFRQU0tmNjF2WFZqMXlYdzFCRUtRYjB4TjQ3WXVpUlpmZ2RLZWZ3ZnI0SDM1MW50a2VGaUZvUjVrc2RKVHdpQlA3THQ4NjEwb0EwdjJycHRwWXYwbDRsSVZGTFg1VFpIYmg2YzVJYVNNaEdJL1RtcUdSZjFneG1YSjMwVVBDb0hSTDcvMXN4a0M1bXdFMEtGdHB1Z1krNDFHc2lic056WTdGcWpVRGZ2V1FXaU1sdE1OdFRadHBVL0lVR2pENWdpekduR1hQdTBLSU9JZHJ6VEhxczFhNVpud2g0Y1llRGRBN3plMzdrZThKWXhwRWptdis3SWFVeVMwc1ZjSU1teGpVVkJkMEkybERFZDVGMWU2SkVqcThBVzl0dHFLTGRyU1B6QmNXVkw3WURjdzlaMDFCKzgwRWJid1Y3SDRkVllXcDZ2VmVHWVJQL2lzNWVxQUhFM3lxRWFzb0orYno4NDk5SVhvRC84ZklERXVJVkcwczFBeTFiUzBZOEt2cTRDWlNhZzgwZm1RN2tncm01aDlKekw0ZzRneVZaOU1YWEJ0RW01dW1wV2NHTUNWczV2OUJhNEhvaHlxblV2Y3FGbElJTnJDcXJjdEUzZHpKRksvb3p0akx6R0M4alFtUVQiLCJtYWMiOiI1YWVmZTY5MzhmNzBlZWJmZDc3OWQ0NWYwOTFjYzFkMDQ2OGQwNzdlYzBlNWMzM2ZjNDE5MzlmY2E3MGY5MjZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZ3UjdnZDJlc3VyRzhaR1c0VnRvOEE9PSIsInZhbHVlIjoieituZ1ZDVm8zT2ViK1krVm8zbzZqbEVpOWNCemRudC9FR2FoN2V3UWc4c0dVcGdpYUM3aEhRUmM3ODZ2UGhIaTQ2Q3ZOdWwvNjhua09YWW5tV1U1ODJGZjFVQUF3MHN0VVNTQWxIeDZBQkpvRXlWdmEvRVV6OXYvSHhNNEh4NWN4TEZWT2RvNmduQlRHdk9HenlzZjZzNFQ0N3RkK0ltdFdPekNzbmxoSW5JSTQyRTJsZkZLU0VBS3FnMjMxV1ZsNUd4dlg3S2dNckthZWR6NTMyNEJWSnZRTGhnc1FyZ0dXbHJ5Rms0OHJsWGs5OFlGYzhETnNKVmtvWjF3YlNBV0FRVnlaM0I3QzlGNmlmd1FqMzdFVUg0c1Z3WCsrak12YyttK215N3RoYTBKcmtTWUJtaVpVYUthTVVIM3d3cFdsNDAyVEFCMlcrMjhaa05XMktXWmdJckVmSVREYXZ1aU5odDhFWjM4N1NxaGdyZEszOXlib0JxOWhvQkFDZkdlS1BBbkNLZkFDajRsZlJRVVdOb0JHQVBlSy9lWlVMTnRzdENrVTRMVzl4UGZ6bnZXTUZ6ZmFkVU9tWHBjcFdKdTY0UXcrbWV6QTZJeFNzZG8yeTZIdDVNVS9md0h1eTVpek92Z05jd3JheExHNndpblVuY3ZZazcxWldiYWtzWUYiLCJtYWMiOiI0MjE0ZThjYzU3Y2Q2OTkzN2ZlNTZjZjg0ZTQwMTgwNWY5ZThmZGJhZjY4N2Y4NzIyYzM2MmM2YTBiYjM1OTcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937026978\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2041726863 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041726863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1955724559 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:57:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNGaVdzNGo5bXdQMXZsQjdwN2gwelE9PSIsInZhbHVlIjoiWVJJVkxDV2pMUXhSR21UNjgwYnNnQ2gvNEl6UGRSN3VZYW1SK0MxNzNHNVZmVk80eXZ4M1VtWjhaSGNTZjJLS0hOdHEybHpKWnJRdXlvR2M1YVoraDhnaVk5UUtxaE1FZXhIdnl1MDZkdFg5TUpiNXJueldiOVJBRzg0SW4reDIvSDlmenJIWmdJd3Jia3N0UFlOL1ZWYWd3Rm1JMGloQ1VNcmNjRTN3ZUNINFFHcTFOWDNSZnprSzJVVTRoTEZ4V2VmbmlrZ25CblI2amRYNHc5c3BNNVJuMy9MOWxjaDRJQnIxSnlvTW0vWXFEaFRSdzMvVSs1cTFybEV2L2J4c2VPS0RvV1JhcklXaVZGT3U1SUMzREo2T280RkoyLzUxbXg4bTFUTVp0N3F5a1BVMWU0bjAwdGdDOGd0cjZjY0JoWUpjekIwQ2ZUS2Fzb28wekNzWmtMSElVeDc3aHRzcjZSbC9DeDlJYXpkSmtRWkpEQTlyVjg0c0w5bVpSeGVBYUVIWTBNb1ZHZ09MSUZDWVJaWm0rMzhvNXRXajlUWDY5anc1cXU4SERXa1JDbXY3RmsyRHo2anM5R1lPWkJkM2RzdTlRS2xxTm43clhJWHBySjN2ZE1VK2NpQ0tnY2ZOOHBTYWlzQ1llM2hjdC9KaDZ3NFVnOGNtemRnb1RsZ2MiLCJtYWMiOiIxNjM0M2U0OTEyNGI5M2E3YzlmOTJiOTgzZWZhYTE3MWJjZmNhYzE1NTZiNzM2MWYyMTY0ODJmMDYwNTk3ZmE1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhKK1VsZmRuSmVyT0V2Q005WkhrZ0E9PSIsInZhbHVlIjoic0pNNVRTREFYZHc3c3lqOGJYQWU4WkNqZFlMRWVVanB4NnhnRVF5SzRyb0RGMHdJRUl4YjBKdkM1UzF6SFk1Mk0vS3N2TVV4YlJWMHZYZmc2a29DRUxtMVdhckRZbHBFaDN1SHhTVzdDVjEvSHM1V3FBYnFDYVR6MzBqaWUzd0o0S2o1eGdqVzJwbTU5Z1lBSmgrSkl0Wnd1RVJycFFUOGNMd3RnUk1UZk9rR1BZTm1pVnB5S3QwRjZWbUk3MzA2b0VMRWN4cmxOK1FYejlJd0FFcExvQzc1b0g2cFR1eHJZc3FjTlR4K3pRL0J3MTh1Z3B5aStKSklwSkdaeWRueTU3UzdsUjBjc3dOUFIvWHZZYnRTNWt4Wk9YbDVPYjlSK0pZcFlCRVB6NXQzZ3AvWEZqbU8rbUhrK09vOWdNVEgxLzVuaFJmQ0toWUIrLzNjUGMxWFRsaWxYaHYya0UxSXo0NjJmOHl6SVcyVUJHeUJuSkh0a1JLZ1cxMlN3U1pxTjNNUEV3MGxtTml4aFRpUHRWbEo3czZmRVlPZEZhZWx0U1RBMU50bTF0QStkalBZYmpDeEl5aW9DUEdPanc1eVh2VWpGdyt3VGdUMG00ZktyZmZKMFJZWEVIanc1TDBuVHhUeHphZFVmUlRJdUd3UFVZMUNVNXh2dGxFdnk3d1AiLCJtYWMiOiI4MWFkZDY2NzE4YzQwNzY1OGM1MDlhZjY1NDI5MThhNDllNTlkOTM0YzE1OWI1OTFjMGY0NzkxYzU0NmExNjBlIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNGaVdzNGo5bXdQMXZsQjdwN2gwelE9PSIsInZhbHVlIjoiWVJJVkxDV2pMUXhSR21UNjgwYnNnQ2gvNEl6UGRSN3VZYW1SK0MxNzNHNVZmVk80eXZ4M1VtWjhaSGNTZjJLS0hOdHEybHpKWnJRdXlvR2M1YVoraDhnaVk5UUtxaE1FZXhIdnl1MDZkdFg5TUpiNXJueldiOVJBRzg0SW4reDIvSDlmenJIWmdJd3Jia3N0UFlOL1ZWYWd3Rm1JMGloQ1VNcmNjRTN3ZUNINFFHcTFOWDNSZnprSzJVVTRoTEZ4V2VmbmlrZ25CblI2amRYNHc5c3BNNVJuMy9MOWxjaDRJQnIxSnlvTW0vWXFEaFRSdzMvVSs1cTFybEV2L2J4c2VPS0RvV1JhcklXaVZGT3U1SUMzREo2T280RkoyLzUxbXg4bTFUTVp0N3F5a1BVMWU0bjAwdGdDOGd0cjZjY0JoWUpjekIwQ2ZUS2Fzb28wekNzWmtMSElVeDc3aHRzcjZSbC9DeDlJYXpkSmtRWkpEQTlyVjg0c0w5bVpSeGVBYUVIWTBNb1ZHZ09MSUZDWVJaWm0rMzhvNXRXajlUWDY5anc1cXU4SERXa1JDbXY3RmsyRHo2anM5R1lPWkJkM2RzdTlRS2xxTm43clhJWHBySjN2ZE1VK2NpQ0tnY2ZOOHBTYWlzQ1llM2hjdC9KaDZ3NFVnOGNtemRnb1RsZ2MiLCJtYWMiOiIxNjM0M2U0OTEyNGI5M2E3YzlmOTJiOTgzZWZhYTE3MWJjZmNhYzE1NTZiNzM2MWYyMTY0ODJmMDYwNTk3ZmE1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhKK1VsZmRuSmVyT0V2Q005WkhrZ0E9PSIsInZhbHVlIjoic0pNNVRTREFYZHc3c3lqOGJYQWU4WkNqZFlMRWVVanB4NnhnRVF5SzRyb0RGMHdJRUl4YjBKdkM1UzF6SFk1Mk0vS3N2TVV4YlJWMHZYZmc2a29DRUxtMVdhckRZbHBFaDN1SHhTVzdDVjEvSHM1V3FBYnFDYVR6MzBqaWUzd0o0S2o1eGdqVzJwbTU5Z1lBSmgrSkl0Wnd1RVJycFFUOGNMd3RnUk1UZk9rR1BZTm1pVnB5S3QwRjZWbUk3MzA2b0VMRWN4cmxOK1FYejlJd0FFcExvQzc1b0g2cFR1eHJZc3FjTlR4K3pRL0J3MTh1Z3B5aStKSklwSkdaeWRueTU3UzdsUjBjc3dOUFIvWHZZYnRTNWt4Wk9YbDVPYjlSK0pZcFlCRVB6NXQzZ3AvWEZqbU8rbUhrK09vOWdNVEgxLzVuaFJmQ0toWUIrLzNjUGMxWFRsaWxYaHYya0UxSXo0NjJmOHl6SVcyVUJHeUJuSkh0a1JLZ1cxMlN3U1pxTjNNUEV3MGxtTml4aFRpUHRWbEo3czZmRVlPZEZhZWx0U1RBMU50bTF0QStkalBZYmpDeEl5aW9DUEdPanc1eVh2VWpGdyt3VGdUMG00ZktyZmZKMFJZWEVIanc1TDBuVHhUeHphZFVmUlRJdUd3UFVZMUNVNXh2dGxFdnk3d1AiLCJtYWMiOiI4MWFkZDY2NzE4YzQwNzY1OGM1MDlhZjY1NDI5MThhNDllNTlkOTM0YzE1OWI1OTFjMGY0NzkxYzU0NmExNjBlIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955724559\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-453981269 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"165 characters\">http://localhost/invoice/processing/invoice-processor?creator_search=Afrooz%20Ala&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453981269\", {\"maxDepth\":0})</script>\n"}}