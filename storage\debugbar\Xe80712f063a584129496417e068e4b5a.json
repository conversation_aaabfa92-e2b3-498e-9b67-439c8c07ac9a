{"__meta": {"id": "Xe80712f063a584129496417e068e4b5a", "datetime": "2025-07-21 01:18:12", "utime": **********.98152, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.467038, "end": **********.981534, "duration": 0.5144960880279541, "duration_str": "514ms", "measures": [{"label": "Booting", "start": **********.467038, "relative_start": 0, "end": **********.897446, "relative_end": **********.897446, "duration": 0.4304080009460449, "duration_str": "430ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.897455, "relative_start": 0.4304170608520508, "end": **********.981535, "relative_end": 9.5367431640625e-07, "duration": 0.08407998085021973, "duration_str": "84.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46520856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02136, "accumulated_duration_str": "21.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.934957, "duration": 0.0205, "duration_str": "20.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.974}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9656072, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.974, "width_percent": 2.154}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.971596, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.127, "width_percent": 1.873}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1966114292 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1966114292\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1958280400 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1958280400\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-756207808 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756207808\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060686382%7C8%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhTbmlqQkUyYkxTb3VnbHUwMEZwcEE9PSIsInZhbHVlIjoieEFZRlBRczYyMXhKVXR1NWVscU96dUxOWHNHS3pmSitFdENicEVRVjdlK3ZXRW1qR3VnYUF1Y0JmZDNHd3lsMlZIQ1ZjSHdiRUdNVHNEUFU0dTFLamlaR2Q1WUhJRnNtbWRKZnBKbHp6UytsUUlZZkc5dHZDa2h5SEIreTR6NjB2T1U0QjJwd1UrSXlnQ0ZBNmtUQ283UklndnhhcVhYYUhkYkhyRXcyM3Jua05JQlE3cUd0SGFhZVcySlFGTUk4T2VvWHFUb2lmSWpPUlVxTDlhTDZKZWtWUGJGY0p6M3U5bGxNcE03TUlzVnR3eFJ0UjN5K3FSZGRjVG4zOXB5bm5LU2kwYmdoRzhIdytRYjJxblQwUjJXeFJLc2lha3B0L2ZDT2MrR25wYk9aQm9jTDVyUnJ5ZERNelNwajNJT21uZzdMMTh4S3VoamZsK0loQjNNY0Vja3Zqanh2WFJkczBId2NhQTZNNG81RVlvb3NkSDhkZFphdWFxUk9EaU1ZcjdGV25LMldJU2lqVk42UVJyMjM4ZDFNUm84RmNzYkd4QkxycGZhS1M1U1JzMldhckJsc2ZwSXNqd012aEhKTWpNTk1QRzQvaUhya093aWsyL0ZpWmE0QytTNEorem9NOUJVWTUzOWZOR0RYcTB4cFkzY0oxakpVZDFTVTVjZ28iLCJtYWMiOiIyZGQyOTI0MmM4NzE0ZWJmOTdmZjBjMjUzOGRkZmIzNDFlMDcwMzljMTcwZTAzYzkwYTQxYjBiMjYzNzk1ZDkzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImxwNlJkbVNDQlpsM3BQT3JUNGM1NEE9PSIsInZhbHVlIjoiZ1o2amtvaUZ6TDUvam00MDJldjJoV09udHc0RktKa2RJQTZlM3U3eGdQWEsyclJaYnZQSHRZemlhUzllTjY2Q29jY3JQTHlJRm9MdFZ0T1Bta29pTVZvY2VDTkgwL1FoTmNzQlRtaFZXNVBhQi8wZHpOalhBL1dXUXBHSWtBQ0s4R1oweTcwUXZFVjRpY2Npd2ZoVFNoWC9YL1FSbitxdGRkbDgrVlhBOXc4cGRic3FzM21NQWZjRUhaRld0dWNRbnpqbGxFam5Qa1M1Mk9VWktnTWJRWlh0SmViZEp6eGcvVmNvSW0wR29LRjRJN0dUeTl1eFVIUzUwbWFXNHdaSkp5RUt0dGVQSklONldGYVF0UmV2YTdhbDErcHk2d0RsZFZteXNxbkVza1lWVUtYRTRIS2Nzei9RMWtJQzM1QmpHTFJ4VVNaV01VelYySXFmNVAzdmlqT0R4VU5BM2VTNFBjQXBudU5ZRVRpb2p3WjJ3aWFwS2tGZjF3emlyeHdEbE9qdVYwVzlsN0RZeWN5c0x5dml1dmFTeStWRitnanpTZ0NxSDFOTEhrRzg4eU1TVm9MVEpUL2t5Rk9RU2taTHFaYlVYdkFqbFdWd3ZsZFEvT25NY3hBQW5GWm5BWFhYVlhMN3lHVVd0OGhuODZGWGRoM3krR3luaFNTS0NaOGQiLCJtYWMiOiJhNzU1NTM2MTUxYzQzYjU4NzZiMjVmNTAxY2Y4MzE4MDY3NzlhOTM2ZWE3ZjZjYmQwYmViNGIwZDk3MmQ3MTFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-26490153 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:18:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUwUEt1VTFMZEFQSUZ5VENGemplRmc9PSIsInZhbHVlIjoiV3grbis1TDYvNkl3d1I0S1hjUmVlU0RPMzVYcDYrV0NPYlpxaEd2T2dWNS95MzQ2eTlOMkZIbC9rZ0JkNVRQUjhnczNncVFOUXRFZGV5K3QyWHUxUkJib0pTelB6L0NjM3J0SGVRWUdYQS9mbi9vZWZ0YmZ2TzBNWjZ5UzFTU1lidU9ydE9HRzFNNkxBZ0w3cXYyT2dFVGg2WUhyWXk5TkpsMkpkcFBjZHI5TDBJNGVwMU1yaG1NNFI3aGpSdjRPeFNlTWV5STg1MVY5NlBGYzI1Yys3V1BkU0tUT1ZoSFBOS29DZTI3M001Vk5qZ0NWUXBqdS96cGpnOCtWYmg5V09pSG9DMG1hQjRoOGRBRTlBdDcvUERmV3JFd3IxWDlTeXlGT2c2WXJmSjNLcXVLMkNSNy9YaXYzd1ZaTVFRcEFxUDRQUkFXdjlYcDJSMHM3SEIvUVB3MEcrckRKUm5wRE5GMXBLZHhha2h6RGYyaWFZRWd1dEdObTVLV3hwY0gybGhVdndyVXkrVTFvaW55UkdFVVJuS3BVM1kxSlAvWGR1NzhkMkt2KzArZjhoMWJIODBXVTVML1J3N3Nqb08wUFpTbGxkLzVSOThUOTR6eWk3VHRYWlVhUUh0NUZRWi9JQTFQR25SZHZCYlpXUkR0UXEzcWQyN2xHT05MM0VlcDMiLCJtYWMiOiI4MWQ4MWExNzZlNmJlMWEwMDQ4Nzk0NTJmMTBlOGQ0OWI4ZTRhYWQzMzEwMWZhNTQyOTM2NTU0Yzk1ZmIwNmNhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlAzWENFODJKbWpxUHY2UU9XNTJqeGc9PSIsInZhbHVlIjoiWTlqQ3ZOaWtGZW9pR3g4c2lNY0JpazgvUWl1bjYwNitRUTI4TEludWJ1R0pOMENkdGl6V29wemd0YURGUW9RRUt0NUtHRklHTmRBYW9BK250T3VnOUZyK1ZXdFpGUGJpQXJJK05pVlVyZUFoSU1qeFU3OGhEY3ZHVE9taHhzaHpaLzFOeUV0cmZlOVNPTnd6dXZNVTA3U0I3cDJjL1dNajhmaDZ6alNBYzM3c2NJa1B5VDFnbExUOVJLS0MxWUJVUkczSVpEelEyeTcxVGR0MDFEOTB1Z04yazhackZmbVFBell4ZVpmek5rYmo4VHptTnJ4ckJEbmY5bU1jd3VpQWNvT24wMS9Ucjlxc2tpNDVoVlA1RDExQTJqZ0JiMjhQYVJ1TTFjbWQ3aUJxdnpLY0V2SXg5MTlmeC8rb1NjVmFGanlCY3RhdmJZSFVNK0duTXh2RjdCK3NUait4Y0h1MTVsNi9FWnIvSFFleElLS3l5OVBZTExmWVdLU3djTEJ3TWwzaE9ZZHZEcW1aTytSZUdmK09VRkVZaU9DcmhQL3dWSVBBT1ZSYVU2Z3hMQkl4eUJxZTk5Wkx5b0VpNXlCemkrWmNjMDVmY0RFeDJWMDRPZ1FBUXRnRHg2dCtpS2pqOVltY2ZJQmFLSml6b3RBbjhiOERianBRUjZLT2JPTW4iLCJtYWMiOiJlY2ZmMThiODU1ODE0NDBmNDlhNzQ5OGIyMzVlMTMyNmUyODY0YjdmMmQzYTA5ZDdiMjM1YTljMDNkNGU5NDg1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:18:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUwUEt1VTFMZEFQSUZ5VENGemplRmc9PSIsInZhbHVlIjoiV3grbis1TDYvNkl3d1I0S1hjUmVlU0RPMzVYcDYrV0NPYlpxaEd2T2dWNS95MzQ2eTlOMkZIbC9rZ0JkNVRQUjhnczNncVFOUXRFZGV5K3QyWHUxUkJib0pTelB6L0NjM3J0SGVRWUdYQS9mbi9vZWZ0YmZ2TzBNWjZ5UzFTU1lidU9ydE9HRzFNNkxBZ0w3cXYyT2dFVGg2WUhyWXk5TkpsMkpkcFBjZHI5TDBJNGVwMU1yaG1NNFI3aGpSdjRPeFNlTWV5STg1MVY5NlBGYzI1Yys3V1BkU0tUT1ZoSFBOS29DZTI3M001Vk5qZ0NWUXBqdS96cGpnOCtWYmg5V09pSG9DMG1hQjRoOGRBRTlBdDcvUERmV3JFd3IxWDlTeXlGT2c2WXJmSjNLcXVLMkNSNy9YaXYzd1ZaTVFRcEFxUDRQUkFXdjlYcDJSMHM3SEIvUVB3MEcrckRKUm5wRE5GMXBLZHhha2h6RGYyaWFZRWd1dEdObTVLV3hwY0gybGhVdndyVXkrVTFvaW55UkdFVVJuS3BVM1kxSlAvWGR1NzhkMkt2KzArZjhoMWJIODBXVTVML1J3N3Nqb08wUFpTbGxkLzVSOThUOTR6eWk3VHRYWlVhUUh0NUZRWi9JQTFQR25SZHZCYlpXUkR0UXEzcWQyN2xHT05MM0VlcDMiLCJtYWMiOiI4MWQ4MWExNzZlNmJlMWEwMDQ4Nzk0NTJmMTBlOGQ0OWI4ZTRhYWQzMzEwMWZhNTQyOTM2NTU0Yzk1ZmIwNmNhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlAzWENFODJKbWpxUHY2UU9XNTJqeGc9PSIsInZhbHVlIjoiWTlqQ3ZOaWtGZW9pR3g4c2lNY0JpazgvUWl1bjYwNitRUTI4TEludWJ1R0pOMENkdGl6V29wemd0YURGUW9RRUt0NUtHRklHTmRBYW9BK250T3VnOUZyK1ZXdFpGUGJpQXJJK05pVlVyZUFoSU1qeFU3OGhEY3ZHVE9taHhzaHpaLzFOeUV0cmZlOVNPTnd6dXZNVTA3U0I3cDJjL1dNajhmaDZ6alNBYzM3c2NJa1B5VDFnbExUOVJLS0MxWUJVUkczSVpEelEyeTcxVGR0MDFEOTB1Z04yazhackZmbVFBell4ZVpmek5rYmo4VHptTnJ4ckJEbmY5bU1jd3VpQWNvT24wMS9Ucjlxc2tpNDVoVlA1RDExQTJqZ0JiMjhQYVJ1TTFjbWQ3aUJxdnpLY0V2SXg5MTlmeC8rb1NjVmFGanlCY3RhdmJZSFVNK0duTXh2RjdCK3NUait4Y0h1MTVsNi9FWnIvSFFleElLS3l5OVBZTExmWVdLU3djTEJ3TWwzaE9ZZHZEcW1aTytSZUdmK09VRkVZaU9DcmhQL3dWSVBBT1ZSYVU2Z3hMQkl4eUJxZTk5Wkx5b0VpNXlCemkrWmNjMDVmY0RFeDJWMDRPZ1FBUXRnRHg2dCtpS2pqOVltY2ZJQmFLSml6b3RBbjhiOERianBRUjZLT2JPTW4iLCJtYWMiOiJlY2ZmMThiODU1ODE0NDBmNDlhNzQ5OGIyMzVlMTMyNmUyODY0YjdmMmQzYTA5ZDdiMjM1YTljMDNkNGU5NDg1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:18:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26490153\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-383545629 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383545629\", {\"maxDepth\":0})</script>\n"}}