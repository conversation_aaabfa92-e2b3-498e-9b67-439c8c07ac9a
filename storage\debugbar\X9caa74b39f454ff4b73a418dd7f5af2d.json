{"__meta": {"id": "X9caa74b39f454ff4b73a418dd7f5af2d", "datetime": "2025-07-23 18:22:49", "utime": **********.361154, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294968.874255, "end": **********.361173, "duration": 0.48691797256469727, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1753294968.874255, "relative_start": 0, "end": **********.29574, "relative_end": **********.29574, "duration": 0.42148494720458984, "duration_str": "421ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.295748, "relative_start": 0.4214930534362793, "end": **********.361176, "relative_end": 3.0994415283203125e-06, "duration": 0.06542801856994629, "duration_str": "65.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46021024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00403, "accumulated_duration_str": "4.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.326211, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.561}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.337166, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.561, "width_percent": 13.896}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.345079, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 72.457, "width_percent": 14.64}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.351466, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.097, "width_percent": 12.903}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1033155931 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1033155931\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-564117129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-564117129\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-804357137 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804357137\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294965225%7C3%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikp5YUlRTXljYThha2hLN1dnVXY0Q1E9PSIsInZhbHVlIjoiQzJwT2ZQTWVkS1hOaVRnbXJINjNvKzdHZkN0bW50QjFOTjRPdmZ1WDdiTkFBaXdHWVdZNExWc1Y4ZTkvUWVNSE95Zmd0N1VWZUZiM0hTbDNLbDZ3eGFqN0VHVDluVUJxSkdla2crZjNVUm9QUkpTTEZISDYwR2dtV2ozK2lHWWNtTnYzQ3AxMmdHTVJyOHp6MjRyd1I0UVgzZjU2QlJVOXY4Wk9OUUhFY3RVdTkzODl5ZFRXK012eVhtZXhRckl1OFpuMXg3K2JDMHNNcDZ3aDB2RFE3QS90WGtEdnlnYUxVanMxeVhvMzdONy9XQnVoTk91TkJnZWRibUk2cGgwRUJRcUkwNDVEUHJYYTFFeUNJQnNndVNpNW94N1M2SXlhOWJDMGo2STUvREgxc2hBVmxadWpsMUQ5bnhtMmNlT09CRGpDc1A5NHRJelR0Qk8xK0hqN1JNcTNLV1k2NGRkNFhadVpMYlRScTl0V3ByN042Y0ViZGg0UXBJNFQ0TlJiRU92VjF2K3hqbTlpU1lCZnFvVE9Ycnp0L0tPbVdlZi8zRTIrTW5uMlhQclhiem5RMGMybC9DZlNTTmdpM0FWN2k4WXNPV01hY0FXNjQ2T1FOYTQwMW5UM3k4U1lrTm5xcnErQzVjanlKTWZhbkh6ZTFtTXFYOWwxRWpsSzArc3YiLCJtYWMiOiI3YjJmZWJmNDFkYTAwMjVhY2FjZmI3Y2NhNzNiNDBmNGY3YjI0OGRlODIzNTI1ZGUzNzkxZmYzMTJkY2QwNDhmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZReTU4SHpGNWpHNkJEZXh3d3A2OHc9PSIsInZhbHVlIjoiRnUvY25RZnlmeDVLaFdxN251SUFYeVJRV0FZR3FZS3Q5S0xwSUpUa3Y3dXFnQ0VndmxMSy9MelBseVRYc3pmQkdMQlY2NzBzcDE1YVJDMWpVSUJGV2JqbElVNE8vMFdRUUlmOTZvU0Z1T0lEclVjWE9lZFFsbVB1MWRZWGMxd0p4T2hELzNoQ3RpSjI5QTFhUTQvbUJlWTVVc3BmTW9EY29wSld4Y3lHN2p5SDdvbEkzRHJxQkhDTW9UN25TMk9ibG1jV2J3cHFYajU1ckE3amJWRFNTdGViUXNKNXNkWmNIYW5LMU4xbGZQRGRhMzZDWUlmdUQ2b0owblUxRFlkUmQrNThleXh6dkhiT1pnWDFMNWhRdDd3OEN2VjQ3WDJjZFlCTFVsdVFQbmhTMlRraEtJb0ovaVduZm05am4walNFVVduOVBWeEcwbkdFR1lMYjJpakRZam9tZ1M1b214TmkwVGF6NlNaazMwNTVtS3ZKNzlVY0NDS0lCc0lpb29heVVBbXdkYXZtNGpzTUZzTjBUVTBBTm9oUTU1bFFlSGllTjl0NEhZSmtQT242aFZab08wWVRJSUdpb2R3aGtzVXZKc1gyZHdwV0h6c2FhYU1hVWkwY0RGbXVzV09WaXVReStHSkF4bzh2bW5EaVFja2VETUpHUWF4VUt6WTFRekUiLCJtYWMiOiIwMzBlNmRiMzZlZGJkYWIyNGNlYmY2NDMzNzdiYmExYTk5ZTI2MDY3MTExYTdmMGYwNGZmNGM0ZDQ1OGYxZGQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-891315216 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6f86AL2UyJbEsnP8wClSGR79UCod8lW0WDs0fxKW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891315216\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldhMmd4dnVoaDg4dlpYREdLWDVmUHc9PSIsInZhbHVlIjoiQWNGeXBydkNRUGtoMTB3cGJwZzNrSlBDOTU2a29YQm9yMkxaN0ppbDFlNWc1WjA2L3NSZ1hrcVYxanUwZ0NJd0dqZWRGajNCUzFaT3poaTZrZDJNNVdGekp5ampEUndGREwrcnpYZGk1Q3UxaXcxRWhld2pHSlFLU2Y0SjJzV0JUenBwa2hvSEpOOFk2UkNReWxrUHJUQ3R3dGxxam9YdzJnZkFsR2dOOVBvZ21yOUVvaWh0bmFkQmJnMVp6ZmhBcFRDb2ZoYzRZcGc4OHVTb0pGY1NBYzZZUnhYUENveW9nSEtoYjgwMmh4VEl4dUFNYkhYUitJU0JCSm9XeHJVZU5sRnh4WlZlQndSalE5NSt4VkNDSU4yUXhTdkUwcURmWWwyYmMxdGxOSTZZQnA1MDVXT0poRC9lZ2J5Mkx3UTNlbjlNVGdVc2xhajRIY3NJM0Z6OWpwYnRvV002aDRteXpIR1hMdS9zQmhRS0F2N3I1M2lPOC8xRlIrc3BtSDgyYlNXSTlTOEdRQVR5a0RNcTdYVXQ0NkJSci9RUGEwVERnSDJwKytQS0pIYy9uQTQ5cHY1RmVKYTU1VDhVa2JEbktDSkUvZUJVSGR0VVlUckxXSE5qbkxPZzkycGpQQ2JTME5wZUlWZGI3cGhiak9xLzBaK0dPdHZwWDg5QnJLK3UiLCJtYWMiOiJlNDAxM2MzMzY1ZjYwZGJlMGZiOWI0NDlkYmNlNjhjNTVmZjkwMGRjOWMyNDcwYmIwMzViMDYzMDkyNmY5Y2RiIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijd3RVRaWElOTVRweDM1SGZFMFl4OVE9PSIsInZhbHVlIjoiQko1cXFRVnBTV0FseldwOEIvVnpibUZ3V2s4aXludEFkY3RKS3R2MEYva1ZNckZkUDRNNEhxcGhzSG5KWGVRVVJYNkJEUkdOSng3RlRySGdOSnp0cWFFTjA4cVhzMmZNN3JoOWY4RmZpSGxSTkhCTng2bkNtekloVGhUSFpKNC9PWWFGcXlxOGJDZmdnQmFmdjdVWnM4UWREeTI5dmJDdkZremNEb2t1alkxR3NsWVM5UkhPdUhrdTVhejBQVmgwci9za3JYZ2x5dkIvUHMvKzZFWURPdFJ1a1RyODhDWXh3d1lXWnkvaGtGUDNSUjROc2k2K3ovNXR6NHY1MC9IbVIyWmIxeUZuNnl5eStGQ3VvU1VMQURmeW9JajhYZnJRck02MDJLOUFVRHBnM3JCRGhZR1pCSHFTbk5HTVcydGZTRjV5WjE0SjFNUm1nekF0NkNaQTJSeUFVem00YzJSa1gzenpwTXhLNFh5Y2hETExqZHFDWVJNS3NYVHcxeVBHcGYrT3JvRGRGbDJoeXNUSEk0Y1diQldLS2xiaU5laVN3TFR1YXJ3VFhucGJjSFJ4bDc4TFJhV2VCeVdKWEZTRytrT28xTThuUzBFOW9kVFR0dy9pR1dEODhvRFgza2RYRTMvZFpuRjFFS095eFZTcTcwWmhVbUYzK3B2S2ZycmciLCJtYWMiOiIxODUyN2FlMzA3ZTBiMjMwNjE0OGY1NGRiYmQzZjM2YmJmZWEwMmM3MGU4N2RlZGFhM2EyMmEyNWQ1MDVkZjc5IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldhMmd4dnVoaDg4dlpYREdLWDVmUHc9PSIsInZhbHVlIjoiQWNGeXBydkNRUGtoMTB3cGJwZzNrSlBDOTU2a29YQm9yMkxaN0ppbDFlNWc1WjA2L3NSZ1hrcVYxanUwZ0NJd0dqZWRGajNCUzFaT3poaTZrZDJNNVdGekp5ampEUndGREwrcnpYZGk1Q3UxaXcxRWhld2pHSlFLU2Y0SjJzV0JUenBwa2hvSEpOOFk2UkNReWxrUHJUQ3R3dGxxam9YdzJnZkFsR2dOOVBvZ21yOUVvaWh0bmFkQmJnMVp6ZmhBcFRDb2ZoYzRZcGc4OHVTb0pGY1NBYzZZUnhYUENveW9nSEtoYjgwMmh4VEl4dUFNYkhYUitJU0JCSm9XeHJVZU5sRnh4WlZlQndSalE5NSt4VkNDSU4yUXhTdkUwcURmWWwyYmMxdGxOSTZZQnA1MDVXT0poRC9lZ2J5Mkx3UTNlbjlNVGdVc2xhajRIY3NJM0Z6OWpwYnRvV002aDRteXpIR1hMdS9zQmhRS0F2N3I1M2lPOC8xRlIrc3BtSDgyYlNXSTlTOEdRQVR5a0RNcTdYVXQ0NkJSci9RUGEwVERnSDJwKytQS0pIYy9uQTQ5cHY1RmVKYTU1VDhVa2JEbktDSkUvZUJVSGR0VVlUckxXSE5qbkxPZzkycGpQQ2JTME5wZUlWZGI3cGhiak9xLzBaK0dPdHZwWDg5QnJLK3UiLCJtYWMiOiJlNDAxM2MzMzY1ZjYwZGJlMGZiOWI0NDlkYmNlNjhjNTVmZjkwMGRjOWMyNDcwYmIwMzViMDYzMDkyNmY5Y2RiIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijd3RVRaWElOTVRweDM1SGZFMFl4OVE9PSIsInZhbHVlIjoiQko1cXFRVnBTV0FseldwOEIvVnpibUZ3V2s4aXludEFkY3RKS3R2MEYva1ZNckZkUDRNNEhxcGhzSG5KWGVRVVJYNkJEUkdOSng3RlRySGdOSnp0cWFFTjA4cVhzMmZNN3JoOWY4RmZpSGxSTkhCTng2bkNtekloVGhUSFpKNC9PWWFGcXlxOGJDZmdnQmFmdjdVWnM4UWREeTI5dmJDdkZremNEb2t1alkxR3NsWVM5UkhPdUhrdTVhejBQVmgwci9za3JYZ2x5dkIvUHMvKzZFWURPdFJ1a1RyODhDWXh3d1lXWnkvaGtGUDNSUjROc2k2K3ovNXR6NHY1MC9IbVIyWmIxeUZuNnl5eStGQ3VvU1VMQURmeW9JajhYZnJRck02MDJLOUFVRHBnM3JCRGhZR1pCSHFTbk5HTVcydGZTRjV5WjE0SjFNUm1nekF0NkNaQTJSeUFVem00YzJSa1gzenpwTXhLNFh5Y2hETExqZHFDWVJNS3NYVHcxeVBHcGYrT3JvRGRGbDJoeXNUSEk0Y1diQldLS2xiaU5laVN3TFR1YXJ3VFhucGJjSFJ4bDc4TFJhV2VCeVdKWEZTRytrT28xTThuUzBFOW9kVFR0dy9pR1dEODhvRFgza2RYRTMvZFpuRjFFS095eFZTcTcwWmhVbUYzK3B2S2ZycmciLCJtYWMiOiIxODUyN2FlMzA3ZTBiMjMwNjE0OGY1NGRiYmQzZjM2YmJmZWEwMmM3MGU4N2RlZGFhM2EyMmEyNWQ1MDVkZjc5IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2067230363 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067230363\", {\"maxDepth\":0})</script>\n"}}