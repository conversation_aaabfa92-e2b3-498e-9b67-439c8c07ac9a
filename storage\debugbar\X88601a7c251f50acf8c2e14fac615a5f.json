{"__meta": {"id": "X88601a7c251f50acf8c2e14fac615a5f", "datetime": "2025-07-21 01:57:22", "utime": **********.183064, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753063041.726766, "end": **********.18308, "duration": 0.4563138484954834, "duration_str": "456ms", "measures": [{"label": "Booting", "start": 1753063041.726766, "relative_start": 0, "end": **********.113065, "relative_end": **********.113065, "duration": 0.38629889488220215, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.113075, "relative_start": 0.3863089084625244, "end": **********.183082, "relative_end": 2.1457672119140625e-06, "duration": 0.0700070858001709, "duration_str": "70.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46008512, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01357, "accumulated_duration_str": "13.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.143753, "duration": 0.01251, "duration_str": "12.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.189}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.164877, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.189, "width_percent": 4.348}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.171608, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.536, "width_percent": 3.464}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_search=%09Afrooz%20Alam&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-775590238 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-775590238\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1069586052 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1069586052\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-167614556 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167614556\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1262497949 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"167 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_search=%09Afrooz+Alam</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753063037351%7C26%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitRVTl3OEV6SHlHUTh0d2t3OWJmVXc9PSIsInZhbHVlIjoiSUlqVlh4VDI5S1J6cVdPMm84WWp5NDFTWjh6eTc4RlFSdjFSNzlRWEN5bGFzM3FnUTZaWkpsaXNXclhNMGtKL1NJcW1oODZabmhlOERoZzFoeW5ENDl4eUZmek9nR3FPc3ZvNVVqOFVGMHo3dkpHQjZoRFlFYmF5WjY4SlNJeENpL1A0NWlZOHpBUHI4THFTOElIR1pjUTlpc1F3QmVMWW00Z01jQUk4Ritvb0RUZXNkSzdETWozSlFTUW5TbjZYajhrWTJGUWlweGlWQnhjOU4veTJWN2o4WFl0K2taT2tTNVdwT2ZJM2JIWFRCc3NHOUV5RFI1dDdyZzFqRFdhWmFjK3I5Q2dHaE5pR256UFBHN0RvY2tHQWxYa2pJWSszUTQvZWVYL2JOeHlYb2l4NVh0THV0VnRKVThpVk1vemxVRmFvV0M4Ukh4QXJUL1Uwc25OT082bWhMSmRYVlFBMmhrU000NHdWWFEwSFJyQjBVeDVwLzN0SUxLVjZvRjZ4NGo5RzhFanorVWwrY1J1djF1RHF2MUphUW93UVFXbDk0ZlhTcGhrNmVsZWdETEk4N05BVjZFVlF1Sm9qNWE0N3hSWDUrOGJ6SzdVeCtsRHRsUFkyVzFiNDhoRC95WmhrYXBybVJOQktXTmthbmhrVFZqOE9VaitXLzFsVS96N2EiLCJtYWMiOiI4Njk5ODZmYmYzYTBiY2Y0MmVhMTRhZWRkODRmMzkyZjIxMWU5M2E1N2E0MDhlZjY0N2JmZjcxODBiM2ZlOTc2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iks0TnFZQVBNZ2E4d09xNUp0elhUbEE9PSIsInZhbHVlIjoiNEJVOEpDUk5BMVRZU0k2TFBqbk50bTB6aWRCZ3RsczlOUHdsNWZJVlJlWkUva3VjVms5Z3F5R2JaMmM3ZUkzMUNiQU1yTlZDVU9mOGJOemhNSEZ2QUgwakN3eVNXRVF3MnJ6U0dRMnNPaVltT0FlWnN3NjlQVXFZdE45WFQ0c2d5WGgwUGxMRGxhQVJhaWhidXBmKzdyTzZEQjlyNVloeWdRcG1zNytwbzhpNzZPVGJMYXN2VkNIRHdBdXA2K3NyeEFMMTMxWHErQ0oyM3FRVHQrTzdRbUVTMXNwYk16N1gycVJVQ1hPb29Jejk1a1NLMnp3WXZOWXBCVUEvbW11SEFlZkxqR3BMRDByUUFGdW5DcnNWYzZxcy9EQld2V3hYa1ZDZC82b0VrdGRkdzY5SCtBSEZhdVhWQW1ZeFNrRS9EZVl4SCt0bWdhbFVBMFArdDRqY0Y2VzRsdVpYb0FJQzV3ODl0cTBmS05EbGR0RURFOGhQVG5iMmJMZkF0cGFQcnhPUW1YMHh3RTIxZGpiZzNtRVR3VDVhUndhUXVrNXJRSndwalZCVFNxci9qU0NHTkZMUXlZVStXUW9IVnlqcGhVVFU5SzBFQXlFb0RmV2NXZ09KNVlnNUdPS2N0c1BmQUdpMEhJano0ZGJyd0p3VVFZYThtNUF4L21pd05Ta08iLCJtYWMiOiJmZmQ0NDI5YmEzOGI4YjU4MjA2NzNjMzQ1MTUzNjRhOTNjOGU2NDliODU5MGQzMDJmZTExZTg1ZDlkYjc1MTcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262497949\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1981195824 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981195824\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1391335962 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:57:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBlK3RtSWw2VEVBWjk2NzZYSVVJeWc9PSIsInZhbHVlIjoiclNwZkZTQWlDWTZhbVV5WWVPUFhjK0RWMlpxT3kxYUltRzRJY1k5UUZhenBNMXVYUFRORGVTdDRraHdqTDlPQkhhbEwxSnVtZTE5eXZjMnNPNUpLOElLZUkvdWhVRysvNk12akVONlBuWU5JZXFxY3prZlFlbFlrdzVXQkxyV3lhVjQ4VDF1eXI1bFNvek9RUERMRUpnc25HQ2VCTTZGejhoMnlLVzF0alBoblFIS2V4OStoazBKZTk4MHN2TVg0c29rbXVDNkQyVjNoVTUweVNPK3NtK3pIN2dCM3puSDB0WEVUS1VHQ084WTI4b25XYlR0OUhRTCtweStOZGFOTDBkbGdWWHdVTkszWTlBcmZabmFreTU0T2RMTlFRRTZLOVFkbUJEWWFVdml6dmhOSUk4cHJaZlZUcThlMURKWEpLcUJSbGhxYUNwQ2R1V0JoRjA3cTVYbzU3dHpmcFY1NU1ROGJvekRueGIvUkVYdGlzVkRnQVBiTlRqeHhQa2J6WlhveGRualJTVFJkN2Vlckw2Yk14eTAxZHZHOEJkTHRROWNXSnJsLzVHS3NSc0NsYTN3djhMYkJ1MWtCTG4ybkhhS2lLeGJUU0d0dVM0QW82cUU1b3hRMnBBOTU3Nm4yU0JOSW5vb1ZaRFBZVzViRzhwSlJYL1FqYS9qTUl6MFciLCJtYWMiOiJmMDMzNWEzNGY4NjU2YmZlNjczMTllNjNkOTA5ODU0NTk5M2YxOWQwMzMwOGNiNzYxYWJkYzcxMDlkYTAzNGE2IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRiR2V4NENoSHpYR3B6QVMzcGVBY3c9PSIsInZhbHVlIjoiMmp5Ynh6aDFGV3Q1QTlyYTZFWmdrd1pHVHVqdVpmcEQwNEsxZFlNckxtbFNVV2pacW1CbE9kcitkellwcm5sY21lS3lka2J0RjFodE9HRGFlMEVoK0MzcGRSdjExemxkVUR0WjN4SGFxcXAwNU5wRUZMclpSNVduMngzeEJDVGtVdVorV08xMlpMZEd5aFlyT1hPbUhJRDNVY1hjWUs4aDNIbTU0UHZTS2ZBZlNYOWc1ZnREaHRENFh3NCs1QTdtbFVSVUgrVEhVbTVPWlFZaEZsMUpoT2s5b01FUW5GR0UvRUE1bUhreUNCWk5JYm5zSVNEMFRQRCswLy9tNEtDTVdWVXQrblFSbDU4bVRMM3Z1NGRaWlRwM0R0VmZNeTRsZGkvdHFFc0hqK1FuMkt4Sm41Y3JXYWI2Q0FMeDgvWEJ3dWpTMnNvNXdzYTVyYWlUZThnN0tnL3NINEZ6UjVCZy9ZblcvWUlCYjZjSTY1UnhBK1BLcEIzSUc0MWsvalpwWG5SaVJHQlZVc0gvZVljbWIyYUJ1UFc4SE5EMUJrQStSSUYvd0FYZEw4Y3Nnb01sL0ZHRmlobXExd1NSKzdGWXVtaFNDTnZsV1FCREFhc3pKbHlOR2o3WnBrZks5OE40SmNoZGk4RXJKQWR5czJJTDR3Ym96NnRyRm5DcUF5cU8iLCJtYWMiOiI5MzNkOTBjOWY3OWRiYWJiMzAwMjQ2YzdhNTQ0OTVkYmU1NjE4MGZkZTEzNDNhYjczZmYzOGJjYTZlMWE0YTQ5IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBlK3RtSWw2VEVBWjk2NzZYSVVJeWc9PSIsInZhbHVlIjoiclNwZkZTQWlDWTZhbVV5WWVPUFhjK0RWMlpxT3kxYUltRzRJY1k5UUZhenBNMXVYUFRORGVTdDRraHdqTDlPQkhhbEwxSnVtZTE5eXZjMnNPNUpLOElLZUkvdWhVRysvNk12akVONlBuWU5JZXFxY3prZlFlbFlrdzVXQkxyV3lhVjQ4VDF1eXI1bFNvek9RUERMRUpnc25HQ2VCTTZGejhoMnlLVzF0alBoblFIS2V4OStoazBKZTk4MHN2TVg0c29rbXVDNkQyVjNoVTUweVNPK3NtK3pIN2dCM3puSDB0WEVUS1VHQ084WTI4b25XYlR0OUhRTCtweStOZGFOTDBkbGdWWHdVTkszWTlBcmZabmFreTU0T2RMTlFRRTZLOVFkbUJEWWFVdml6dmhOSUk4cHJaZlZUcThlMURKWEpLcUJSbGhxYUNwQ2R1V0JoRjA3cTVYbzU3dHpmcFY1NU1ROGJvekRueGIvUkVYdGlzVkRnQVBiTlRqeHhQa2J6WlhveGRualJTVFJkN2Vlckw2Yk14eTAxZHZHOEJkTHRROWNXSnJsLzVHS3NSc0NsYTN3djhMYkJ1MWtCTG4ybkhhS2lLeGJUU0d0dVM0QW82cUU1b3hRMnBBOTU3Nm4yU0JOSW5vb1ZaRFBZVzViRzhwSlJYL1FqYS9qTUl6MFciLCJtYWMiOiJmMDMzNWEzNGY4NjU2YmZlNjczMTllNjNkOTA5ODU0NTk5M2YxOWQwMzMwOGNiNzYxYWJkYzcxMDlkYTAzNGE2IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRiR2V4NENoSHpYR3B6QVMzcGVBY3c9PSIsInZhbHVlIjoiMmp5Ynh6aDFGV3Q1QTlyYTZFWmdrd1pHVHVqdVpmcEQwNEsxZFlNckxtbFNVV2pacW1CbE9kcitkellwcm5sY21lS3lka2J0RjFodE9HRGFlMEVoK0MzcGRSdjExemxkVUR0WjN4SGFxcXAwNU5wRUZMclpSNVduMngzeEJDVGtVdVorV08xMlpMZEd5aFlyT1hPbUhJRDNVY1hjWUs4aDNIbTU0UHZTS2ZBZlNYOWc1ZnREaHRENFh3NCs1QTdtbFVSVUgrVEhVbTVPWlFZaEZsMUpoT2s5b01FUW5GR0UvRUE1bUhreUNCWk5JYm5zSVNEMFRQRCswLy9tNEtDTVdWVXQrblFSbDU4bVRMM3Z1NGRaWlRwM0R0VmZNeTRsZGkvdHFFc0hqK1FuMkt4Sm41Y3JXYWI2Q0FMeDgvWEJ3dWpTMnNvNXdzYTVyYWlUZThnN0tnL3NINEZ6UjVCZy9ZblcvWUlCYjZjSTY1UnhBK1BLcEIzSUc0MWsvalpwWG5SaVJHQlZVc0gvZVljbWIyYUJ1UFc4SE5EMUJrQStSSUYvd0FYZEw4Y3Nnb01sL0ZHRmlobXExd1NSKzdGWXVtaFNDTnZsV1FCREFhc3pKbHlOR2o3WnBrZks5OE40SmNoZGk4RXJKQWR5czJJTDR3Ym96NnRyRm5DcUF5cU8iLCJtYWMiOiI5MzNkOTBjOWY3OWRiYWJiMzAwMjQ2YzdhNTQ0OTVkYmU1NjE4MGZkZTEzNDNhYjczZmYzOGJjYTZlMWE0YTQ5IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391335962\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2046731915 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"169 characters\">http://localhost/invoice/processing/invoice-processor?creator_search=%09Afrooz%20Alam&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046731915\", {\"maxDepth\":0})</script>\n"}}