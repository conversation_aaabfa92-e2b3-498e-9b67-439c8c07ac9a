{"__meta": {"id": "X522f6658a35ede80baa165649b9fffd4", "datetime": "2025-07-14 17:58:31", "utime": **********.857821, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.42408, "end": **********.857838, "duration": 0.***************, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.42408, "relative_start": 0, "end": **********.804957, "relative_end": **********.804957, "duration": 0.****************, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.804968, "relative_start": 0.****************, "end": **********.85784, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "52.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0037, "accumulated_duration_str": "3.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.832397, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.842691, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60, "width_percent": 15.946}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.850487, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 75.946, "width_percent": 24.054}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C**********303%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjE5R0FHK1doM2VYc0tBSTZqQmF4U3c9PSIsInZhbHVlIjoiTGhBTHNvbXpPaW1GQUliSXg0V0tmZHJIZ1k3cUU2dHI2V2hlOWpaYWpXOHdTTlZMWCtuZUttVkhMOHgzSWJoNW80ZHVqNE96bGNPL1FHQlREVGxONGJoWERKcFZzTGZaVE40V3ZQSklZa3Y1K0c1ZHUrakM4WXlydE9tdXFRUk9KSnhONllEbllNam5YbGlSZkh6WEJvU0VvSUtTU1E2NkRrcDcvTVVwazBRWHNzSm9QRUZ5eDRFSnR6cUpsQkZ5QzRUUExXdkI2bS9DYTUreDdKaFkrT0dwbVA3MDFDbVEyekFrcGpVOXlKWjRiVnB6ckZkM2pmVElLcGp2dlN5ei94bWJvZWg0R2xSY3h4dW84MitUUDFLWjRsVzBCRFhFSTByN0taNVFUMTlXV0hyYkFSQS9QZVFhWUhPbnJpc3oyOEZDQlR1WnRENllmRGhlOEpGK1Y2NWkwaHBwWFBFQWpJVFNxTlo2SS92V3VVdFVpUlRma0ErczI3RXFzYVFQNDI5bVZQN2FDcTMyUkpCWVk0b3A4Zm1NMDB3a2M4aUtVVXFBY3pSV3gvbStObWJRV29hNnZEWU5Xd01XSlArTEh4dnY3R1labEU2bHZEM2hnTXIwVEJVV1RmT1drSGpyTThrTmxTRkYwenA5VkVyMDZ1bVY3VC8xNkpNWUVURloiLCJtYWMiOiI0NDVhNjI5MzM0YWU1ZWM1ZTE4OTMwODJmZTY5NzUwNmQwNDNiMDdhNmE0MDAzNzA3NTM4MWVlNGE5MDRkYWY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVscFBieHhEN1gzZEF2TFdiN3JOVEE9PSIsInZhbHVlIjoiRnJKbFc3U2lEY1RXb1NwSWh4c0tOMFlNZzJZTnNVU20vZGkxMFNRVUtJcllOaTdjcHVXcXZoNGMwanB5SGU4TnBNNUpyU21ENGEreFBaZzAyanFCVXRRZW52Q0lUTU9oSjJ4VXgwTnlsNnEwWnZFMVhRWmZtVmNmTm5LMjN1UEZsQmNldnRJcVMreFo3MnNnQ3EvVlpJOVFYNlcxaW01ZlhFb0QzNVhVOFEzOHQwMUp6ZmNTS3JxU1h1d3E2a2dxVTFFRTB0VmpGclBFSWhncmlRRElkUUJwaDdSTHdwaUplYUNRN2M4WTBOejBpcGhLKzFNQTZUYi9GK0ZFbEJzdElaMEV5TUJYejBjL2NhMUxHS1pFbzVFN0ZZTVB1Y003NUdMRFNRQ1M0V3IyUjhPQ1lyMHRycE5KbXBiSUZvVDcwbWhQSHN4Zk50MkFzWVA2OWdLYWF1Z1FhNmRlTjM5SDNSU2xZK0g1NGZpME1QZFVNN2RoME53VkZkMzhqRVFUbXlnbE1NV0paN1Z5MWF0blpvanhqM3VBWnEyODUySUlkZUtXM05zUHRoS2tGVUV5Qm9NYktaVll6SmpFditGR0JHNDZMcXBMcmlRM3lIYzRVS0NBVFRTV2p5ekpWY1FLbGlESThONjVuVVZ5M0ljYmhjUkcvWUM0eHcyNU5oZlIiLCJtYWMiOiI1ZGM4NGI0ZWNkMzZkZGRkZTAwMzNiOTJkYTc2MmJhMzQyNTUxYWUyZWRhMWQ0MmM5NzliZWEyNWYwZjYzZTQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1017685329 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017685329\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1305676760 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9PSmRtYytCOUx0Q0dOUTBVb21JakE9PSIsInZhbHVlIjoiaCt5dEFMeENwdEVvUjd6SDR3amVJSFhOWVV6eVZHUUYrK3VENCtsNVlDTW93Y0tPajlaa0VvdTc4RlJIbEdGSTQ0K09tM3ZMQmo1Q25NOFl3NVBGVWFqN1dxU0JoMm1FTmVZL2EzcjJTMnA0ZW11MHhPdWYzVG1PL3grUnhvLy9LMmY3NnRSMVg0clBxYTdtTG9sWFp1UW1VRnNQQTNoMDVXc0VzVFZTZGJlb01qdW41aE1wdlZGQzNka1RTYnJ0MXR4bDhEV1JYSVB5clpncjN5eTR5RjhXOUV2TWdmQlc2STI2bEw4bXU0Zi9iNHhZVlAzNG5TNUkrc0hTT2RCWEwvbVIyOS85R29pcWtUT1NHekhNamhpWDM5WmhBQlU5RTBKcm43Tk1LNzJBRDIyVGEzaGRuTkNKalhDdXFoTzJjL3ZLS01LRDlSbW9RWlJKbTk2bUFqZ3dZRURLcWYvRXMvZXFqU3pVYWZXb2lIeEtDNm1yWTllWnBTUzZleGNJNi9Mb1FPN0dDY01xSlhVNXB0QUk4NXg3MWVQMXJEWHJxNkdoSGVXSC9lWGxzbXBFQ2xxdXhjRzhPd2dIdnNVbzJEaGhma09tVEtEZk16UWUwWGhOclhNdi9ycHNmZ0o3LzQ3MmlrVTFCTy90MGg5WG95QlV5M2ltSEtFRURxdlciLCJtYWMiOiI0YTA4ODZmNTk2OGU4ZWQ4YmFmN2U5YTIzMGMwNzVhMmJhNjY1NzUzNGQ0MjE3MjQ2NGEwZTgzYTc2ZTM2OTFlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlM2S25aTTVzWDMvNm1zM2M5THpybHc9PSIsInZhbHVlIjoiM2QxRjVZR2NIc0RVL3pOSm5NU1JCS1RNeVVkdTF4bHlQTkxlRi9GdHl0d2xBdk5FTEVPT1ptdTlJeGxzUVNnYTR4MGprVk1GcW02VHRxYkh0T2ZkbHgwMjVmcGlnVUppQzVKczUzcEYvcHFIWTFwZEE2bndjT29JbE51QU0xeXJCQTBWM1ZUVXR6YzRXYk1Dd01FdFhreFZxaWtpQW91MFFqcGcwR3VYeE9GVC9XWkJYMUFsenRUK2w2Y0pFMjZsVHhWczNpdTc4ZXZyOG5DcDg5UUhvZDlwN2Y3MCttaEJVUlR2QmtoRk90UHV0d2dnbVpIVlRyWSsrQi9aeDNTd2Z2RnJBNVR2dG5kZWRrVXBBUVEwT1V3ZU9uZXU1a3VXckd0dFJGYmhsbUFiSlJ5bmJPd3pMQ3MzL3p3THlqNnRqdWpqeFdTenVQUk1VNTVBUFFCeEJMZVA2WTJqT0dzaGhvekZDRHdEeWNYVEFITEJiU1p3eVU0MGFKc3gzdXhyU2x2TStTaFZVY05iNXBxelQyb0dKaW04SnBWcitMODBtYVhmKzlIblVqb2F3Y2pJZWJYakNobjJ2cld5YVVzK1pWcFhZZ1Y0Ukc3WlMvY3ppTElBRGhTTXhncmZsclBPNEk2TXFzaGttUGRHbTJiR0xlbEswRG1CNk55NjgrelYiLCJtYWMiOiJkNjg4OWUxNjY4MGE4ZmRjMzI1MjI0Nzc5YTNkZjNkZmQ5YzhiODQ1MTZkZmEwMzBkNWM5YjAwMTY0NDA2OTE1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9PSmRtYytCOUx0Q0dOUTBVb21JakE9PSIsInZhbHVlIjoiaCt5dEFMeENwdEVvUjd6SDR3amVJSFhOWVV6eVZHUUYrK3VENCtsNVlDTW93Y0tPajlaa0VvdTc4RlJIbEdGSTQ0K09tM3ZMQmo1Q25NOFl3NVBGVWFqN1dxU0JoMm1FTmVZL2EzcjJTMnA0ZW11MHhPdWYzVG1PL3grUnhvLy9LMmY3NnRSMVg0clBxYTdtTG9sWFp1UW1VRnNQQTNoMDVXc0VzVFZTZGJlb01qdW41aE1wdlZGQzNka1RTYnJ0MXR4bDhEV1JYSVB5clpncjN5eTR5RjhXOUV2TWdmQlc2STI2bEw4bXU0Zi9iNHhZVlAzNG5TNUkrc0hTT2RCWEwvbVIyOS85R29pcWtUT1NHekhNamhpWDM5WmhBQlU5RTBKcm43Tk1LNzJBRDIyVGEzaGRuTkNKalhDdXFoTzJjL3ZLS01LRDlSbW9RWlJKbTk2bUFqZ3dZRURLcWYvRXMvZXFqU3pVYWZXb2lIeEtDNm1yWTllWnBTUzZleGNJNi9Mb1FPN0dDY01xSlhVNXB0QUk4NXg3MWVQMXJEWHJxNkdoSGVXSC9lWGxzbXBFQ2xxdXhjRzhPd2dIdnNVbzJEaGhma09tVEtEZk16UWUwWGhOclhNdi9ycHNmZ0o3LzQ3MmlrVTFCTy90MGg5WG95QlV5M2ltSEtFRURxdlciLCJtYWMiOiI0YTA4ODZmNTk2OGU4ZWQ4YmFmN2U5YTIzMGMwNzVhMmJhNjY1NzUzNGQ0MjE3MjQ2NGEwZTgzYTc2ZTM2OTFlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlM2S25aTTVzWDMvNm1zM2M5THpybHc9PSIsInZhbHVlIjoiM2QxRjVZR2NIc0RVL3pOSm5NU1JCS1RNeVVkdTF4bHlQTkxlRi9GdHl0d2xBdk5FTEVPT1ptdTlJeGxzUVNnYTR4MGprVk1GcW02VHRxYkh0T2ZkbHgwMjVmcGlnVUppQzVKczUzcEYvcHFIWTFwZEE2bndjT29JbE51QU0xeXJCQTBWM1ZUVXR6YzRXYk1Dd01FdFhreFZxaWtpQW91MFFqcGcwR3VYeE9GVC9XWkJYMUFsenRUK2w2Y0pFMjZsVHhWczNpdTc4ZXZyOG5DcDg5UUhvZDlwN2Y3MCttaEJVUlR2QmtoRk90UHV0d2dnbVpIVlRyWSsrQi9aeDNTd2Z2RnJBNVR2dG5kZWRrVXBBUVEwT1V3ZU9uZXU1a3VXckd0dFJGYmhsbUFiSlJ5bmJPd3pMQ3MzL3p3THlqNnRqdWpqeFdTenVQUk1VNTVBUFFCeEJMZVA2WTJqT0dzaGhvekZDRHdEeWNYVEFITEJiU1p3eVU0MGFKc3gzdXhyU2x2TStTaFZVY05iNXBxelQyb0dKaW04SnBWcitMODBtYVhmKzlIblVqb2F3Y2pJZWJYakNobjJ2cld5YVVzK1pWcFhZZ1Y0Ukc3WlMvY3ppTElBRGhTTXhncmZsclBPNEk2TXFzaGttUGRHbTJiR0xlbEswRG1CNk55NjgrelYiLCJtYWMiOiJkNjg4OWUxNjY4MGE4ZmRjMzI1MjI0Nzc5YTNkZjNkZmQ5YzhiODQ1MTZkZmEwMzBkNWM5YjAwMTY0NDA2OTE1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305676760\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-765543904 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765543904\", {\"maxDepth\":0})</script>\n"}}