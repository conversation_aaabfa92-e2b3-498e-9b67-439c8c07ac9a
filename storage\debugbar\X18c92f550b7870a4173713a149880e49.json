{"__meta": {"id": "X18c92f550b7870a4173713a149880e49", "datetime": "2025-07-21 01:36:31", "utime": **********.875695, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.395215, "end": **********.875712, "duration": 0.480496883392334, "duration_str": "480ms", "measures": [{"label": "Booting", "start": **********.395215, "relative_start": 0, "end": **********.821672, "relative_end": **********.821672, "duration": 0.42645692825317383, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.821679, "relative_start": 0.4264640808105469, "end": **********.875713, "relative_end": 1.1920928955078125e-06, "duration": 0.05403399467468262, "duration_str": "54.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46006320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034200000000000003, "accumulated_duration_str": "3.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8499522, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.327}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.861072, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.327, "width_percent": 16.374}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.867477, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.702, "width_percent": 19.298}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1844343492 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1844343492\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-542829954 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-542829954\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-971853978 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971853978\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-34554076 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"149 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061761576%7C20%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imp1QXN6L3E4RnltTWtoamowTDBRUGc9PSIsInZhbHVlIjoiVUllSXpodWlEaHMzMGRVSFE1YWVhT1BrMnlLa2txSXBCV1QzMTNOOEpzOGxKRnc5a1dyUnVBMnp3Z2R2U0JUaVpIZlhwMkdyK1JlYm9ScklTZWRyT1U3Z0RmY0tnMjFYbE5ieEd2WWJhTndWRlJCODlPSExiV01kZXB2eUcrY1lERE5RY3lWdG45NTV2aDVucjhWNnJjbFJ0YVdoamZsekpnUk52U2d0UlVrSU1sSzFOMHpYVk1CaUIwWVhSazRSMTJxbWtudDh4cXJOMkxKMis4VUZ3UVk4dllmZHZ4aDkrNkJQcnVDcmtGSWdFNHZWZ2xCM1BkS2FvOFVseXk2dWRwTkFzNUIwSHdqbHVYaVFjZGdObzQ1cWRNTW4rR0R0bWJZbW1FNFdnMWtnSGRaYnI4aGZzV0JsSStQTTV6U2JWYng2WWhaWHEvbUtPcjgzTGpreHQycGZycW9DVTcwUTIrd3g5QkVxM0h4a000b24wSUw0cFlXM2djaFJYbVhlaEx3a0pzN3RoK3dZYm1XcDR1b3cwOWVrL0lmNGM0MUNCNnVoMjhwVmtiMGJXSkF2UEFFK09RcXpiRXNlSFhMekZtNnAyakVSUWNkYW15SkVKaGxxY0pLNHB3VEJqdjVQZG0wRmZWZDVGTjRNT0s1WE5wbkJFR2NURmdZWWE1c0wiLCJtYWMiOiJlYjQ5ZDY2Y2U5MzAwNGM5YzFhNzNhMDE1ZjNkOTIwMzZiOWJiZThkMDJmNjVlOWRlYjcyZjg1NTY5NzdlNWJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJ3RGdLUklhVW9EQ0paU1UwcDltVlE9PSIsInZhbHVlIjoiMk9DZ09JcGlzRFRuL3NnNXV2WUJPRno5YzlRM1AzbU5nZVZERm1UdGRnelhQQktEWWMzdnRDS01PaGtsZEtNcXRYVDNweFd0RnhEWXNEYlNpeE5COFovNG5PTlpNTG9pTXFHdjVsZDhSNWMxOUdLai9ob1Y5VS8zL3Biazh0c2pHakR3eU5NU3gzbUpHaEJ4cEhJL2RKNG1QaVV2LzZScWF5T2YwRm94Um5CN09kb1NRV2F6ODRkU3NYVFJQbnEyWURRQnVWeXFVUExiRzYvNU8vWHNkZzVjQTd4alFpT0Nma0dvVUxSYXh6bUFKYWRUampIWGpDRVBXcGVNbFJJam0vT0RVSTNyK0g2YWE5Q1pwb2hKY2RQUHhsS1Z6UXU5dkpwVWh3ZUxwZDV2eUF4R2JLWTVEVkVlVTJzN3NnSHFSRWRZZnlyTWZhOGxOeU9tSnJjZlhxSzhFT0lKbVlyWDlKSW5HM1h1eE50YmFRRW5ESnA0MHpOZFZGWVQzVEdydjhhLzhlOWd6N3Jzek53bTZlQnZRQnA5Z3NsTEthZU1YbWp2SCtVQ2oxZzFjS0FyNXpKRkFCNEF3cWFLbEpUVitmVE5NRS9CZWVQWUw2ZllSNStBdGlqS3NZQmNST2x2ZG9UUDJvV2drWGVxMjVGa0FMU3V3R1BmeDBvNFNhNmkiLCJtYWMiOiJhZDM0ZDM0Yzk3ODUwZjk4MDAyZGNmNjVmOTFhMDc1YmNiZjgyMDljMGM3YTQxY2E4ZWQwMGViYjg2YzUwNzdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34554076\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-589569469 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589569469\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-372928204 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:36:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9ZUWt0Ynh4dHh4VTJVeSt4VGgvSWc9PSIsInZhbHVlIjoiczJJcmZTQ1pjbGJBUUJiZ2w0M3d6QWppY3BTQUZQRFI5emNibjVVdmw4MExiT0pNbkppd0YybTArWEd6ck5XUXB0ZlpPTzRldU5WaTBOOXorME5WK0FobTdDemFsdFNrYlo4d2N6WldjNlJJYjNCNm1lQzZnS0t1S2Y5YjJ5eEIxcVE3NThBZEUvVzBURnpYS2lJbUVMNDZOOTlYYlI5aEdYTCtPUCtrZFg4S2pBQnhCTDBBNHN3ekd1QXp4U1ZpNTAyb2dsaDVPVUZrYlRzb3Q5QUJiQnplR1QvcUIxQ1l3Nk1tUkpzbjBaQ0pUUWFUSHJwalZxdDhoQ2k0U0RDTXpYQkRKVk5QbUtCYjNzeHFRK21BUVBWUU0zSHRyYjJ4elhBSXdmM0FUVGZEYm5TU0d6azVOa21jUFhJY3c2SjM1U3Y4amdjbkdVSUFLVnQ0YmZCMktXL3VaeTF6UFZjMGxTVWdKWnhNWDRrOE9YdVJzVEVlR3k3TTZ1cWpnd0g5TXE4emRwMUFkb2w5WEdMVThnM1hnUEtCMzN3VjVDUnJibEVCTmFYakRnK1o3bE5YR25JWllYaWhCVytZYjlxTDFvUi80SzlVOWlWdXQzSVFCWE4zWnFSOU5wMVVudiswYWZDdThDWDkxdENIK2l3ZTlodWVsSWIva2hhT0tqbHgiLCJtYWMiOiIxNDlmNzdjNmM2MDcxZWE5YWE3ODk2MmRjYmViMThmZGJiYjBhOGUxMGU2NzEyNzU2OTBiYWNjZWYwNTM3MjNjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:36:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitxYUk0TUFQTkg3K2FJNS95Y3h2Vmc9PSIsInZhbHVlIjoiWFNvZGV1a1dBWHJoVlJGaDhSemZKVHkxNEltME9tak1RVmlrYUtHVjFpRmMzR05XUnBDdFA5dUtmZUx3MU53MjhXOHZ1SUhWN0c0dmtJWWNXZm5paGJ1b2NrSTc5NXJ1MnkzNjdPNW5xOC9PMnJyQkg5RFV4UFlCS3Z6dndITVEyQ2xVNjdmUWFzc2o3T0JQN25UV1J1enMvVmhONlFodVJITHdvcXo4MTZOSndqR1JyNVppalh5Nm1QWS9EUXpBRGdWbHBDbVhTbUsxNFljcTk1d3pZWHA1VDFsanJ0dTZsbzQ3MW5hR2RncVdoRWk5b2RBNHdvay9zdU9QL2VUbS9acUd4VnI3NmdKa2Q0U1pycVV2VVNiQzFjS0RQQWRhTzhNdVNVZnRuZDAzUk1HVzFuQXZEVElkNHV5VHpEV2dmMURCQTFUaVNUVHpaVkNNUnQyTnEwa291L1lmZllxdXlGTE0rUlFYMk4yYmVQK0xPalk0Qkk0SjMvR1REZ2RFN0hwSzlWSFVwS0U4ME5qdkZsbEFudy94K1FiT0tkRERBMW9jcThmTGZTWjMydUVzd0QwaDRLNHJuVlpydXRqVjl1NFZBVXNHTVhwOUFXM1RURjE4Wk5EVmRyc1lpUjFnRmJVK0ExK2E5cFlIRnVjZklhYVJncnJFTjJGMVMzNGIiLCJtYWMiOiJmOTEwNzkwYjY0OTgwODUxZDk1YmJlZTViOWUzNWUzM2NlMDI2Yzc2MjVmYmE3YzU0MTY1ZWZiNWY1N2Y1N2VkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:36:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9ZUWt0Ynh4dHh4VTJVeSt4VGgvSWc9PSIsInZhbHVlIjoiczJJcmZTQ1pjbGJBUUJiZ2w0M3d6QWppY3BTQUZQRFI5emNibjVVdmw4MExiT0pNbkppd0YybTArWEd6ck5XUXB0ZlpPTzRldU5WaTBOOXorME5WK0FobTdDemFsdFNrYlo4d2N6WldjNlJJYjNCNm1lQzZnS0t1S2Y5YjJ5eEIxcVE3NThBZEUvVzBURnpYS2lJbUVMNDZOOTlYYlI5aEdYTCtPUCtrZFg4S2pBQnhCTDBBNHN3ekd1QXp4U1ZpNTAyb2dsaDVPVUZrYlRzb3Q5QUJiQnplR1QvcUIxQ1l3Nk1tUkpzbjBaQ0pUUWFUSHJwalZxdDhoQ2k0U0RDTXpYQkRKVk5QbUtCYjNzeHFRK21BUVBWUU0zSHRyYjJ4elhBSXdmM0FUVGZEYm5TU0d6azVOa21jUFhJY3c2SjM1U3Y4amdjbkdVSUFLVnQ0YmZCMktXL3VaeTF6UFZjMGxTVWdKWnhNWDRrOE9YdVJzVEVlR3k3TTZ1cWpnd0g5TXE4emRwMUFkb2w5WEdMVThnM1hnUEtCMzN3VjVDUnJibEVCTmFYakRnK1o3bE5YR25JWllYaWhCVytZYjlxTDFvUi80SzlVOWlWdXQzSVFCWE4zWnFSOU5wMVVudiswYWZDdThDWDkxdENIK2l3ZTlodWVsSWIva2hhT0tqbHgiLCJtYWMiOiIxNDlmNzdjNmM2MDcxZWE5YWE3ODk2MmRjYmViMThmZGJiYjBhOGUxMGU2NzEyNzU2OTBiYWNjZWYwNTM3MjNjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:36:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitxYUk0TUFQTkg3K2FJNS95Y3h2Vmc9PSIsInZhbHVlIjoiWFNvZGV1a1dBWHJoVlJGaDhSemZKVHkxNEltME9tak1RVmlrYUtHVjFpRmMzR05XUnBDdFA5dUtmZUx3MU53MjhXOHZ1SUhWN0c0dmtJWWNXZm5paGJ1b2NrSTc5NXJ1MnkzNjdPNW5xOC9PMnJyQkg5RFV4UFlCS3Z6dndITVEyQ2xVNjdmUWFzc2o3T0JQN25UV1J1enMvVmhONlFodVJITHdvcXo4MTZOSndqR1JyNVppalh5Nm1QWS9EUXpBRGdWbHBDbVhTbUsxNFljcTk1d3pZWHA1VDFsanJ0dTZsbzQ3MW5hR2RncVdoRWk5b2RBNHdvay9zdU9QL2VUbS9acUd4VnI3NmdKa2Q0U1pycVV2VVNiQzFjS0RQQWRhTzhNdVNVZnRuZDAzUk1HVzFuQXZEVElkNHV5VHpEV2dmMURCQTFUaVNUVHpaVkNNUnQyTnEwa291L1lmZllxdXlGTE0rUlFYMk4yYmVQK0xPalk0Qkk0SjMvR1REZ2RFN0hwSzlWSFVwS0U4ME5qdkZsbEFudy94K1FiT0tkRERBMW9jcThmTGZTWjMydUVzd0QwaDRLNHJuVlpydXRqVjl1NFZBVXNHTVhwOUFXM1RURjE4Wk5EVmRyc1lpUjFnRmJVK0ExK2E5cFlIRnVjZklhYVJncnJFTjJGMVMzNGIiLCJtYWMiOiJmOTEwNzkwYjY0OTgwODUxZDk1YmJlZTViOWUzNWUzM2NlMDI2Yzc2MjVmYmE3YzU0MTY1ZWZiNWY1N2Y1N2VkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:36:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372928204\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1527304692 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"149 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1527304692\", {\"maxDepth\":0})</script>\n"}}