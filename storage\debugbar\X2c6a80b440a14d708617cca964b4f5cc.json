{"__meta": {"id": "X2c6a80b440a14d708617cca964b4f5cc", "datetime": "2025-07-23 18:20:34", "utime": **********.069172, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294833.564231, "end": **********.069189, "duration": 0.5049581527709961, "duration_str": "505ms", "measures": [{"label": "Booting", "start": 1753294833.564231, "relative_start": 0, "end": 1753294833.981058, "relative_end": 1753294833.981058, "duration": 0.4168269634246826, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753294833.981066, "relative_start": 0.41683506965637207, "end": **********.069191, "relative_end": 1.9073486328125e-06, "duration": 0.08812499046325684, "duration_str": "88.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46536336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027299999999999998, "accumulated_duration_str": "27.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0152402, "duration": 0.02574, "duration_str": "25.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.286}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0513408, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.286, "width_percent": 3.516}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0585098, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.802, "width_percent": 2.198}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImdFL1dKV2YvZHdlVHVQb00xSGw0L2c9PSIsInZhbHVlIjoiNmVFYmVjV1hpZjFta3lvMWJtR0RBdz09IiwibWFjIjoiMDVkMDM2MjM1YmYwOGZhNWY3YjQ2YjE3ZmRlMDZlOWE5NzhlMzBmMTE0MTA3MDdmZjVjMzlmYmMwZWQ2ZDQ4MiIsInRhZyI6IiJ9\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1674272915 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1674272915\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-820437948 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-820437948\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2114365652 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114365652\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-689165115 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImdFL1dKV2YvZHdlVHVQb00xSGw0L2c9PSIsInZhbHVlIjoiNmVFYmVjV1hpZjFta3lvMWJtR0RBdz09IiwibWFjIjoiMDVkMDM2MjM1YmYwOGZhNWY3YjQ2YjE3ZmRlMDZlOWE5NzhlMzBmMTE0MTA3MDdmZjVjMzlmYmMwZWQ2ZDQ4MiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294702561%7C4%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImY3MWdpbTYzdUZ1WTVWL0pKaE1HOWc9PSIsInZhbHVlIjoicWRmOEwwVjVpc2NnU3RCOVdzR0poNFF2cWU3YnNrTVdubWJ1NjVCRXBkNzU5VC9MejB6R0g0eHQxR2ZPYlRsL3JuVkRSTnZpVmx5MjNpb2pIOUc5cXRpNjZSMGZ0eUp4MUxZS3lLZmN4MEdBT2ZBVkJZRllRRGRPc3hUOUU5Uk5LNWovSHR1R1c4N3BCbkxKd0VIYWVsQ3dDaitZdEZWbk5UU0ZrWFhQVWtiMytOZHpRR2FKWU9qdU4wOWlpUVZhQjRkbkxxZUFtYm1BUEk3SGdHcy9HMnRmRkdwd2RzQ0NiL2ovb05BSkwyOWYybEpKL2xSWEkzMjgwOEk3NzM2NWxEakp5aERKZk5XWkc3WTNieWdWOVhzRjZzaUc5RzdibE12MXdhOGlEUVF6dFFBUUFnWHF1L1BVUlhYQTUzWU1RS1ZMQ3RiNGFaQVlhVkQ2R2U4UEs2TTF4c1o2a2RDV0NWRmtzRGRKTXZINjBGbVhJY21DWlZ2d05pRGt2NzNsZE42ZTV4MFFvajZwb1JIWW92VXlTemtWVm1TVWN5NTRjMlVpREIySWRsbFZYdzF5dVM3Mkk1MnQzZGg2UWZzOHVJVFJDVE5CaWhHZ2ZOcG45b0x6eS9zUW9UUEpPYm5PbFhNYmhQa3JKK1ZvSGMwRk15MjUwVktUTTVaditTdFAiLCJtYWMiOiJiOTE0ZmE3ZTMxNzE3YjE0ZmIwMTRlMWU4ZGM3OTY3NTU1ZDk3MGYyYzQ0ZDk1MWI2ZjMwMjFlNzg4YWIyNWNhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZmZ0N5aW5Wc0haUkRqUWFWY25jWnc9PSIsInZhbHVlIjoiRktadGp3a1h2VkxtV0NhdG5uY1Q1TStDQWQ5cThnY0JUMnJVbjQzU21RUkhxcXVqU2ovTWZQLzVCL1M4QzlGYnJLQzRsWDBMUVRZZ1VaOUpsQ3NjOWhVQStxWExVanRzMUlabjNlcHE1VmZOUEYwQ2Fycit2WktMZUhKWXd5VUhrUjhxYlp4em8wcCtOODB3eS9wTWhqZEIyNGZFL0gwVUExbGFZZHR2YW9DVkdLcTNpcFg0cVRkRlE4QUJIT0NFcEpkVVAyWHpOa056UlZTb1VEdVA1S092TURtODhhWXZkU3EzeVN2bG50bTRLOEhsZHJyZVEvSU0zZmI1NVEraU5abVd5MDRKUFBqbWlRQTl4OWVjK0NZb0tpUU5seFdJMFdKOEY3aXNMTzA4cDR1OExoV0RyZU5RT2FMSFJvR2V1c01iY1AzWHUvY3lJRlJGYjdCbERzOXljUXQvSkNhakRxTzBUZW5VU1I0RVRlQTFIMHhacWdPOWgxcnpsNHNxaXdTdURZemd0TFA3SzVPNmZXb1FzdXZmQkZQYS9tYXVvNW15MXQ5V2R0L01PMmtMMVRYN2I2MlNCQWxxbk8yM2pwL3lWMDRWdkdRNlJxV1lMaUk1Q2VIakhiRW5EMzNqV2tNR29LWk9vblZIYnFnejBLWFhKUURzUmFKc1VpNEQiLCJtYWMiOiI3MTc5OTIyYjYxMWUyNzU5ODMwMTFmNzAyMmM5YTYwYTJjZDRhMjZkNGNlZWRjMzQyM2RkYzYxMjRjNzJiNWMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689165115\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1250725120 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250725120\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-736369683 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:20:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZWYitwQXV3L2pXT1YvZi9CZjlpOXc9PSIsInZhbHVlIjoiWmV4eGpXYVNSVWxMRWpEdjNxWERvTGtVc3lJaGRQaVVkNlRwQ3dwZkZHV0JIbGp1VmQ1dTMyM2tRcmxrMXdyZGtyQnJOK1czendCMFkxYklINllYSmhPQzJmeTJGcFFNU043N2tZZ1dFY2hEUkoxa2pweEJCSmQ1RWJpaFJPbGY0MHZEYTJhaFlCSk1GKzBVQ0hJRXlqblVkK2djdG5TdGE0ZXpzdUJzc0xqNUR5aytLYTArcElQZmtROFNlMmRTZWJHSGVsU1JQU0t4VVh6Q0lsb0NJNGFmam9iRjdVU1NKdlhqU1pJYlRESXVzaTUxaFRVOVJmM3ROaHh4bGE2c0ZzZjJ6azUxTk1pNE0rQVVHTlNEOXRyUGk4S2V3L2pkV3RCaWVaa0xqekM2ZnVndGR0UFJBZWNkRFJaT3RianB0dmZMalNxNWtzWldOb3Fqd0RTU3NVQmozbGRCcnFsYzZOQXRaRURoMFZhSDg5bkNmV3hONndGbmMwSW1VNzdYTkxQeEMyMVZEbWVWYTZDQ0FkS1JzbWlZR1FHQWlqMW9wUW5HSXIycVh1ZWg3enBHN3RnQ05aeEhNM252dVQrTWxkdS9GV1BZOEM0WVpaRlZuSUZuN0R6bno0VnJhYmdNNVpVNGJiWE8zNFowdFdQNVlkN3JkeFUvMytrY2xIMHoiLCJtYWMiOiJlOWI3MTZjODllMTY2YWE1M2Q5YzI1ZDlmM2U0YzFjMzBmZTIyZDlhMDRiOTNhOTdmNjUyNDQwN2NhMGQ3N2JhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpDclEzWm9iNHFzWXpmbDl4ZHBqN0E9PSIsInZhbHVlIjoiSnREQkNFMHZUMHp5R1lzZ2hOYXNWT1BjQVFEQ3Vsbm9acS9wMDEremhWSzdxZktBVnBMWFllV2poWWhVd2lxSnpNK0VmeHV3OG4vT3N3bXNBN1orMWt3VGUwbnViN0FKYU5hSVNXVVZsQm9yZHFxdnM3S0grdTZ4U2NyYjZlaUdQQ1E5Yk50MUkwMnY4M0NYSmFMakE4cm5laEFyb1hYTDMrYUh1Wm51aE5KeWQ5YlRJTzNDL1p3MmhQWkgwZit3UkR4dVBQdXNZcUhQbVZHZWVheVphdlc5dXQzK29DT3FXK0FvZGZNM3NjWElSdjRHL0hGK2FscTNKTjJ6a0RWMVZyMHlVdVc5VFlCZ2c3dms1cWNzcjJvWE5Fb2FrOGtxUnQ4bTBWMFpqVDlkaTc3U1lFazBmdFFPUFhCcXJ1YVNXQ2ZoQVZ5NVYzeHo4MGc1Y1gzZHZ3WHorRndNZjhOZ2hMNkxuS2xsOEFuaHN0S2tHWUgyZm1UVVVSZVhEN2lRQktMUnBkWG5hTzhzWkd2N0UralFDTFRNTS9GTmw1SWdVV1pua3VieG9oMUF6V3E4c3ROd3ZXWVp5VExGR2pldjNlNUppV042OEdhTWpmbzdYTStlUGNzOXJjV0RFNEdMYnhnM1ladmJTYVJCaXkvQnB5aWJaSi9jb0V5ck5XVXEiLCJtYWMiOiI5ZjhiNmI1MDRmMzk3NjI2MTYyNWY1Y2ViZjgxYTZkNDUwYzRmZDNlMzkxZWFiZDhmNTJhOWYwZmQwNjI1NzM1IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZWYitwQXV3L2pXT1YvZi9CZjlpOXc9PSIsInZhbHVlIjoiWmV4eGpXYVNSVWxMRWpEdjNxWERvTGtVc3lJaGRQaVVkNlRwQ3dwZkZHV0JIbGp1VmQ1dTMyM2tRcmxrMXdyZGtyQnJOK1czendCMFkxYklINllYSmhPQzJmeTJGcFFNU043N2tZZ1dFY2hEUkoxa2pweEJCSmQ1RWJpaFJPbGY0MHZEYTJhaFlCSk1GKzBVQ0hJRXlqblVkK2djdG5TdGE0ZXpzdUJzc0xqNUR5aytLYTArcElQZmtROFNlMmRTZWJHSGVsU1JQU0t4VVh6Q0lsb0NJNGFmam9iRjdVU1NKdlhqU1pJYlRESXVzaTUxaFRVOVJmM3ROaHh4bGE2c0ZzZjJ6azUxTk1pNE0rQVVHTlNEOXRyUGk4S2V3L2pkV3RCaWVaa0xqekM2ZnVndGR0UFJBZWNkRFJaT3RianB0dmZMalNxNWtzWldOb3Fqd0RTU3NVQmozbGRCcnFsYzZOQXRaRURoMFZhSDg5bkNmV3hONndGbmMwSW1VNzdYTkxQeEMyMVZEbWVWYTZDQ0FkS1JzbWlZR1FHQWlqMW9wUW5HSXIycVh1ZWg3enBHN3RnQ05aeEhNM252dVQrTWxkdS9GV1BZOEM0WVpaRlZuSUZuN0R6bno0VnJhYmdNNVpVNGJiWE8zNFowdFdQNVlkN3JkeFUvMytrY2xIMHoiLCJtYWMiOiJlOWI3MTZjODllMTY2YWE1M2Q5YzI1ZDlmM2U0YzFjMzBmZTIyZDlhMDRiOTNhOTdmNjUyNDQwN2NhMGQ3N2JhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpDclEzWm9iNHFzWXpmbDl4ZHBqN0E9PSIsInZhbHVlIjoiSnREQkNFMHZUMHp5R1lzZ2hOYXNWT1BjQVFEQ3Vsbm9acS9wMDEremhWSzdxZktBVnBMWFllV2poWWhVd2lxSnpNK0VmeHV3OG4vT3N3bXNBN1orMWt3VGUwbnViN0FKYU5hSVNXVVZsQm9yZHFxdnM3S0grdTZ4U2NyYjZlaUdQQ1E5Yk50MUkwMnY4M0NYSmFMakE4cm5laEFyb1hYTDMrYUh1Wm51aE5KeWQ5YlRJTzNDL1p3MmhQWkgwZit3UkR4dVBQdXNZcUhQbVZHZWVheVphdlc5dXQzK29DT3FXK0FvZGZNM3NjWElSdjRHL0hGK2FscTNKTjJ6a0RWMVZyMHlVdVc5VFlCZ2c3dms1cWNzcjJvWE5Fb2FrOGtxUnQ4bTBWMFpqVDlkaTc3U1lFazBmdFFPUFhCcXJ1YVNXQ2ZoQVZ5NVYzeHo4MGc1Y1gzZHZ3WHorRndNZjhOZ2hMNkxuS2xsOEFuaHN0S2tHWUgyZm1UVVVSZVhEN2lRQktMUnBkWG5hTzhzWkd2N0UralFDTFRNTS9GTmw1SWdVV1pua3VieG9oMUF6V3E4c3ROd3ZXWVp5VExGR2pldjNlNUppV042OEdhTWpmbzdYTStlUGNzOXJjV0RFNEdMYnhnM1ladmJTYVJCaXkvQnB5aWJaSi9jb0V5ck5XVXEiLCJtYWMiOiI5ZjhiNmI1MDRmMzk3NjI2MTYyNWY1Y2ViZjgxYTZkNDUwYzRmZDNlMzkxZWFiZDhmNTJhOWYwZmQwNjI1NzM1IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736369683\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImdFL1dKV2YvZHdlVHVQb00xSGw0L2c9PSIsInZhbHVlIjoiNmVFYmVjV1hpZjFta3lvMWJtR0RBdz09IiwibWFjIjoiMDVkMDM2MjM1YmYwOGZhNWY3YjQ2YjE3ZmRlMDZlOWE5NzhlMzBmMTE0MTA3MDdmZjVjMzlmYmMwZWQ2ZDQ4MiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}