{"__meta": {"id": "X735204f14fa12e1720e2a7522452dd23", "datetime": "2025-07-14 18:17:45", "utime": **********.522085, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.054711, "end": **********.522101, "duration": 0.4673898220062256, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.054711, "relative_start": 0, "end": **********.445215, "relative_end": **********.445215, "duration": 0.3905038833618164, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.445228, "relative_start": 0.390516996383667, "end": **********.522104, "relative_end": 3.0994415283203125e-06, "duration": 0.07687592506408691, "duration_str": "76.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988528, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01623, "accumulated_duration_str": "16.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4772952, "duration": 0.01489, "duration_str": "14.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.744}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5040371, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.744, "width_percent": 2.773}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.511553, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.516, "width_percent": 5.484}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1718063927 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1718063927\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1253049630 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1253049630\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2066774272 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066774272\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1806380986 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517062421%7C16%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikh4MFhwdU9PaEw1Yk5MZ0k2dzZMcGc9PSIsInZhbHVlIjoiWUU0QXd6R25vaHdPSFVyUjZFUUt3MGt4WEtzOHdJbHRlRWUzbGJsQmtncEU5SlRQb091bkc1Rm5STHlzd3ZVRXBFd0NKK3MvZ1BHMnJCOHlRL2l0SlhKaGl6S1ZqK3Bxc2pQM0xzbE5wbk4ySmtrTXZ6T3hucjVydTh6dXFzVFpuUVhIMzVTQjlPWmtHZCtoSHhIT3V0VkN3OEVwWlpueWdMUVE2c3B0U2xkeEh2VXlqT1oveDdGd0J0b2lscmVRaWxXMXRxVFZSZFd6NzlmZU5ObEpWWWFzOEZENXJMcTZyUU43MkZmY0VoWlk3YjRISFpOcDdIV1E0Yk83ZG1SL2FQTURVZy8rQlUyN3FKVUoxTzFWalNpZUNlcUN6clNuZ2FuZVZwcUR1d2lBSURJY2gvU2ZvenpnVHdNY2tiY0diSnVRNW9KckRocW8vVHpoZHJyQlI4a0pwWVVIUmZUN05kY3pRb3FwTmZvM3lPdUY4Qk1ldEo2TDNNalYveXU4SERZL2p3bTJUUXo3OHVBNTgzclJNY2pFcEIrMGw5Y0tKNkZObExhU2traEFUQm16Y2IrZDg5d1V5VnVTUFFUR2duWS8yTmI0MitSdUo3ZjJzeW9ORUgvZnRCMU9SRTVtUG56VnRIeEVvdmVLNkozQ0JCdUlBZFVjaEJIenB5ZG0iLCJtYWMiOiI3MzU2OTVlZmJmMjhiMjJiMWQ4MzBiNThiY2NlYzk4ZTMyNDg0ZWRhOTkxOTBlOGY5MzY2OGRiMjNhYjBkMzhjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InYzaTNXcFpTdXozWm95MUxDekxLMVE9PSIsInZhbHVlIjoiTy9KL0ZnOUJSTjVscGZET2dQT3ZUT3ZWeFJ0NG1NRXlraW9DS3VtaEF5L3RzMGJXbkJXdlQ3Wm9RRWRESXI0aDZsaElKMkNZdmQ4UDNKc3Vqd2VqRFNRQzRJNU5tWmJzS0FQZytZZWx4K3pDK3FjMmNMK21RWjllbkgwYUJ6VmkwWENSR2xyL2l3cEd1RHp4bzA3YzYrWDhQS05EdXJRZUNPYlp0a3VUSHkxeVFVU0RFTGFHbXQzYzQ3Uk9qNURYTEUwK25hZjFOZjdXOUxBSlByVDFVT3lWSUlsUGJFbUJQV0hBYzJVeEFVNVIrbGNtQjdxSi92cFc5TzlCdUdJWmQ3TUZvZmJ4V2ZJSWdCeGNlRVZEMUdhajBmOHlTR0QwWllGU2RmYWpQS21UZ25YTXZ2MWd0a1EweC9HUU1zcVNtMVRtV00zaGRJUXhhM2k3TDNFNmJBSVJSVVA0LzBhN1MwdGNmZVhJcXRNRTJWaVVRUjU3bHA1SjlnU2JoOGE2VVhWUUtlYUlWNzI2anZ2Uk1mVVFQRmU4aGhIbWNCeTZtRGdObmNnRk9HWi83ZzhLS2Z4c0s1aUlKNExZNFhsWjdKOEF4K0VuRXZrelVGdjdFcmFPY2JoSkNJS0g2SkJDNVRZK2ZrVXR0SjZoOHl2bmVld0NiQ0tlcE9vTHhZd3YiLCJtYWMiOiI2N2ViMTE2NTNjOGYzMWZiZmRhYzI0NWQ1NDU2NTJjNjIwNTk1OGRjYTUzNjMzMGMzN2VhMDhjMDQ4YmFiM2QzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806380986\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-287302257 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287302257\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjE4aDc4NnNVVTVtWlpqbGJJT2pwZVE9PSIsInZhbHVlIjoiRkdtMUZqNmJOb25XQjlSK3F1cmhMNU1FaGtDK0s4VXdLUlQwQWliY2RVQ25XOE1sVW5HaEUwMVZuZDlWZmtNNHBZdlBPbGhtdnRGSVQzTTRTckV5QzlvUzZZL25JWERWdVphYlNBek1JclVoTmlRUUNleE9iRktQelJxaU9oWm1nT0V4eFF2VkthQk11UHR4b1BPdHFUUkZ2U3ZzV29KQ3lMMktyZStTTUk0VTJOUlpVeVlxSWNHb1k2TEdqc2hpWUZzMDBSeUpad0VPWW1XSWxDTE1RSnRSbEd3bFB6c0J2eWd4T21zbXZJSE9kdkw0MUVSc1dYR1F3NUdxTm9OdTA1emtCeXhHaW50ZlkvZGsxK21oay8zUzZtSFNZODYxbDhKeHN4aE0xRm5hL1VvREorQ1lHdlk0Q2p5ZUNUYjlqTkhoK2oxVnBkSFJLVmxGdlpLSk1PNnJSb0plbDdKM0NkZ1VJR2hKMG5YMmdnbGtaRXhZM25xYTYwc3E1TUJmU0dKdWJ6Y3MydGE2TEVBdkt0YWhQcUxxcEYrVmdZNlAzdWFoZmtScGJ2TWNJeitnMnMrUFVRZmp1ajJwNDB4T3ZCaDladjZKa3c4ejhTRWFLSlpOUHZCR05xeUl0T1BiV2JSZE5qYkxlUFdlMURPSStubERvQ2VZZitaakRYV1IiLCJtYWMiOiJhODc0ZDgwZmNjMDNmOWUzN2Q3MmEzMzFkYTkzMDQ2MDU4YjFmMzQ2YmNlNjc5ZWU4ZWY5MjQ1M2U2Nzc2MTNhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ill0eFZENzIzZ3dCTzFuQ1VSOXNpNlE9PSIsInZhbHVlIjoiaXlBTnVDVi9GM3Q2V2xOQ3lFTzRtdkpaWGpoMEF6U0ZDL2x5OTROWUdYV0JNZGJzOGhpTFdXK3l2QjhpN01kVzdjS083OHBUeHlGb3p6SUk4azJqaFNqemwwZklGRGRNMjV1RzZiQWhYdncwUWI5RHV3SVdnM0JvaXkzamF4NVVZSzlQdW5aSWt1eHlVN3BCL1RvYjA1M0xxOGdnZmtqNXVrNk81RGZpQjFlb3oyL2V4WUJFczhPTXBMckw3VEg0UmM0cDAxRUh1L2dTNUVYbWZORVlWK1NSZzJtSDNzN2d3YVJVUlJON3k3enkxanN3eWZNbkwzZ0RDdDlaRlZXc2tJQW1oMXo3cWVhOW1oTnVDOE5Zc1VPZ2tPWVI4cHNOQXliajQ1dmlVelpVZ1NQTS9La0VrMEhVNzUzMjVtaXQyV284clBCNzQyR0tMSm5zTklvZW9pSm0zQ01QNHJzanNRSmhvUStCUTRnRm1lQTJXeEwwQU1BWHcwVUhjYmZxZzNxdXZSVk1wUmdLN05zMnpqbit4RlFlc3htNDMvUDZvaE0xTlFEeW5JNEpzcmdMWEU1VEJBaEs3OVZzMzZ2QlA5Y3d5Nld5NGxrdm1RSFRvNFRKTG1uZmVGVzUxaElLR3RZdmgzMmlJdkt5OE9KdWNqR1lIK2VXN29MT0RTMjgiLCJtYWMiOiJiYWU5YWU3ZGU4MTFhNDAwYzRmZjBiZjRmZTliODQzM2ZiMGNiZTUxMWRiZWY0OGE3YWM4OTQ5MWJlNjcxY2U1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjE4aDc4NnNVVTVtWlpqbGJJT2pwZVE9PSIsInZhbHVlIjoiRkdtMUZqNmJOb25XQjlSK3F1cmhMNU1FaGtDK0s4VXdLUlQwQWliY2RVQ25XOE1sVW5HaEUwMVZuZDlWZmtNNHBZdlBPbGhtdnRGSVQzTTRTckV5QzlvUzZZL25JWERWdVphYlNBek1JclVoTmlRUUNleE9iRktQelJxaU9oWm1nT0V4eFF2VkthQk11UHR4b1BPdHFUUkZ2U3ZzV29KQ3lMMktyZStTTUk0VTJOUlpVeVlxSWNHb1k2TEdqc2hpWUZzMDBSeUpad0VPWW1XSWxDTE1RSnRSbEd3bFB6c0J2eWd4T21zbXZJSE9kdkw0MUVSc1dYR1F3NUdxTm9OdTA1emtCeXhHaW50ZlkvZGsxK21oay8zUzZtSFNZODYxbDhKeHN4aE0xRm5hL1VvREorQ1lHdlk0Q2p5ZUNUYjlqTkhoK2oxVnBkSFJLVmxGdlpLSk1PNnJSb0plbDdKM0NkZ1VJR2hKMG5YMmdnbGtaRXhZM25xYTYwc3E1TUJmU0dKdWJ6Y3MydGE2TEVBdkt0YWhQcUxxcEYrVmdZNlAzdWFoZmtScGJ2TWNJeitnMnMrUFVRZmp1ajJwNDB4T3ZCaDladjZKa3c4ejhTRWFLSlpOUHZCR05xeUl0T1BiV2JSZE5qYkxlUFdlMURPSStubERvQ2VZZitaakRYV1IiLCJtYWMiOiJhODc0ZDgwZmNjMDNmOWUzN2Q3MmEzMzFkYTkzMDQ2MDU4YjFmMzQ2YmNlNjc5ZWU4ZWY5MjQ1M2U2Nzc2MTNhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ill0eFZENzIzZ3dCTzFuQ1VSOXNpNlE9PSIsInZhbHVlIjoiaXlBTnVDVi9GM3Q2V2xOQ3lFTzRtdkpaWGpoMEF6U0ZDL2x5OTROWUdYV0JNZGJzOGhpTFdXK3l2QjhpN01kVzdjS083OHBUeHlGb3p6SUk4azJqaFNqemwwZklGRGRNMjV1RzZiQWhYdncwUWI5RHV3SVdnM0JvaXkzamF4NVVZSzlQdW5aSWt1eHlVN3BCL1RvYjA1M0xxOGdnZmtqNXVrNk81RGZpQjFlb3oyL2V4WUJFczhPTXBMckw3VEg0UmM0cDAxRUh1L2dTNUVYbWZORVlWK1NSZzJtSDNzN2d3YVJVUlJON3k3enkxanN3eWZNbkwzZ0RDdDlaRlZXc2tJQW1oMXo3cWVhOW1oTnVDOE5Zc1VPZ2tPWVI4cHNOQXliajQ1dmlVelpVZ1NQTS9La0VrMEhVNzUzMjVtaXQyV284clBCNzQyR0tMSm5zTklvZW9pSm0zQ01QNHJzanNRSmhvUStCUTRnRm1lQTJXeEwwQU1BWHcwVUhjYmZxZzNxdXZSVk1wUmdLN05zMnpqbit4RlFlc3htNDMvUDZvaE0xTlFEeW5JNEpzcmdMWEU1VEJBaEs3OVZzMzZ2QlA5Y3d5Nld5NGxrdm1RSFRvNFRKTG1uZmVGVzUxaElLR3RZdmgzMmlJdkt5OE9KdWNqR1lIK2VXN29MT0RTMjgiLCJtYWMiOiJiYWU5YWU3ZGU4MTFhNDAwYzRmZjBiZjRmZTliODQzM2ZiMGNiZTUxMWRiZWY0OGE3YWM4OTQ5MWJlNjcxY2U1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}