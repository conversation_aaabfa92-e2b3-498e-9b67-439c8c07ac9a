{"__meta": {"id": "X792eebb9f96c574d60281683ca088c27", "datetime": "2025-07-21 01:57:17", "utime": **********.581971, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.09133, "end": **********.581985, "duration": 0.49065494537353516, "duration_str": "491ms", "measures": [{"label": "Booting", "start": **********.09133, "relative_start": 0, "end": **********.521783, "relative_end": **********.521783, "duration": 0.4304530620574951, "duration_str": "430ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.521793, "relative_start": 0.4304628372192383, "end": **********.581986, "relative_end": 9.5367431640625e-07, "duration": 0.06019306182861328, "duration_str": "60.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45993576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028399999999999996, "accumulated_duration_str": "2.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.556186, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.606}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.567959, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.606, "width_percent": 17.254}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.574508, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.859, "width_percent": 15.141}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=15&customer_id=7&date_from=2025-06-26&date_to=2025-06-26&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-193528554 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-193528554\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1111397106 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1111397106\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-421086556 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421086556\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1551190533 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-06-26&amp;date_to=2025-06-26&amp;warehouse_id=&amp;payment_method=&amp;customer_id=7&amp;creator_id=15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753063034623%7C25%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJJSFh2amhyem0xMHJXSWRzZlhEWEE9PSIsInZhbHVlIjoiaElJVEUvdTI5ZXV6TDFVVUJPN0c2YVk1VHBlMUlwWWJxUEtmY2tRcFYvNnRqcEttb1BqQVRsYXVmNC9pSlVWbXhHdWpaS3p6NFFod0kyUEY3UzVqRm1nT2c1ZHBQN0hMcEhDSjRJWG1oWWZsTzJCc0g0Y3k1Z2Zwa1Mwa243NzN2OHVPa1c3Z3pLeUVGb3Yva1UvUmZYTEh6THRuN2VZRWNFTGFnaXdzWTJ4TTFGeWE5MGluUk01bVZXOFJKQnhFaGd4Zm96alJBTlZkcVkzZDZKUW14VENkWTVwc285ekxZdVZwWEdBMGhlaHc3WFlYYko5TW9wZFhkaXZlRk4yR1R1Zk0wNlJuMXUxNTREWk1rRllGaDQwQi8yVVZNUWpKTGZUZXNRVEp4WXg4cG9YakRLQnlrbmZvY1pQaDZuSUQ4OHVDRlFvY2dEUENyaUZsTW1BanhNQy9wZlNYU0VyL25CT2pSaHlzVXZ3Ni95SzNyb3Y4R3Z1MW9CSXFRS0U3VWVpQ2RMdVFJczZhdWVva3h1M05ZVTZmdkZSQzVuUEZ2R1pETmFlanU3MmJoRkRzWVJFdTVIRTFzcFV0V25KV3MvUUJYaFFkUTRUTWlNZ2pDN2tXazBxbkRhQm5vRjNDUzVMZ1RuYnYyZFhBMVp2a3RKeVQxS1hlSnJET2llUC8iLCJtYWMiOiI5ODM1NTgxOGQ0NjViNmQwMWZkYWQ5MWQzNjMzYWYxZTNlMzhhMWNjYzI2NTMyOTE4MTIxMmU0N2E1NWUwZDFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InNBVEx0KzE1eHFTbG10dUFLZTNWOGc9PSIsInZhbHVlIjoibTJiNXBOTkI4RW9lN1ExNlhFdmdQQk9sZXIwTUZTcGEvQ2FFUWxrc0lOZTNuZm5LMUlyamU2T3o2S0JoMjlWSWgzKzBxczRzTEJ1Nzh2WWE3elhwM2R2VGFEQndRMlhaUmNzUld3R2d4T2tZUkdrTTZ6N2xaMUNYT1c2M0hraURZcXZGT0FKVmhhNlJBN01PWHZ5WUJuRzBaVTBYb1NOZEsxMDdab3pFT3NsWFozWXRha21Pakg3Rys0ZGc4UDY4bEVCWTdwZWV5dVVGanJIZ1l6T2xUeEhKMzZmWlZZdk1XN1BxNXc2MzJQM3BZd2pCWjN2bEZqME9IS1pDOTBuSHBxKzUxRjJGZFkyN2tHUVBabFl3Y0lkWklpSG1OQXE1eCtsQjkyT000SDR1aTFWZXVLd01LNXJyVTRoV01WblJVS2d3ajBHQVJiSHc3bmJZclhoVHliSHlaenFoSHB4WUhhSnU2NmhIMmoyWVRHQXZ0SENFeGtnZ05rSERpeFdJclIxS1ZJT2Z0cFFtVEhXd2Y1UkhuQ1BrYmJoMTEyL0JLZGNkNndLNlluazVwR1k3OVl3YmowVUh6cWRSd3RrcVdic3N5UHR2WnZhaDBpL0QyWkY5K1l5Q0pkTllnRnBXdFR0N0JMNTlLVXlnNXJWaEQ3RFdTYlR3Z2JVNDd4ZjIiLCJtYWMiOiI3Njg4MjM5ODE1ZWIxMDQ4NTg4NmFmODQ5OGUxMzg1MjUyYmM3YjJjMGNjMjIzOTFiZTYwMTg2NTJjZTRmZjg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551190533\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1401761963 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401761963\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:57:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InR1dnFaN0wrQlJQdDBNdTdRZGUweFE9PSIsInZhbHVlIjoiell5eVQ0NHc4R01LbytPTTFEL1hOa056YTN0YU43bWg5bmJhZHhGQzNPTkNsNFBMdGFXb2QvNXc2dmUzekk3OUJqZ01EQ0tvSkRFb0dKclZ3YkFIZnVRUGIyQ3laQWdyTDZEMnRsNWtCZy9QSjVsK3hzNWlHUVMvKzBmSm43UStHZ0JPZTRFYmtydmczNEpObUM1UW96OEk1aGZuVGZPanBJU0E1NDEvZTliaUh5ekV2RkRuNUxxMGpLK0M2Qk1GUUFpMW40QU1TTWE3ckxEWXMyZDYwbjBtUkROSTlBS3pUYW5zU3Vwek5IeDZTRGhtcGlteXpaZzc3MDZ1SGlCZ3hrdVZqSEpQYXp0OWZGT0lBV3VxN3Z4TEd0WXpMakdpZDFEeHo2ZkU4MWZqa0JLT3g3ZURGZVJKbDNBLzcxdE0rb3NoN0N5QS9tZlNWcEUxaUV4cXR1YUpRWVpsZWVFTEZ4eTZ2ekZKdE1hVmJkTW9qZy85aWl2MGVkSDN5TXVwVlpqWVVBelZ3aUVPYUdSTWJaMm5RdG5xRzRpNFVBZFQ5WEQyM3dxL1h0bnQrZTFaWGVmeFMyWTlFSVErOEd2czZRdnFMc3pzcExUcUdWRm01ZW03RFlNdEU2MERUajUvVndkNFRpK0plc2x5UzcvM25DcVpzKzBNOEJHMEY2TkYiLCJtYWMiOiI1MWNjZTE1NDhmMzZjZmJiMzg4YjU4ZmQxMWU1NDRlOWFmN2MzMTA3YTliZjVmYmVhMDdmMjI5OTAwMWUyOTNkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InN1bHRwbHZ2VDFpUGRLMTdVRGhmUkE9PSIsInZhbHVlIjoiaFllcS9nbDJTTUpHdVdjenN3QkE1NnE1MGwvL2ROcENqaUR3UFVlRXVWMndkeE9LeGJFVnJ3a0psQXN5cGI1aGg1dGpleHFqY01ka04zckZJV1hscWIwZ0VMTlVVZURzT1lJb3gwbjUwWWFoaXhqZ2FPbHRpVzFRN1BXSU51cExVK0pSdm9vdkJ2Ukp6bncxc01UeU9RQ1NJYUdlZDhNb0I1WExnemZRNnFXSUphcXRjdCs1OVRjRlhIRjFRbkN5RWluY0ViTDBIazI3MWk4VU5qMzZtK1BYUWo5TnU2WHpVOStpc0xxZmxOQkZOS1V0UGp2SmRLVnFFTWFha0p4OGh6NUJ1ZU5ZTUNiSFo0SXl5R014SDVORExaVkJnUG5BVE5wSXFZQnRnMDBlaGtSU3RHQ05mSHR1NzlEdUJqdHZVdlZkeUR5bithd0FpYXlES1NSUDBkc0Q5MWwySmJZYzJFV2IwWTc4aHMvK3V0aG1SVitPYmVwS2hpNEZHK3JNZVorWG9vbjFSbWVOZ2RZVEFpbGJHVHh1VHczOVlHL3BVb0c0SHBlMmVzR3VDQ29KTUVFMDJESjh3TTJwWHEyNS8vL0hPSTVEQXJmUXVzQlNsV1MwNnFQbCtOdytqUURLYVJiWkdTczRQM20wZ1gzaU9maDUzUkExOHY0QXNXaFoiLCJtYWMiOiI2NWJlZmQyMTY5YWY0MGU2Y2M5ODRiMzA2OTM2OGU4ZDk5MDQzMDgzNWMzODBjNmQ5Y2E1MjA2NzcxZWViMWFhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:57:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InR1dnFaN0wrQlJQdDBNdTdRZGUweFE9PSIsInZhbHVlIjoiell5eVQ0NHc4R01LbytPTTFEL1hOa056YTN0YU43bWg5bmJhZHhGQzNPTkNsNFBMdGFXb2QvNXc2dmUzekk3OUJqZ01EQ0tvSkRFb0dKclZ3YkFIZnVRUGIyQ3laQWdyTDZEMnRsNWtCZy9QSjVsK3hzNWlHUVMvKzBmSm43UStHZ0JPZTRFYmtydmczNEpObUM1UW96OEk1aGZuVGZPanBJU0E1NDEvZTliaUh5ekV2RkRuNUxxMGpLK0M2Qk1GUUFpMW40QU1TTWE3ckxEWXMyZDYwbjBtUkROSTlBS3pUYW5zU3Vwek5IeDZTRGhtcGlteXpaZzc3MDZ1SGlCZ3hrdVZqSEpQYXp0OWZGT0lBV3VxN3Z4TEd0WXpMakdpZDFEeHo2ZkU4MWZqa0JLT3g3ZURGZVJKbDNBLzcxdE0rb3NoN0N5QS9tZlNWcEUxaUV4cXR1YUpRWVpsZWVFTEZ4eTZ2ekZKdE1hVmJkTW9qZy85aWl2MGVkSDN5TXVwVlpqWVVBelZ3aUVPYUdSTWJaMm5RdG5xRzRpNFVBZFQ5WEQyM3dxL1h0bnQrZTFaWGVmeFMyWTlFSVErOEd2czZRdnFMc3pzcExUcUdWRm01ZW03RFlNdEU2MERUajUvVndkNFRpK0plc2x5UzcvM25DcVpzKzBNOEJHMEY2TkYiLCJtYWMiOiI1MWNjZTE1NDhmMzZjZmJiMzg4YjU4ZmQxMWU1NDRlOWFmN2MzMTA3YTliZjVmYmVhMDdmMjI5OTAwMWUyOTNkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InN1bHRwbHZ2VDFpUGRLMTdVRGhmUkE9PSIsInZhbHVlIjoiaFllcS9nbDJTTUpHdVdjenN3QkE1NnE1MGwvL2ROcENqaUR3UFVlRXVWMndkeE9LeGJFVnJ3a0psQXN5cGI1aGg1dGpleHFqY01ka04zckZJV1hscWIwZ0VMTlVVZURzT1lJb3gwbjUwWWFoaXhqZ2FPbHRpVzFRN1BXSU51cExVK0pSdm9vdkJ2Ukp6bncxc01UeU9RQ1NJYUdlZDhNb0I1WExnemZRNnFXSUphcXRjdCs1OVRjRlhIRjFRbkN5RWluY0ViTDBIazI3MWk4VU5qMzZtK1BYUWo5TnU2WHpVOStpc0xxZmxOQkZOS1V0UGp2SmRLVnFFTWFha0p4OGh6NUJ1ZU5ZTUNiSFo0SXl5R014SDVORExaVkJnUG5BVE5wSXFZQnRnMDBlaGtSU3RHQ05mSHR1NzlEdUJqdHZVdlZkeUR5bithd0FpYXlES1NSUDBkc0Q5MWwySmJZYzJFV2IwWTc4aHMvK3V0aG1SVitPYmVwS2hpNEZHK3JNZVorWG9vbjFSbWVOZ2RZVEFpbGJHVHh1VHczOVlHL3BVb0c0SHBlMmVzR3VDQ29KTUVFMDJESjh3TTJwWHEyNS8vL0hPSTVEQXJmUXVzQlNsV1MwNnFQbCtOdytqUURLYVJiWkdTczRQM20wZ1gzaU9maDUzUkExOHY0QXNXaFoiLCJtYWMiOiI2NWJlZmQyMTY5YWY0MGU2Y2M5ODRiMzA2OTM2OGU4ZDk5MDQzMDgzNWMzODBjNmQ5Y2E1MjA2NzcxZWViMWFhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:57:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=15&amp;customer_id=7&amp;date_from=2025-06-26&amp;date_to=2025-06-26&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}