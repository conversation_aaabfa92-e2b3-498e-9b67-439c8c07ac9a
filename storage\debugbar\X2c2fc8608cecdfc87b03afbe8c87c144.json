{"__meta": {"id": "X2c2fc8608cecdfc87b03afbe8c87c144", "datetime": "2025-07-14 18:17:49", "utime": **********.4097, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517068.896921, "end": **********.409713, "duration": 0.5127921104431152, "duration_str": "513ms", "measures": [{"label": "Booting", "start": 1752517068.896921, "relative_start": 0, "end": **********.350115, "relative_end": **********.350115, "duration": 0.45319414138793945, "duration_str": "453ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.350124, "relative_start": 0.4532029628753662, "end": **********.409715, "relative_end": 1.9073486328125e-06, "duration": 0.059591054916381836, "duration_str": "59.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025700000000000002, "accumulated_duration_str": "2.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.38402, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.872}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3953798, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.872, "width_percent": 16.732}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.401925, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.603, "width_percent": 14.397}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-612710309 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-612710309\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1548331665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1548331665\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-827929404 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827929404\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1391375297 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IkFCajdFbUs3czVXd1U5WEw1R2I0bnc9PSIsInZhbHVlIjoiMGtUcXpERjQ0R0xQYTNjNTA5dko0dz09IiwibWFjIjoiOWMyOWIxMDFjOWI4ZTM3ZDI0MjVlYTc0MzM4ZGE0ZGZjNjRiYTYzNTBjNzAzNmY5NjcxNGM2ZjczM2Q4YWYzMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517067333%7C18%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZqNHBSaWhWWXJHNnpNZXhlRlF5U1E9PSIsInZhbHVlIjoiWXBRUHkyQnYvQzF6dmZIMCsvOHZHbUZXbTBDU1hrcUZvVlZPUlNaYlUxQThzczk1aWNDbGhwNk5mWUNzUU9ZRDhEL0xTYlBUSlVlZ0hydlhDOXlDbDYrdWprTXFlZitSSXRBcCtpNnFXMExVSGQwc0ExRzYvL3VoQ1NLQU5GajlCZVVIQnRsbHAySTNyWXpCNFdmd0I4ZHhBdFkyMTM3WlhtbkdCQVZ4NVZUTmxycEpENXNNSnJFWC9SQ2QvRHNSVnBHdVpjczIvNFhyeU1vcm5EUU8wNXpkTFNsUkEzNkZseUszZWNqWWNsNkJFUWpmOWdOSzF3V25tYXIrL1ZlYUlXMXA0MmtYOGI5R2Q5MTMzQnY5YzUvMHhuV2dmcVFyUzZlYjZCSTZBOFpOQTVRWUczbDVGL0h4a0wxMUVYR0pESUVLWHo4eW9JVHl1Tml3eUxGNnMrL1BhTVpXWDBrUUtpZkdEa2hzb0hQSkt1VmR3OE82Q3J6cUtFZnZWNDVGd1Vrd0VvL0JtNitVcittQ0laMUt1NjRTdkxGS2g5bHVJTlREUTVxQ2Fobk9QSXJHWjVucnk2QWZPcGcxTGpMZzYvbFdaczJzUkgyZnN1YmZwVEVWWEtCdklmd1FlTlI3V1BXVFUyR1hJOEZ6dlNLakIxTzBZVldKeUF5YU5NbzEiLCJtYWMiOiIxODFiYjNiZjFlMzVmNDI1YjI1ZTU1MjA2MjlkMDM5MzRkYTdjZDVlZWQ3OGFlNzMzZTlhZmM0NzUzZGMxOGEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZaKzh3N3I2QnVaaktyY1U2Z1hrbHc9PSIsInZhbHVlIjoiNDk2d01IOGNUVmM5YjluUTJpbFJBengzQzRMdHJFRnN2WnkwS21WVFE4Z0Z4WERDN3V6Z1Y0a1JUZThnSkJzTktxNDJhK1NEWUlYWXBENDBwZTZxdTdVakIvOG5uNGs1THd2Nzl3MUltZFJWUjBKbXNEU2xWeVk2cUJqVm5nYjZQZFliRkQ3WGtEaFQvNWx2OU5JSXBjT0F4WGd6Q0xmRVc5Z0VkTXV6NVdLMFUyWjNyS1hKcHpTSUQyRitKWVc5MWVGRzNnQ0dvMzc4VitmaUtHSGhQNSt2T0VVbzAwZUpPV2VBU2FGdlJ3SkFwaVc5S3hKa3kzS250TlI2Y2ZxUUZJZXJsWWR1QU9la2VoRjlmQVVBUGc0R25TMjR3M2N4RTVMZUU4ZTNpaFRoRUREMXR5LzFDS3A1ZURUWVdxTnFLS0FDQ0lkZGxiQ2pTU0R4dTBod0ZHQjNUYnBZNi9CT1BTVFRKVWMxTUhKaTZZZzR5NUZKMmZDOWl2ZXRsbmplbm0ySmlYc0hVc3VmcU1XcGgzTHdLT1BzUjFsRjdncFdYVUFQM05JVWtRL3M3ZWxPWXRWeWdVUXFCQzdqOHI2QUVDSEdWandmNE5ldHhZNE9LQWp3enVnRWlJY2xONGpYNlZGVHJWM2FQVDBXV2dlbm9KT0VlbC9RMk8xZmRkMG4iLCJtYWMiOiI4ZDllOWIxYjcyZTA0Zjc4ZTJlNWU3YjdjZDEzMWE4ZGQ5MWY5NGE5ODk0Yjc3YjBlYWQ1NTdiZWEyNDFmZTk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391375297\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-651451915 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651451915\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1102103936 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhmS1RKaEJYYVFnM3hKZ0wzeXpqNGc9PSIsInZhbHVlIjoieTA2NTVreXZjaWZIQnBLbHZhdGNxK3c1aTEwamJ3SSt1MTMrT0FYKzU2UU5VNS81WFhVUER3cVZBMXpIdGFLRlFUMk5BeWNYTXVWemhCWVYzeU94MDVOSm9CU2RZQ0NvSjRXU3JwQU9KRWJsZ2puTGJkUXFWemNUQ2V5K1ZESElXZFZudCtzcHVpRjdRM3VucFlWOUR3b1hJUHEzYTU2NmE2ZnY0bzRMazlDRDV3UVBsUG4zRGN1L3VkOW5yN2dNQk5LR3BoRU02Q1hnTmJlTXpPUW9HWGxvUytucStXTTkvMzN4MkYrc3RaU2pQNHc5UWFCb0FVYlMyalNKc204bnNFdG5rZXVyR2J5TnF5dVZSbC9ZbzJBekhUYnhaYW5xRWZoamVFdWZjV2FGRGZEcDVhTnBHTThLS1FQNXZGc1d1S2F4RzVoYnJQSWNWZVBuM1RhUlhHNUdXclREQ2ZEZ2MySDRFMEpCUTRSbmlhcUlCL0ZqaVE0UGJpN3gzOS9yYXoxVlRZVkhpRzNkRlcxUmltMHp5QkJwdXdPeGV4SmpWVmpaN3VIeFhvVVcrSGkxbzUxT2p2N1RtaDRWNTBhaFA2REx3MzZURk5RNnRpcmxFeUpiRjdhT0trS29paVl6NktPbUpIL3FmVG5xOTdxVldUOGk4dDhNanlsazZqU2MiLCJtYWMiOiIzZGZjYzY2ZGM2ODNlYWU1Y2NlNjVlMTFkMDQxZWQ3YTdmYjFjNDQ0Y2VkMDRjNjAyN2Q5ZDg2NmIyZDlmYTllIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJZczcxUUhIQzJjWmRtM25SRHk0c2c9PSIsInZhbHVlIjoiZTdpR1dnQTR0YjhCUmcxUDh3THhLR2F3RjNIVStwVi81NnI1UzJMcXpWK1oyVmNqbGRBck9qeTNlUHlFQkx3U0x2ZjE5OXMzeitaeGl5VmZaektBeFRsaDV2RmtmRVR0aUJjbUhpV2xuVVVwV3dkN2R4VmhueEtXSkJobVZQQ1VVWHJDWllxQ2ZFOXdqWlZ4bWhvWi9uN0poNGNoZEt6V1ZYZHdJYi96dFpYd1hYNEpkckQxT3hkSEo1amlMeml6TnVublFDcVNXekVvTTRpYmlxUzlJZmRjZlBRd1MzUG44QUpENVNWdGR5ZUxSOFlvcWwrWlp4ak1PSXFwSzg3ZmlJK2R3eUxVOHlMVTI5Qy9aZzE2NEhKVmxOOEN4SERjWklIajVKQkdrZ0tzTWFTWjIrcVVsdTBFUEpJTTRRMDBTSGtuMzVpTVBDRTh6WGNRUTRpaG12ajY0T2RWK0NvUDI3VWoveEZ3WnhZMHFlenAxaGh4RFROVUZia1dkVFdXajJuMWNXTVZqK0drSGoxTDVUOWNHVGlvWWVPSGpxNjNvOXFBWnhaVWJSclBVZE11ZXlQV0Z5RitxUU5waGhOTzZ2aXVMMUhpT0RiSkYxM3BOS3NiTzJjbWZMYUpibHdCMko4Y3RSeG1aNGV1SFUzOGhiOUpGS0NHUGJ2RWo5WDEiLCJtYWMiOiIxM2E0NjFlNmRmNTJiOTJjNWMzOGJhMDBmNzhkNWVmZDM0ZjdmMDJmM2VlNDgyYTdhZGM1NDk1NDRlZTk4ZGMzIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhmS1RKaEJYYVFnM3hKZ0wzeXpqNGc9PSIsInZhbHVlIjoieTA2NTVreXZjaWZIQnBLbHZhdGNxK3c1aTEwamJ3SSt1MTMrT0FYKzU2UU5VNS81WFhVUER3cVZBMXpIdGFLRlFUMk5BeWNYTXVWemhCWVYzeU94MDVOSm9CU2RZQ0NvSjRXU3JwQU9KRWJsZ2puTGJkUXFWemNUQ2V5K1ZESElXZFZudCtzcHVpRjdRM3VucFlWOUR3b1hJUHEzYTU2NmE2ZnY0bzRMazlDRDV3UVBsUG4zRGN1L3VkOW5yN2dNQk5LR3BoRU02Q1hnTmJlTXpPUW9HWGxvUytucStXTTkvMzN4MkYrc3RaU2pQNHc5UWFCb0FVYlMyalNKc204bnNFdG5rZXVyR2J5TnF5dVZSbC9ZbzJBekhUYnhaYW5xRWZoamVFdWZjV2FGRGZEcDVhTnBHTThLS1FQNXZGc1d1S2F4RzVoYnJQSWNWZVBuM1RhUlhHNUdXclREQ2ZEZ2MySDRFMEpCUTRSbmlhcUlCL0ZqaVE0UGJpN3gzOS9yYXoxVlRZVkhpRzNkRlcxUmltMHp5QkJwdXdPeGV4SmpWVmpaN3VIeFhvVVcrSGkxbzUxT2p2N1RtaDRWNTBhaFA2REx3MzZURk5RNnRpcmxFeUpiRjdhT0trS29paVl6NktPbUpIL3FmVG5xOTdxVldUOGk4dDhNanlsazZqU2MiLCJtYWMiOiIzZGZjYzY2ZGM2ODNlYWU1Y2NlNjVlMTFkMDQxZWQ3YTdmYjFjNDQ0Y2VkMDRjNjAyN2Q5ZDg2NmIyZDlmYTllIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJZczcxUUhIQzJjWmRtM25SRHk0c2c9PSIsInZhbHVlIjoiZTdpR1dnQTR0YjhCUmcxUDh3THhLR2F3RjNIVStwVi81NnI1UzJMcXpWK1oyVmNqbGRBck9qeTNlUHlFQkx3U0x2ZjE5OXMzeitaeGl5VmZaektBeFRsaDV2RmtmRVR0aUJjbUhpV2xuVVVwV3dkN2R4VmhueEtXSkJobVZQQ1VVWHJDWllxQ2ZFOXdqWlZ4bWhvWi9uN0poNGNoZEt6V1ZYZHdJYi96dFpYd1hYNEpkckQxT3hkSEo1amlMeml6TnVublFDcVNXekVvTTRpYmlxUzlJZmRjZlBRd1MzUG44QUpENVNWdGR5ZUxSOFlvcWwrWlp4ak1PSXFwSzg3ZmlJK2R3eUxVOHlMVTI5Qy9aZzE2NEhKVmxOOEN4SERjWklIajVKQkdrZ0tzTWFTWjIrcVVsdTBFUEpJTTRRMDBTSGtuMzVpTVBDRTh6WGNRUTRpaG12ajY0T2RWK0NvUDI3VWoveEZ3WnhZMHFlenAxaGh4RFROVUZia1dkVFdXajJuMWNXTVZqK0drSGoxTDVUOWNHVGlvWWVPSGpxNjNvOXFBWnhaVWJSclBVZE11ZXlQV0Z5RitxUU5waGhOTzZ2aXVMMUhpT0RiSkYxM3BOS3NiTzJjbWZMYUpibHdCMko4Y3RSeG1aNGV1SFUzOGhiOUpGS0NHUGJ2RWo5WDEiLCJtYWMiOiIxM2E0NjFlNmRmNTJiOTJjNWMzOGJhMDBmNzhkNWVmZDM0ZjdmMDJmM2VlNDgyYTdhZGM1NDk1NDRlZTk4ZGMzIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102103936\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-582223092 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582223092\", {\"maxDepth\":0})</script>\n"}}