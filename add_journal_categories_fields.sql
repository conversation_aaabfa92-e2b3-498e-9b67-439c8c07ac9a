-- <PERSON><PERSON> Sc<PERSON>t to add category fields to journal_entries table
-- Run this script if you cannot run Laravel migrations

-- Add category fields to journal_entries table
ALTER TABLE `journal_entries` 
ADD COLUMN `category_id` INT NULL AFTER `journal_id`,
ADD COLUMN `category_type` VARCHAR(255) NULL AFTER `category_id`,
ADD COLUMN `total_amount` DECIMAL(15,2) DEFAULT 0.00 AFTER `category_type`;

-- Add index for better performance
ALTER TABLE `journal_entries` 
ADD INDEX `idx_journal_entries_category` (`category_id`),
ADD INDEX `idx_journal_entries_category_type` (`category_type`);

-- Create some sample income categories if they don't exist
INSERT IGNORE INTO `product_service_categories` (`name`, `type`, `color`, `created_by`, `created_at`, `updated_at`) VALUES
('إيرادات المبيعات', 'income', '#28a745', 1, NOW(), NOW()),
('إيرادات الخدمات', 'income', '#17a2b8', 1, NOW(), NOW()),
('إيرادات أخرى', 'income', '#6f42c1', 1, NOW(), NOW());

-- Create some sample expense categories if they don't exist  
INSERT IGNORE INTO `product_service_categories` (`name`, `type`, `color`, `created_by`, `created_at`, `updated_at`) VALUES
('مصاريف التشغيل', 'expense', '#dc3545', 1, NOW(), NOW()),
('مصاريف إدارية', 'expense', '#fd7e14', 1, NOW(), NOW()),
('مصاريف التسويق', 'expense', '#e83e8c', 1, NOW(), NOW()),
('مصاريف أخرى', 'expense', '#6c757d', 1, NOW(), NOW());

-- Verify the changes
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'journal_entries' 
AND COLUMN_NAME IN ('category_id', 'category_type', 'total_amount');

-- Show sample categories
SELECT id, name, type, color FROM `product_service_categories` 
WHERE type IN ('income', 'expense') 
ORDER BY type, name;
