{"__meta": {"id": "X1745f9a33f05b150fcc47a9aac201f31", "datetime": "2025-07-14 18:29:41", "utime": **********.886704, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.417791, "end": **********.886727, "duration": 0.4689362049102783, "duration_str": "469ms", "measures": [{"label": "Booting", "start": **********.417791, "relative_start": 0, "end": **********.81133, "relative_end": **********.81133, "duration": 0.3935391902923584, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.811338, "relative_start": 0.39354705810546875, "end": **********.886729, "relative_end": 1.9073486328125e-06, "duration": 0.07539105415344238, "duration_str": "75.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989544, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02109, "accumulated_duration_str": "21.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.842204, "duration": 0.01984, "duration_str": "19.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.073}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.871726, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.073, "width_percent": 4.03}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.879205, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.103, "width_percent": 1.897}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer/eyJpdiI6Ii8vcUpSdU5ZdStPNU1jU0hqOVFiTFE9PSIsInZhbHVlIjoiUFZjbkNJSlJGYk9FRXB1b1V3NHozUT09IiwibWFjIjoiNTRkNDg5YmIwMmU4MzdkNjY5NGVmN2I2NDVlZTUxZTdjYzdkNjZlODc1YzU0NzJhNDE3Y2IwMzU5ZTA2MzYzYyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1661837274 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1661837274\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1922152483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1922152483\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1028226046 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028226046\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6Ii8vcUpSdU5ZdStPNU1jU0hqOVFiTFE9PSIsInZhbHVlIjoiUFZjbkNJSlJGYk9FRXB1b1V3NHozUT09IiwibWFjIjoiNTRkNDg5YmIwMmU4MzdkNjY5NGVmN2I2NDVlZTUxZTdjYzdkNjZlODc1YzU0NzJhNDE3Y2IwMzU5ZTA2MzYzYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517779037%7C36%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVMam1yaENseVR6eGRuZ1B5VDM4bmc9PSIsInZhbHVlIjoiMHJWMThZTytiWmFOaTJPaWo4ZWQ1M0RQall4c2N4RGxnZmt2dy90QWFXbnpxaHQ0MEdTL1h5bzRjNXJEVTgra1hpWUdobXg2enNyZGI5NTdqTHBKZGljZmd0dmxWTXFjWHVsdVNOSFAyZzJCaVl1T2VlOXZJWHM0Z2UwUzBxeEorK3kyV0Z1K2RDSTFiam5DOXdxeFdqcm5TMVoyNjNiZjVSOXZ0VXJSdzdFWEgzZ2VscWFYckVueE95S24zRnp2d3JRYm1kdnRqZWVkVkI2OEJuUDBuWlFqeUhnYlcvT1g5RXdDRi9UZm81RU5JbElNZUJpbHZEN1pFd21ZaUt6WkZHaFJHQ0xkZkxTZzc4dnF4bnA2MGZxVmVRQUtldDM2bXgwSkt3cGZybm8zY1hhaXFYWnhFNG9KWXlQcHozRGdadnl5ZFdnbUFLUzl1ZVZYcVZPekk4RGRJdDRrREZWd0ppcFlBY0Evb1F2Q2pxT0pUdEhtcVFjd0FWYmtjQ0NtUFRDWnd0VkVwcGJKMlowYisvWnQ1OGh5QkNHby8zUThNbC9WMEtYV3RUenlPbk9OcWJSQ21JVi9UWU10aGJOeGxrdXg0cWRCd2k4K092UVRsZTJManYrQjIyZkdsTW1vSCtWbE9sbnFqR0k0SWgrc1U3MEdtdzlHNU5MY1dIZ2IiLCJtYWMiOiJjNzgzZmIzODdiYzQ4NTBlODUzODlkZTBjMzEwYjdiOTE4Y2RhYTJmYWUzMzI3NDk3NjhmOTI1Njk5M2I3YzFmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imc3bTUyUXNyQ0RiZndQSVNUdHJ5Ymc9PSIsInZhbHVlIjoiYkFsUXB4bDFjdDhlbmoxR05jZXdRTGR2NWxoUHVKRFE0b2dSRGY0bHI3bjN1NzNiVEFEeGlPL25uRDRMdHhDVkhJam8wRkpSbkxKZDRnWUZUcGVzY1JWT1V6b0dyRVF1S29sZ1dPbnZoYWlxYnkzQVdsbklwdTVlZlhEUEtCQmFuTjhPN04ySUx5c3pleXNjYjBSdjNHTGNCdTR2My9DaUpxdEpQZy9wSGJRREJpUTFNcnFkOGIwazRvSGVjZTNja2NSSzV0SGxQTSt3MExhZTNrMjhNUGwvSit1Kzdzc2NPVXpYRnJOZTNaN0UxMzdaMWJQOGdLYzFyV2dOdmNTN0ZtdTdBV3hCcW9SbEdsQjN1WWgrZlV4TEVTTkthMDdYSnNQVE1JZHFDeXVGQnN0Y0RTYkZBMFUxckRMWHA0bU1HMVhhY29kU1ZwZVNYNWFMYzFnbGswZC95NklBeWphMEY5SngyNUQ1K1d2U3ZYU0w1bTh6VzR3QUoxTHoxRlZJbURYeDJpUWhuRGdEcVpYRmFaSU9ZWis3akdOU05vMExZWjFVeDhod0U5VnZudEVwVFgwMW1aVmhoWHZwMVZQazZXb2M4VmwxMVZmZWdYTHBzTDQ1b3FRQkwyakdUalkvYWNjM0E4bDRKcHZQcU8wRHRnallWWEhJVmxRWlFIamYiLCJtYWMiOiJiMGQxMGQwYTQyZGJmNjhmOTMyNTk1NWY0N2ZmYjIwMTYxZjNhNjNiNmExNGQ0Y2Y1NzIzYTFlZGIzYWVmMGZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:29:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFYNC9aS2lnaVZIR3ZRM0NnSXB2UUE9PSIsInZhbHVlIjoiZVdlSlpBUk5LVnFuV0h5NDJmYk0vQjg0Wk44ZDFGNGVMSHROdmowbFpBWUhlS3JMVlA2cXBqQTR3MEpHR0ROM1owMlZqV1NnU0Rkd1I3TEZBTG5HWElFMGlxV3JPT3BtRFJIUTVGZ2UydGEvOHo2VTZyWWs1MURKVFRQbGI2M0EwRWZUZHZXTDBYc2NWTE9vYmF5VCtlVDJiWFduT1Z2MktuQkZWcE9oNDlDRVJvc2lFbFNhNDUwaDZsR0RPYk9EeWZwaDBOYXhkL1lESGZ4MmhUSGxoSUdOdXd5Vm5yemdYL2ZPNlF0UTJtRlZpc09ZSzhLRzkvQyt1VjR5Z1FxU1pnck05aEZmZTRkRzVybjgzUlUyd2MyZmlkeDNzcEpSMEgwME10U205ZG5aSWNQc1B0RWVwU1JYajFUdUtXT2s0UEw5T1J2SVBUbTBJbTVzUzFia3BLU0tZL3Uvc2xOMGxZektoQ21KaUxJdFMwWWVBVnpNY01WL0lRcjh4T2IwK2c5bS9mY25hTnB0K2NrR3oyZytHOU5pQnVrNVFnSEtUR2F3T3haZWRLaCtYZm9leWtKcHQySEpGMEJISXg2c0JTRVRza1VSRWxkUDZmM293R2R5QlYyd1dzK0RPMTFnMlNHc2cyWHYyNXBqUyswaUlNd2hTcHFoUUVQS3NKMDAiLCJtYWMiOiI5ZDVhZDQyZmY4NGNlYzg2M2Q2NjRiNTc0MmMwNjU3YmE3NWQwNTIyYjE2MzA4NzYxYzYzNWUxZjgwNTllOWU5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNESk56VENnWDdxMlpMVVdoSVVrRlE9PSIsInZhbHVlIjoiSVZvWUUrdWV5S0tPSHpZbU9DTms0bGRRRmFmcTBiMklHUzVkZGF3Y0Z2OGhuNzRDWkJkMXVXRldPMnVLMW1mLzJtZGZ4ZmpNTldiUmpsczZsMEpRaW1rQlY4Sy9VTW9TTk5uekRRbmF5a1QxM2dWa0U2UDhIRytHdk1jbDRJMDBYWEJodjdqbjlVRndWK0JzSzNhMnViTmd1YlB1enNwN2lRVjFMSTQwSTNSNFNOSFMxU1dscUF0QXE2K3Rla21iVFBmcUJBV3F4OStTOUhkL3ptazNIMVBId05oYUk0RENWd0dhUUVvYkFPTGFPVlJIN3duTDhUVk5KZm5MblNRRHNvTjZFd3hSWDFMRVBGQ3hZM05HbDdINitaOVJ2bTRJbE5SZkNidHdyMnd0MU5nbWRweFBsMXl1K3BqcU9jcDdja0c1dU5JWkJ1ZkJ3eVFsUGM4cmNsVzBBbmtsUG8zOFFCbS9vSjhTNVZ4RHExUkh1QVpvNXAyUjJGaGd4OWRhc2FXcWJDaEpYbmVrQ1cxamxrR0ZrUENKTnExRVVCV1N2UjRjdWVpd0dTb0UyZlJxTEFWNnh1KzZiUGtseC8vcVNxMkc2UE9SMWxRdUVDazdjTWpoci9KRW91VjlabDlydmtlT3Vadjl1b1ZLbW9zSGNwaERYRE5RVmNBSjRsQVkiLCJtYWMiOiI4MDFjOWVmNTIxODQ5ZTZjNWVlNmQxYmJlZmQ4ZDhjMWEwZThiZWMzYjhkMWY0YzhjZGU4NThkZGI2MGE0YTc5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:29:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFYNC9aS2lnaVZIR3ZRM0NnSXB2UUE9PSIsInZhbHVlIjoiZVdlSlpBUk5LVnFuV0h5NDJmYk0vQjg0Wk44ZDFGNGVMSHROdmowbFpBWUhlS3JMVlA2cXBqQTR3MEpHR0ROM1owMlZqV1NnU0Rkd1I3TEZBTG5HWElFMGlxV3JPT3BtRFJIUTVGZ2UydGEvOHo2VTZyWWs1MURKVFRQbGI2M0EwRWZUZHZXTDBYc2NWTE9vYmF5VCtlVDJiWFduT1Z2MktuQkZWcE9oNDlDRVJvc2lFbFNhNDUwaDZsR0RPYk9EeWZwaDBOYXhkL1lESGZ4MmhUSGxoSUdOdXd5Vm5yemdYL2ZPNlF0UTJtRlZpc09ZSzhLRzkvQyt1VjR5Z1FxU1pnck05aEZmZTRkRzVybjgzUlUyd2MyZmlkeDNzcEpSMEgwME10U205ZG5aSWNQc1B0RWVwU1JYajFUdUtXT2s0UEw5T1J2SVBUbTBJbTVzUzFia3BLU0tZL3Uvc2xOMGxZektoQ21KaUxJdFMwWWVBVnpNY01WL0lRcjh4T2IwK2c5bS9mY25hTnB0K2NrR3oyZytHOU5pQnVrNVFnSEtUR2F3T3haZWRLaCtYZm9leWtKcHQySEpGMEJISXg2c0JTRVRza1VSRWxkUDZmM293R2R5QlYyd1dzK0RPMTFnMlNHc2cyWHYyNXBqUyswaUlNd2hTcHFoUUVQS3NKMDAiLCJtYWMiOiI5ZDVhZDQyZmY4NGNlYzg2M2Q2NjRiNTc0MmMwNjU3YmE3NWQwNTIyYjE2MzA4NzYxYzYzNWUxZjgwNTllOWU5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNESk56VENnWDdxMlpMVVdoSVVrRlE9PSIsInZhbHVlIjoiSVZvWUUrdWV5S0tPSHpZbU9DTms0bGRRRmFmcTBiMklHUzVkZGF3Y0Z2OGhuNzRDWkJkMXVXRldPMnVLMW1mLzJtZGZ4ZmpNTldiUmpsczZsMEpRaW1rQlY4Sy9VTW9TTk5uekRRbmF5a1QxM2dWa0U2UDhIRytHdk1jbDRJMDBYWEJodjdqbjlVRndWK0JzSzNhMnViTmd1YlB1enNwN2lRVjFMSTQwSTNSNFNOSFMxU1dscUF0QXE2K3Rla21iVFBmcUJBV3F4OStTOUhkL3ptazNIMVBId05oYUk0RENWd0dhUUVvYkFPTGFPVlJIN3duTDhUVk5KZm5MblNRRHNvTjZFd3hSWDFMRVBGQ3hZM05HbDdINitaOVJ2bTRJbE5SZkNidHdyMnd0MU5nbWRweFBsMXl1K3BqcU9jcDdja0c1dU5JWkJ1ZkJ3eVFsUGM4cmNsVzBBbmtsUG8zOFFCbS9vSjhTNVZ4RHExUkh1QVpvNXAyUjJGaGd4OWRhc2FXcWJDaEpYbmVrQ1cxamxrR0ZrUENKTnExRVVCV1N2UjRjdWVpd0dTb0UyZlJxTEFWNnh1KzZiUGtseC8vcVNxMkc2UE9SMWxRdUVDazdjTWpoci9KRW91VjlabDlydmtlT3Vadjl1b1ZLbW9zSGNwaERYRE5RVmNBSjRsQVkiLCJtYWMiOiI4MDFjOWVmNTIxODQ5ZTZjNWVlNmQxYmJlZmQ4ZDhjMWEwZThiZWMzYjhkMWY0YzhjZGU4NThkZGI2MGE0YTc5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:29:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6Ii8vcUpSdU5ZdStPNU1jU0hqOVFiTFE9PSIsInZhbHVlIjoiUFZjbkNJSlJGYk9FRXB1b1V3NHozUT09IiwibWFjIjoiNTRkNDg5YmIwMmU4MzdkNjY5NGVmN2I2NDVlZTUxZTdjYzdkNjZlODc1YzU0NzJhNDE3Y2IwMzU5ZTA2MzYzYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}