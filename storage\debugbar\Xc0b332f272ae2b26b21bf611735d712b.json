{"__meta": {"id": "Xc0b332f272ae2b26b21bf611735d712b", "datetime": "2025-07-14 14:34:30", "utime": **********.702209, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.225634, "end": **********.702225, "duration": 0.4765908718109131, "duration_str": "477ms", "measures": [{"label": "Booting", "start": **********.225634, "relative_start": 0, "end": **********.634237, "relative_end": **********.634237, "duration": 0.4086029529571533, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.634245, "relative_start": 0.40861082077026367, "end": **********.702228, "relative_end": 3.0994415283203125e-06, "duration": 0.06798315048217773, "duration_str": "67.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45990768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00329, "accumulated_duration_str": "3.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.674515, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.644}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6885638, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.644, "width_percent": 18.237}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.694798, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.881, "width_percent": 9.119}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/journal-entry/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1988982870 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1988982870\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1371477665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1371477665\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-530899171 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530899171\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503667928%7C14%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFzME14V2lWS3hwVzROL1I5bDU2cUE9PSIsInZhbHVlIjoic0hYdlEvbTFEVDRRK1p0dVVuYW9RZUtVNWhpaHUzRnlMRElaOGxsdG9xU0JEbEZndHEwa29XM3d2T0xibm5XcU13ZzBzeTN4R0FsOGR6aFp5OHRaVGZrQ0ovNDFpNVZmb2J0SGR0eXVhYUdJNlR2cnNPYmY5bUtONE9iVTRlWktNSTAzZmgvZWx5OXRxYmJyMG53YnVtMGdMM1RMb0ZuWmVaeHJhUEVoc1Y4VTdDUTJqVDBiVWNTVGZSMi9HT0hWOGMxZU15UW5GeEppNkZUSVU1SEx4WDR5WlVaVDJMU2V4WHEvY0Yyc0czaGNVYldIc0gvWGwwS1gxOWFOUEZ0QWNlVXM1SkpTR2h0YzkvWHBXTVh2M1R5OENNWVZ4WHV1VGQ0YnJCVittNHRDUVdWblRKQjJKNFJ6UXQvTVUvcnB2eitvenNHNzIyd3hrVkxmNTJEYlN3ZnlSQmxKenZFOFNKOWxDSWV4NVZTdU83ajdCZWdiRnFXRHBzWkE5ODhUb3liMWRvV29IWll2ckNYMkpTdDNRVHlPaGNSblFuZTRHY20wWHE5alliNTFJS1N2RGFJVHJ1akZ6cE8zUU4xY3BOMUc4VWtVcW5uNDdXSVFJeC9NdWJhSGRoMGp0WmpiaVRncVNCd0YxQjhvemliWlZjVWkzT21LRWd6M3ZLZDAiLCJtYWMiOiIzOTg5MzUzYTFjMmI1N2E2MjhiOGQxOWM4ZWQxMjEzNGExNDE0YmVhMjZhMTM0YWRjNTgxZjQwZDcwNTk4ZWNiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhISndZVjRmMEJKbWNHa1RnRTgvSVE9PSIsInZhbHVlIjoieXU3Q0h6UnkyT0g5M0MyL1ptbUhIck15NzFIc05RMEZlRkdkdmFnaDBwb0JsOUF4dnc0ZWNDUTZBVU5yOEliU3p0Um80bkpTalc2alF4UnVCd1kxWFVqcjZoL1RsZ09VWE5iMkkra1NXbGx4LytuNFVRWmVzTi9UZ2ZubGtzdkt5Y0dhV2hDcjRaZERDMlE3dDlLWXBESlQxcFJRSDRvYUhjOENLRngxZVlXbEdwWWNUMFIrSmlRNzJGbkwvUElaN2hUWTVEaTRUS0FvaVpEVkVJbHNvdFFHWVhYd0UvbSt6MGY5Y1F3Nk9PTUltaDZCSWQwQ0U1cDJuMUNQYzJrQTZ3NG93ZzFkeHQrdi82a3NsQVdJblprZUNGaVZaQnAwV09ERFVCblloWXVpNXVSczNldWgzZ0FLYnIzMmxmazY0S2RjVTBDaTBEL1FrQnRXTzlxRmZQM09qc2toNWlMbGpzZUNONWpzNlQrTUFLNm83Qml0dGl2QVM3SHRaV2ZxSUU4ZnhjdzRNVE9pTi9xUW05UUo0bDJmNlJSbUVrRGQ5ZjV5U1FQczE4ZEhON0ZxMkh1SVNhSmtxVnNXelRVbk4wUVVJazZmNUgrc3hETFpyNUdtVlUwcUhHaDlPUHFTbnN3Qk9BekJ5N2V1RWhzZzRuSG8zUkNIT2lXQS9YRmIiLCJtYWMiOiI2MTAzODA1MTMwNmNhZjliYTRjMzY3NTljNWZlZjNlNjM2NWNlODU5MWJkMmY0NGViMzJmZWY1YjcxYTJlYjNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-661461175 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-661461175\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-155080547 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:34:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFLcnozVnlGZEZETVNUVjh3MEtDNWc9PSIsInZhbHVlIjoiN3FydVN5WHg2MjMySkxTczRKV0NTZDQycktGNWxWVEloRE96RzVxVEJtQXQwV25LN3g4V2puMGVaZWNaME9XVGtqVkV4TnBtZkp3YXpzeE9LWGs0b2hicldCVXdXcjZXbS9SQzI0enUwbURsZ3hqTy9PUVVTekVEUEREWFZQQlpWS3FPRDZ2U3UxeGJVWmI0dmxDOGtmSDk1TzhGTUkyQ2lwSnVRd25rZTdYVFRvWDdlUklJc3JvSWQ1Ym02WEt4RFhkRmg1bzB4UnhxVThqV2FPQWM0a0J5dmdpN1djT1FGbGRtZjBuWEkzYmxKUlZRcE9GbUdRR3UxU296ZFM0NjcwUXRiaXZTMXlsTW5pNyt0djZRU2Z0UGI1NmdJcG5vNWZFRGVQbGM0R3pjbDFmcWhlblBLWE9LRmc0ZTIyUDk0clMweERuZnQ0YnBBTng1WHp3ZzlBL29pQmYyUGdHaUUzWXBGMm1GbnNpc0NIUzh1WURGZ2dLWTZpei9CMTNjR0NsWVJyMVd1Y1hMWjUxVnAvcW01d3NmcFgrK2tWRkU1Y05iK3o1QzJWcENMTEI0ZURIQjh0VlpvZnZPano0ZWY0QTFvWGxaUGpTVktzM3dBNzlsNHFFdnpNYVBFWUFVOVgrcTJ4OFhFSWFKZndHaGRsdjhCWnY3NDMxZTRrd28iLCJtYWMiOiIzYTUwOWFjN2UwMjJhNjY4MThiMGUxZTllZWFjYTc5OTNkZmY5MmYyM2QxNDYxMTU2NDJhNDYzMTc2MzY0ZjMzIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:34:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlMzNnY0SkNoR1VSZ0dkYmhKdEFhcGc9PSIsInZhbHVlIjoiN0drT1RvcTd2d1JKcmdOcDhYUG12dkVjejRnbnZjd3Z4c3daMXB3Q2RJYXVxdldldlVPeVZoYVEwNnBXZlpsN3owMldEV0M4Rit2S3I1K0dMREFEN1RhUi95WmhSMmxTN2hBYzhFbGVXU2d6T2N6ZVpjajdONStIcGdUSmZvZXVJdnhJUDV3VXJCYWt2cnZGZVkvbEhWeWRySDdNVUlMZGJjTmdlNFMwOXlPZ1ZNZzYwQXVmWElVWnN0TVZqSTBhUHRBYVlyaVFHL3VQSWlWVUxJa1dFTjNMMXo3SzNlb2ZyZ3B3ZklxcnF3RVVwbFBMMXV6UnVaaDdRd3FHamJaUGNrMGs2UkZORmtudCtoYjQ1YUR0a2JYRUFyVXNFYTk2bVRXb3Q5SzAwUEFSR3ZJOWoyTmFWQ1ZVOXUwdEhXdnk3aEpVcVE2cWd4T3VvU0dBNDhwWXMwVUE3V0dDT2F2SUpEZnNFOUJrelBUVmFVWmZmOWhXOG4zTHFrZnh4UlJEc1BrT0pMbFhqaUZISFVGdVIrYnFaSG8zaGs3KzRXbHBJWVNxL0gwdFBwb2VaQUNCMkszeHFlR1FGcVpPMjBaWmJoU0hxeDFwdHZVcTErUDMwbWhkUm5HaXgxRk5xb1Q0ZkxVd0xiN1FBTU9rc2NzNWNaeVNuVkRuWXF1cXVEV3kiLCJtYWMiOiI5NTE4MWZjZjFkNzA3MzIyZDM5ZDdiNjNjMDA1MDc2ZjYyYzg4Y2UwNzY2N2NhYTIyYTkyN2FhNjQ1ODdjOTM3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:34:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFLcnozVnlGZEZETVNUVjh3MEtDNWc9PSIsInZhbHVlIjoiN3FydVN5WHg2MjMySkxTczRKV0NTZDQycktGNWxWVEloRE96RzVxVEJtQXQwV25LN3g4V2puMGVaZWNaME9XVGtqVkV4TnBtZkp3YXpzeE9LWGs0b2hicldCVXdXcjZXbS9SQzI0enUwbURsZ3hqTy9PUVVTekVEUEREWFZQQlpWS3FPRDZ2U3UxeGJVWmI0dmxDOGtmSDk1TzhGTUkyQ2lwSnVRd25rZTdYVFRvWDdlUklJc3JvSWQ1Ym02WEt4RFhkRmg1bzB4UnhxVThqV2FPQWM0a0J5dmdpN1djT1FGbGRtZjBuWEkzYmxKUlZRcE9GbUdRR3UxU296ZFM0NjcwUXRiaXZTMXlsTW5pNyt0djZRU2Z0UGI1NmdJcG5vNWZFRGVQbGM0R3pjbDFmcWhlblBLWE9LRmc0ZTIyUDk0clMweERuZnQ0YnBBTng1WHp3ZzlBL29pQmYyUGdHaUUzWXBGMm1GbnNpc0NIUzh1WURGZ2dLWTZpei9CMTNjR0NsWVJyMVd1Y1hMWjUxVnAvcW01d3NmcFgrK2tWRkU1Y05iK3o1QzJWcENMTEI0ZURIQjh0VlpvZnZPano0ZWY0QTFvWGxaUGpTVktzM3dBNzlsNHFFdnpNYVBFWUFVOVgrcTJ4OFhFSWFKZndHaGRsdjhCWnY3NDMxZTRrd28iLCJtYWMiOiIzYTUwOWFjN2UwMjJhNjY4MThiMGUxZTllZWFjYTc5OTNkZmY5MmYyM2QxNDYxMTU2NDJhNDYzMTc2MzY0ZjMzIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:34:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlMzNnY0SkNoR1VSZ0dkYmhKdEFhcGc9PSIsInZhbHVlIjoiN0drT1RvcTd2d1JKcmdOcDhYUG12dkVjejRnbnZjd3Z4c3daMXB3Q2RJYXVxdldldlVPeVZoYVEwNnBXZlpsN3owMldEV0M4Rit2S3I1K0dMREFEN1RhUi95WmhSMmxTN2hBYzhFbGVXU2d6T2N6ZVpjajdONStIcGdUSmZvZXVJdnhJUDV3VXJCYWt2cnZGZVkvbEhWeWRySDdNVUlMZGJjTmdlNFMwOXlPZ1ZNZzYwQXVmWElVWnN0TVZqSTBhUHRBYVlyaVFHL3VQSWlWVUxJa1dFTjNMMXo3SzNlb2ZyZ3B3ZklxcnF3RVVwbFBMMXV6UnVaaDdRd3FHamJaUGNrMGs2UkZORmtudCtoYjQ1YUR0a2JYRUFyVXNFYTk2bVRXb3Q5SzAwUEFSR3ZJOWoyTmFWQ1ZVOXUwdEhXdnk3aEpVcVE2cWd4T3VvU0dBNDhwWXMwVUE3V0dDT2F2SUpEZnNFOUJrelBUVmFVWmZmOWhXOG4zTHFrZnh4UlJEc1BrT0pMbFhqaUZISFVGdVIrYnFaSG8zaGs3KzRXbHBJWVNxL0gwdFBwb2VaQUNCMkszeHFlR1FGcVpPMjBaWmJoU0hxeDFwdHZVcTErUDMwbWhkUm5HaXgxRk5xb1Q0ZkxVd0xiN1FBTU9rc2NzNWNaeVNuVkRuWXF1cXVEV3kiLCJtYWMiOiI5NTE4MWZjZjFkNzA3MzIyZDM5ZDdiNjNjMDA1MDc2ZjYyYzg4Y2UwNzY2N2NhYTIyYTkyN2FhNjQ1ODdjOTM3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:34:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155080547\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-205306047 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205306047\", {\"maxDepth\":0})</script>\n"}}