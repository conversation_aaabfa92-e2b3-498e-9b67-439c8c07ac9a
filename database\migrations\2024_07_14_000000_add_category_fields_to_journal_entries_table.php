<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCategoryFieldsToJournalEntriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('journal_entries', function (Blueprint $table) {
            $table->integer('category_id')->nullable()->after('journal_id');
            $table->string('category_type')->nullable()->after('category_id'); // 'income' or 'expense'
            $table->decimal('total_amount', 15, 2)->default(0.00)->after('category_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('journal_entries', function (Blueprint $table) {
            $table->dropColumn(['category_id', 'category_type', 'total_amount']);
        });
    }
}
