{"__meta": {"id": "X4fa5ab47a5041a0a70378603ccd0c35a", "datetime": "2025-07-23 18:18:16", "utime": **********.642448, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.123613, "end": **********.642463, "duration": 0.5188498497009277, "duration_str": "519ms", "measures": [{"label": "Booting", "start": **********.123613, "relative_start": 0, "end": **********.551612, "relative_end": **********.551612, "duration": 0.42799878120422363, "duration_str": "428ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.551622, "relative_start": 0.4280087947845459, "end": **********.642465, "relative_end": 2.1457672119140625e-06, "duration": 0.09084320068359375, "duration_str": "90.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02803, "accumulated_duration_str": "28.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.582898, "duration": 0.02641, "duration_str": "26.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.22}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.617852, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.22, "width_percent": 1.998}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.625196, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.218, "width_percent": 3.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-955593777 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-955593777\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1898190806 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1898190806\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1405431180 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405431180\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-765184962 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; XSRF-TOKEN=eyJpdiI6ImNoQ0lQQjNWTmdIb0V2RkFsUFQ2K3c9PSIsInZhbHVlIjoiTmQzMlNQbmhNdzlsakVHdHJMK01vZCtRcU94SEJPVkxPRTlITUlLbXcxOWF5aUFiMndORWpodmhlUG0ycXNLVGxNMG0xY0J5ZklyOWlackN0cXh5VHd4QUxLTEVZc2RpWS9yYnQ4Y1pYMnc4RStLYkZmRWlhS1RYSE9neFhWR3BCQ1hHS2tma05Nd3FXdjZlUWMwYm9sbFVCS1M5Z0JCd3loZHp1YktzTjBiS3FJeDRSRFgrdlZjVUNFbHZCVDEySDFNc0tMM3Bkc3djd2wxR3l4K3AzSFozWmczejhGRXhUQzdjYUZXUGdoaWJNcm11RDlDeGZIVndYeEwvejlERExhbFdybmFzZGc1Nmd6cjBZOFB3S01SaXBWZnM2RmRUWUVyZ3JmSlpaUVpIWFNSLzlmWGlSREpDaDJpY2YxaTE5WHdsVXhIdlV2Ty9aRE5qb1hPU0wxakd1NkFMd29EVlJ1R0hKVURWYTlGMm1pS1gxU0QvdXF6S0JLY3pObEQ0YWJ2ZnEyUWdOS2RxQThBV1hEVklPTjA1Y2N2bmZuK1hQcnFudEJxMUoycFVYM0xHOGl4UXl1ZVFrNkRsYjNWV3AySGVvdjYrTnZ2MnhaV3lRSWw4WGZqeTFYa2ZvT08vZyt5UStDcEZrd3MwMXowZ1piQ1h4dDA4RFBlTVBwUHoiLCJtYWMiOiI1MGJjNzg2ZjAzNzNmMjIyNTMxNjgxOTEwNjRjZWViOGExNGZiYzZlMTIyNDBmNjY3ZTVjZWM5OGViNmU4ZWYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpFQk1TRTJ3dXRzR2VQY3pKNk12TGc9PSIsInZhbHVlIjoiaWZxOFFjRTEzQkxnQzFOU1EvUzRRSWZJanIwdVRlbDBuQUJML0dCaWRDd3hDSTNvTk1meW0wSGxTVmNMZEJWbWZObmRsRDROYjV1VHBKYklQS1poL25sTkpOa1AwMnVnTEEzVksvL1ZiTlFFSVFXYytVYVIwb2N1R2QzNnlkN0FoZ0trTE9tMXhhUDZaL2dXY1FqUkVycCtwRSs1aDV1TUVjQmhCLzl1YktpQkVQYkNoRFBMbmxHSU9PODNicklJSnNoVWRKMUpuMCtqRzVydEFoUXhjSTI3U1FjVjB3WG9HdzFhaThzcG8wWUkyb2R2QzZZUDQwSjFTS0c1b3VBOHFhWG8vOUU1ZFFsTXoveGZKck1maXl5VEQvYzVNeUJFNDk0RWQ5dlE3L09CTXlBSzJMclY5UytueGhjYWFiaVVkOVZrc3NNRjl3SVJhNnBmVUVLM3ExTzVIcVlQTE5rRzdCamlLbkd3bTZoNk1CdTVMWHVPZk01NGtrbGFhVFp3RDZKVzh0YkdVVS9GVkdSZzNXanZaMWFrOFk4UWpvemZqSklEQ1A2Y1VjenNmdFQ3MXh5MG80MUNndXpWckFZVXhTVXh1UHFwWmpBNk5DY0sxUWxMaGFuSVR3U1FCTXYrdUNvdjZLRmFldVUxUERwbUpzbUFteW1uWmQyVi9KanEiLCJtYWMiOiI0YTFiYWE5MmJjYmZmMmZiMzg0MTRmYjU4ODEyMGM3ZTdlNzE0M2RkMjM0ODUzMzc4NWRmNzgwNWY2MzdkODUxIiwidGFnIjoiIn0%3D; _clsk=c6ed4t%7C1753294695171%7C3%7C1%7Ca.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765184962\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-745785706 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745785706\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-679667690 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:18:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNUUmMvbHQxU0NQTXluTDV0Smd0Rnc9PSIsInZhbHVlIjoiUVh1dUZIL3ZLbnhjU3FEQ2NZaHRRN28zOGRkSlU2RWhLNks1V09kcWZTYnNyUVhrZThYNlZVTkQwTXpVUFF1dWlpaEFYUkF4LzBveEJQNE1Qb2JHaVU3T0JZUUlqZFdLUk1EVXcxdnQxOFZ6UDAvTEJCMnlMUktsVW1KUmQ0bkUzT0xPaE9wQlN4dmtEOE9IQWMyQTJFNzMrK3NGa3RQcFljeGN0UCthUUJDL0V5RzM0ZWJXOUJJelpwSW8ySnQrdzNDcG5LT29tU2hOUnpsZlBCTyt6VWRCREVkSTZzMlp4L0hCL2s4NjZZUlh5bm9hTGRyQlJrRmpGTVBSWkVmMWVVRCtTMzdHMkVkcHVleXBSeUtRU0k3VFNNcVR4cFkvb2prSy9Iby90R2lqSnZINTJ6YVhNZmhzMHRyb3l4WWc2cCtzZjB4N1lWc2o5THNTNVFkWE5BWDBTUzEvcklrL1ZDcFdVTkFab3VlcHE4b0lsbmZtQ2FJUzBxRTNUcTNSU1Z2L2s4UFIzSDFMNm9ESll6Uk53R1BJbzE3V2xwNytlWUhXdGNIZFcyWDY1bjdOczdrbC95RUp3Nm1jZ25sQ3NPTmdsNVdaUnU3Z24xeUNuTWU3dE1TNVJRWWRiQVMxbnpBclpJMWdCNFRBR25hUkRFMkpsMGliQkg0bmFxcEQiLCJtYWMiOiJhMjcxYjRhYWZmY2JkMzQ2MjI5YzIwYzc2ZTU5OTM0ZDEwZmE5NDA1NTdmYjIwZjFlZjgwMmZhNjQyNDY3NzUwIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:18:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZtSUkrZkVUMW01aHJYZE1aQkVDWWc9PSIsInZhbHVlIjoiMFQxc1NZdEJ4TlQzQjJOYk1XN0JTQ0oyek5RYWVYeEN0RTYrbDJOT3VwR3EzQzU3UHkyUlg1eVVxSlExN0NtajRCNFpIc29QM2k5clduaHNFNmpYT0RJUm9SWW9QeUVhcmhZVXhMdVpRWkhMT1h3c2ZIUVc5bUJ6cW9lWkdjTHk0TlU3YVIvZ3FmdVdBVnR3czM2bnpqL2RSZVRjTjd0cDEzYktJMVNNN1orSG9LUElTNHJ2UGFNYzdxMFNyeWZnbkg3eUVLNlg3YUprUGkxYlJyTUdBTFZoalp3blorQkpYVXRMOUx5ejh4SWxERy9heHNyWHBBVmVvdE80eHVrM1Q2WWpMMzcveTM0RVhTZDhtV0NpMm1WYWxRWEt2c3hJOFk4b3JwcUNNTWJBWkIzZ3BDcWZydHlQQ0tBaVR5Q3NpWi85QlBzM3JodG9PSUREcUdUM1V4VVo4d3Zha3RKRGJoUk9mRXNsc1NxSHB0RG1uZUtyaERLd0E5eVBwbFlrbU90b1p3YnN4YzJFdC9kRmZKYUNvVmZsZGtsZSt2M3JRaVdqTnNFN2NBaEhqRFZTZmJuWTU2MzBuSHREbEZIWkM4UzY4ZXlUR2U1SC9VL3hKajNiNmtuWFBDMjdzd3UyZm5xNkZUT1hibjRtaTA5MHI1cUJxVVc5YnoyUW0vUW0iLCJtYWMiOiJiZDEyYTFkZTA4ZDNiMTE5MDkzNjc5MDdlOTAyYThlZjA0ODVkNTg2ZGZlMmFlN2M2MGVhNWFkNTI0M2JhNmU2IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:18:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNUUmMvbHQxU0NQTXluTDV0Smd0Rnc9PSIsInZhbHVlIjoiUVh1dUZIL3ZLbnhjU3FEQ2NZaHRRN28zOGRkSlU2RWhLNks1V09kcWZTYnNyUVhrZThYNlZVTkQwTXpVUFF1dWlpaEFYUkF4LzBveEJQNE1Qb2JHaVU3T0JZUUlqZFdLUk1EVXcxdnQxOFZ6UDAvTEJCMnlMUktsVW1KUmQ0bkUzT0xPaE9wQlN4dmtEOE9IQWMyQTJFNzMrK3NGa3RQcFljeGN0UCthUUJDL0V5RzM0ZWJXOUJJelpwSW8ySnQrdzNDcG5LT29tU2hOUnpsZlBCTyt6VWRCREVkSTZzMlp4L0hCL2s4NjZZUlh5bm9hTGRyQlJrRmpGTVBSWkVmMWVVRCtTMzdHMkVkcHVleXBSeUtRU0k3VFNNcVR4cFkvb2prSy9Iby90R2lqSnZINTJ6YVhNZmhzMHRyb3l4WWc2cCtzZjB4N1lWc2o5THNTNVFkWE5BWDBTUzEvcklrL1ZDcFdVTkFab3VlcHE4b0lsbmZtQ2FJUzBxRTNUcTNSU1Z2L2s4UFIzSDFMNm9ESll6Uk53R1BJbzE3V2xwNytlWUhXdGNIZFcyWDY1bjdOczdrbC95RUp3Nm1jZ25sQ3NPTmdsNVdaUnU3Z24xeUNuTWU3dE1TNVJRWWRiQVMxbnpBclpJMWdCNFRBR25hUkRFMkpsMGliQkg0bmFxcEQiLCJtYWMiOiJhMjcxYjRhYWZmY2JkMzQ2MjI5YzIwYzc2ZTU5OTM0ZDEwZmE5NDA1NTdmYjIwZjFlZjgwMmZhNjQyNDY3NzUwIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:18:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZtSUkrZkVUMW01aHJYZE1aQkVDWWc9PSIsInZhbHVlIjoiMFQxc1NZdEJ4TlQzQjJOYk1XN0JTQ0oyek5RYWVYeEN0RTYrbDJOT3VwR3EzQzU3UHkyUlg1eVVxSlExN0NtajRCNFpIc29QM2k5clduaHNFNmpYT0RJUm9SWW9QeUVhcmhZVXhMdVpRWkhMT1h3c2ZIUVc5bUJ6cW9lWkdjTHk0TlU3YVIvZ3FmdVdBVnR3czM2bnpqL2RSZVRjTjd0cDEzYktJMVNNN1orSG9LUElTNHJ2UGFNYzdxMFNyeWZnbkg3eUVLNlg3YUprUGkxYlJyTUdBTFZoalp3blorQkpYVXRMOUx5ejh4SWxERy9heHNyWHBBVmVvdE80eHVrM1Q2WWpMMzcveTM0RVhTZDhtV0NpMm1WYWxRWEt2c3hJOFk4b3JwcUNNTWJBWkIzZ3BDcWZydHlQQ0tBaVR5Q3NpWi85QlBzM3JodG9PSUREcUdUM1V4VVo4d3Zha3RKRGJoUk9mRXNsc1NxSHB0RG1uZUtyaERLd0E5eVBwbFlrbU90b1p3YnN4YzJFdC9kRmZKYUNvVmZsZGtsZSt2M3JRaVdqTnNFN2NBaEhqRFZTZmJuWTU2MzBuSHREbEZIWkM4UzY4ZXlUR2U1SC9VL3hKajNiNmtuWFBDMjdzd3UyZm5xNkZUT1hibjRtaTA5MHI1cUJxVVc5YnoyUW0vUW0iLCJtYWMiOiJiZDEyYTFkZTA4ZDNiMTE5MDkzNjc5MDdlOTAyYThlZjA0ODVkNTg2ZGZlMmFlN2M2MGVhNWFkNTI0M2JhNmU2IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:18:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679667690\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}