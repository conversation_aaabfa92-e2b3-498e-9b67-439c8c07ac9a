{"__meta": {"id": "X0976ed505d45967b22fea9b93e60b064", "datetime": "2025-07-23 18:22:21", "utime": **********.639087, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.187026, "end": **********.639109, "duration": 0.45208287239074707, "duration_str": "452ms", "measures": [{"label": "Booting", "start": **********.187026, "relative_start": 0, "end": **********.575155, "relative_end": **********.575155, "duration": 0.38812899589538574, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.575167, "relative_start": 0.3881409168243408, "end": **********.639111, "relative_end": 2.1457672119140625e-06, "duration": 0.06394410133361816, "duration_str": "63.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44312472, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01933, "accumulated_duration_str": "19.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.606453, "duration": 0.01933, "duration_str": "19.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LxYrkz8rORa4pTqCE37VgOfI8VB8tmttgTKr2E1u", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1521329300 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1521329300\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-376509541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-376509541\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-903751212 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903751212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078502223 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1078502223\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1041238442 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRnWDFVc0hmYjdqeHkwZDBUWE8zeHc9PSIsInZhbHVlIjoiSHUzM1dkMHpqYjJ4SnpDOVo2cG9QQ0k1dDk2UG9ERUZDRjRUNmxaVi9nVFdUZlhiVkJIYW40bWtWN0U0TEducXpISm9XQVM4VHVyWG9nSDV2SDY1aWRSU2c4Z0pxN1VQYVNiYkVZYThHOWozWDBxZ2k2UnBjeUI4RWE3TklCZFc5WXpSczNNK3BwakxSQkdHTFNjZ2Jvb3djc0pMTk5MT1lWZUFiMjNwdHZuS2hpRVZDaHBrTGFiU1NqMi9MMFNmSG9lM05BSGNSZWYrZGVXck55TTdTQjhwZTVwS1RHWU9CTU1OaUNhQ0R1YlFabWpISjBwVjZrUVRsNU1GRjZpSktmTHl2NFJSR2kvNDl2NnNKZWRkSGVCV05NQWlQQm1PeUsvTHdUek4vaTh1c09kQkZJcU5GdTFiZlhHNVpZLzcrdXJSRGxjbm1FbkIxTmswaXpCc1lTVzhwWjA2OWtaNkl2RnVDKzRldVI1VkI4eVRaVkI3bFRyREJsUjkzaERZQW41Vjd5UDQ2aXJPZ21IbG5XMXJKcVNWS3h5NDNzVUNTWlpmM2xQMkJlYWtnMnRtejZkQjFNM01SYW40dXNWZklxQUhHNm9IZG4wZ01TTGRtWEFYaC91N01ZQ2RDN0k0SlkzQjM5ZCsvZGpxUzJubnowaTlVSGlERm9rbHd0MXQiLCJtYWMiOiIyMzFjZDNmYTQ1MGI0OTdmOGYzZjhlMTQ2ZDhmZDgzZWY4MjY2ZWMxY2MyMmJhZmUxNmM1NmI1NzNkNThlMTg4IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inp0NGJYNXhlcjJ5djkwWmtlQnB0Y1E9PSIsInZhbHVlIjoieERkMGRkajM0Z3pubkI5bjRKaFVBc1I0UjVMR2I0ZU1yWVJsaFk2WWN5Q0h2Z1dDVHNERmNsSElKcFNscnY4K0hxTysyUktzVWQwMkZCbUk3ZFVQTlRKZHRoaE93Uy84VS9jMU5yYjFlcUdVV3k5VitUYlVjTFk4cEdydlR5dzZhT0NEOS9QUHVkRVNONWxPemlrVENTK0p5bVJ3d0dOOVVwcnc1L0NpY2Y5aVd2ZTI0QU1uQWsyUEZyaGY5akNEZTdFVDRnNkVJQzEvenFnSm0yeko0WTQ1QWFxbjdkN3hMYWZWcEZzd2FLQVAweWRPeHAwYlpMalpRSGpaMGROcUdvdmg0Q3d4c1R2THlhNktDWnhCSHJFQTJDUFZranBBeFBVRjBCYUphOHZmTCtoeWlJRlpvNkpnTXlSVUVXTXN3MDdSYVNlSGxhRnBWcWtzNUdWRDZiUDZET0R3MmI4YUwzU0xncTZVNGg3eTB2K3pjb3VGbmZDRmR1czArSFZtZVRPelJEODVFeERxZXZIZms0WXZTUmZFQ1ZtQlZ5Nm1Jb1RGREdTT2p5c2lpRUVMZmlrSm5tNThBZG9NQ29SeGlFM2c4V01lK0xQQTl1TG0weXd0TlhmQ0J6WjJORXg4T0hnY1FBTWlzOVZLak8yUGtUZE1kUklPbTQ3dktJZ0wiLCJtYWMiOiJjMjdjMDQ0NDMzZTJlOWU2NzA4NzhhNDZjZWE5ZTdhNGU5Mzg2Yjc3MjgxMDljYjFmOTE1ZjJkZDQ3ODZlYWE0IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRnWDFVc0hmYjdqeHkwZDBUWE8zeHc9PSIsInZhbHVlIjoiSHUzM1dkMHpqYjJ4SnpDOVo2cG9QQ0k1dDk2UG9ERUZDRjRUNmxaVi9nVFdUZlhiVkJIYW40bWtWN0U0TEducXpISm9XQVM4VHVyWG9nSDV2SDY1aWRSU2c4Z0pxN1VQYVNiYkVZYThHOWozWDBxZ2k2UnBjeUI4RWE3TklCZFc5WXpSczNNK3BwakxSQkdHTFNjZ2Jvb3djc0pMTk5MT1lWZUFiMjNwdHZuS2hpRVZDaHBrTGFiU1NqMi9MMFNmSG9lM05BSGNSZWYrZGVXck55TTdTQjhwZTVwS1RHWU9CTU1OaUNhQ0R1YlFabWpISjBwVjZrUVRsNU1GRjZpSktmTHl2NFJSR2kvNDl2NnNKZWRkSGVCV05NQWlQQm1PeUsvTHdUek4vaTh1c09kQkZJcU5GdTFiZlhHNVpZLzcrdXJSRGxjbm1FbkIxTmswaXpCc1lTVzhwWjA2OWtaNkl2RnVDKzRldVI1VkI4eVRaVkI3bFRyREJsUjkzaERZQW41Vjd5UDQ2aXJPZ21IbG5XMXJKcVNWS3h5NDNzVUNTWlpmM2xQMkJlYWtnMnRtejZkQjFNM01SYW40dXNWZklxQUhHNm9IZG4wZ01TTGRtWEFYaC91N01ZQ2RDN0k0SlkzQjM5ZCsvZGpxUzJubnowaTlVSGlERm9rbHd0MXQiLCJtYWMiOiIyMzFjZDNmYTQ1MGI0OTdmOGYzZjhlMTQ2ZDhmZDgzZWY4MjY2ZWMxY2MyMmJhZmUxNmM1NmI1NzNkNThlMTg4IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inp0NGJYNXhlcjJ5djkwWmtlQnB0Y1E9PSIsInZhbHVlIjoieERkMGRkajM0Z3pubkI5bjRKaFVBc1I0UjVMR2I0ZU1yWVJsaFk2WWN5Q0h2Z1dDVHNERmNsSElKcFNscnY4K0hxTysyUktzVWQwMkZCbUk3ZFVQTlRKZHRoaE93Uy84VS9jMU5yYjFlcUdVV3k5VitUYlVjTFk4cEdydlR5dzZhT0NEOS9QUHVkRVNONWxPemlrVENTK0p5bVJ3d0dOOVVwcnc1L0NpY2Y5aVd2ZTI0QU1uQWsyUEZyaGY5akNEZTdFVDRnNkVJQzEvenFnSm0yeko0WTQ1QWFxbjdkN3hMYWZWcEZzd2FLQVAweWRPeHAwYlpMalpRSGpaMGROcUdvdmg0Q3d4c1R2THlhNktDWnhCSHJFQTJDUFZranBBeFBVRjBCYUphOHZmTCtoeWlJRlpvNkpnTXlSVUVXTXN3MDdSYVNlSGxhRnBWcWtzNUdWRDZiUDZET0R3MmI4YUwzU0xncTZVNGg3eTB2K3pjb3VGbmZDRmR1czArSFZtZVRPelJEODVFeERxZXZIZms0WXZTUmZFQ1ZtQlZ5Nm1Jb1RGREdTT2p5c2lpRUVMZmlrSm5tNThBZG9NQ29SeGlFM2c4V01lK0xQQTl1TG0weXd0TlhmQ0J6WjJORXg4T0hnY1FBTWlzOVZLak8yUGtUZE1kUklPbTQ3dktJZ0wiLCJtYWMiOiJjMjdjMDQ0NDMzZTJlOWU2NzA4NzhhNDZjZWE5ZTdhNGU5Mzg2Yjc3MjgxMDljYjFmOTE1ZjJkZDQ3ODZlYWE0IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041238442\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1266549532 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LxYrkz8rORa4pTqCE37VgOfI8VB8tmttgTKr2E1u</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266549532\", {\"maxDepth\":0})</script>\n"}}