{"__meta": {"id": "X2644668963732bd22fb1f8a2ca2493db", "datetime": "2025-07-23 18:23:40", "utime": **********.825809, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.388224, "end": **********.825824, "duration": 0.43760013580322266, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.388224, "relative_start": 0, "end": **********.772494, "relative_end": **********.772494, "duration": 0.38427019119262695, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772503, "relative_start": 0.3842790126800537, "end": **********.825825, "relative_end": 9.5367431640625e-07, "duration": 0.05332207679748535, "duration_str": "53.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46536136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00289, "accumulated_duration_str": "2.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8008678, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.668}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.810608, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.668, "width_percent": 19.377}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.816844, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.045, "width_percent": 16.955}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1031239245 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1031239245\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-677446368 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-677446368\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1561244182 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561244182\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1998013796 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294910769%7C21%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJYYkpjZTVIZ2NudFVNcnNwc0daS3c9PSIsInZhbHVlIjoicnNuRW5iUWhYTjlwVzExVldmMThqdSttdlg4Q3Q2WVpmT2lBNWV0RnNqdDNxd2VqZ0xYNFpYeDJCRnpZN21DUmZTbyswTjdxVFFRVXRVdmFxU2F5NlFZL2NQUnRvOGFPME4zM05HSEZCVndJUUQzOXFSQWpFcmNxY1ErS1FhYlJJdXcwNG1MTnRka2tQQWhpbjhYcWlEUUh1V3Rod2tWa3lEVUxiaUx1NWFjdzJkcHBLVHZoclI1L0d6dCtEYkJrbzBKVjlxM3JVenBoWE5oMGN5K2Y5UjBhOHhGQSt4b2VsbzErT0NXZmRrR0RGMmtBWlpKcHVybTFrODN2enVBMVpvSWNJQ21ZRHZpTmYrZmgxSnEyZ2ptdVIxdWJ4R3BzM3BaMVkvVkNLTjlSc1V2bWJKb2lmZEdNZE1zaGVZTk9oRm9WdEd1MmhWdGtzWCtyZGdPWVd4RlFxdWc3eUpZcjBWVTgzcURsNkU3L2FwUDNCRjB1Z2hJWlpkdDIyaVJmYk1LZEh0TmY4NWc0THlNR0ZvcU5tOVZSdDRQaHZoSTdxSmkrMzllS0R0aEdGT2Q1WUNVYWVjMXhzRGlPT2dRbm9ta2tKbUk4c2hrbXMvOEVIcWd2blBlcVFabnBIVEVORzdJcFhabVVwSmNxVkRiZzVMOVcrVTQvcHRGc0lRNlQiLCJtYWMiOiI4NjZkZjYxNDk0YzUyNTY3N2Q0MGNlMjM0NmFkYTVhZTA4YmQ0MDhiMmU1ZmEyZGNmNTQyNmJmYmE4MjJlYTY5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktadTJtODRzWUtzaXhzbVFSSGVGYlE9PSIsInZhbHVlIjoiTkc1cTlDemFwc253Nlo5WG1DWnAwckRHQkhxWCtWWlpuWlZPdlZabEovYS9CcTdrNEJIQUtWcHZucUtwMk5WbzhPT1BjRHdJNzhmWElYMzh3REJmbGM4dGJvbjdVV3V0Uldjb3ZadndGZlJ4RTgwUjUydVFtbmNSR2JROHFVcHdzZHM1clJ0NDVCOG00MXdxUVNqMWRNbVhHam1LZXNpVGdIU2RNUWpDRG1XVGFVcURCVGVWcjF4WGJiZmVDWWlqbHMzNUlzdkZSTlEwV21VNVVUL2FrOUhUVXdWT0szZndXMjIvcW4vLzcwSi8xSnovTGNVeXk1WGxPbUN4Y2ZwWXFJVXBKMkJVL3VKbEF1KzZ4Ulg4SDNsMkMxZzVLZVUzSmR3RmJlWHR6clNZNGVRNERxbjRyeHA2SXpvZlZ4b3hXbDVxdVo5ano5SGVjZVFaU0xVdi91enVoV1ZRbWxmNHdNc3hnZ2Y3dFdPRGZpdFBOcnRmN2hZNHZYUzRRalFZNUFLUjZsejNwcEhKeTM2SlByR3VLSitzWWo1SE96VWUyNVhUcjlmb3dpZjRxeXBHOU4xbk5NQUFhaS9QQXV4KyswZjBpR0FJQm1DZ0dZa1FRQlhxVWQxWDZoSTcxVjZLTU5ncDdkVXkyWFBkdEdPcEtlcHdXQ0RIQ1JIeFRlSkMiLCJtYWMiOiJhZGJjNTQ4OGI2NzU0OTZiN2I5M2ZhZjQyNjk5OWFiNTI3NDliZTA2MjgxNmVlYmI3Mjc0MzM2NTJjZTlkMjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998013796\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-196683517 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196683517\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-337411506 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:23:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlCM0ZrSUpKeDRkcjl1OENCRUFMaVE9PSIsInZhbHVlIjoiM0tkcWxyaTkzL1VwUVZPbXBveWRZbFpXMmFaNE05dnhVZWVCMWY2TXpCU0ZQVWNSd0lDT01lRVZGYXNlOFJ6QVdVT2hhYnpWdno4ejh5OHFuN0NmZ21EL0FKKzAwUnN4b2hORUwrL0NXTSs5OXdaUHVCblZSamdZcHJoUG9aaWJlaWlPMnRnR0VUS1VmYU5XYzRnRFFjd09ZcnVaTmZBNmg3SHl3Y040elkvVjFNWklaNlBBVGc0d1dOT0ZvM25ZeVZkUkExS3BIZWVEUkRibjBlMEpLOUhYNlZ2Q3E4S3NBa2ZFWUZnQ3JMb2tSOTRmYjRReW9FU0VMK1FyeFhMemtJSXhZN2loQ1ZLN3NDd1lEQW5ta3B2NnNQWTZUR05oNlg4RXpxNFBBd2tOVUYvZmtUdHJlelhQL2RMTFUxQkhVRHVTS2pQSGV4MDVoZ1h6VVk0N21ndzhSQWpaZUFBUzc3dnU4aitEa25DdVVUZ3BOOFdDUEpJZXNaMHFFVU44ZWFzZTlGc1F4ZWRjRytEVFk2K1cvcHM5M2hkNGZmL0RnWnJlUmdwYTZoRzBSUHJIM3AvN1E5TERyS21GSzlYaWF4ZlhrTzR6Sk9HYXJnWE9zSVEyT3VmSmtiak8wZXlFRGhteHR1alNSSVV6UDJxWFNGY2k5TFVoUU11bjZTajUiLCJtYWMiOiIzZmY1ZjU5ZjhhZDNiYWY1ODQ0MGIxYzdkNzM4NzUzOTcyZjJlMTAzZTFlNGIyZWEyMzBlYmM0MDg5NTc5ZTc3IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjQxVmFITVNUS2V2ZXlSWXhFMnN3Q3c9PSIsInZhbHVlIjoic2tzM3FJelhKS2VBS0J0MjNLRkZxR3YrLzdENjF1OE5mcXljZGJUNCtHNlQ5Wm4vOUtpSitaeUhJc2JGVlpSNzZzaThhZWoyNmhsZWhiWXFETUhuSmh0cS9EMVhGYlhHMHFibWRtVFl6MURoMC92S1Eza2czdmpVYWNyWWtxN2dOUTFROUpoN1ZIakIweXQyNE94amFuaThNTGpxa29zWXdicFVXVGVGOCtrbTVheHdyeWtVSUlLMmtYZkdDNDZNQWxEc1VCb2d0Zy9KRW03bDhTaFp1bVNHTWZNcU5MSEJjemNvS1J5VmtPYWxnaUdZemdCemJINHRyOU8vcCt1M1JWZzJJYjR3ZVdCT1hKM0tHRU5GdDhzOHhBUVJIOUFrNDFYazF3YzZMWUVtTkdMRVIyZkFuWnBsbGdMTE82b3dONklBa0ppU1FaeVY1bzhjei9QV0lDNnZnM21wUGZsNlZ0SnVWbkpZNDEzaDRraTlYWFJVUnVDd0oxaDl6NUdvQm1mQk43M1QxRXQveHY4ek5wSk9kUTZSbENTKzBBb29CY3N0UHljQU0zdHhPTUlTOUNJTlJsc2p4LzlkYzQ4eTJhNmkyUFV1SGM1eU5wanFQNy9mRkZoeEloZ3UrMVdiQU84NmdNQVBzV3VFOXZsYjZ0ckkzTmJvbmMwYVBMajkiLCJtYWMiOiI4Mzc4ZWI3NmE2MGE0ZTk5NjRkNTMzMzZhM2ZjYThlMzkxNzgyNjAwMWM5NmE3NmRkMGNmYjcyNzY1ODJiNmNlIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlCM0ZrSUpKeDRkcjl1OENCRUFMaVE9PSIsInZhbHVlIjoiM0tkcWxyaTkzL1VwUVZPbXBveWRZbFpXMmFaNE05dnhVZWVCMWY2TXpCU0ZQVWNSd0lDT01lRVZGYXNlOFJ6QVdVT2hhYnpWdno4ejh5OHFuN0NmZ21EL0FKKzAwUnN4b2hORUwrL0NXTSs5OXdaUHVCblZSamdZcHJoUG9aaWJlaWlPMnRnR0VUS1VmYU5XYzRnRFFjd09ZcnVaTmZBNmg3SHl3Y040elkvVjFNWklaNlBBVGc0d1dOT0ZvM25ZeVZkUkExS3BIZWVEUkRibjBlMEpLOUhYNlZ2Q3E4S3NBa2ZFWUZnQ3JMb2tSOTRmYjRReW9FU0VMK1FyeFhMemtJSXhZN2loQ1ZLN3NDd1lEQW5ta3B2NnNQWTZUR05oNlg4RXpxNFBBd2tOVUYvZmtUdHJlelhQL2RMTFUxQkhVRHVTS2pQSGV4MDVoZ1h6VVk0N21ndzhSQWpaZUFBUzc3dnU4aitEa25DdVVUZ3BOOFdDUEpJZXNaMHFFVU44ZWFzZTlGc1F4ZWRjRytEVFk2K1cvcHM5M2hkNGZmL0RnWnJlUmdwYTZoRzBSUHJIM3AvN1E5TERyS21GSzlYaWF4ZlhrTzR6Sk9HYXJnWE9zSVEyT3VmSmtiak8wZXlFRGhteHR1alNSSVV6UDJxWFNGY2k5TFVoUU11bjZTajUiLCJtYWMiOiIzZmY1ZjU5ZjhhZDNiYWY1ODQ0MGIxYzdkNzM4NzUzOTcyZjJlMTAzZTFlNGIyZWEyMzBlYmM0MDg5NTc5ZTc3IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjQxVmFITVNUS2V2ZXlSWXhFMnN3Q3c9PSIsInZhbHVlIjoic2tzM3FJelhKS2VBS0J0MjNLRkZxR3YrLzdENjF1OE5mcXljZGJUNCtHNlQ5Wm4vOUtpSitaeUhJc2JGVlpSNzZzaThhZWoyNmhsZWhiWXFETUhuSmh0cS9EMVhGYlhHMHFibWRtVFl6MURoMC92S1Eza2czdmpVYWNyWWtxN2dOUTFROUpoN1ZIakIweXQyNE94amFuaThNTGpxa29zWXdicFVXVGVGOCtrbTVheHdyeWtVSUlLMmtYZkdDNDZNQWxEc1VCb2d0Zy9KRW03bDhTaFp1bVNHTWZNcU5MSEJjemNvS1J5VmtPYWxnaUdZemdCemJINHRyOU8vcCt1M1JWZzJJYjR3ZVdCT1hKM0tHRU5GdDhzOHhBUVJIOUFrNDFYazF3YzZMWUVtTkdMRVIyZkFuWnBsbGdMTE82b3dONklBa0ppU1FaeVY1bzhjei9QV0lDNnZnM21wUGZsNlZ0SnVWbkpZNDEzaDRraTlYWFJVUnVDd0oxaDl6NUdvQm1mQk43M1QxRXQveHY4ek5wSk9kUTZSbENTKzBBb29CY3N0UHljQU0zdHhPTUlTOUNJTlJsc2p4LzlkYzQ4eTJhNmkyUFV1SGM1eU5wanFQNy9mRkZoeEloZ3UrMVdiQU84NmdNQVBzV3VFOXZsYjZ0ckkzTmJvbmMwYVBMajkiLCJtYWMiOiI4Mzc4ZWI3NmE2MGE0ZTk5NjRkNTMzMzZhM2ZjYThlMzkxNzgyNjAwMWM5NmE3NmRkMGNmYjcyNzY1ODJiNmNlIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337411506\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1583418334 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583418334\", {\"maxDepth\":0})</script>\n"}}