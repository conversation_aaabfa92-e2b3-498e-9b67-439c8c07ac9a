{"__meta": {"id": "Xca68532d45caee0aa0a22bbdbb1b05f6", "datetime": "2025-07-14 18:28:56", "utime": **********.412314, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517735.964571, "end": **********.412329, "duration": 0.4477579593658447, "duration_str": "448ms", "measures": [{"label": "Booting", "start": 1752517735.964571, "relative_start": 0, "end": **********.340375, "relative_end": **********.340375, "duration": 0.37580394744873047, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.340384, "relative_start": 0.37581300735473633, "end": **********.412331, "relative_end": 2.1457672119140625e-06, "duration": 0.07194709777832031, "duration_str": "71.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45989544, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019969999999999998, "accumulated_duration_str": "19.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.369295, "duration": 0.01902, "duration_str": "19.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.243}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.397722, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.243, "width_percent": 2.454}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.403701, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.697, "width_percent": 2.303}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjVjVWVLRjNpUTByQm91ekJDUnpnR2c9PSIsInZhbHVlIjoiaHZqYkFSNnJhbVVkanhtWVE5c09oUT09IiwibWFjIjoiMTA0NmU2OTlmMmM0YzE2MjM1ODhlYjFlNDk3ZTkwZjE5MGM1YTgzNzEyMWI4MDhiNGUzOTliYzU2M2MwMjMxMyIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1210045099 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1210045099\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2108757115 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2108757115\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2140751048 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140751048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-641996350 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjVjVWVLRjNpUTByQm91ekJDUnpnR2c9PSIsInZhbHVlIjoiaHZqYkFSNnJhbVVkanhtWVE5c09oUT09IiwibWFjIjoiMTA0NmU2OTlmMmM0YzE2MjM1ODhlYjFlNDk3ZTkwZjE5MGM1YTgzNzEyMWI4MDhiNGUzOTliYzU2M2MwMjMxMyIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517731773%7C27%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNJTUZGNzhsV2g5M3RiL3Uxei85RHc9PSIsInZhbHVlIjoiL1E0bVFYNDI3TWtFMU45MWdCQ3ZHUEFjRWxiZ2lGQjVLd3A0QXh2dUE4cHdlR2t2ckljK3ZLZjlVS3J5RGpEcHNobmU5bzlpMUx1NGtSc0FNMVlLQWdFMnVMZnlVa0E5M0YrclFrZWhQc3E1SHZETTd1TDMvUm1LNCs0MVdzb3VUU25iY0M4UWVHdG01cGZZakt4bkNhMFdjY1FQeGFDQ1ltaURFd0FNNEwyK0dsaXRucGxEdFhZRjFYS3h6YVlQaDFDMGJkODB2a2g1TVJrZ04rTW9RZ3NYSGZ4TWdxTG5NT21SbjZGQXBudCtoTmJHZlk3REcrTlc5ZWpKdVNuQkhDWWhWN2NTL1B5dWxUODJpUllnemZlaVNFaFdLaUdJcXhUNlhWckJwaEltWlVpWG9pNU02RnVxU3Fmbkl3dHNRTERMZVVqZE5KS25FQlFxQjRVdWdQdE5xUU9YQ1pOYXViRlVIQldSMllTcVBld0l6d1pQMklWSzJpUEc5a1RUTzBldUN5SVc5MlVPNlBBMldQTk9maVlLM3dTTlAwT3ROck5OcENvbkV2M2ZKN3E0Q05KbEc5NUE0aGNVaVlSVzdWZ3hISDdGdnFKaVRXcUFJaXp1TkozaEpmMkRnd3BFeEs0SHY4MERYV0hOQklMYzZ2cVlIbGNiQ1BwL3RlMy8iLCJtYWMiOiI5ZTEwNjA3MTRiNzFkNjdhYmJmODJjMTMzNDYyMmMzNTg5M2UwODlhY2VjMWU5MWZlZTdmNDJlZmEwOTNjMWJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhGZ1NCclJPa3NmSEtuRHYyUHBpWGc9PSIsInZhbHVlIjoiTnQ4NzZyVGtrSW1GUzRNdWNIQXM4dkNVa0liQy9SazhUd2pwdzc5TW9VWWVNVTNtd1lPOU5vdTc4NERONS85cC9zWlVveDc4bWJhRG9QZ0crTmRzeW0ydUxTOXJNM1ljNlVQYUwvTmhPZmVtZmpaRXZsVk14QnV4MTlteWlXVU96YzJDWnU2L1hqbFpPM0xFa0RsYzdEOHlKU2F3SGN1b21jMFhNTTZ5cHBSeGhBU0Q0T25yME11bE1WRGNUV3kycTZ1WUZVc2VSeE5EQWxiMCtCbmwwem14OEpMVlBLUXF3dndRQ1pydm5uQjNWcXgreE1JM0QzZzBMc29VSXh1a2lTekVkL09rUUJ1UlVwZ2ZXNkYwaDNqQzhCV0M5aGxsS2FFS3JtZGJCR2RMcGZSeDJnZnBPdkZYa1RZNk1kU0xyS2lXMHZaYmE2MzhSR2NkNEVZSk9zWmpUQUF6RVU0eVVhWEFKUnBEK211QUJJMlIvb3F4b2JSV0JQeWY4VnN5Sy9JY0IyMmlNSEVQakNndmZDd1BmOHdoNi9EbzZJWTFvc3FWQUpNUUxWWmFGeFFGOTVVelJEVG5tUWxaVGxaS25pRDBzMFVGVmtMejB4Ykg4azJaUE1RcjNZbWIxb2xCNkpqOWRrcUh1emdpOTFORDVuOGsxa21YbkpnNW9lNWUiLCJtYWMiOiI1ODdkNTU5YWViOTIwYTJjODAyN2JkYTYwN2U2OTMzNzVlNDAxZGNiZjlkZDUwMjIwMDA1Y2Q4OWFkNmNiM2U5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641996350\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-964955316 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964955316\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1915156487 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:28:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik8rczVMR1VXZUVnL2t5dkw5ZHVSc3c9PSIsInZhbHVlIjoiUGJ3dFVpam1aUUlLUlo4ZC9ZOGlFU1l3OHgvckpGSG54aXBvLzVkaTZDQXZLRFRLMmx2dzVJZmVBamhqcnQ5VS9sSW9xSjBXMXZKeHdURzFFSXZQQnkvNnp5ejN4V3ZUTGVEWHBXTWJRS1EzRzZTU0l6WWlLdGphOGtwZlVRSXZlR3AreGpMUjl3czVwRkpuRDZBYWoyaktJSGFncjdRb2syNUxWQm9yQnowbkNJL2loMERrd0o0b3laK1lkOUZyY3FYNEl1NFlmOHU1Q010enh5RFFhdUZsVlhvMVlzZklkeWdUMWFvRkE4RXlSR3NQZ3JWMnVrMEI5NG5NRS9EbzVZd0d2VFJxeGpxMWc2ai9ZV2hGc0xXSmd6Q0hoS2ZhWFRUYXh0VkpKTThqNlhQUUl1eENib1piWEprM0Rib0I1VnFWcE55TzhlVGhJaGhvYmJVcU15Q3FwdGJEdkp6cTIxUVJBeHBJYWdmbDNaajd0ZzVwdklTdVkzc2FvV2IvSVhjazFhZllYZ1B4SkdYenRpSVNTYUJZVkZidVQ0clk2cWlQM3psbHpFQWNtU25UcG1rTGxLd3JMMjZ4ZjV6bm1ScGp5L2Y3V2JqNXg5NFpwdGEvbnZ2eTVuTlMwMzVPNTNjcmJhekFvOFcxeUJ1RnZDdUErekRmSjNCamY0U1AiLCJtYWMiOiI2MTk4Mzg4YjlmYzdhZGM5YTgxZDgwZmYyNzBhZTYzMjgxNDBhZjZiMGZhOTRkN2UwOWRmMjVkNWJlMDkzOGFhIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFSd1hObTJrZGVRcmcySnJ4VUxUSkE9PSIsInZhbHVlIjoiazM4OHVEcnpZWEM2UHZPT1F5M3ljZkNXVjNURFJvZmFKTi9GZTREYzF0TXprVmcra05TZkFDb1dkdE1SL3ZVckd6eG50K2lsZXlydytjZzV1NGE1d0I4OEQvem1wT1lNUkpYSzNnL3I5MHFHMC9HQmZXZ1JuS01US2tzbkNCTzl1MWkzWVhvZ1lEM3VqZnREZ3ptWjFXTVpKUG5UNlNvbTVuSTR0RTB6Smp1djhqOXUzZmJUUDJFTkltTytxdERGaTB1SXdST3l4dUZOMU5yWXdDVU9ublpFdFRVdkJLTVd4NGJSZEMrYkE4MTY0QzVqNEkzd0VRUlhqcFdESXJJTnZ4TzM4VmY0S3d4R1BERHFGY2VxQ1g4alhtNEJkWEQ5eHkyaFhFSmJ2aFhTRmxibUNia216SS9USjgxRjFqZnhKZExmeEpRYzNlNmpBTk15b3FnalpHd1NLVjF1bEVBQmFvUDZQNC9UK3ZNTStHUzlqaTdsVkpOajJEbmtabUI0eVpOUzZJY3J0ejZ0eXphWGhoeVl0aFhURnpOT1FpcTRBTHk5WHpLL3NQeHNKWFhENnRWQUZ5Y0Z0U2dKNkEycWRidG5PWDluWkd4S2IwaE01UTJIcEVid25IWHF0czZETUlHNHRtdVJzSUN0b0dMUmNLUG9xUDNlY2pxaUloc2oiLCJtYWMiOiJkZDVhMjg4MWY1MzQwOGVmZmQwZjJkMzBlZTM0MjRiMmYzZTUzNWYxZGE0ZTMyNjgxZjRhOWY2NzkyZDk1YzJlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:28:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik8rczVMR1VXZUVnL2t5dkw5ZHVSc3c9PSIsInZhbHVlIjoiUGJ3dFVpam1aUUlLUlo4ZC9ZOGlFU1l3OHgvckpGSG54aXBvLzVkaTZDQXZLRFRLMmx2dzVJZmVBamhqcnQ5VS9sSW9xSjBXMXZKeHdURzFFSXZQQnkvNnp5ejN4V3ZUTGVEWHBXTWJRS1EzRzZTU0l6WWlLdGphOGtwZlVRSXZlR3AreGpMUjl3czVwRkpuRDZBYWoyaktJSGFncjdRb2syNUxWQm9yQnowbkNJL2loMERrd0o0b3laK1lkOUZyY3FYNEl1NFlmOHU1Q010enh5RFFhdUZsVlhvMVlzZklkeWdUMWFvRkE4RXlSR3NQZ3JWMnVrMEI5NG5NRS9EbzVZd0d2VFJxeGpxMWc2ai9ZV2hGc0xXSmd6Q0hoS2ZhWFRUYXh0VkpKTThqNlhQUUl1eENib1piWEprM0Rib0I1VnFWcE55TzhlVGhJaGhvYmJVcU15Q3FwdGJEdkp6cTIxUVJBeHBJYWdmbDNaajd0ZzVwdklTdVkzc2FvV2IvSVhjazFhZllYZ1B4SkdYenRpSVNTYUJZVkZidVQ0clk2cWlQM3psbHpFQWNtU25UcG1rTGxLd3JMMjZ4ZjV6bm1ScGp5L2Y3V2JqNXg5NFpwdGEvbnZ2eTVuTlMwMzVPNTNjcmJhekFvOFcxeUJ1RnZDdUErekRmSjNCamY0U1AiLCJtYWMiOiI2MTk4Mzg4YjlmYzdhZGM5YTgxZDgwZmYyNzBhZTYzMjgxNDBhZjZiMGZhOTRkN2UwOWRmMjVkNWJlMDkzOGFhIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFSd1hObTJrZGVRcmcySnJ4VUxUSkE9PSIsInZhbHVlIjoiazM4OHVEcnpZWEM2UHZPT1F5M3ljZkNXVjNURFJvZmFKTi9GZTREYzF0TXprVmcra05TZkFDb1dkdE1SL3ZVckd6eG50K2lsZXlydytjZzV1NGE1d0I4OEQvem1wT1lNUkpYSzNnL3I5MHFHMC9HQmZXZ1JuS01US2tzbkNCTzl1MWkzWVhvZ1lEM3VqZnREZ3ptWjFXTVpKUG5UNlNvbTVuSTR0RTB6Smp1djhqOXUzZmJUUDJFTkltTytxdERGaTB1SXdST3l4dUZOMU5yWXdDVU9ublpFdFRVdkJLTVd4NGJSZEMrYkE4MTY0QzVqNEkzd0VRUlhqcFdESXJJTnZ4TzM4VmY0S3d4R1BERHFGY2VxQ1g4alhtNEJkWEQ5eHkyaFhFSmJ2aFhTRmxibUNia216SS9USjgxRjFqZnhKZExmeEpRYzNlNmpBTk15b3FnalpHd1NLVjF1bEVBQmFvUDZQNC9UK3ZNTStHUzlqaTdsVkpOajJEbmtabUI0eVpOUzZJY3J0ejZ0eXphWGhoeVl0aFhURnpOT1FpcTRBTHk5WHpLL3NQeHNKWFhENnRWQUZ5Y0Z0U2dKNkEycWRidG5PWDluWkd4S2IwaE01UTJIcEVid25IWHF0czZETUlHNHRtdVJzSUN0b0dMUmNLUG9xUDNlY2pxaUloc2oiLCJtYWMiOiJkZDVhMjg4MWY1MzQwOGVmZmQwZjJkMzBlZTM0MjRiMmYzZTUzNWYxZGE0ZTMyNjgxZjRhOWY2NzkyZDk1YzJlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:28:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915156487\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1293360686 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjVjVWVLRjNpUTByQm91ekJDUnpnR2c9PSIsInZhbHVlIjoiaHZqYkFSNnJhbVVkanhtWVE5c09oUT09IiwibWFjIjoiMTA0NmU2OTlmMmM0YzE2MjM1ODhlYjFlNDk3ZTkwZjE5MGM1YTgzNzEyMWI4MDhiNGUzOTliYzU2M2MwMjMxMyIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293360686\", {\"maxDepth\":0})</script>\n"}}