{"__meta": {"id": "X074095e709a68ad9762987e935bfa38d", "datetime": "2025-07-21 01:17:43", "utime": **********.266092, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753060662.820753, "end": **********.266107, "duration": 0.44535398483276367, "duration_str": "445ms", "measures": [{"label": "Booting", "start": 1753060662.820753, "relative_start": 0, "end": **********.196615, "relative_end": **********.196615, "duration": 0.37586188316345215, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.196626, "relative_start": 0.3758728504180908, "end": **********.266109, "relative_end": 1.9073486328125e-06, "duration": 0.06948304176330566, "duration_str": "69.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44837464, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02703, "accumulated_duration_str": "27.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2285872, "duration": 0.026269999999999998, "duration_str": "26.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.188}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.258138, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 97.188, "width_percent": 2.812}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:2 [\n  1897 => array:9 [\n    \"name\" => \"option- خيار\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"1897\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  1896 => array:8 [\n    \"name\" => \"zucchini-كوسا\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"id\" => \"1896\"\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1038595900 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1038595900\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-92921626 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92921626\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1683116983 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1683116983\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753060650396%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhGVHBCeHRKWFR3bGpUVGdhK20zM3c9PSIsInZhbHVlIjoiZWZWdWgvTzcyL3dFSisxWHVQNC9ad1ZWcmZuaXF0V1REcHcwbk1kMDBuNVJOZnhMWnJWcWE5WkhTZGloeC9meDRIZkVkMTNpSjRuR2IyUGVuWmROcHlIbzY2cytOcGsyQlBjdC9wWDAwOWQzTW55Um5HcGlIcHNsQTBVMmNpdmdkMlRIdjhlcy9ZdVJmR2xkWjBUZnVtc1diU3lMbXlNU1BFVkNZNEpYVFdBNkFDWklrUUw1UlZuTWJoUmdSeFd2WHZjWjhnYmtSOVAzT2FXYURCNnBZS3BRVUlxT0duYzFIemtLdEc1dVNRSGxEcnVKS3p0N25RdVNEdnFKVUhUUUM5bjlQQkxxTXF1SldmM2JUU0IvcWhxbXg4d25tWGdsdkJRaWFQMStKek1KL1RmTE5aKy80RjdLOFk2YmtTVTRjZWRtQndjQUJQK3ZHcG9TeXdKM2FoWHRpeEtqaloyN0FRbDU0T2xFQTI4ZXlvVzRlcklnQ0tiaDhOd3dMQVVXY0hnT0hQWUxtZ2lLejVoQ1BQczB3UEdZWGU2WXFlZzArK3dMeUxodmNmcWVBVHRuMTFrNUEvbHZqUlZJQjBaSWlMeXk2T0xBSGc1ZjV5TzhPQkN1RTdwWGtKYTlWdkNZRWtPVUN5Tng4aXlJc2hIeGxPdFphbTVFM01ySEdKaGsiLCJtYWMiOiJiMmY1ZjYxMjhkZTM2MmVmN2Q2NTcxZDhkZDBlODE2ZDU1YzZhMTI3MmUwM2EwODkyMzBjNTU2ZjUyNjc2MWQ4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdNWkd3anY2NlNBQWI2RUJZV1JGQnc9PSIsInZhbHVlIjoiaU1HbkVDcE1zMDE0czBzL0p3dmE0QVFIMTVMdTk0blNxb1Y2SnF3TEg5R2owZEcrOUhuMXhtOGNaKzFMeUpOUGhXdjJMWGp0bW5IVVlDWHB5R0N6cmc1cHVIcGdCZDVONm1xSlVGTUNKTjkxZnNPbWt0V3ZmNGg3L3RLWFEzTHcvb0JROFJCdm0vT3VPU0RZZCt5SDVlTFhJd2JBejZjNlVNTVlSMi9maXJndFRmNzFIS3ZyU1lhL1Nkcm81bkxGZTFldHFFbWdXSFBnM0ROdGo1VWUzMHRxYi80cU96YmgycHo5SkhQcVV5b0oyTm1WVlA0OGc1WVlrUnRVUzlmYnU4SlpBOE5NYjZKRnVSTzNub3YrSVAvMFJvZkZaL3BZL1FKTEY2RHNJOW1wVUlmS2hKaTFvUGxHcjZhcGI0Q2JjMHhSQmFOai9LZ1dBWjgya0NsbmVTRmpYV0d2SUlkb2t2TjcwanRZdHRWb2VMbTRpOUVYTzVUUjZMa0JDNHI2TlBEaitVY0xRbzNuc2VtbmhzN2dKM1R3ZnZpN3BGelFxTmVGUDdFWjZ3SGE5RDhUTm96UU1uWUhla2Nmcm92aE0zckFhMmE0Mjl6bWZTWlZMdy9UT2t6d1JDRE4wVHByVkYvK2dXbW5FRlJSbnhCVHNTeExyMkdYckFpekJGSmYiLCJtYWMiOiIwMjc1Yjc0OWM1YTRiOWEyZmYzNzE0OGQzYTJhOTJlYTQwMzBjMWM5ZjYxYzViZGVkZTA2YTYxZDZhOTgyYWY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1597268991 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:17:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRCTjRBQTNiZVRCYVhuQ1dZdURUOGc9PSIsInZhbHVlIjoidVQ4U1kwWEJ0WUFLNVF3Njg5a1lRSDB5UXVrL2xjc2R0WFdjWVJ2U2lnR1F0bmMvaHoxNUdScHkzTktmRklqZHZOb2VhOW5xRThURDNpQTFZZHJ4ek43aGYzNlE0c2I3T0J0M2FENEV0L25kb2JmQmRZV1pOK3NRTlBFSDREQzdieGdoaGxXemtJRFlTc1NKQ3lqNXAwUVdjRE5yalRldTBTSTh2N0lnMVZBN20weXZsVTBzSHhEbWZ5S0s3d3gvYm8waVRZUkVacWhUWm41bGJDdmxUUXVyNFNVTnBXeit0SXBRRVJ1bmN5eVlRbVNYSW5IdjBLbjJhcnMrcDlBVjVaeVJvM2RVRHkxZVVudFhaa1RXeUVFYlBPVEp6eUJjWWpKb1JiOFVCZEozcWMzZnpoVmVEZ3hLb25CZlBWaFkvbTNHRFR4bzF5a0pkQ1R2bHFjZjdzd2dndzNzbzRuL2pUUjEzWTNsbVV2bkxUM2kyanU0bFZCZ1dBVVo4NmUrVFNzNlpUQmdGMXRhbVFoVUtRUW01VWVLdHpseVVQZUdqMzJDcDB3K09kM0VYb3Z6bFQrK294T1lqeUp5K2hNYnhLd1lKbFZydENWWi9aS09KOVBPbzBvMllsVnNTb2x3NGw1V1dPRndpdzd4UW1CbTRKcXhNM0l4YkJSMDQ4K2QiLCJtYWMiOiIwOGI1ODg2Y2Y1ZDAxNTk4ZTgwODhkZGQwOGNiM2EyMmFiMmQ3MDkyZWI2MmMxNGNkNGE5Y2I5YjdiN2U4ODBkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9FTmZQU3BxVGZCK2YwSDJuazg4SGc9PSIsInZhbHVlIjoiTGtlaVcrejMvdDhzNG5MOUw2T3BzTlMva3d3bWJVeG9WR0lzemhoRUpyTndnWWU5amdhbk5pY0VoQ252Mi9ySkg3OGFDZEJyMnZOU2ZmVTFKMTRRZE92Q05ST2FQNHd0QS9SdVRydDU0eXhnd0dSSHBDNVBrelpBWlFYSlo0RVFvSG9hMEJRUThVREFhR0gySEMxV0hMcWY4ZFJPSDhYM2tEN2tKVVROQURMQWZTdWhDbUN5Wll6TTZVc0k2bkoyQWVhNE5vWTNoUE1MQlpIeDlkajFoV0JscFRBRHZRQzR2cEx6NWlOcjV6NzRPWFM0RGRnN0NLTWFxbml6cGcwN09RTitBeE91OFhleHZjZWoremFROU1oQ3oxUDFnNFV4SjBWUlVKUkpvTHF5QlZQd09NekNlUDhCd0paN0MyczVMb0lpdkVCTU4wOEtaNFpMV09XYVlSZ09VendlMXhKR0Z6WjFsZHpla1pKMFZHWEhQbzZrYXFMcVQxNDZlbnBua1VCUC83eDlVVnNuR2NGOEplU3NmNk5KaXlNMVlZT29KeVhMc3pxT3E4b0R1VVhRcDQyNzR1a09KYWJQZVJacExha21KcFpLWW93aExWNnlUT09EYktJSnZ5SVk0dWVUOTFLVHJNdmVEWHdtcWpnRmwwbU9YMENnVndlLzdiRmYiLCJtYWMiOiJmOGEyN2VkM2M3NDllNTEwZTIxZDU5MGJmOTI2YzE4NDYxNTMzZGVmZDkwNmY2ZTZlNDlmMzg3YTNhOTdkMDk5IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:17:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRCTjRBQTNiZVRCYVhuQ1dZdURUOGc9PSIsInZhbHVlIjoidVQ4U1kwWEJ0WUFLNVF3Njg5a1lRSDB5UXVrL2xjc2R0WFdjWVJ2U2lnR1F0bmMvaHoxNUdScHkzTktmRklqZHZOb2VhOW5xRThURDNpQTFZZHJ4ek43aGYzNlE0c2I3T0J0M2FENEV0L25kb2JmQmRZV1pOK3NRTlBFSDREQzdieGdoaGxXemtJRFlTc1NKQ3lqNXAwUVdjRE5yalRldTBTSTh2N0lnMVZBN20weXZsVTBzSHhEbWZ5S0s3d3gvYm8waVRZUkVacWhUWm41bGJDdmxUUXVyNFNVTnBXeit0SXBRRVJ1bmN5eVlRbVNYSW5IdjBLbjJhcnMrcDlBVjVaeVJvM2RVRHkxZVVudFhaa1RXeUVFYlBPVEp6eUJjWWpKb1JiOFVCZEozcWMzZnpoVmVEZ3hLb25CZlBWaFkvbTNHRFR4bzF5a0pkQ1R2bHFjZjdzd2dndzNzbzRuL2pUUjEzWTNsbVV2bkxUM2kyanU0bFZCZ1dBVVo4NmUrVFNzNlpUQmdGMXRhbVFoVUtRUW01VWVLdHpseVVQZUdqMzJDcDB3K09kM0VYb3Z6bFQrK294T1lqeUp5K2hNYnhLd1lKbFZydENWWi9aS09KOVBPbzBvMllsVnNTb2x3NGw1V1dPRndpdzd4UW1CbTRKcXhNM0l4YkJSMDQ4K2QiLCJtYWMiOiIwOGI1ODg2Y2Y1ZDAxNTk4ZTgwODhkZGQwOGNiM2EyMmFiMmQ3MDkyZWI2MmMxNGNkNGE5Y2I5YjdiN2U4ODBkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9FTmZQU3BxVGZCK2YwSDJuazg4SGc9PSIsInZhbHVlIjoiTGtlaVcrejMvdDhzNG5MOUw2T3BzTlMva3d3bWJVeG9WR0lzemhoRUpyTndnWWU5amdhbk5pY0VoQ252Mi9ySkg3OGFDZEJyMnZOU2ZmVTFKMTRRZE92Q05ST2FQNHd0QS9SdVRydDU0eXhnd0dSSHBDNVBrelpBWlFYSlo0RVFvSG9hMEJRUThVREFhR0gySEMxV0hMcWY4ZFJPSDhYM2tEN2tKVVROQURMQWZTdWhDbUN5Wll6TTZVc0k2bkoyQWVhNE5vWTNoUE1MQlpIeDlkajFoV0JscFRBRHZRQzR2cEx6NWlOcjV6NzRPWFM0RGRnN0NLTWFxbml6cGcwN09RTitBeE91OFhleHZjZWoremFROU1oQ3oxUDFnNFV4SjBWUlVKUkpvTHF5QlZQd09NekNlUDhCd0paN0MyczVMb0lpdkVCTU4wOEtaNFpMV09XYVlSZ09VendlMXhKR0Z6WjFsZHpla1pKMFZHWEhQbzZrYXFMcVQxNDZlbnBua1VCUC83eDlVVnNuR2NGOEplU3NmNk5KaXlNMVlZT29KeVhMc3pxT3E4b0R1VVhRcDQyNzR1a09KYWJQZVJacExha21KcFpLWW93aExWNnlUT09EYktJSnZ5SVk0dWVUOTFLVHJNdmVEWHdtcWpnRmwwbU9YMENnVndlLzdiRmYiLCJtYWMiOiJmOGEyN2VkM2M3NDllNTEwZTIxZDU5MGJmOTI2YzE4NDYxNTMzZGVmZDkwNmY2ZTZlNDlmMzg3YTNhOTdkMDk5IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:17:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597268991\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1275471485 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1897</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">option- &#1582;&#1610;&#1575;&#1585;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1897</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>1896</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">zucchini-&#1603;&#1608;&#1587;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1896</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275471485\", {\"maxDepth\":0})</script>\n"}}