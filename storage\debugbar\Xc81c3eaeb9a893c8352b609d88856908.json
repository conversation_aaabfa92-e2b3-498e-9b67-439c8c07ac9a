{"__meta": {"id": "Xc81c3eaeb9a893c8352b609d88856908", "datetime": "2025-07-14 18:17:55", "utime": **********.375034, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517074.94797, "end": **********.375052, "duration": 0.4270820617675781, "duration_str": "427ms", "measures": [{"label": "Booting", "start": 1752517074.94797, "relative_start": 0, "end": **********.322866, "relative_end": **********.322866, "duration": 0.3748960494995117, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.322875, "relative_start": 0.3749051094055176, "end": **********.375054, "relative_end": 1.9073486328125e-06, "duration": 0.05217885971069336, "duration_str": "52.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032299999999999994, "accumulated_duration_str": "3.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.350222, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.467}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.360173, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.467, "width_percent": 13.932}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.366673, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.399, "width_percent": 22.601}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjRLL2xUOVRXN0JWNUJYUGxvM0pmSEE9PSIsInZhbHVlIjoiZnQ5dVduR0tWUjdKa00wOVVKaTQwdz09IiwibWFjIjoiMzQyODJkMTk1ZGMyZGZkMmRjNTZhZDIwODZjMjA5Yjc1NGM2MjI1YTgyODA3NjAzZDJmMzhhN2UxZTUwNjYxYSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-956006004 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-956006004\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-250370827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-250370827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-780600490 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780600490\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1173256663 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLL2xUOVRXN0JWNUJYUGxvM0pmSEE9PSIsInZhbHVlIjoiZnQ5dVduR0tWUjdKa00wOVVKaTQwdz09IiwibWFjIjoiMzQyODJkMTk1ZGMyZGZkMmRjNTZhZDIwODZjMjA5Yjc1NGM2MjI1YTgyODA3NjAzZDJmMzhhN2UxZTUwNjYxYSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517071250%7C20%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpQQ1lrb3FzczJ2dVhmOS9vaktHRUE9PSIsInZhbHVlIjoidnZGZkVGWWtmTDN2TnFyb3VCbEM0Kzh4ZmlpQjNOTDNST3BvTythMEY0U2FhcENhbkJkRDFLMnVuc1d6UVg3V0lSSU84WjhBV2lwOEJ5M0ZBbm1vbWo0VWE5UjV4UXJIcG04Q3RzZFYxajRaaEQxRUpPSTkyeVlEWUZyRFBRaGludkgzNitkZDZ1Uk9mUGwzaURDSWNBNElqU050REtXQ3FXWnFsL2xqMjVqTDVZR3RLNUFtMHJOblQvWmhoOW5WU1RmUGVDRVVBcy9rUmwzalVHVVZ5WVMxYXN3Nkd4RzNjMTAwb0o1RTdLcnBSMitXMnRpYWVnUGN1STJxbngzdWlvQnJKWld3bUc1VEdEYTlHN2NvbzZqaXZZUXBmcmtJUTdpWjVHMlBaTmNyWTUyKzJsUGVWcE5xcHNzdWtXbGhrSmtUeVhQZFZ5YzJsQkwwbVUwS0VqZ1kreDBRUUpCTjI2NDZmNGx3N3NLY2t5SzZqSG9BdGZHcGMranlJNy83dTA4OE40czNybU1aVkh1Qnl0QVBYaURQNHVxNDZqckcvVW9NM25yc1hpMEpudUo2Y2UvdkJ4OGRGU1o0clBheGxyR28vVHh2SWVHM1d4WTdRdjc3OXMwbW5IZk5rdVdRclpPd1ZSNDBFam1ydysyNjU3MlluL3RJeHc5NXE5K3MiLCJtYWMiOiJkZWE1NmNmZWU1NjVjMTk3ZjNkYzNmYWZkM2ZjNjhlN2VkMWNmMTE0YWFiYzAxOGM4Y2VjZTUwZGU3NGZlOTkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imw5TElIWW1hU2tuQjNzZHc5bFBKblE9PSIsInZhbHVlIjoiNDk2UG9rTHU0MVV6UFVwS0k1NzhTUmpnSjNHWmd3MzJKZjQzQW1WT25aWDByZk5zeGtSQVFPL0ZmZ1pwK2lTQ29DK3hvejg0YytEV20zN09pZENTOUFMMUhvMEZsN1M5Q3hLSHNxelA4T2JZNitiQmpBS0JMY1k4b0QvNEZOZkxFNVVaOFY3R1FmNldPSGJoODBJQmZ2MWlOWWJxUll2MVRQVWNENkpsVktUVW9pTEhERXBCMUMvN21kU2xRTGsvZ0lqakZ2bnd3b3Yza2pLVU1XZlVxWFl6OTN1dURJMTdGNU94cFp0Qk9UV1Y1eWo3b0ZtWTh1WHhXYzZtWEFjc3cxR3dPcUFjQW9DakNsZmlYZzdOQVdKLzhQVjJhb0FtazdIR0hHM2lLb0hSWmlMNkdERmRkTDB5eXkvU2N1MjRqTm1zVkprL3VDd2kzUFVTOHVUc050KzNNV2N6N0lRZ0cwTm42UFJxaG9BdXVqbVQ3bUdlVnRwaVA3d0J1RFJWdlFrZ2dIM1hmWnFuWmkreFg1aVpyTmNMbjhhUXN0dithMnNnM1lrUmRwUkdaY1NIS3NlZUptMDZBQXVQMU9VRDBsWlhKTW9haVZ2NEZaUFlNV0ZvUkN1aTgrQitqUkxoWUFRWGJyQ2NVNU01OC9YYkZlWGVFbjdjbUJDeTA4UDciLCJtYWMiOiI1Nzc3NWJhMjRiMzEzZDY4MGUzYWM5OWFjODMxYTdmMDA1MTMzOWNlYTExMTdmMWFmNWI4ZDA2ZmZkOGZhMWJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173256663\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1657000008 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657000008\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1244027944 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZiUkpFSmpacFNGdDdnZytxWkJTL2c9PSIsInZhbHVlIjoiTnVmeG1xUXZBWTRvRFZ3Qlp2Q1JyN0Q1OEdWZFNvSVhsODRJSnp1VEZiQi9Jbjk1alE0Vmxxd2pRVDRrYXNSM05scmJTY2FZd1VrRWdqa3ZuZTE0R00xZDNlRTZpeUI2eDkzcjl0elpmN0xzUk9oemFzaXdITkhrMHcxOWdJb29qQ1NRSGVab0lmaUFYT3JqNDNNWHJQRVI2NTB2ZUdTRkJBOXg5S0JxT29QOHo4NVpXdVV3RDBSY0VxUjkzUzA2VmVkSmRpVWgySlBUSTNZT29jTTlpa0c1QndHTXJOdHRZYUlTdC95em9QNVo3RU9YU1NUZUtJR1lpVkRoUGxpZklpZHNRLy84bHlaN0NaVmxRRW9tRzNFNEpYWUhIb0dWSUVlQjkzVEpMZFFzMXdNOFJWbWc1MmEyNGdWVVY1UUdXNFNyZzlSeU5XSmhFUVp1Y0VadTk1WTczOEt4RHp0cGdYckFSTWMyWFFrUnhOazFyVUw0NGlsVGlvcHJZcnhYdHlFQ09UcUxKamNudTVEcnJzL0c3bUlXYndMcnZRQlloaUxWV3JkeXRQTk5SMEdRS1dGRnZxV3o4MFJkZWdZem04OGhFaHZTU2ZXbjJyWUdLSkJFR1ozTWJRZ2VuSWxGRlh2c1hmdTdNWExrOEE3NkhWNHdaQzVaZVRZKzI1aDciLCJtYWMiOiJjYTBkYTkxZWI2NDNhY2ExYTBlNDMyNjY3NjQxNWE2NzE4MjRlNzA3Y2YxNGI3NTA2YmQ3NTg1YzcwYTdlNzljIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJGNU96WHY0M2s2V25zV0tMRG93N2c9PSIsInZhbHVlIjoiaFI0WHZONDJyQllEMUF4K3N2L3NtK2hBdURvVzVBaGg3a1lrZm1lTVF5Y0htWC9ZRTQwN3VHK09IRmFxeW9oU0QrL1doa083WG5EcjY2SUN4UDBra3JtZE1zMjcvTUZlSXV0b2JxTytLQ29sdVpJYko4NDRhRC9saFRqRHhSZ2U5Vm01bmFaTzZBcDNNRVp3V3pleTVaVER1KzAwYnhqU1FlL0QwUDducUV5cjQvSlhjaVVGNWtHbjFxSmVRcCtzWkFtVWp2K0ZmSSs5aUJNYVNKUFBaaEtWWmdGVWM5cEZYZ2t5YXdKa1hjMlkwei9aWUQrNkRYSi8wbkcxY1V5Y2hjYUVpZ0R3TnB2dFhuNzRFV2Q0emI0ajBIZWh1WWx4U3IzSmlBM082WGg5NzZVcFRNWEdWd0Y5cnVRNjdzcE9OUFptQkdtS0VCelpvQXdlYTk2bWNlMnF4Z1FkSWtqV3RNVFg2eHhSTjRaamxreG8rSkowQnBuZER5OTFTMElnZ1g2Z29ZY2w1eVRzNDdQUGZseEZrTG5mekxVbStITHdUZ0N4MWJqdXJYUjE1MWtqRU44a2xpWWxrUHFrdnY4cFRReGxoSGx3UXFnUUxnQUE3YXoveW90dHZaLzE5QVd0dUUxMlFpSGl1S1hlOXZyWXZqcVlZMEFTUFdUNzhyeHoiLCJtYWMiOiI2NGYxMzlhOWVkYTU2ZjA3OGI5Yzc2MTQyNDRkZTA5MTdkYWM0OWQ4ZmJjYjE3ZjhlMmY5Mjg4YmQ4YjgxYzJmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZiUkpFSmpacFNGdDdnZytxWkJTL2c9PSIsInZhbHVlIjoiTnVmeG1xUXZBWTRvRFZ3Qlp2Q1JyN0Q1OEdWZFNvSVhsODRJSnp1VEZiQi9Jbjk1alE0Vmxxd2pRVDRrYXNSM05scmJTY2FZd1VrRWdqa3ZuZTE0R00xZDNlRTZpeUI2eDkzcjl0elpmN0xzUk9oemFzaXdITkhrMHcxOWdJb29qQ1NRSGVab0lmaUFYT3JqNDNNWHJQRVI2NTB2ZUdTRkJBOXg5S0JxT29QOHo4NVpXdVV3RDBSY0VxUjkzUzA2VmVkSmRpVWgySlBUSTNZT29jTTlpa0c1QndHTXJOdHRZYUlTdC95em9QNVo3RU9YU1NUZUtJR1lpVkRoUGxpZklpZHNRLy84bHlaN0NaVmxRRW9tRzNFNEpYWUhIb0dWSUVlQjkzVEpMZFFzMXdNOFJWbWc1MmEyNGdWVVY1UUdXNFNyZzlSeU5XSmhFUVp1Y0VadTk1WTczOEt4RHp0cGdYckFSTWMyWFFrUnhOazFyVUw0NGlsVGlvcHJZcnhYdHlFQ09UcUxKamNudTVEcnJzL0c3bUlXYndMcnZRQlloaUxWV3JkeXRQTk5SMEdRS1dGRnZxV3o4MFJkZWdZem04OGhFaHZTU2ZXbjJyWUdLSkJFR1ozTWJRZ2VuSWxGRlh2c1hmdTdNWExrOEE3NkhWNHdaQzVaZVRZKzI1aDciLCJtYWMiOiJjYTBkYTkxZWI2NDNhY2ExYTBlNDMyNjY3NjQxNWE2NzE4MjRlNzA3Y2YxNGI3NTA2YmQ3NTg1YzcwYTdlNzljIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJGNU96WHY0M2s2V25zV0tMRG93N2c9PSIsInZhbHVlIjoiaFI0WHZONDJyQllEMUF4K3N2L3NtK2hBdURvVzVBaGg3a1lrZm1lTVF5Y0htWC9ZRTQwN3VHK09IRmFxeW9oU0QrL1doa083WG5EcjY2SUN4UDBra3JtZE1zMjcvTUZlSXV0b2JxTytLQ29sdVpJYko4NDRhRC9saFRqRHhSZ2U5Vm01bmFaTzZBcDNNRVp3V3pleTVaVER1KzAwYnhqU1FlL0QwUDducUV5cjQvSlhjaVVGNWtHbjFxSmVRcCtzWkFtVWp2K0ZmSSs5aUJNYVNKUFBaaEtWWmdGVWM5cEZYZ2t5YXdKa1hjMlkwei9aWUQrNkRYSi8wbkcxY1V5Y2hjYUVpZ0R3TnB2dFhuNzRFV2Q0emI0ajBIZWh1WWx4U3IzSmlBM082WGg5NzZVcFRNWEdWd0Y5cnVRNjdzcE9OUFptQkdtS0VCelpvQXdlYTk2bWNlMnF4Z1FkSWtqV3RNVFg2eHhSTjRaamxreG8rSkowQnBuZER5OTFTMElnZ1g2Z29ZY2w1eVRzNDdQUGZseEZrTG5mekxVbStITHdUZ0N4MWJqdXJYUjE1MWtqRU44a2xpWWxrUHFrdnY4cFRReGxoSGx3UXFnUUxnQUE3YXoveW90dHZaLzE5QVd0dUUxMlFpSGl1S1hlOXZyWXZqcVlZMEFTUFdUNzhyeHoiLCJtYWMiOiI2NGYxMzlhOWVkYTU2ZjA3OGI5Yzc2MTQyNDRkZTA5MTdkYWM0OWQ4ZmJjYjE3ZjhlMmY5Mjg4YmQ4YjgxYzJmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244027944\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1157284422 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLL2xUOVRXN0JWNUJYUGxvM0pmSEE9PSIsInZhbHVlIjoiZnQ5dVduR0tWUjdKa00wOVVKaTQwdz09IiwibWFjIjoiMzQyODJkMTk1ZGMyZGZkMmRjNTZhZDIwODZjMjA5Yjc1NGM2MjI1YTgyODA3NjAzZDJmMzhhN2UxZTUwNjYxYSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157284422\", {\"maxDepth\":0})</script>\n"}}