{"__meta": {"id": "X3fcbebc3190817192acb0fd28b333aec", "datetime": "2025-07-14 18:30:15", "utime": **********.074601, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752517814.601276, "end": **********.074617, "duration": 0.4733409881591797, "duration_str": "473ms", "measures": [{"label": "Booting", "start": 1752517814.601276, "relative_start": 0, "end": 1752517814.997374, "relative_end": 1752517814.997374, "duration": 0.39609813690185547, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752517814.997387, "relative_start": 0.39611101150512695, "end": **********.074618, "relative_end": 1.1920928955078125e-06, "duration": 0.07723116874694824, "duration_str": "77.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991128, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02259, "accumulated_duration_str": "22.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.029811, "duration": 0.02146, "duration_str": "21.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.998}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.060281, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.998, "width_percent": 2.258}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.066389, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.255, "width_percent": 2.745}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/payables\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1536135835 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1536135835\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1594912520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1594912520\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1672475556 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672475556\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-187381305 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/report/payables</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; XSRF-TOKEN=eyJpdiI6IlpnaGo5c0tYdzVYek80TzM5STNqRkE9PSIsInZhbHVlIjoiY1hDQ3gvYTlHamo4YTc5S2NkRGFKUXM2OTZsVkx0RFBZZjZKWXdvTTdOZ2RmYytqZjhINW1ybXZxckxkV091QmJhSHJ3Q1V6dkFYRkU0aDlvL1MzaFI0M2FpM3EybGRuTzRCOTNYeVUzTXgvQ05rZGZvanQ5dnpIZkNoNzVsU0tXM2Q2ZnY1TVVZSGcyWXluWHdBSEhUbk9GbzRnNGlwS1J4bXZoY0VqeHZQL3llV21kbTlFRUVaZUUrSlRuZzNTZVJpU3kyd0RJcGVYNUFGMENUL1pYbVRoMTNFYVNWYTZMOUFmdDdVakVXRWg4eGg3V2s5UG50cE95UEpkNkNzSVpxako5MnRrMVNlRXk3RWFmY2g1UjdVaEU3OS9lbHM0N2hibm1iMkhMNnluRVVXcFZJVnhRVkZZM3M4Q2w5UXRLMTlGVHVXdDlSVDdkcmpCSndFMzNydkluakZBZE4rYjc5ZnNzTmRCV25PWEFBeWd5MUNlY0lPWU5LUFpSdjFGM2ZHd2U4OER6b085MWM4UWxmL2NxUWJQd0tRV2NvVDZTakRBN1Z6ZHhxaWdjZ08xUVNNNjRNSWdPQmMyWDAyQXExMWRoMmdYZE1tcENGandDRWxGRFVDYXNsMFkyL05kc3VETkNiTnBNLzF2akZmeG5LM2E5VDVOUzZCQmZSTEEiLCJtYWMiOiIzNjQ2NDU4ZDc5M2U1OGQ1YjhkNDI1NDNlMWM3Mzk2OWQxODZhMTg1MGY2MWQ3ODdhZDM4MTg0MjU4ZjFmNjMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktnN2Z3TVFZaTh5b1pWV3FWV2lleHc9PSIsInZhbHVlIjoicFozbzBTYzNUZkd6RHk3ZWs5Yk1VTDBEOG92blR5THZmUWsvZTRIeU1GUEJld2IvNWh5c2JOdlVhMUdwVkhoKzhkYU0xaEdJQ0Y0TXpTY3FOTDVhRThoYmR1ckxudHdaTnRBT1FrS1pWU2hnSi9NT1NubHRFQlNybWltZzg1WVpMZ3ZrN2hjaFVHRWV2ZzNGR0l0emdHK2RPdFlRcytNazkxTUtuc0F5aFFwU2YyNGU0NUc2ZnZwL01GQWtaMllWR3VtaSsvL1l6cVgzdmcyMm1CQTcyQUYwbVpYdWRLbjRUYVR3UnR6MVRkQmtPRjdGSmpDWXhjUUNNcC9LTmVFU3dhTEVMd0cwRkJVMUpwbjlQNEUrVCtBdXEybDM5QjJVYWZ3dEExYXhPRXpFNzlIRWprakovc1pkL1FsaGZ3Y2VKQ1ZHUnV0d3RoRzJua1JNVWI3NVBsUVZnemVydnBoalJzbG9QWkdUcFJNQjltRE5lbUtJNnE2dXZTekFaZDhuT1Q5S1BjeGJaWXM4UVl3anRpTUFuaTRBNUdRUmZXSU1RU1ZCTWl5aGVKU29rOWtGMDJwR0M5MFk1My9kUHZkTHh3NXBtYmdyR2RUUm1qL2Vac2NKYmQ1clQ2MjU3TXhCV2F4UW1LdUx3anRwMVpXNHJIenJvam9CY2c5ME1LZ3YiLCJtYWMiOiJkNWRjYzJlNTFmN2UyMmMwNzNmMDdjZmRjOGQ3ZGFjZjU2NDMyNWNkOWU5MDIwZmJlMTkxZTIyNzExZDM4N2NkIiwidGFnIjoiIn0%3D; _clsk=14bfr1r%7C1752517814272%7C40%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187381305\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1330522154 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1330522154\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1275863347 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:30:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5kRUtaVGwxcnZwa3NzQkMrYThTb1E9PSIsInZhbHVlIjoiVHFvT1RkQnhrSUFKN2ZiNXdkN3hneWJmei8vSGF5di8xSXJtVUlrSHo3SFQ3YStZS3lQem4vTUxUaGwzYUJ6MjFFakM0WGxrUjhnMU91TWgycW9ZcGs4UjVicjgyVGZXYVc1ZG9xOElBRk54SDRrUlRHaDNvbFdWYW5ORDNGdW16Y0ZWVkRmT1p2L3Zabk5XUzY2eTR0aGZKa2xidStiZG5LVjAyTkgvbjBscnFjRis3QUo1TnFGQ0JvVW1YcEFQS2lIOXo2U01nWXhuOGt4ZTR1MkZhcGtpUG9saUZidkxOWjJDaElvV0VxV1JBZVNVUWZWNy82MVo4TGZWTlpuNUFjMzVyMUNEVnJZVXNZeDFtc05LWTluZ3VwdS9jRko4SUZ6U0oyb0puM3g5YTJmWVhsZW1iL0RYL1o1VG9kSzF5MXdlT2hiMmhVYlJpQlNhTnMwK0ZHR05wbWYvN2JyVWNoeFNaNnFHWktIRVA2MEhla21JTEFiVk56RElMVlh2c0lBenNNY045TXA4bnBjdlJiSy9JWERqTTMyQ1FwSHRKVFNRaU85YUtGMkRCVUxnVDN2aXRGSVhMOVMzV2hOcVpXU1BsOEM5ckVVYWZBTXFkMnpGenhzZ3lRL2I5Qmc4ak9laE9tSUQ2SnlxbW15WDRNM01Zcm1SQjBWOWt1TlYiLCJtYWMiOiJkZWI3NmZlYzBhNTg5OTU4OTg2MTMyN2MzNWY3YTIwYzZmMzk1MTMzNjZkNjU5NTQwMzRjNGM5NzMzOWQ0YjFjIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJGRk9aNVFUa0dnK2VOMEV0azJnamc9PSIsInZhbHVlIjoiVVgvam1NVkVpbVMwcDlJY2VrRE5GbHJXOE1hZHlyTGpYaERmd1Q0MlBIMEh0VlUxcDM1dExySXVvRjB6T0U5ZktYK09iTmFlTmVFbzk1bFpCWUlmYkxaMkZLdHZ6UmNmeGhDSmYvSnpma2ZHT2o5OHBTS3RNQXduNk9rYThFVVVESVM5YzE5TXlXdVFQYzZJMUxKemJpWnlGazYzSnZMWTgvZnlnQnVkR0k5U2pxVENzREN1UUsrU0N4c1RvTm41VVdnYXNUYi9KcmdMaUxObElxRHdYcjMyRzlBVFRzTUdCNFNwZFZ4VDZNUlMwWlV2QzVCaENER2R3Qjg1SXBGZ3ZqWWc1VDBGdUsxQ0JQdzZhbWtUNzhFYXNuZ1pDOStSYWZDVTFYKzArQWdNSVZDU2xLYmc1Mkl1TTZIVERVMGppamNUaXNWY1ltdTNKQTBIU2RjWHpOQU1Eb2FWcC9sMExOUG5VbG0raHpaSUhtYWtWVm1oeGRLUGxLdzRCL0RsT3hERkZDc2NBNXhGZHNUSjNHVXJUMW9VeC80OWlBV1dZcGR0QnNibDg3cG96SWs0N3R3RGIweVRlckUyRmdFRFBXMEFWemQzVzE3cFBYWW1IeXlrNHFFNmpCSmJHdnV4NmZzSTk0YnlGa29ITmV0MUVnS09TR1RnNGNzRjc1SUQiLCJtYWMiOiJhNmQ3OTdiOTFmYjZiZjhkM2VlYzU0NDY2MzRmY2U0NzhmNTQwYmJlMWQzMWJjMzA3YTNjODAwNmQwZGViNmQxIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:30:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5kRUtaVGwxcnZwa3NzQkMrYThTb1E9PSIsInZhbHVlIjoiVHFvT1RkQnhrSUFKN2ZiNXdkN3hneWJmei8vSGF5di8xSXJtVUlrSHo3SFQ3YStZS3lQem4vTUxUaGwzYUJ6MjFFakM0WGxrUjhnMU91TWgycW9ZcGs4UjVicjgyVGZXYVc1ZG9xOElBRk54SDRrUlRHaDNvbFdWYW5ORDNGdW16Y0ZWVkRmT1p2L3Zabk5XUzY2eTR0aGZKa2xidStiZG5LVjAyTkgvbjBscnFjRis3QUo1TnFGQ0JvVW1YcEFQS2lIOXo2U01nWXhuOGt4ZTR1MkZhcGtpUG9saUZidkxOWjJDaElvV0VxV1JBZVNVUWZWNy82MVo4TGZWTlpuNUFjMzVyMUNEVnJZVXNZeDFtc05LWTluZ3VwdS9jRko4SUZ6U0oyb0puM3g5YTJmWVhsZW1iL0RYL1o1VG9kSzF5MXdlT2hiMmhVYlJpQlNhTnMwK0ZHR05wbWYvN2JyVWNoeFNaNnFHWktIRVA2MEhla21JTEFiVk56RElMVlh2c0lBenNNY045TXA4bnBjdlJiSy9JWERqTTMyQ1FwSHRKVFNRaU85YUtGMkRCVUxnVDN2aXRGSVhMOVMzV2hOcVpXU1BsOEM5ckVVYWZBTXFkMnpGenhzZ3lRL2I5Qmc4ak9laE9tSUQ2SnlxbW15WDRNM01Zcm1SQjBWOWt1TlYiLCJtYWMiOiJkZWI3NmZlYzBhNTg5OTU4OTg2MTMyN2MzNWY3YTIwYzZmMzk1MTMzNjZkNjU5NTQwMzRjNGM5NzMzOWQ0YjFjIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJGRk9aNVFUa0dnK2VOMEV0azJnamc9PSIsInZhbHVlIjoiVVgvam1NVkVpbVMwcDlJY2VrRE5GbHJXOE1hZHlyTGpYaERmd1Q0MlBIMEh0VlUxcDM1dExySXVvRjB6T0U5ZktYK09iTmFlTmVFbzk1bFpCWUlmYkxaMkZLdHZ6UmNmeGhDSmYvSnpma2ZHT2o5OHBTS3RNQXduNk9rYThFVVVESVM5YzE5TXlXdVFQYzZJMUxKemJpWnlGazYzSnZMWTgvZnlnQnVkR0k5U2pxVENzREN1UUsrU0N4c1RvTm41VVdnYXNUYi9KcmdMaUxObElxRHdYcjMyRzlBVFRzTUdCNFNwZFZ4VDZNUlMwWlV2QzVCaENER2R3Qjg1SXBGZ3ZqWWc1VDBGdUsxQ0JQdzZhbWtUNzhFYXNuZ1pDOStSYWZDVTFYKzArQWdNSVZDU2xLYmc1Mkl1TTZIVERVMGppamNUaXNWY1ltdTNKQTBIU2RjWHpOQU1Eb2FWcC9sMExOUG5VbG0raHpaSUhtYWtWVm1oeGRLUGxLdzRCL0RsT3hERkZDc2NBNXhGZHNUSjNHVXJUMW9VeC80OWlBV1dZcGR0QnNibDg3cG96SWs0N3R3RGIweVRlckUyRmdFRFBXMEFWemQzVzE3cFBYWW1IeXlrNHFFNmpCSmJHdnV4NmZzSTk0YnlGa29ITmV0MUVnS09TR1RnNGNzRjc1SUQiLCJtYWMiOiJhNmQ3OTdiOTFmYjZiZjhkM2VlYzU0NDY2MzRmY2U0NzhmNTQwYmJlMWQzMWJjMzA3YTNjODAwNmQwZGViNmQxIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:30:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275863347\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1315160814 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/report/payables</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315160814\", {\"maxDepth\":0})</script>\n"}}