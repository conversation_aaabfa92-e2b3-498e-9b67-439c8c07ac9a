{"__meta": {"id": "X439774679b64bd0e27c60984824d2dfa", "datetime": "2025-07-21 01:19:49", "utime": **********.62003, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.188803, "end": **********.620045, "duration": 0.4312419891357422, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.188803, "relative_start": 0, "end": **********.569306, "relative_end": **********.569306, "duration": 0.38050293922424316, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.569315, "relative_start": 0.380511999130249, "end": **********.620047, "relative_end": 2.1457672119140625e-06, "duration": 0.05073213577270508, "duration_str": "50.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991264, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025499999999999997, "accumulated_duration_str": "2.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.596197, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.02}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.606506, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.02, "width_percent": 12.941}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.61208, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.961, "width_percent": 18.039}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-611791587 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-611791587\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-832357946 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-832357946\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-771862619 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771862619\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1225205456 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753060779736%7C5%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpBempLRHZQY3ljQ04xUnhoRGUrd0E9PSIsInZhbHVlIjoiQWJZdlJjZG9sR1B6cVRvdjRGMm1XTGtEd3NqdWJoMGowZ2tRbmg4TXMwRStSbXR1TUFHdXQ4TFNuQi9rWHU4QUI0aUNIZkZBdThHSnBYczNMclRWU1F2MmNaVEVQUGJyNVJNTGRZSGhTWEJCbE5iQjlDUGpqc0pRT01nOHk2VE1QTXluVVNLMEh2RGxxZno5WkdnVUU2cXd3LzdZM2ExSmJ6WXpGcHRBRElZOGxFZmV6MEFreGFGb0xGaHJtSkpZUTdjNzBJYlhwUXcyU0dOWU1PV3BwdVgyTG16aFhHNU9rOGswQnZ6U281QVBUeVcycE54YnIwNlpHMWloeldiZ2lnS1lrUVhTZDVhQjduOVYycDF3NGQ2Q0RKdVJuUy8reWxzalBEVEtrK0JQc2J0eVFVZXg3MklFYlVpakVVcHNDVWNsc1lBek05L21XUnhJNmVMcS9BTEtkQ21Ra1NiVmt2OUdCSlpJYjd3ZWV6MmhYMWpvSXgrbmM5d3RaTG5GbXBYQllxc3FQUjN1VUZpYmdkRDh1eXdDRnJCdmlEYmNhWFQweVROd01rZmcrVHRzcmd3QzRWZU50V1dnZ2xTRndWb25QRFJ5T0FqcHd6K0dlYVk5SG9OYVBma3ptMkVWaTJOd2hETFcxOWp4aTJpOVY3SWhlU1F6aFNyalNvejciLCJtYWMiOiI2ZTBmZGM2ZDE3YWI3M2IzMGVhNjI2ZThjZGU1ZWE4MTRhODU2YzRlY2NiYTc0MTAwOWY1NjJjM2NiNGI3YTRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9QV0tsVERwSHc2NDNTRm50WVlGWUE9PSIsInZhbHVlIjoiMGxJbk51YTdCQ0hPUTh0UnFPamhYNndZdm1KUm9UUitJYXVwYWZXVWQ2MXJtTGNQQzZFbUxTb1ZpOGV2Q2MwQmV2M2tiTGMxaEpCT2F5TmFHVTJvcDNUYWNSRTJhSFNkNmxjUXZVd2ZpTGtuc0wxSks4eDlYYWw4U2g1UU9xVlg4T3pFSElNdHU0VndNUHJuWXoxMVR5WDljdkpYRmpuNkFLS1crZEk3L052ZzcvSHVPaWxGamdqRnk3L0phMzBodjJ5MkJ4Nm5lZGI1SlozeERwQktzZWtMTloyZWVaaTZ3Qm00RmpMSmd6STdFeExSd2FudGJVZkhJZGN0ZGlVbW9Xd3Arc1ljVHRMMkd6KytUd1RFakFhMlZmS1pqc2FiL3hGQWxaSWFnTVJqWnY5KzZJQ3Yxa3NaYVN4ZmlOZkM3eld3Ky9QcnJIVGU5TlN2RjVETUQ2TEJ5azJhVHR6TG80QU03K09JT2s3Q1BUdDdLR3loTEovaUhZTkh0OWI1b05VWFpaK2hka255dHFRcm1vektlR05Zckwxcm1PWXR4a0xvS1JidGdvRFM5SXR4dUsrUmd4OTFNLzlqdXR5L2l5aTdHUUNqWnp2SGEzOUxMSDlhY3FTbFcyZzNjVDFsSUxxWEtaNkZlWWZ6TnZTQ3dwUDlZd1J5S0ttZ0FYdy8iLCJtYWMiOiI4ZGZlZmQxMzE1ZjNjYzVjMmUyZDYwY2IzYzZmNDdlMGJkOThhNjM4YTQ5ZjU5MjU5OGIxNjUzNmUzNzMwMjkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225205456\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1151758692 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151758692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1279162293 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:19:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJGWXNIZ0xKeHgxTHFSVE5IT3R0blE9PSIsInZhbHVlIjoiUUZZSXlLNFlvYXplZjJ2ZVo1b2RBNjhmeTFwWmxkbVRuM2NzR2RwV3RMbVR2TUc5QzBqM1NHTzFFZVY4SFIxVWtscTExT0xmOHFHY3A0L2U3OHZ6dUkrb3ZWMTBIUFpTMlVIWHlyZG1tdFZtZWV4b3JrenhxYVhPUlZNZUxndGh0NTJFSVJxaktBSUgwdndxSDBPUTREUWw3N2QvbnFWZnZDWGFpWjI5OU5DUm9lYkpHVENGSnc2VWg2R2hIUHBXKzJXN3JpYkR3ZHFrc1NBRXhQNENsWVZuTTdjTzI4TFVCY0dDRFl6ckpxWlpvZjRqd0xUV2JyZWhWTXBMK1A2WTFVdHM1eXYyWjVqR0NjOTgxemE2dzF0aS9KdW1LQnJFUEVrdEFuQUVyQmhjZnAxQTRDRFRSY01QMVFhT3Z3bXNCSU5GM1g0RUhhUTg0d3Uxc2VCc0pzdGprU3dXalgxUWk1YTArNDVPT0w0aWNTY1ZaaW9lRU9vb1Z5N29QbUZESStvbnNJWGMrVGJyK1puTzVkMVFDbFlVWXM4ZmV3MDgvTlROUjE5SXk5eGFhbjI2K1lVMWNuVnRJYWJMcEt1QmlKUVowNzluZ0pSVTRuWUFRRis0ZEc5bEluVFMyLzBrajc0Nmw5M1hVZC9tYXJnM25sM3lqNHoxRGpwSzRkOFkiLCJtYWMiOiI0MjgxM2JiMWExYmQ3NzI1YzNlMDlhMzIxZjRkODA3YWI2OTk3Y2RiOTM0ZTI0YjczYzQyYTg2YjJlNTNmMjc5IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdYZEhNN2hUdGE4a25lS0pKbzRxS0E9PSIsInZhbHVlIjoiQWcyUGhkUG0yeGhtUGNJNXh0K1IxTlpJQU5YNmF0NmFhZnk1M2NIQjJoVVBmeFJhOWNwU2pNU2dIRkhkcjk3RFh3QnlVSFpqZlZCT0hNVDQ4OWdTSnorSEdGNG5aQ2dLVUdsamF4SEluSmtCOEpMNTN5NSt5MENXeitxN3VZbmp4dUp2SjBGd2hmMm1UVlhXN0Jwb0hKWXJLUmJzcDdxS3djd3E4Z01DWHNTOGZUZDBQckNGSmxrZWphY3AyNE51K3RRYlJJK0x0dk5CQ1hJb1pPTnpWOHNxQlRnRkJscmlTTU5sRWJKSTc2OHhuVzBrVTBhdldSNVhXRUpoS0tXQndGQ3JLNFZ4cTN3N3ZERTRjYUNmNkRZNkhOZE9QTmFlU2ZQOFZUc0xXenNtbGcyQWIzbjRONjlPUmU5Ti85R2tLSEQ1OU5PNUwzMkhTbFVvYlhMZFpvUEZxOWpzcndFWi9reW9rNFc3bGw5UUhXUE1TS3RlMm5nZURpZDdlVEtNeElubEdsMGxWR0VmeUQ5R1U4ckJ1ekJuNktLR3hrazcycVNkWVBESjJweUhxOU56U29lc2UxVHhnYlhtTWQ2dzFyY3lwYzBKSEtXemRXTk5Tc3VyOE5SazFLbEZJVmt3emFzcmt3ZXdKRUJPdnNNODF3eUZKaVNhR0FsN0RhN0oiLCJtYWMiOiIxY2Q1ZWJkNDAxODVkMTY2NjQ1MjhmM2VmOTdlNGY0OWFkOWFmM2U0NDEzZGJkYjRkZWI3OGI5NTcyYzVkMDMxIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:19:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJGWXNIZ0xKeHgxTHFSVE5IT3R0blE9PSIsInZhbHVlIjoiUUZZSXlLNFlvYXplZjJ2ZVo1b2RBNjhmeTFwWmxkbVRuM2NzR2RwV3RMbVR2TUc5QzBqM1NHTzFFZVY4SFIxVWtscTExT0xmOHFHY3A0L2U3OHZ6dUkrb3ZWMTBIUFpTMlVIWHlyZG1tdFZtZWV4b3JrenhxYVhPUlZNZUxndGh0NTJFSVJxaktBSUgwdndxSDBPUTREUWw3N2QvbnFWZnZDWGFpWjI5OU5DUm9lYkpHVENGSnc2VWg2R2hIUHBXKzJXN3JpYkR3ZHFrc1NBRXhQNENsWVZuTTdjTzI4TFVCY0dDRFl6ckpxWlpvZjRqd0xUV2JyZWhWTXBMK1A2WTFVdHM1eXYyWjVqR0NjOTgxemE2dzF0aS9KdW1LQnJFUEVrdEFuQUVyQmhjZnAxQTRDRFRSY01QMVFhT3Z3bXNCSU5GM1g0RUhhUTg0d3Uxc2VCc0pzdGprU3dXalgxUWk1YTArNDVPT0w0aWNTY1ZaaW9lRU9vb1Z5N29QbUZESStvbnNJWGMrVGJyK1puTzVkMVFDbFlVWXM4ZmV3MDgvTlROUjE5SXk5eGFhbjI2K1lVMWNuVnRJYWJMcEt1QmlKUVowNzluZ0pSVTRuWUFRRis0ZEc5bEluVFMyLzBrajc0Nmw5M1hVZC9tYXJnM25sM3lqNHoxRGpwSzRkOFkiLCJtYWMiOiI0MjgxM2JiMWExYmQ3NzI1YzNlMDlhMzIxZjRkODA3YWI2OTk3Y2RiOTM0ZTI0YjczYzQyYTg2YjJlNTNmMjc5IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdYZEhNN2hUdGE4a25lS0pKbzRxS0E9PSIsInZhbHVlIjoiQWcyUGhkUG0yeGhtUGNJNXh0K1IxTlpJQU5YNmF0NmFhZnk1M2NIQjJoVVBmeFJhOWNwU2pNU2dIRkhkcjk3RFh3QnlVSFpqZlZCT0hNVDQ4OWdTSnorSEdGNG5aQ2dLVUdsamF4SEluSmtCOEpMNTN5NSt5MENXeitxN3VZbmp4dUp2SjBGd2hmMm1UVlhXN0Jwb0hKWXJLUmJzcDdxS3djd3E4Z01DWHNTOGZUZDBQckNGSmxrZWphY3AyNE51K3RRYlJJK0x0dk5CQ1hJb1pPTnpWOHNxQlRnRkJscmlTTU5sRWJKSTc2OHhuVzBrVTBhdldSNVhXRUpoS0tXQndGQ3JLNFZ4cTN3N3ZERTRjYUNmNkRZNkhOZE9QTmFlU2ZQOFZUc0xXenNtbGcyQWIzbjRONjlPUmU5Ti85R2tLSEQ1OU5PNUwzMkhTbFVvYlhMZFpvUEZxOWpzcndFWi9reW9rNFc3bGw5UUhXUE1TS3RlMm5nZURpZDdlVEtNeElubEdsMGxWR0VmeUQ5R1U4ckJ1ekJuNktLR3hrazcycVNkWVBESjJweUhxOU56U29lc2UxVHhnYlhtTWQ2dzFyY3lwYzBKSEtXemRXTk5Tc3VyOE5SazFLbEZJVmt3emFzcmt3ZXdKRUJPdnNNODF3eUZKaVNhR0FsN0RhN0oiLCJtYWMiOiIxY2Q1ZWJkNDAxODVkMTY2NjQ1MjhmM2VmOTdlNGY0OWFkOWFmM2U0NDEzZGJkYjRkZWI3OGI5NTcyYzVkMDMxIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:19:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279162293\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1270905515 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270905515\", {\"maxDepth\":0})</script>\n"}}