
<form action="<?php echo e(route('receipt.voucher.store')); ?>" method="POST" class="container mt-4" dir="">
    <?php echo csrf_field(); ?>

    <!-- Date -->
    <div class="mb-3">
        <label for="date" class="form-label"><?php echo e(__('Date')); ?></label>
        <input type="date" class="form-control" id="date" name="date" max="9999-12-31" required>
    </div>

    <!-- Amount -->
    <div class="mb-3">
        <label for="amount" class="form-label"><?php echo e(__('Payment Amount')); ?></label>
        <input type="number" class="form-control" step="0.01" id="amount" name="payment_amount" placeholder="0" required>
    </div>

    <!-- Sender -->
    <div class="mb-3">
        <label for="recipient_from" class="form-label"><?php echo e(__('Recipient from')); ?></label>
        <select class="form-select" id="recipient_from" name="receipt_from_user_id" required>
            <option value=""><?php echo e(__('Select recipient')); ?></option>
            <?php if(count($users)>0): ?>
                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($key); ?>"><?php echo e($user); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        </select>
    </div>

    <!-- Purpose -->
    <div class="mb-3">
        <label for="purpose" class="form-label"><?php echo e(__('Purpose')); ?></label>
        <textarea class="form-control" id="purpose" name="purpose" placeholder="<?php echo e(__('Enter purpose')); ?>" required></textarea>
    </div>

    <!-- Payment Method -->
    <div class="mb-3">
        <label for="payment_method" class="form-label"><?php echo e(__('Payment Method')); ?></label>
        <select class="form-select" id="payment_method" name="payment_method" required>
            <option value="cash"><?php echo e(__('Cash')); ?></option>
            <option value="bank_transfer"><?php echo e(__('Bank Transfer')); ?></option>
            
        </select>
    </div>

    

    <!-- Submit Button -->
    <div class="mb-3">
        <button type="submit" class="btn btn-primary"><?php echo e(__('Send')); ?></button>
    </div>
</form>
<?php /**PATH C:\laragon\www\تشليح ملفات\erpq24\resources\views/voucher/receipt/create.blade.php ENDPATH**/ ?>