{"__meta": {"id": "Xb8786a733a877b0ffa8ae6f45f951bcf", "datetime": "2025-07-14 14:40:11", "utime": **********.162587, "method": "GET", "uri": "/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 259, "messages": [{"message": "[14:40:11] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036373, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036557, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036632, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036703, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036769, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036835, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036901, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036965, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037031, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037102, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037165, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037229, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037298, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037361, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037424, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037489, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037553, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037616, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037684, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037749, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037819, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037891, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037956, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038019, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038084, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038152, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038216, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038282, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038402, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038502, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.0386, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038682, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038757, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038829, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038895, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038962, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039031, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039097, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039166, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039231, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039295, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039362, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039428, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039491, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039565, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039633, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039698, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039761, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039832, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039898, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039961, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040028, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040092, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040156, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 18.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040222, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040285, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 18.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040349, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 19.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040415, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040478, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 19.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040541, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 20.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040606, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04067, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 20.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040746, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 21.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040812, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040875, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 21.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040938, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 22.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041011, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041073, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 22.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041135, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 23.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.0412, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041262, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 23.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041324, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 24.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04139, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041452, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 24.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041514, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 25.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041583, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041646, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 25.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041708, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 26.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041774, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041843, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 26.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041909, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 27.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041976, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042038, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 27.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042101, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 28.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042175, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042238, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 28.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042301, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 29.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042367, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042431, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 29.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042493, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 30.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042559, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042623, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 30.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042686, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 31.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042752, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042816, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 31.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042878, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 32.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042944, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043007, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 32.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04307, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 33.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043136, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043199, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 33.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043263, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 34.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043392, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043463, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 34.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043528, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 35.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043594, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043658, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 35.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043722, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 36.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.043789, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044011, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 36.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044121, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 37.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.0442, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04427, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 37.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04435, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 38.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044423, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044487, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 38.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044551, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 39.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044618, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044682, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 39.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044746, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 40.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044813, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044877, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 40.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.044939, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 41.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045006, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045069, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 41.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045135, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 42.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045204, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045269, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 42.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045334, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 43.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.0454, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045463, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 43.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045526, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 44.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045593, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045658, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 44.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045722, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 45.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045789, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045862, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 45.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045928, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 46.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.045994, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046062, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 46.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046126, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 47.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046193, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046268, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 47.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046333, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 48.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.0464, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046464, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 48.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046528, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 49.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046595, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046658, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 49.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046722, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 50.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046789, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046852, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 50.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046915, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 51.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.046981, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047045, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 51.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047107, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 52.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047173, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047236, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 52.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047299, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 53.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047366, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04743, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 53.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047494, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 54.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04756, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047623, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 54.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047687, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 55.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047753, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047817, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 55.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047889, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 56.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.047955, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04802, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 56.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048085, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 57.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048151, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048215, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 57.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048287, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 58.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048353, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048416, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 58.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048479, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 59.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048545, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048609, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 59.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048672, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 60.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048739, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048804, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 60.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048869, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 61.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048935, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.048999, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 61.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049063, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 62.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04913, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049193, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 62.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049256, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 63.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049327, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04939, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 63.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049454, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 64.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04952, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049584, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 64.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049648, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 65.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049714, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049778, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 65.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049847, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 66.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049917, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.049982, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 66.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.050046, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 67.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.050111, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.050177, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 67.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.050242, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 68.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.050308, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050376, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050441, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050505, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050571, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050635, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.0507, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050767, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050831, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050895, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.050961, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051024, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051088, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051155, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051219, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051283, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051356, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.05142, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051483, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051549, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051612, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051676, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051742, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051806, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051878, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.051945, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052009, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052074, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052142, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052207, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052271, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052337, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052402, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052466, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052533, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052599, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052669, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.05274, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052806, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052871, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.052938, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053007, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053071, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053138, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053202, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053266, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053333, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053397, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053465, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053531, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053602, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053668, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053735, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.0538, "xdebug_link": null, "collector": "log"}, {"message": "[14:40:11] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.053864, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752504009.909363, "end": **********.16338, "duration": 1.2540168762207031, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1752504009.909363, "relative_start": 0, "end": **********.307908, "relative_end": **********.307908, "duration": 0.3985450267791748, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.307918, "relative_start": 0.39855504035949707, "end": **********.163384, "relative_end": 4.0531158447265625e-06, "duration": 0.8554658889770508, "duration_str": "855ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55466080, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x invoice.view", "param_count": null, "params": [], "start": **********.415631, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.phpinvoice.view", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Finvoice%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "invoice.view"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.075892, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.086385, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.126581, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.137978, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.140263, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.140686, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET invoice/{invoice}", "middleware": "web, verified, auth, XSS, revalidate", "as": "invoice.show", "controller": "App\\Http\\Controllers\\InvoiceController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=377\" onclick=\"\">app/Http/Controllers/InvoiceController.php:377-413</a>"}, "queries": {"nb_statements": 29, "nb_failed_statements": 0, "accumulated_duration": 0.02837, "accumulated_duration_str": "28.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3439531, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 7.155}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.354435, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 7.155, "width_percent": 1.974}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.372319, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 9.129, "width_percent": 2.644}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.37447, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 11.773, "width_percent": 2.326}, {"sql": "select * from `invoices` where `invoices`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.380409, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 14.099, "width_percent": 2.08}, {"sql": "select * from `credit_notes` where `credit_notes`.`invoice` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3835661, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 16.179, "width_percent": 1.093}, {"sql": "select * from `invoice_payments` where `invoice_payments`.`invoice_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3852541, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 17.272, "width_percent": 1.022}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.387072, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 18.294, "width_percent": 1.093}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3901281, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 19.387, "width_percent": 0.987}, {"sql": "select * from `invoice_attachments` where `invoice_attachments`.`invoice_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 387}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.391754, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:387", "source": "app/Http/Controllers/InvoiceController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=387", "ajax": false, "filename": "InvoiceController.php", "line": "387"}, "connection": "kdmkjkqknb", "start_percent": 20.374, "width_percent": 6.204}, {"sql": "select * from `invoice_payments` where `invoice_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 390}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.395246, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:390", "source": "app/Http/Controllers/InvoiceController.php:390", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=390", "ajax": false, "filename": "InvoiceController.php", "line": "390"}, "connection": "kdmkjkqknb", "start_percent": 26.577, "width_percent": 2.221}, {"sql": "select * from `customers` where `customers`.`id` = 8 and `customers`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 392}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.398171, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:392", "source": "app/Http/Controllers/InvoiceController.php:392", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=392", "ajax": false, "filename": "InvoiceController.php", "line": "392"}, "connection": "kdmkjkqknb", "start_percent": 28.798, "width_percent": 2.432}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 397}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.400043, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:397", "source": "app/Http/Controllers/InvoiceController.php:397", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=397", "ajax": false, "filename": "InvoiceController.php", "line": "397"}, "connection": "kdmkjkqknb", "start_percent": 31.23, "width_percent": 0.987}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 398}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4016821, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 32.217, "width_percent": 1.093}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'invoice' and `record_id` = 5", "type": "query", "params": [], "bindings": ["invoice", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 401}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.403286, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 33.31, "width_percent": 1.234}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'invoice'", "type": "query", "params": [], "bindings": ["15", "invoice"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 402}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.405027, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:402", "source": "app/Http/Controllers/InvoiceController.php:402", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=402", "ajax": false, "filename": "InvoiceController.php", "line": "402"}, "connection": "kdmkjkqknb", "start_percent": 34.544, "width_percent": 0.599}, {"sql": "select * from `credit_notes` where `invoice` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InvoiceController.php", "line": 404}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.40643, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:404", "source": "app/Http/Controllers/InvoiceController.php:404", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=404", "ajax": false, "filename": "InvoiceController.php", "line": "404"}, "connection": "kdmkjkqknb", "start_percent": 35.143, "width_percent": 0.67}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 99}, {"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Invoice.php", "line": 136}, {"index": 22, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 30}], "start": **********.026895, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 35.812, "width_percent": 4.688}, {"sql": "select * from `invoice_bank_transfers` where `invoice_bank_transfers`.`invoice_id` = 5 and `invoice_bank_transfers`.`invoice_id` is not null and `status` != 'Approved'", "type": "query", "params": [], "bindings": ["5", "Approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 726}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.062268, "duration": 0.00809, "duration_str": "8.09ms", "memory": 0, "memory_str": null, "filename": "invoice.view:726", "source": "view::invoice.view:726", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Finvoice%2Fview.blade.php&line=726", "ajax": false, "filename": "view.blade.php", "line": "726"}, "connection": "kdmkjkqknb", "start_percent": 40.501, "width_percent": 28.516}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "invoice.view", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/invoice/view.blade.php", "line": 728}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.071474, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 69.017, "width_percent": 4.406}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.076409, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 73.423, "width_percent": 3.419}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.080693, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 76.842, "width_percent": 2.432}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.092668, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 79.274, "width_percent": 2.221}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0953472, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 81.495, "width_percent": 1.551}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.127067, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 83.045, "width_percent": 3.595}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.129284, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 86.641, "width_percent": 10.046}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.133873, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 96.687, "width_percent": 1.057}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1358361, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 97.744, "width_percent": 1.128}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 6214}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.138387, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 98.872, "width_percent": 1.128}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\InvoiceProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=1", "ajax": false, "filename": "InvoiceProduct.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 78, "messages": [{"message": "[ability => show invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-446226098 data-indent-pad=\"  \"><span class=sf-dump-note>show invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">show invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446226098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.37901, "xdebug_link": null}, {"message": "[ability => send invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2005796716 data-indent-pad=\"  \"><span class=sf-dump-note>send invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">send invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005796716\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.030364, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-520822188 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520822188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.030675, "xdebug_link": null}, {"message": "[\n  ability => create payment invoice,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1286622598 data-indent-pad=\"  \"><span class=sf-dump-note>create payment invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">create payment invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286622598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.031032, "xdebug_link": null}, {"message": "[ability => show invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1116710183 data-indent-pad=\"  \"><span class=sf-dump-note>show invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">show invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116710183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.03144, "xdebug_link": null}, {"message": "[\n  ability => delete payment invoice,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1206154322 data-indent-pad=\"  \"><span class=sf-dump-note>delete payment invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">delete payment invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206154322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.061738, "xdebug_link": null}, {"message": "[ability => edit credit note, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1343428061 data-indent-pad=\"  \"><span class=sf-dump-note>edit credit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">edit credit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343428061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.074357, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2084224948 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2084224948\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.074606, "xdebug_link": null}, {"message": "[ability => edit invoice, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1308490436 data-indent-pad=\"  \"><span class=sf-dump-note>edit invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308490436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.075146, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-348725443 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348725443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.097762, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098117, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098338, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098497, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098777, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1561494196 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561494196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.099063, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-453041813 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453041813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.099325, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1860275163 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860275163\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.099636, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1134750769 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134750769\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.099891, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-73538610 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73538610\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100158, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1136012926 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136012926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100399, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1380252739 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380252739\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100644, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1249516791 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249516791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100888, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101197, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1939077945 data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939077945\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101568, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1009510000 data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009510000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.10177, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1208735016 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208735016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102389, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1209857812 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209857812\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102622, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1481309873 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481309873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103527, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-277674080 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277674080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104036, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-509482786 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509482786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.10447, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-244921271 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244921271\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104916, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-167950458 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167950458\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105533, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1017526938 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017526938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106107, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1436224998 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436224998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106696, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1173150310 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173150310\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.107295, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-668419961 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-668419961\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.107789, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1165630193 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165630193\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.108271, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2004216330 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004216330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.108831, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.109321, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.109815, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110357, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1851723586 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851723586\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.111038, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-834026324 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834026324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.111577, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-553391838 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553391838\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.11209, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2005782082 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005782082\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.112674, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-381249273 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381249273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.113242, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1428301922 data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428301922\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.113638, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.114088, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.114359, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.114576, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.114803, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-462387254 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462387254\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115001, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-801578673 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-801578673\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115283, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-404572140 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404572140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115534, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115738, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116079, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116696, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116937, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117134, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1466222461 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466222461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117311, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1261670077 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261670077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117462, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-558794776 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558794776\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.11762, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1773345905 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773345905\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117934, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2067311992 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067311992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.118166, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-921533430 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921533430\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.118372, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1873435406 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873435406\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.118588, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-681151751 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681151751\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119294, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-287889469 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287889469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119981, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1158782546 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158782546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.120718, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-519050471 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-519050471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.121435, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1341356618 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341356618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122188, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-25408668 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25408668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122889, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-409292718 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409292718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123711, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2077310848 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077310848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.124438, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1986597991 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986597991\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125145, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125321, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125968, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1367608861 data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367608861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.126287, "xdebug_link": null}]}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-995067147 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-995067147\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1052530213 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1052530213\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1252817420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1252817420\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-870429889 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"226 characters\">http://localhost/customer/eyJpdiI6Ik5RSndNZWIwZm1YVHN3UlpLcWZtY1E9PSIsInZhbHVlIjoieG9wQnQ0N01rbU0wUWhRREFEK2NrUT09IiwibWFjIjoiZjQxZDQ5NjhiZTAzZmVlYTFmNWM4Zjc0Y2Q5YTY3ZDMwOTAyNmNjNzQ4MGU0YTQzZTY3YTdlNWViMDY0NmRjMiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503997295%7C20%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktRZU5BaTJodGo4WGVQTFlCbUhsWFE9PSIsInZhbHVlIjoidWpvUlowV2FxODUvYzFJa2pLOCszdDlNSjdqRDJmd2xZNUtpTW5RYzJ6aUhKZ1ZUYkdXNm42cGdtOG0wSGczZlZvQlZocVd3SU45TFJzeVFUQklOeDF3Y1VtMkVrSVNpcnpsUkJhUmVqSWE1TEQ2SVQvV0F3ejkyanUvY24yclNadTZuYUhDNG5CVFlSd2VxQk1VY1ZGZ2cwOWF1dmkydThYUlRsK0ZZVER2NUhIdzAvb25nbDNBT1ZpbnBSZ3dhdzh1Nms1RjgxK2VnVFlzUlVkeFFYNitvS2lYZWRPMzRpMEFBcnAzOTlQMDlDTFlqVkMvOVl5cStiYWxhN0JhUjVoRENydExqdVo0NWxrN1FYaWNYTkt3bVlROWhMZDZpckVYZit5cWpMMUVEWmVDbDVjUi9BTEF2bW1iemhOME5aQWhwb29WS3VWZFdzL0FHMVRjWFU0UTNBUG44SHBra0RxcFpZbnFCQnZsSG0wNXZ5aWtiRzhPTnBrUGdrSk5pNUlhNlRMeVBDdjhTWS9sMVRhZFVKenJ0UUVFTFA2Y3NNbk42VXNwaXViVlpUQVpHNWtpOEFRaHdVU0VIWlpoWkVGQlhpTkl3N2lKalM4V042UDBJaThvYkw4ZTFzRUh1TTNwWFIvR1dBT2d3UStwMjFzaFNqczh3RVRHVHRNMmciLCJtYWMiOiI4NmE3MjQ2ZmZjYWYzNzEyZDkxNTM4YTkwNjZlYzk1ZjBiOTZmYjEyODNjMzMzNzk3ZTAzNGEyZmI1ZDNhZDkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpOb1A5b2d2U2VUSEV2M3o3eEFzY0E9PSIsInZhbHVlIjoiMzI2bDdWeFRnK3ZlbGNQaWFKejJSZm81UHF4SXdoTXo0TkZ6VjRJVWlWN3F5bDJXUzNQZ25Tbk94ZlJjSVZtc0U3WWYyZGRKYXpPTFBqRmk3QTBxNml3NUgzbjNFdDFudFZJUldWVnZUSnNyQkFhdnc0ak1UTU9ERXpNUVlldmpQeXNDaUwzcDVCOGFpWk4xU25Pd2tzeWtzUUFBdTFJdElaam5TTk9Cc1plZG9vMnJZcU5IeVdzN3I2SzhWbXAvcG5FaCtTODlqMDBNemQ0OWIrM2ZSeDdETTJpSEVBbzJUOFhkRUtRTjFTU3JBMUFlc1lMYmsyNHNMcFlTdHVIa2xIbFBlM2FQcUtRWDcrcU1PYUZzVWlXRVBTcDVVMUVVRW1xQnhsWitXVUw4MkU2cU13TE9BcTNlZEMrdmtBa1Q5aUF4VVFCY1B2MjAyZm94dm9OQklYREFpcW9aUVo2L1pZYi8xMk15RzUrWXI3LzkwcTZ1ZnRxM281Y2REbnByVDFPRkQ2eGJaUXBPT1ZIdFFXU2U2emVNMDFJb3JwYUlFVXcwQlRKNFZQckdaMjJBdi9EL0dxM0U5djIvMkM2cjdEaWk5Y1hqMHNnS2FKS0JNREpkQ1NGSUZDcWZ0eW9FeHNieFZ3NkhlM3BrQjJDaEk3eFV1RTExTmxrZFp1QWciLCJtYWMiOiI4MzRiMDRlZjcxZDFiY2EyMjQxMDhlMGFmNTE0ZWNiOTRiOTcxZGQ0ZmU4ZmYyNGFmYWZjZmU0ZjQzNzZmMjVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870429889\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-435484538 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435484538\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1409109896 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:40:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlluczdDOG5zQzVMenVibEhrNmZ0UlE9PSIsInZhbHVlIjoibzBkOVB0N3NHSmVZVWZpYXJrd3VxVmVtQXpqZ1RQWjVVUWl4RjkyaDdnbWtEQ0dwZzUwK0FvVDdxSkpCZGQ4WUltcFRJeUE5a1NYbEhYWGdCSGdWZGx0bjh0WHI4dStpam5WVEt3YUdRV0hTK0NZN3hqakJ5clA1Tk54VDF0M0tZVkJmWnpkRjVKeFp3NzhWSWx2NXlsb3lEeDArSXNNYVRIR1I4cWZLNzhxakM0YjFlaEp6UUgzRjRKWjB5NVMwRnZKUXg1UkFYOS80eHprQm1TNkU4TlljZlF6K0w4RE1iSTdUQVpjbUQzdU82RnhRMFNCVVd4L2oyT0YwQ1N2RG1WQ2t6dkxycC9JZkFKTVh5RUZoMytVeE40QjBRTnVMNHEwVlYrdGRoQjdWUytweEprUFdzdFh2L0lybDNIN2lDZk0xQlA5OUtxQTVOMkw5SFpmRys1TkM0TzB5bjdNZDYraTE3WWFlNDh2ZFpzUzdSTXdVMjV0T3lRWGtZNlJONkZ4QnlOQlJ4dmJVMkd1bEJOTllpYnAxN05KdFpPcWxJWkg4L2lmV3dWZnM0dkp6OU02d3JJbkNVMWYwUVZtaVVkVjZ6czJ2RGkwVTBRcHBGRFU0TWN3bHI4MEw4OGFBVnFVaWEyaW5FSmNoUVNpWGZzQzQzNUMvL29LdkROWUIiLCJtYWMiOiJjYjgyZGExY2MxMzQwMGMzOWZkOGFmMTg4ZjkyZTRiMGNlN2Y5OTZhY2EwMjkwMzc4ZTA1YmZiYTFlODJmMzVlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InQ1d2NRTjV5Yi9RelF4VnpLTFJmSEE9PSIsInZhbHVlIjoiQjJQZzl5aHZTTk1RWlZVd3lRVEtRenZIN1UzVXB2dVYremRWN1pnRXFLQ1lDbW1ZNWVDVkkxRDNROXVCSThoSVpkMHJ4VXhBcWpzUi9NS2RkSzlUUFR6WkFNOVJ1MWNSdUZFdkVqOWhVWFlsbktwWWUyTW53WmpqVWpaUHdHWGZkeFZVaEQ0TUhIUU5KaUZOcGVxbEFCaFo1VERzZ3BjRWRLTEVJVTAxZ1pyc3prU2JuS1U1enlhZ1BZUHYwU0habHZOekdNTHVMVUQ2ems3Q3ZhTVQ4WjB0YktqYnJ4WW04OGlualJjT2lLTERDczFQQ2E0SDBRbGd2MlNxaXIxcndVUDh2SFMwVys0YUxUZ29oS001SWU0VzVadFJBV0pHNVZkY1RyYkRMTnFpUG5SWHNpNnFtVndkZlFTVElqdjVVckdXbFNOaXIwT25JS3Bub2JTZnJTZU9iYWFWcDBCT3VJRGV4YW5BRGFwMHA1b1hwUlNCQjVGK3BIVVI1Vmx2bTQ2ZXZaTHdzZWlmUFFxSGUvdzZNYUd0WWRsZDdtTnhzWkhCelJwK0xYeEczSzhISUVlZm9GV0ZtNmI4UU8yYTNocGhyNVljamZCck5SSHJFcnpvSFBNQWYzMmN0M2VTQ0tScFpDQ005TVpmcUNnb1ZqQ2UrWEVKNG5Nb1lHVUUiLCJtYWMiOiIwOWIxNzM2NTI2MjU1OWY5NjFkNjAxOTczMWU5NTI5ZTZmY2ZjNDY1ZDMxNTY4MjBmYzhkNzZkMzY5MmNiNDFiIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlluczdDOG5zQzVMenVibEhrNmZ0UlE9PSIsInZhbHVlIjoibzBkOVB0N3NHSmVZVWZpYXJrd3VxVmVtQXpqZ1RQWjVVUWl4RjkyaDdnbWtEQ0dwZzUwK0FvVDdxSkpCZGQ4WUltcFRJeUE5a1NYbEhYWGdCSGdWZGx0bjh0WHI4dStpam5WVEt3YUdRV0hTK0NZN3hqakJ5clA1Tk54VDF0M0tZVkJmWnpkRjVKeFp3NzhWSWx2NXlsb3lEeDArSXNNYVRIR1I4cWZLNzhxakM0YjFlaEp6UUgzRjRKWjB5NVMwRnZKUXg1UkFYOS80eHprQm1TNkU4TlljZlF6K0w4RE1iSTdUQVpjbUQzdU82RnhRMFNCVVd4L2oyT0YwQ1N2RG1WQ2t6dkxycC9JZkFKTVh5RUZoMytVeE40QjBRTnVMNHEwVlYrdGRoQjdWUytweEprUFdzdFh2L0lybDNIN2lDZk0xQlA5OUtxQTVOMkw5SFpmRys1TkM0TzB5bjdNZDYraTE3WWFlNDh2ZFpzUzdSTXdVMjV0T3lRWGtZNlJONkZ4QnlOQlJ4dmJVMkd1bEJOTllpYnAxN05KdFpPcWxJWkg4L2lmV3dWZnM0dkp6OU02d3JJbkNVMWYwUVZtaVVkVjZ6czJ2RGkwVTBRcHBGRFU0TWN3bHI4MEw4OGFBVnFVaWEyaW5FSmNoUVNpWGZzQzQzNUMvL29LdkROWUIiLCJtYWMiOiJjYjgyZGExY2MxMzQwMGMzOWZkOGFmMTg4ZjkyZTRiMGNlN2Y5OTZhY2EwMjkwMzc4ZTA1YmZiYTFlODJmMzVlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InQ1d2NRTjV5Yi9RelF4VnpLTFJmSEE9PSIsInZhbHVlIjoiQjJQZzl5aHZTTk1RWlZVd3lRVEtRenZIN1UzVXB2dVYremRWN1pnRXFLQ1lDbW1ZNWVDVkkxRDNROXVCSThoSVpkMHJ4VXhBcWpzUi9NS2RkSzlUUFR6WkFNOVJ1MWNSdUZFdkVqOWhVWFlsbktwWWUyTW53WmpqVWpaUHdHWGZkeFZVaEQ0TUhIUU5KaUZOcGVxbEFCaFo1VERzZ3BjRWRLTEVJVTAxZ1pyc3prU2JuS1U1enlhZ1BZUHYwU0habHZOekdNTHVMVUQ2ems3Q3ZhTVQ4WjB0YktqYnJ4WW04OGlualJjT2lLTERDczFQQ2E0SDBRbGd2MlNxaXIxcndVUDh2SFMwVys0YUxUZ29oS001SWU0VzVadFJBV0pHNVZkY1RyYkRMTnFpUG5SWHNpNnFtVndkZlFTVElqdjVVckdXbFNOaXIwT25JS3Bub2JTZnJTZU9iYWFWcDBCT3VJRGV4YW5BRGFwMHA1b1hwUlNCQjVGK3BIVVI1Vmx2bTQ2ZXZaTHdzZWlmUFFxSGUvdzZNYUd0WWRsZDdtTnhzWkhCelJwK0xYeEczSzhISUVlZm9GV0ZtNmI4UU8yYTNocGhyNVljamZCck5SSHJFcnpvSFBNQWYzMmN0M2VTQ0tScFpDQ005TVpmcUNnb1ZqQ2UrWEVKNG5Nb1lHVUUiLCJtYWMiOiIwOWIxNzM2NTI2MjU1OWY5NjFkNjAxOTczMWU5NTI5ZTZmY2ZjNDY1ZDMxNTY4MjBmYzhkNzZkMzY5MmNiNDFiIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409109896\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1481378554 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IjltSjdDUi9yRVY3eE9RR2JDRkF6L2c9PSIsInZhbHVlIjoiczc0ZUZFR1diYUJBWUNRYmYvdTU1dz09IiwibWFjIjoiOGJkMTM5NDRjNmI3NjFhZDZmMmEzNzNkNTc4OTFjZDBiYjBmMjk1MWY5OTRmMDY1ZDYwY2MyMDIzNDRiNzk5OCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481378554\", {\"maxDepth\":0})</script>\n"}}