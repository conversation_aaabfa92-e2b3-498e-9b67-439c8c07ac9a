{"__meta": {"id": "X4f82cfa92bdbe672d3c712975b9a76a2", "datetime": "2025-07-14 18:17:47", "utime": **********.756107, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.287252, "end": **********.756122, "duration": 0.4688701629638672, "duration_str": "469ms", "measures": [{"label": "Booting", "start": **********.287252, "relative_start": 0, "end": **********.696063, "relative_end": **********.696063, "duration": 0.408811092376709, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.696072, "relative_start": 0.40882015228271484, "end": **********.756124, "relative_end": 1.9073486328125e-06, "duration": 0.060051918029785156, "duration_str": "60.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036200000000000004, "accumulated_duration_str": "3.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.730794, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.757}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.742159, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.757, "width_percent": 11.05}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7482, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.807, "width_percent": 15.193}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-206573343 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-206573343\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1392136130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1392136130\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-897395627 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897395627\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1525556164 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"225 characters\">http://localhost/invoice/eyJpdiI6IkM2cGlaZ2pTc3F0NTR5QXNsQkdERlE9PSIsInZhbHVlIjoidlhZcjVsSWNDYks1cElNeSt2K0Ftdz09IiwibWFjIjoiNjBkOTY5NzJiODI0MzdmNzBhNzhmM2Q0ZmE5YjQxM2JmYzE2Yzg4ZWQ5MWI4ZTI4YTRhYzQzZGM5ZTJlNjhiNyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517065368%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNnd0tBTU1qcnZhUjJKMjNoNkp5WkE9PSIsInZhbHVlIjoiNGwyS2J3ZVdlWUsxa1FLU3dpVnpVYjAvZWp6LzA0aTNaai8vMzBiSWk2cmV5S0trNm1XS2dxbWdVdXg5Z2NRRXMyS1BLeEh4ZVNoOFRwbEY2WjA4ZkRWK0MxQ2pQNDRPTzNEYVBlRFNJRGhtM05rUEdjMnE5SjY2MURTb3o0VVdsOVA2Z0VRMFFMd3lHQTVaVG5YcG40SE5SVFpXazQvd2srejc3U0Y0clc0OGVIRmFEWUxDb3NidmdGU1lDaGdyOGNnTVg5MnZ3ajh1clhNYjBCZzdJVCt0eXJ5OG1kUEJlZU8rT1BvVVMyS1Z0VFgxcFM1UmRXWmtZS0duWk12QmNzV3EvM2lqUzIxM25tVzRuZmIyTktNVWxsSmFaL2s3MUxxcDIvUW05RHl0eVFWZHFXY2RsMVJ6U2gxd0RLdUFSeUJuQ0UxRk1CenpyKzl2STF4K2pKa0dNTW5VRWlCOEZQNkdtbDRmNXBxYUc2UnFGSWNxOWwzbVEycDV1MWZHeVl0NXpDOVZuMmUwOVRDKyt5eWhIeG4zVEliMXBpeWZ2Uk1neUxoUUthR0RYL1RFK2xBSWZteDNmT3JjK3I1MXhic0IxcnFIWHhjQ0JmNHFFdFkyaGtZNTh6akN5U01SMUdjeXVlTTh5NVd0RDBZUW1jaDVWR0N2RUJKRW5BNW0iLCJtYWMiOiIyODc1ZGVmZDgzYzc3YzE4MTA1NDBmMTJkNWEyM2I3YjdiY2NjNzE0YWI4NmUxNjgwZDFmMzgyNjgwYTQ4MGU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdvZHRmSThxemthcENYc1lnOXNKSHc9PSIsInZhbHVlIjoiMVU4UndEbWhJVENEdE1Oclp4dlI3VlhhNU4zOWd0MzBCZmtNL3NyZUhaUG1nVnE3NXpHMXZVWTUvUU5NUm1Ia1BySEVQam5tTDlsRmh1UjV2REpPK2M0MFlDaFIrbm9Ic2VTZXFuczk4OElXMFl5Tyt6K01GMUo1b2NleUtNZnFlenJIaWtxdnhPQ1FteXE4R3pQdWVSellxckNkWmZyYlNDZ1ZKUlNaWnlKQzdWNyt5OFVnRTRMQ2dWYVNrTlk4WnZWR0J2MThxVnN2M3pRZ01ka2lZaHdBSXNxTTZBUDdIaHNBaE5GRHRDeUxSeUJaand4aXBlKy9KSDRESjFYVjBwMXhlc0x2bXcycFNlMS9JQjR0TFA3ektHcUd5NWhEU2lIR05nNjBwTDZkSmphYTBjU09jTTFzQThtTTJCSnVyQmJDUkVIZXYxZUZ4ZkFORHE2NnpYZkVmdldLRWN0Zyt0SXFPQy8wSzk3V3ppNFRiWHdsS2hkUVN0Vms3QjlGUGNlcXNtanFMQUJWVXBJdDdXejJ0S1A3YXZURlNQdmxxeTJ0WXdDaFdUN2ZsRjJDNDkzNkNwdk1kVmNXbW96Y2NHVGc3UTIrS05WYk9IRGpGazRORi9ZMXNvcU1tbi9nNXBlaEhla3NpcSt6NWtmVUZiWWN1S0tGNTZUajNLMU0iLCJtYWMiOiIzZWViMTNmYmVmZGM0YWZhNGQ3ZjQ2ZTVkMjgwNjVhMDE5NDIwMjI4YWUzMmRiNDUxYWY4ZTc0MWRlNzkzMmE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525556164\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-608790087 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-608790087\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2067921242 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:17:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRFUlYyVkY5L1lmK2t1RzZxTTFGTUE9PSIsInZhbHVlIjoiK01lbnVYT2JROVVIYnQ1b281UnpRMlB5VStQMHVvcGRvb1kwQUJ6bGhUUDJFaDlVYlpCcS9KZ0YzSktYT2RlQllGclVNNytPVUNqbWFYYmd1dit2NkhTUExTVjFRc3lzeXpvcHBHajk1b3lEY3gvL1BmekYzbXl6Y0tZRDhwamJoTUVsMUhiT3FiamlOR3M5ak1sSTFLZW5qcmZzY2NSN1BBZ2lUeWYveDVEZjRGbTR4a0llYkZaMWM0M01MYTR1U1dNdk5YOFRZL0pXNUI5N1ZwNjRXbnVsUWR3WTY1KzdXZ2Vzdy9QU3NhV1plUkNBc21RQnZFMVZBeDhzaDNKQktkSVhxQTNpUkcrdSszL2wwbit0cGs4dUtsSVRxUnVkcWdjR3ZnWnRhZHphU0krdy9DeTB0NHhVYzBJaGZDR3hQWUdmQ2VkZkliRzBicDNqQlJwUzUrZkRRTlYxWWhyYTduR28vZDlreHFsL3QrVFIxZkpGMUJlVmtTY3ZkbFRoazBKTm5PL0xqeXNBeURJcEYxa2FvKzRFdFZJck93N3JydTVrcllTQVU0aGxhN2lMQmd3T2dOOHYvUWtnVXR1S0toblBFTmhlQWVnT3N6UjEzYnpUdVRMNEVUMnhNNTFsazk2VEFpWUxxMUZxMVlFRkVIM3VOQm1aTXhVT1lJNEoiLCJtYWMiOiI1ZjExMGJhYzcxMTA1OWRkZTczZjdmMzIyY2M2YTZmOTM2YjMwNDlkMDY0ZmRiMzkyODEyMmE3MGI1ZTQ2YTUyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imozc3JSZ00wb2dkVlFKblJhSVJ6OFE9PSIsInZhbHVlIjoiTWpmYXV6NVM5Qnc4YWhHdkJMTlRYSFFhRGVRYVBnT0gyRDBEZXdDRS9KM09mSUd1cTJNMmlXRlQ2Wk1HWEJpelJpUWNoNkdjbWNkV2l5Y0V2OVdvR29lWUhtVzBiQXNReUF3WUJoam1BZVZ4SWdHS0RlQWlDSjNFbW1QU3VmTDZFbEY1bTNsMGJVQ3F1Q2liVk1FNTJpRjRhY0tML2thd1EzbnpMa0RnVGxYb3JRdlFLZ0EySld3UXlJMG43K3VpTWgrUjJKamFyTFpkaUNLRFdaNTZLQWE3aDRaN3paZUI1VDd0cm5aTklaRmlQMXZrSHV0Njc4MldvdWtZaVU0b3B2YVNGRXlBZ3lORG1jKzJKVnB1cUNZOXF5SGNpeTZUdGVPUXFLaS83dUxodWtEV0Q4Z3lERDNhU3FQeFRkMVNPSFJucUNJRlJ0UTNNdXlqdWcvM1l0ZlRJTTk2Y0Vvb2ZiaFFSTWk3bUdPa290aE9zeUlBaXRrdkxmUjh4ZHBhSlNnMHpEdXlvbXFrbzZGMmxydWRBaFhjalJUYUlXN3Ezb2hGZlhITlUzVlZKVHJRZ3VjenNjNWpLN1NIcFU4REpMWi9jMUcyL2dkOW1qbFVSVnF5NDE0c0VtQmUrWnFMZ1ZSeWRCOVRSM2Q3dWlwRzFyWTEwcVZmUTRRTDFYNk8iLCJtYWMiOiI3NzUyYzFkNjVjZmI0NzYxMDgzM2Q4NWRmYWUzYmQ4YzFhMzQxOWIxMGVmNDU3ZWM2ZGY2OWFkMjA0NmQ0NWJlIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:17:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRFUlYyVkY5L1lmK2t1RzZxTTFGTUE9PSIsInZhbHVlIjoiK01lbnVYT2JROVVIYnQ1b281UnpRMlB5VStQMHVvcGRvb1kwQUJ6bGhUUDJFaDlVYlpCcS9KZ0YzSktYT2RlQllGclVNNytPVUNqbWFYYmd1dit2NkhTUExTVjFRc3lzeXpvcHBHajk1b3lEY3gvL1BmekYzbXl6Y0tZRDhwamJoTUVsMUhiT3FiamlOR3M5ak1sSTFLZW5qcmZzY2NSN1BBZ2lUeWYveDVEZjRGbTR4a0llYkZaMWM0M01MYTR1U1dNdk5YOFRZL0pXNUI5N1ZwNjRXbnVsUWR3WTY1KzdXZ2Vzdy9QU3NhV1plUkNBc21RQnZFMVZBeDhzaDNKQktkSVhxQTNpUkcrdSszL2wwbit0cGs4dUtsSVRxUnVkcWdjR3ZnWnRhZHphU0krdy9DeTB0NHhVYzBJaGZDR3hQWUdmQ2VkZkliRzBicDNqQlJwUzUrZkRRTlYxWWhyYTduR28vZDlreHFsL3QrVFIxZkpGMUJlVmtTY3ZkbFRoazBKTm5PL0xqeXNBeURJcEYxa2FvKzRFdFZJck93N3JydTVrcllTQVU0aGxhN2lMQmd3T2dOOHYvUWtnVXR1S0toblBFTmhlQWVnT3N6UjEzYnpUdVRMNEVUMnhNNTFsazk2VEFpWUxxMUZxMVlFRkVIM3VOQm1aTXhVT1lJNEoiLCJtYWMiOiI1ZjExMGJhYzcxMTA1OWRkZTczZjdmMzIyY2M2YTZmOTM2YjMwNDlkMDY0ZmRiMzkyODEyMmE3MGI1ZTQ2YTUyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imozc3JSZ00wb2dkVlFKblJhSVJ6OFE9PSIsInZhbHVlIjoiTWpmYXV6NVM5Qnc4YWhHdkJMTlRYSFFhRGVRYVBnT0gyRDBEZXdDRS9KM09mSUd1cTJNMmlXRlQ2Wk1HWEJpelJpUWNoNkdjbWNkV2l5Y0V2OVdvR29lWUhtVzBiQXNReUF3WUJoam1BZVZ4SWdHS0RlQWlDSjNFbW1QU3VmTDZFbEY1bTNsMGJVQ3F1Q2liVk1FNTJpRjRhY0tML2thd1EzbnpMa0RnVGxYb3JRdlFLZ0EySld3UXlJMG43K3VpTWgrUjJKamFyTFpkaUNLRFdaNTZLQWE3aDRaN3paZUI1VDd0cm5aTklaRmlQMXZrSHV0Njc4MldvdWtZaVU0b3B2YVNGRXlBZ3lORG1jKzJKVnB1cUNZOXF5SGNpeTZUdGVPUXFLaS83dUxodWtEV0Q4Z3lERDNhU3FQeFRkMVNPSFJucUNJRlJ0UTNNdXlqdWcvM1l0ZlRJTTk2Y0Vvb2ZiaFFSTWk3bUdPa290aE9zeUlBaXRrdkxmUjh4ZHBhSlNnMHpEdXlvbXFrbzZGMmxydWRBaFhjalJUYUlXN3Ezb2hGZlhITlUzVlZKVHJRZ3VjenNjNWpLN1NIcFU4REpMWi9jMUcyL2dkOW1qbFVSVnF5NDE0c0VtQmUrWnFMZ1ZSeWRCOVRSM2Q3dWlwRzFyWTEwcVZmUTRRTDFYNk8iLCJtYWMiOiI3NzUyYzFkNjVjZmI0NzYxMDgzM2Q4NWRmYWUzYmQ4YzFhMzQxOWIxMGVmNDU3ZWM2ZGY2OWFkMjA0NmQ0NWJlIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:17:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067921242\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-956349866 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"230 characters\">http://localhost/invoice/eyJpdiI6IjRLVWtzR29NRHpDeWlrREVFRGQ0NWc9PSIsInZhbHVlIjoiMG9jUlo5OGpXRmNxS29FL2FCdjArZz09IiwibWFjIjoiOTEwMjMyN2U4NmE5NjI0OGVjMTk4YTUzOTFmNGMzNTE3M2E1ZGU5YzM4YTc2OTA4Yzg2YWRhNGE1YzE5NjMyZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956349866\", {\"maxDepth\":0})</script>\n"}}