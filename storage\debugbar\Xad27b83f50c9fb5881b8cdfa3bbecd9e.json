{"__meta": {"id": "Xad27b83f50c9fb5881b8cdfa3bbecd9e", "datetime": "2025-07-23 18:22:59", "utime": **********.899741, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.480744, "end": **********.899761, "duration": 0.41901707649230957, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.480744, "relative_start": 0, "end": **********.845158, "relative_end": **********.845158, "duration": 0.3644142150878906, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.845166, "relative_start": 0.364422082901001, "end": **********.899763, "relative_end": 2.1457672119140625e-06, "duration": 0.05459713935852051, "duration_str": "54.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45992840, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00273, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.873331, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.271}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.884687, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.271, "width_percent": 21.245}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8912792, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.516, "width_percent": 16.484}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-803861194 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-803861194\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1394658340 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1394658340\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1048780345 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048780345\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2101629018 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294975464%7C5%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAyYW9TOG05ZEw0MTJsNGo4NVFNNGc9PSIsInZhbHVlIjoicWR5Z2xsbXR6RHljV3c1Q0dYbmNsQXVRSnErMWNFSXJSOThlWVBWVUdURWx1WHAyTkJTUkNrOHgxZGFuVFNzMk1kYUpKeWhCdFRHUzVaSncyMkgvQTc1UzJTZ2U5aTdKTmlvd2JFMzRubnBxbFhXdDFnL2RLa0U1ZlArbUcxalpoOXAyNGhjNUJhR2hmdDJubkdDM3NQemNIS1ZLb25CcnNwUnQrOWdlc3RPdkY3R3ZTeFRsZk5NNGVITHhrREErN0NCWktwUnlQeEhtNnZuUzRpN1BhSld6TzVNbEtOcmRYV3lnWTJCY0RrOTFhUGNvS21jOUVaWTlscUIrUzFhUjl3c0llenN6UlJwWHpCdi9ackxQazc4SUJieFpHVVp6TEJLOTN0R3phYS9yTzRLODg2NlZvYm03bHRrSmRYeTZBeU9VczZ4NDVSanRYelBKcjNJaTZtNU9jUWhQdXRRSnFGUjIrYkhGQXIwU3lyTXZDWk9lYjlSZU04NUFqZlh1dS9ScDVsaWNrVDB2Y0E3V1ZTN0tpWkQ0OFFTYUdBVjAyU29rU2hOTVRVZi9sOXYva0M5WTVZY0RLUEpJajhxeXRleXcvYjRNZTVRSmRoLzVwL29xNW9mL2FFUUU5MnMzakl0VWpTcnVWM3Bnb094OVc1NnJpZDNnNk5BREtGMUkiLCJtYWMiOiJlN2MyZDgzOWRmYTg3Yjk5OGU3Zjc5MGM2NTBjZDUwYTNmM2UzYTk1NjE3MTM3N2Q3ZDAxMmEzYzgyYTRmMmZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldEbDhVT3RYTmpqUWNlS1VzbWJiZ0E9PSIsInZhbHVlIjoiMWRaL2JVdC9KSUljR1VFQ01uS1dIcllxL2NKMXp3Vk1QTDcvK2dWeDVoSExYOEdEeXE2SkdjNTVldTdTbkdXYjduYjJadDBzYVo3QmFscUdEaG1lTWY5T2pJMkhkS1pyUlYwOFg4ZUkxbFM1M21NN3ZHUnZGNDF1TThVNHVDVE9aakoweHNkMmlLcFh4VERxbnoyRFlVWkpJeEhNVFFDTE1Za0FpK1NYOHp2ZnhWOEZoSjRNcTcvYkc4ckNDa1RWSXNpYWFkOGtjTTBobEc2SHBoU05XTzZ4R1o5emw0SjhNYzNLbnJjVHZ1MlhzdjZSak81UVJBY29pNXFNS2Z5U2tpTU91ZjNkOHJGMkk3QnEzc3NuaHk4eEJsbk9yUnkrVnFnUU1xOHVlU1Vlb2F3SWRjTUJZeEF2NlZnSDZ3TVVVR0s0clMrSTNob0pKR01rRWNOWU1scXMzb20rUEdiS2w5WWNZREhBMHJ3eHBXVS91aWtOVjNYNkpwbG9raUttNDNPY2JDMStpRkcvQzNtTXlJOWsydGtjYkg4d2lzNFE1cWc1R3BFSXJRbmV5TWtRb3lqZXlJbEtvYnVUdlZyY0lBOWFNNnBwMlMxbEdxRmg3d294MTFBRmtzeHdUK1lmbkRZaXdDbmpJUkdObnd4dnk2WkVHMEk1djZ5aVpzTWQiLCJtYWMiOiI3NDUxNTEwYjMwNzUxNWFkOTFkNjFkNTY2YzAwNjU1NmE1ZjdlN2VhMTMwZDdhZmJkOTcxYzFiMzBhOGJhYzI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101629018\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1070601823 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xWPGJpNWbmDLkwLBtG0MdpEYkMbvhJtQ6DvLx5Cj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070601823\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1611989429 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtNSXdaYWlmL3VqbW9KTGFJMHROYlE9PSIsInZhbHVlIjoiVEZLaTU1elNTaXZMWjJHMTlxRUVaN1ZxdEFkc090WTJJdFZ5bStqTWlpdXpueU0yNTA5V1VkbUl6K0NIajhBSzIrTFpkRjgwZzZ4NVRQcmpjd1B3ZExWOW5heW9UVFlod2JXSmNmY29vRklEbnVEQ0dnUWg3TkdGSjk5bXo2aXh1Y21sK1ZLSXZIVXlGYmt5VGFnUmdRR202eWdtQ1pwYTFVUmVUTEpmeFhMd1Q0UktVSkU0VUNqUVJZYXJHMnZTMk1DSzZ3UzBKSmorMCtiQnk2Ync1MjQzSDFGNTJGa0ZNMXFzSWNTVUZyU0VJY3FlNTVqSGFZZUhxakRjZU04elVvNHhweHlPbVRvZUk5dy9ieXlxaHU4c3VvMXhJVzlLMHp5NERwY2tkczZjWlA1dms0cERiVUJQSFNwOFR0NnJtaFhJWGUyWm1YZ1p1WWxSN0hyWDEzYnpzSlUyT09STVRpZ3RlTFdLMWFTQXh2Y0ZpaHJEdk92R2ltVjU1NUNsZEthUlFVTnNHTmhudDFJckM1eVJaT0hxQ2MxaXk5aU1YdXIxNWl3L2luOU5nZ0ZxVTM4WHJUNnk1SlFBSGgzUGZlNkVQNzNjMVVqS25LT0gvRmRRYmVPaFNiNm5NT25QRHZUWHVsdWpuUXR5SFpOQTZMYXQrdkdZajB6UExjcUgiLCJtYWMiOiI3M2ZhNTQwNTU5MjQ2MmEwYzRjZGVhMmQ3Mzc5ZmE1MTJlYWEzYzAzZmQ4YWFmOGE4ODQ4MThjZGZjNDdkM2IzIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxKNVY3c2IzM01KdDhnNzI3T1JvZ2c9PSIsInZhbHVlIjoicUpzVXNYNFR2SEdpUVJuQWg1a1l0MWYvNXRwK2VGNThKMGtUSXBlbDUySUgxTkt0eEZjTnBUNFJabGtOc096Um1tNExhT2VsbldWYnBwQTdmNHozQXllWCtmOVNHREVOM3J4Y2w5amRZTlpvTEtLWFR6MjIyZ2JydUI3Qmc2QldIc2Q1c2Fwb1RsUEhDZVF1OG9DSThYWjNYdlVzbGdLdlpnU244ekJoS054aCtzKzZSS1JNNmJhM0FrLy9nQXp2UjdZRENEZkw3WG80WlNjSWRvSU0zemFQYzY1d0gyai96RGg3R21IWkFwQWpZRklScm1IYW1zcE9TQ3ZlcmJxS0llWUFBSk9CT2J0cjZWUzcvNVpmS1B6UEFMSlBJdG9mTXgzYUhsWTE4ZEdHTW9MVEpRcVVXd1psZ0hqa2lkWVZ5NTVrWGhzL09iOWIzZG9OOWlnTnNraHZQKzczUzBvOGxzNGtNL0lDRlh2N044dzNjbUc5TzF5WTI1ejU5Y2pCKzVRSFV5OEw2YXEwczF6QXoxY2FQRFlQb1NDL01vVWgrL2VDdHM0NUVLd0JtOWc2ODZoZS9Td2hLYlI0bFV2V2xoOXk4YkZLV0tydjYxNWJTLytiS0NmdGtIVitiYTZTNzAyekcvNEwzMlIvRTJiRloxbFZodGc2WVBQQzNjcjIiLCJtYWMiOiI5YzJmNWJlZGVmYWY0YjJhNTNhZjdkNTRiODU3YjY3MmU2ZjU4MWQ3MDcxMGU4NWNlODBhN2JlM2UyNWIyZTFkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtNSXdaYWlmL3VqbW9KTGFJMHROYlE9PSIsInZhbHVlIjoiVEZLaTU1elNTaXZMWjJHMTlxRUVaN1ZxdEFkc090WTJJdFZ5bStqTWlpdXpueU0yNTA5V1VkbUl6K0NIajhBSzIrTFpkRjgwZzZ4NVRQcmpjd1B3ZExWOW5heW9UVFlod2JXSmNmY29vRklEbnVEQ0dnUWg3TkdGSjk5bXo2aXh1Y21sK1ZLSXZIVXlGYmt5VGFnUmdRR202eWdtQ1pwYTFVUmVUTEpmeFhMd1Q0UktVSkU0VUNqUVJZYXJHMnZTMk1DSzZ3UzBKSmorMCtiQnk2Ync1MjQzSDFGNTJGa0ZNMXFzSWNTVUZyU0VJY3FlNTVqSGFZZUhxakRjZU04elVvNHhweHlPbVRvZUk5dy9ieXlxaHU4c3VvMXhJVzlLMHp5NERwY2tkczZjWlA1dms0cERiVUJQSFNwOFR0NnJtaFhJWGUyWm1YZ1p1WWxSN0hyWDEzYnpzSlUyT09STVRpZ3RlTFdLMWFTQXh2Y0ZpaHJEdk92R2ltVjU1NUNsZEthUlFVTnNHTmhudDFJckM1eVJaT0hxQ2MxaXk5aU1YdXIxNWl3L2luOU5nZ0ZxVTM4WHJUNnk1SlFBSGgzUGZlNkVQNzNjMVVqS25LT0gvRmRRYmVPaFNiNm5NT25QRHZUWHVsdWpuUXR5SFpOQTZMYXQrdkdZajB6UExjcUgiLCJtYWMiOiI3M2ZhNTQwNTU5MjQ2MmEwYzRjZGVhMmQ3Mzc5ZmE1MTJlYWEzYzAzZmQ4YWFmOGE4ODQ4MThjZGZjNDdkM2IzIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxKNVY3c2IzM01KdDhnNzI3T1JvZ2c9PSIsInZhbHVlIjoicUpzVXNYNFR2SEdpUVJuQWg1a1l0MWYvNXRwK2VGNThKMGtUSXBlbDUySUgxTkt0eEZjTnBUNFJabGtOc096Um1tNExhT2VsbldWYnBwQTdmNHozQXllWCtmOVNHREVOM3J4Y2w5amRZTlpvTEtLWFR6MjIyZ2JydUI3Qmc2QldIc2Q1c2Fwb1RsUEhDZVF1OG9DSThYWjNYdlVzbGdLdlpnU244ekJoS054aCtzKzZSS1JNNmJhM0FrLy9nQXp2UjdZRENEZkw3WG80WlNjSWRvSU0zemFQYzY1d0gyai96RGg3R21IWkFwQWpZRklScm1IYW1zcE9TQ3ZlcmJxS0llWUFBSk9CT2J0cjZWUzcvNVpmS1B6UEFMSlBJdG9mTXgzYUhsWTE4ZEdHTW9MVEpRcVVXd1psZ0hqa2lkWVZ5NTVrWGhzL09iOWIzZG9OOWlnTnNraHZQKzczUzBvOGxzNGtNL0lDRlh2N044dzNjbUc5TzF5WTI1ejU5Y2pCKzVRSFV5OEw2YXEwczF6QXoxY2FQRFlQb1NDL01vVWgrL2VDdHM0NUVLd0JtOWc2ODZoZS9Td2hLYlI0bFV2V2xoOXk4YkZLV0tydjYxNWJTLytiS0NmdGtIVitiYTZTNzAyekcvNEwzMlIvRTJiRloxbFZodGc2WVBQQzNjcjIiLCJtYWMiOiI5YzJmNWJlZGVmYWY0YjJhNTNhZjdkNTRiODU3YjY3MmU2ZjU4MWQ3MDcxMGU4NWNlODBhN2JlM2UyNWIyZTFkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611989429\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-523715527 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523715527\", {\"maxDepth\":0})</script>\n"}}