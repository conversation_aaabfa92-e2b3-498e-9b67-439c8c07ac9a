{"__meta": {"id": "X901a0e4399729f6894f6005edad0d9bf", "datetime": "2025-07-14 18:58:34", "utime": **********.898017, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.468222, "end": **********.898033, "duration": 0.4298110008239746, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.468222, "relative_start": 0, "end": **********.845705, "relative_end": **********.845705, "duration": 0.3774831295013428, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.845714, "relative_start": 0.37749218940734863, "end": **********.898035, "relative_end": 2.1457672119140625e-06, "duration": 0.05232095718383789, "duration_str": "52.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00279, "accumulated_duration_str": "2.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.875437, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.477}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.885308, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.477, "width_percent": 13.978}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.890998, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.455, "width_percent": 12.545}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&date_to=2025-07-10&payment_method=&warehouse_id=9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-740923814 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-740923814\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-126039768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-126039768\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1818420998 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818420998\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1686723682 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"124 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-10&amp;warehouse_id=9&amp;payment_method=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752519504098%7C55%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcvME9TaFk1U21iU2l4VHByQm1QL1E9PSIsInZhbHVlIjoiWHVnS1crZS9OYk5VS3V0Y2lIcGdqTkFMa0lNLzFaczRYOGw4N0JvWlBkSlJsK05wTUgvUGtKRTFScC9xMjNLaC91Y0R2L0FKRFRBZlZOS1hFY016c3F5WXRqZ0RuMDFVZkRZajc2RUVrcEtBQzJCZWJ0K3lPUlRPUFdPM1Vma3l6WVNHczQ2NFc1YjJoc3BVcFQ3dzcyRC9jNm5DVXBqNU9WV1lMY2JPM05mSXd2OFRKMzF5S2JyTGFlcXlteWw5bHhnbTZzMjVla2Z3aVZXMmFGb0FSYWhUM0l2N0RzRVE0VzVUYmtpZnAwRzB6V1UydFhkZjAwMlIwUWIvN0pObjBCRk9BMnRYdGQyeWFRNEpHWEYxdmxYK2JIZGJHTG1abE02VmNrckRVR0RnUGpVQk4zYnV6Q1dFRytFMzZTMG1WVVJ4WGVrRDhLaTZZVXp5OUdGVUZCcGVGNDBlSkpSVDJoOGRQd1FBNCs3am14VFl4bUVicTl1VzJxbWF1a3JoR2FUNnhEWU0rTTErSDJycUZmMElwWXFZaEdiNXJoMTlpVTBFTjk2Q21DMWpSZjBzTXNkdU9BK1d6dXVUdlhaMFBNOUI5U0NvVkJhQTNITThmMmRTSmVyd1JQNWs3T0t0WkhWU0gzM3h3bVNUMkN4VjdGQ2NpYURkOStZVUtJREciLCJtYWMiOiIxZTI0ZDE2YjZkYjFlYTZmNjA3NzUyMGE0NTllMzc0ZDg2NDNmNmU5Y2M2ZDBiNzYxZTQxNmU2YThkZTRhMTA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRNSGZPSGZ2MFd0eENDdHdtb2xJb1E9PSIsInZhbHVlIjoicVZLYWVmYURlQUdkdVhRclg4TG55S01mWHNWUGhSSG1tT0xUYlRoQjZ6YXFiTnNhVjM0WWo0VEczTWt5M1ZiYWVzTzFjVWdFZGkvS2doMkNoc0pVVkZ4eUNSOWxFeDBmc0hsSTNZWDVzUm9LMzB5MituK3h0ZktMUjZTSVgrazdOL2tOWnFaMmgvWlI1b05jQjJ4NnlzZC9qeFpYdjdlKzN0c0ZrcVlkUjRTNG91QW9lSVRlQ3NyTlhaMERQUW5GdGZIN0FvV3ozYTMyUTBYZUdtWElHbHZ2RE8wL2w0OVU5ZHg0TWZ1VFRWUy9uMm01Zzg0WW4zWmFMQTBmZExKRTAzZGk1eEp0VVlIU3I2T0locmZZaDNQSThhbDdpR1pHTzlWVFJLdXdZVW90WlZudlFaMWdxa1craWJnZURHQy9XV3Qzd3VEaHEzRjhSS0tRa1VySU9UNDVKSGN6dXZjQlI1bmM5b0xGZXJ2ZFlQdzVLS0FvZll6VWcwRHBneFJrK3FtRzk0eVBEOUIxRXBOOHArbVQwa1pxbFU1dFFnYnQzQ2pkRWkrdnBOVnFzOXlsTWFLcitCMzB0QUFCRTNPN2xHY0dzTWxYa3JwY2ZnQ0NTTDFZQ0g0UGNpMVZ6OU1reGFnVDhLa3BLYVdJTDNWNjNMS2xwMDN1Z05LektNS2siLCJtYWMiOiJkMTZmNTY4MjI0YjQ5N2M2MDhkMzU1OGQ2M2MyMjI1NzEzNzU1OTIxYjdmN2FkMDBiZTVmZGQ2MDMzOTkyMTVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686723682\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1107159956 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107159956\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-359652621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:58:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkROVFN0NFJ0czFWK2RlNHJrVU9iQUE9PSIsInZhbHVlIjoibk9lK0MvSThMZGYwV2Yvdk0xb0FIUURkYUl1SVZTQytaSVkvaE1lWXovck1Ya3FLd3J3UjluK2xxQjYxK3lScjVPYzlabWdwZmd2WS95QXBVTWkzRStVUW1mVnd4SUtOY1ZubmdZNjNROFg1QmNWM2ZuMkYxRjNyL3ZWUjRBVUMwTWN2dGFELzN6Si93c2RKd3VscHZ2L2xSV3VXdmViaTVhN3IvY1diMkdZQ2hsa0hCTDZ6dGhTRVZ4TlFnbXJSaHZBNEtnZzFoNm9ZOFk2bDlGZXBrbytVbU0yZVZZQzN5VjNSSm1WVVZCRkJWVXJWU3dpaVR5N2hzSE41RDkxSDkrakE4SzBMdldwZThsVnVzbzdUNG9rWmVUOUZKRlhqaGM4ZGo5MDdyQWkwdFF2S1NWL2NRUG1CTmhKTVQyNStIaUFsQmtXL1hvNkRNSzdGN2lhUUtlOGd6cHpqNmcrTU1UaWdwYkwvNEU0bEI0RVJCSVF1SWdYaEZEVEhyU2g0YUt2NWhZcFRIVGpyNTliUm5xRE43WDFlVkowY09KL29Zemkvbnk3ZnRDUmVobzFFaEdKS0grOEtMUUlXL25WTTlHZUUweWNqY05LWnJUUm9oZ3EvT2JPMXREbmR5Qkp4RnlpR2M2Z0NBUU9uUzF0ZjJFSFJRdElnS1RCVVdjZi8iLCJtYWMiOiI2MjQxYTg2YWVkN2QyYTg1ZjgwNmZkZjExYjg5ZTVkMzI1NzUwYWE4MzYxNWI2Mjg5NWE1ZTEzZTIzYmM5ZGNmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxqZGlBUUdOSHdybFM1cUxxTnhsUlE9PSIsInZhbHVlIjoiYXVpcUdvdk5QdU0xQWtzN09Mc0ZRZStEZzk4TWR4RTRjdk1JeDIwUkFoaHN1eHN0dUNEcFNPdFdab3BEcGxpb21UTXRROVN4VXh3UVZSWldZQmNpTlpIcFpjYzl6NGwyTUsvN3NHYkl4N05FWEN6LzNibjBQRkZVMDJmTG9FcldhVU5RYTdmY24rYldCU2ZJT1lQV3oyUnNTdG43U3JPdjVkYkRVaktBRm5rZWxBNVBmdGJseUZ6KzZaRnU1S1hrb0FWZ3ZOZ2pmdU5rUXcvN0ZZZ0Q2OEtoVzFPbFFIdzZJQ1BabFVxYm1US3FvZ3NzQkNtdDZsM0pYQURuT3F6MkR1aDJhdzdyTEphQjdzQmRRcWU3R3ZEV3prTXlnRVhlSVlWUFlPYTMvWDdYTWV0dVJjTm5mK0R3QjJTYW42SjhvUGJOTEd2RGVQSWJvd0dHckZYRWVJRFMrd1RXT2xYelpENUdQT01aVlBIWjEwUFdBS0JJUEV6TUY3SDMxN2g1NGJvSzQzQ01CeGJJOUltRkQxQTBlbEVKbDNRMTArQTZpU25MRS9aTHZpVVFrRVVMb1hxMnpjbXAxZGlCbVJnR0grZHltcDM0c21pdmU2QWxxTytZemJQN0dERXVoUitjdUNvU2lSMTJGa1VQWElDcUg5cnhwVXp2ZkM3UnlXaDYiLCJtYWMiOiJiMWJmNGY4ZTMxYmM4NTQxZmYwNmJhYmIxN2ExYTlhOGI5NGQ1MDUzZmU0ZGY4OTZmNjNkMWNhMWI1YzE0ODczIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:58:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkROVFN0NFJ0czFWK2RlNHJrVU9iQUE9PSIsInZhbHVlIjoibk9lK0MvSThMZGYwV2Yvdk0xb0FIUURkYUl1SVZTQytaSVkvaE1lWXovck1Ya3FLd3J3UjluK2xxQjYxK3lScjVPYzlabWdwZmd2WS95QXBVTWkzRStVUW1mVnd4SUtOY1ZubmdZNjNROFg1QmNWM2ZuMkYxRjNyL3ZWUjRBVUMwTWN2dGFELzN6Si93c2RKd3VscHZ2L2xSV3VXdmViaTVhN3IvY1diMkdZQ2hsa0hCTDZ6dGhTRVZ4TlFnbXJSaHZBNEtnZzFoNm9ZOFk2bDlGZXBrbytVbU0yZVZZQzN5VjNSSm1WVVZCRkJWVXJWU3dpaVR5N2hzSE41RDkxSDkrakE4SzBMdldwZThsVnVzbzdUNG9rWmVUOUZKRlhqaGM4ZGo5MDdyQWkwdFF2S1NWL2NRUG1CTmhKTVQyNStIaUFsQmtXL1hvNkRNSzdGN2lhUUtlOGd6cHpqNmcrTU1UaWdwYkwvNEU0bEI0RVJCSVF1SWdYaEZEVEhyU2g0YUt2NWhZcFRIVGpyNTliUm5xRE43WDFlVkowY09KL29Zemkvbnk3ZnRDUmVobzFFaEdKS0grOEtMUUlXL25WTTlHZUUweWNqY05LWnJUUm9oZ3EvT2JPMXREbmR5Qkp4RnlpR2M2Z0NBUU9uUzF0ZjJFSFJRdElnS1RCVVdjZi8iLCJtYWMiOiI2MjQxYTg2YWVkN2QyYTg1ZjgwNmZkZjExYjg5ZTVkMzI1NzUwYWE4MzYxNWI2Mjg5NWE1ZTEzZTIzYmM5ZGNmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxqZGlBUUdOSHdybFM1cUxxTnhsUlE9PSIsInZhbHVlIjoiYXVpcUdvdk5QdU0xQWtzN09Mc0ZRZStEZzk4TWR4RTRjdk1JeDIwUkFoaHN1eHN0dUNEcFNPdFdab3BEcGxpb21UTXRROVN4VXh3UVZSWldZQmNpTlpIcFpjYzl6NGwyTUsvN3NHYkl4N05FWEN6LzNibjBQRkZVMDJmTG9FcldhVU5RYTdmY24rYldCU2ZJT1lQV3oyUnNTdG43U3JPdjVkYkRVaktBRm5rZWxBNVBmdGJseUZ6KzZaRnU1S1hrb0FWZ3ZOZ2pmdU5rUXcvN0ZZZ0Q2OEtoVzFPbFFIdzZJQ1BabFVxYm1US3FvZ3NzQkNtdDZsM0pYQURuT3F6MkR1aDJhdzdyTEphQjdzQmRRcWU3R3ZEV3prTXlnRVhlSVlWUFlPYTMvWDdYTWV0dVJjTm5mK0R3QjJTYW42SjhvUGJOTEd2RGVQSWJvd0dHckZYRWVJRFMrd1RXT2xYelpENUdQT01aVlBIWjEwUFdBS0JJUEV6TUY3SDMxN2g1NGJvSzQzQ01CeGJJOUltRkQxQTBlbEVKbDNRMTArQTZpU25MRS9aTHZpVVFrRVVMb1hxMnpjbXAxZGlCbVJnR0grZHltcDM0c21pdmU2QWxxTytZemJQN0dERXVoUitjdUNvU2lSMTJGa1VQWElDcUg5cnhwVXp2ZkM3UnlXaDYiLCJtYWMiOiJiMWJmNGY4ZTMxYmM4NTQxZmYwNmJhYmIxN2ExYTlhOGI5NGQ1MDUzZmU0ZGY4OTZmNjNkMWNhMWI1YzE0ODczIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:58:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359652621\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-674516994 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"124 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-10&amp;date_to=2025-07-10&amp;payment_method=&amp;warehouse_id=9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674516994\", {\"maxDepth\":0})</script>\n"}}