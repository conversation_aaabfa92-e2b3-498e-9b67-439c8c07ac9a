{"__meta": {"id": "X97bb417a205fd348ff7e59b2a52a23be", "datetime": "2025-07-23 18:23:57", "utime": **********.779187, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.245006, "end": **********.779206, "duration": 0.5341999530792236, "duration_str": "534ms", "measures": [{"label": "Booting", "start": **********.245006, "relative_start": 0, "end": **********.677194, "relative_end": **********.677194, "duration": 0.4321880340576172, "duration_str": "432ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.677203, "relative_start": 0.43219685554504395, "end": **********.779208, "relative_end": 1.9073486328125e-06, "duration": 0.1020050048828125, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46536136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036899999999999997, "accumulated_duration_str": "3.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7109191, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.64}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7249222, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.64, "width_percent": 26.558}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7328522, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.198, "width_percent": 16.802}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-822004293 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-822004293\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-543289228 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-543289228\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1572794487 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572794487\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1311982022 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753295020738%7C22%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklBbnUvR0t3OVdtTkRXTCtFN1NNYlE9PSIsInZhbHVlIjoiMVNpZlVLWWJlbTkyQVlndWR4bUlSNzhKY25rTzdIdWZiL0dIWndLNDdnOU1qZjB6SlQwbmdYYjdtSmpnSVlnS3VLS2JjMnM4RHZReGRRaWt2UFJWd3dNcGtkaUdKUTM0OGNWaTZTc1RyT2hmQXJwK3BXdHBZeFNCSDhUaEFJdzhOeUVVYmVkMlBsWUJ3NTI1VkdXbWticU9SQ0czRTRnd3NuL3NhSUNxRlczZ3cwSG9ETDgrU1Z4dER3dkdTeDZyY3VvQVhJT0VvbnA4aDMzSTlCZy9sY3lNUWNEbWc2R2VEbmtndWkxVjUwOVV1clpzVytGSUkyckg1WWkrTkdkT2thNitlNzFrN2RsM3dZK004RFIxSnBkWVlSUDdnVFRhK0J3eVBDME9lbUs1eklDODdQSjNoZkpvZi82UzNtSlg1NytLYU9RM28wVldvbWdYYjkydnN2eko2WHJkWk1pUkoxRU9mdHM5Vk9rbHlEUy9CT3RnK0JPd2JQV0JsRG1MMGF0dmI1a3NHTVlyZnRVK0cwWUtuZHh5TVZadTJLMW5Kd0ZxaUQxRGh4N1kvRHhVZFc2d1VrQnF6QmJqNVNJVVIrRU5OUjdwZlZ0eG5hZnRuUnNaNDRhY2R6WDVVKzRLdk1IMTZOUTNhUTlxSHNZOExwbU95QzBpWnpVbUFvNysiLCJtYWMiOiJkNjM4OGMxMDZmNjk3MjYzZTA0N2IwMzI4YzdiY2ZjZTYzMjg3MWE3NTI3NzhjYjAzNDgxNjI0Zjc5ZTE2MmYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllFYno3ZmxHeFcvTzg1UGtlK1U2RXc9PSIsInZhbHVlIjoiS0tqeUhnNERxRDhsOVplcm56QmxxM05abmpYS3RXbUxnNFJmSi8zenQ4TDVEM1NLdXU2TDhYZGsvSHBLeTRHK2d3RzhXaEVKRmFhVWMyM09xTXR2Y2MvM2Y3WXBvM2pVZ0cvejloN0pZTnRWRnE4MWV6YnRaK3ZvLzZpWjE4TG1xRGtGQ3hwcjMwT3hCcnFYQzZ5cGQrck1IcldrWnNTdnQ2VEhLSnhkV3FuS25VWmhrY3R4NXJOc3FoVTdUNEhQZHIwT1hjMWhZQnZsMEp5OUFlTzZUejBLeFZSMFhzZnJzOW94SHVTb0JwMjJXdytIZWx5Yzg3MXVmUU95WHZKaFMzdzV1SzY1VXkvTDhWa3pYdUxwSC8zVHdmZmNjcUh5UVhQR01lbVIyVGR4LzF1Z0ZnMWdlNmZkNDhuYklUUW5rZStQTWxLTmk5a2JjeVV6NWhaQ2JtTi9jV0lXdjNMWFlQNjVqSStNUFRUcXI1dTQyUzRsMjhtcWg1MGJ4VmNGN1REd0RYTjRMYWRCdWRCQWVQZkNKeEV3UWZYR3NxOG1LQ1BJMjExR2QvN25ObWMrWTlLUTg0RFdBSndMMS92d1l0MjBjTUpKdjU2S2dFSmFaZzVUNWc2NWhDVU9QVlpXUnI1NWFTanpDQlNlTEQrd2VWaVAwWDNxaFRlRmFHV04iLCJtYWMiOiIzNjQzYzg3MDdmOWI0NDMwYWUyNTNjNTliNGFiOTgzMTgzNDBkZGMwYTJlNzAyMjY2ZjQ0NmUwYzFhNTBmMGQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311982022\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1723393986 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723393986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:23:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ims1Ymp1K2hBUHY5Yzk2alM4R3pER3c9PSIsInZhbHVlIjoiaTFWS2xCM09sUUtDT1NlcVpweUR1QTJsdGZBblhWTlY1bmVESlhDekNWRWFVeS9SVU5Jd3JOTXAzZFl0V3lXUFZXV1dzL1JvZUx5VDVMK1hqcnpoc3d6NFFXWnl3L0hJRGVMS3dNV1pldU9QMGtHcU5vVWFUVlJmbGl5T1kyVFJ6d3QyYVByWmtZaFVqcUI3aElqUjJTc09LbklFU3o5OEJYZVRIdFVLZ1crcnYzVVdqeWNsWjhzUkJsaTMzQTBIQ3gzQ3RsMWcvaVF0c2hkeC94T041S21ZaTJMODQySXlEYXVPK3A0M2VUR0VJSSs1WUlpcyt3eXNxRmhXY3g0eU4zckNGYk5WMGJTdjJkTUV2bFc2L1krY1dXZjhGS0lLV0lGeEtDZGlBU1FjM3ZnR3VRMDk4S3BRU1dsNlNYS1Ivb3pSaHRNL2pOa3RLZmJpNmNDNHFRbUxoR2k2NkJ0bThyTmw1OXJhdUVSU29pWk9ZZmxhUzYvVnhKclQxVStlaHh3Ukprd2syWUFnZEhPblRJM2phS1phWEdRNXlaY0RkSDJwYVp2R1hzNUh1RWVrS2hiZUg4RjFZM21QU2QwZjBhcGJ1TUpCUHF6YW00YVBPOHQ1VVN4VEZDUEYxU3NxWks1TWlGalIreUZxVGZ0YmdNUVhHZVNJbFRUK2VpVjgiLCJtYWMiOiI3YzRlMmZhNDNjNGJkNTQ0NWZjYjA4ZGI1YmQzMjA4ZGZmMjA4NTgzMGY1MTJlYzNmYTEwMmM2ZmIxOTY5MmY5IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkwyVGpINlJQTFVIb0hWYTdKak1SZlE9PSIsInZhbHVlIjoiT2FBUDhVb2lPRGRVQlYvUVRFeGhYcHdVN1VJaEJoQlVDdG5kOVpEZG1CZjlaOWZhOGdUVzVQUm5wSWYzbVZNSTdwbURpNjZnaFRCdDlWV1R4MEpYRGlEaXNienNBelYwMmV1TTVkYlJnZmtUL01taUN6eXpSVXpWWm1BeDRESmZJL2hLUnl4dWhXV3prdDd0dXp6REFPY1M3ZjA1R3B4OGY0bzdGcFM2MVFLU090TDhmNnJINzJQRGRXZDYvRFlMUEhTMFZ1bVZVa1dZdzA2bFJTMEw4N0hQbjZyZ1lKZ29TSTlGemI4cldoL1hweUs1SWF3QlBGV0FYWGpnL0F3ckpMbXJWeXpJeGJ4ZmJBS2Y5dmNzZ0k2TmxDaTByZnVnd3B6bUVxaW1malBQM3F2VE40a05qNmk4ZUtMSEw0VW9TK3U2Ny8vbU1HdUUzRDI3SDlscjBFeXA3ZFc5Q0Z1VSs2UFJ0UzVRWXo4K2pxSXVKeGIwL0kvNTZ6TFBmS3BySXBDTHFIUXJ3TlNIenEwV21qc01iaCtrNCtkbXM2UTFpb3Jac0c5d0JHazRJa1hYZ0VVb3lXS2FveGVleXB5bVBSaWl6Mk9wMGk0ai94RTd3N3ZuUm1QNFhZK3hKcTNKQ083d1VQYXNETVZ3eVp2OHg5UkN6UFYzak0wVlhPM2oiLCJtYWMiOiI3ZGYyYzlkZjc0MDJkODQ1ZjdmYmZlMDZkYzAzMjJjZGQzNDFjMWExY2NhMTJmYjA1Y2E3N2U2ODg2ODVhNjc2IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:23:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ims1Ymp1K2hBUHY5Yzk2alM4R3pER3c9PSIsInZhbHVlIjoiaTFWS2xCM09sUUtDT1NlcVpweUR1QTJsdGZBblhWTlY1bmVESlhDekNWRWFVeS9SVU5Jd3JOTXAzZFl0V3lXUFZXV1dzL1JvZUx5VDVMK1hqcnpoc3d6NFFXWnl3L0hJRGVMS3dNV1pldU9QMGtHcU5vVWFUVlJmbGl5T1kyVFJ6d3QyYVByWmtZaFVqcUI3aElqUjJTc09LbklFU3o5OEJYZVRIdFVLZ1crcnYzVVdqeWNsWjhzUkJsaTMzQTBIQ3gzQ3RsMWcvaVF0c2hkeC94T041S21ZaTJMODQySXlEYXVPK3A0M2VUR0VJSSs1WUlpcyt3eXNxRmhXY3g0eU4zckNGYk5WMGJTdjJkTUV2bFc2L1krY1dXZjhGS0lLV0lGeEtDZGlBU1FjM3ZnR3VRMDk4S3BRU1dsNlNYS1Ivb3pSaHRNL2pOa3RLZmJpNmNDNHFRbUxoR2k2NkJ0bThyTmw1OXJhdUVSU29pWk9ZZmxhUzYvVnhKclQxVStlaHh3Ukprd2syWUFnZEhPblRJM2phS1phWEdRNXlaY0RkSDJwYVp2R1hzNUh1RWVrS2hiZUg4RjFZM21QU2QwZjBhcGJ1TUpCUHF6YW00YVBPOHQ1VVN4VEZDUEYxU3NxWks1TWlGalIreUZxVGZ0YmdNUVhHZVNJbFRUK2VpVjgiLCJtYWMiOiI3YzRlMmZhNDNjNGJkNTQ0NWZjYjA4ZGI1YmQzMjA4ZGZmMjA4NTgzMGY1MTJlYzNmYTEwMmM2ZmIxOTY5MmY5IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkwyVGpINlJQTFVIb0hWYTdKak1SZlE9PSIsInZhbHVlIjoiT2FBUDhVb2lPRGRVQlYvUVRFeGhYcHdVN1VJaEJoQlVDdG5kOVpEZG1CZjlaOWZhOGdUVzVQUm5wSWYzbVZNSTdwbURpNjZnaFRCdDlWV1R4MEpYRGlEaXNienNBelYwMmV1TTVkYlJnZmtUL01taUN6eXpSVXpWWm1BeDRESmZJL2hLUnl4dWhXV3prdDd0dXp6REFPY1M3ZjA1R3B4OGY0bzdGcFM2MVFLU090TDhmNnJINzJQRGRXZDYvRFlMUEhTMFZ1bVZVa1dZdzA2bFJTMEw4N0hQbjZyZ1lKZ29TSTlGemI4cldoL1hweUs1SWF3QlBGV0FYWGpnL0F3ckpMbXJWeXpJeGJ4ZmJBS2Y5dmNzZ0k2TmxDaTByZnVnd3B6bUVxaW1malBQM3F2VE40a05qNmk4ZUtMSEw0VW9TK3U2Ny8vbU1HdUUzRDI3SDlscjBFeXA3ZFc5Q0Z1VSs2UFJ0UzVRWXo4K2pxSXVKeGIwL0kvNTZ6TFBmS3BySXBDTHFIUXJ3TlNIenEwV21qc01iaCtrNCtkbXM2UTFpb3Jac0c5d0JHazRJa1hYZ0VVb3lXS2FveGVleXB5bVBSaWl6Mk9wMGk0ai94RTd3N3ZuUm1QNFhZK3hKcTNKQ083d1VQYXNETVZ3eVp2OHg5UkN6UFYzak0wVlhPM2oiLCJtYWMiOiI3ZGYyYzlkZjc0MDJkODQ1ZjdmYmZlMDZkYzAzMjJjZGQzNDFjMWExY2NhMTJmYjA1Y2E3N2U2ODg2ODVhNjc2IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:23:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-281383174 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281383174\", {\"maxDepth\":0})</script>\n"}}