{"__meta": {"id": "Xaa5b5eb54be278b39b7148b0825acad5", "datetime": "2025-07-14 18:31:35", "utime": **********.867429, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.366974, "end": **********.867444, "duration": 0.5004699230194092, "duration_str": "500ms", "measures": [{"label": "Booting", "start": **********.366974, "relative_start": 0, "end": **********.791262, "relative_end": **********.791262, "duration": 0.4242877960205078, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.791271, "relative_start": 0.42429685592651367, "end": **********.867445, "relative_end": 9.5367431640625e-07, "duration": 0.07617402076721191, "duration_str": "76.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01626, "accumulated_duration_str": "16.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.828923, "duration": 0.015349999999999999, "duration_str": "15.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.403}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8538458, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.403, "width_percent": 2.522}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.860105, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.925, "width_percent": 3.075}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-457604913 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-457604913\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-814053130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-814053130\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-454056437 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454056437\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-372844639 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517839891%7C45%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijd3ODh3eXJNUzcrVTNqWitneXYxQ2c9PSIsInZhbHVlIjoiWS9kcFhPT295cFY2cmhEa1VVUEx1WmJrNldGNk5Fc1BlUm5oMWlSZWhFZzZ5SDhvUmhaNGpGQVVSYXFyYjVvNzJwZVpwdkVsOW12cUtzbHI1M0I2RlY4YmZEdjNGYnNzNGdib3hhaDd3YkxtREVadjI4TWYrVzFxQlE1RkxDSkxZeGYvYzNxMi9KTHF1a1lXYnBYV0swaTBBZHV1b0RsOS9xYmlqZ1NjdFhuOTFGdTdva2JmZC9aSTdtdG95SjZ2ZkwwOFhHWW4ralkwakpWdHVMY0xDRis0RDc5Qml6dGNLVG5OZ0VpRFgxTzc3NGhNMDJZTTYzSGkwYVM5OE5CSk5wR0VlZmVUV0lIRkt2Y0dhOS9ySDZ4Wk9kRFZEMXpkaHdzS0pNM2p2S3NYbkRoaURjY0xxZTlrY3JYeTZ0a3QrVGI3ZDZ4d3hJUWVpa1lhQWk5NHVDUVdpek01eFp5N2VZd2VsczZSZEtKMDNoQmhiOGRsdEwwbS9ESHhNZlYrZlBFLzEvS1YrL2JsbFkzN0JNRExzcnFZbC80Q2kyaVIydDNIV3pHcTlDd2VGNzBjQVNHRmk4cTJCMDBvNDhuTi9QdWFXSFNqLzhYa3I4ZldzVndNVFNXb0dIWlUyTEZIRnVka3QyeWFFTzlqU0loVE00NHd0S3hpYm4vMndOSGkiLCJtYWMiOiI2ZjZmMmY0YjUzMDkyMDI4NGRmMjFjY2VhNDRlODhjMWJkZWMwNjRiNGM3YTFlYmNhNTU2YzdmNTZlM2ZiZThkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktyUEFUVDJFdkN3ZExyT1VsbFBUWFE9PSIsInZhbHVlIjoiV1JJR1VtWDFLUlNPQmI2ZVhDVW91bTZtSHpaOU0zTXJUR2x0NlByMnZXejJQWlZEc1h3MExXUncySmxsak85TmFqSEluRWRScDZKQnBYQXFQNW9mYXVTTmorS3hDOUdGWVpuM3FNRFV2TzZIb1RsUmoxV0gvbmdVaE90UnFCczRhMDU5NXZwV2g2QllSVEUyS2d4V3V1eENyajFsNE5iVkZuQXA3eXdIU1l0VzZSa1I5R2xQTndVK0c1Wjl4L1FqaWNyK0s3OC8rdVlSL1lMQ3YyNEVzYVVMbWRxS28xMWdLeDJHTm0wZk4xM3ZWNFh0THFzNG9PN1VPTnJQR0JVYlQzRXZwclM5WnE4U2k3WEw5SE55OUcwcDBKLzhaSys4RUxONjNFQWVZZ0xUM25sWDliOUZIS2NldFlHbWFhOG9RSll6NzlzM0NZaFNZVmRtdzR5cy9nc3NqWEVZT3RoZmk3K2Uwc2pTdEhCL3RqbFBrZW80cS8xRjBPb2xoam5tNFZLTEEwbzQ3RXlUTGtYcW15VzRKMEFxNDRiejcvN1RobzZRYW5uK1c0SkpuYkMwaDROcjRrdjBLYWpBNlhvQ0lRclA0UnAvZGV3L3FhL2ZFcWpKTGRnOWVZR2wrOUxYdXJ0ek5uN1plKzIySDh5MHJrcFcwYzJ6b0xnOXRvR2siLCJtYWMiOiIyMmFlNGI4YmZlMDE1OTMxZjUyNDAyY2U4ZTlmMmMwZGI3OGZlYWQ1YzRmYWE3NTI4Y2NkMGI0YWY0NDdlMjAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372844639\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1609609272 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609609272\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1873537340 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:31:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVSTm1DMkVJZDdxeXB5THE1SFZxWUE9PSIsInZhbHVlIjoiK2lYRDI3Smk3amQ1RFpEb0pYVTVDNzBCRlEwZkg0Z1MvaWxTVjd1L0RaazJ5UE5GdWJoNDFBMEs5ZTZ3UWF5cUo5TnhCa1JyWDdLdFJ1eWxmaVJOQ1pVeStCKysrOXo5QnEwSEtER1pEeTBVSWZrR0tvVFcrbk5qZkN6NGk5TzM2bDc3WlNzakRmMkJXZEc0SWE5U2ZEK0dobWxOQWsrdEw3NTVsVkZMU2UyOEkrTE1vTVdITm10cGRhN3ZPMTVlbVEyaktLU3pyL1dyNCtsdGg2LzUybFRnZ09PdlF1VXE5Wk81QTV0cFNqL2pBRzlGQlltUTUrMnExcjdENlZMOTNydjRiREhJZGVuRFdQS09kUW1NbXNaTkFtakpsRHpNR1BCQ1VQakt6NjdRMU42VUlicUFBekZ5bkdwdVNqcm9xOWtlNXNSNXh5VjZJRzB0ZGdnSWxmWnJTM3VSRys4ZFFBeUpkT3hEOHl1RnkxTUljRmlsS1BqeUVaNVE5aXhCcEFPNnNyTU5jcWNzTHVFKzBPYkhRTG1rU0lFTzM3L0JZS2FZTVpjUkxJRjNGZEpoQUxnMCs0YkRGR3NCK3FUSUh2aFFlK1A0MmlzY2RyTlE4MWpsNVdRNm50TEsxeWlrOWNRZEdrVXo0TDhZNVFmUnVuOGM0cGY2S0RSVzRDOFEiLCJtYWMiOiI4NGVlY2ViNDM2ZDE0MDkzNDA1MTJiNTg0YmFjYjZkOTg5YWNlOTE3OGU2ZDVjOGY3ZDFhNWQwYjk2OGI0MTY1IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:31:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitBelU4OXBKZythc3lOOWROcUlNT0E9PSIsInZhbHVlIjoiNnVMK0cvZG03Zm41Y2NDNWpkUFMxazVQcDJTYlVQY3YxZlM3d041aXRBV0ZNaWJIYjFuVmo1VUYwc29tVzdvbmhieXZob20zSDNJdTVkZmo4U2Q3WlptTVhaRmVCYWs4bk94VXhVZXRJb0RVL0Y5dHJVajVnMUJDdDlrY3hOMU9rUzlZdW85NFBNc1IwSERCYTdyelRpNUI4dFRwSmQwOFdOaFdaQ2tUN0FyQUlBcG15MUtlYXpxUi8wa28zejdVS29TR1NyWGtCNGZ5elFpT0ZBWi9WOFlWaVVIQWFjNTZSbUthM3RUSk5FWmNseWtLWkVQeEhwM0VUbHJUVWt6NmRuNzVsZ3ZMWGk5Yk00Z05UQWJreHgrelVZUmVyK3M2VUJlQ0Y0cnJoaUU2SjN5bjFaTzVGUUpWbHBFWlV0QWR5bkNVVVpuQ0RrU0I5QjhxN0xuektFNGh3dE11K0U3dWM4OEM1Y3RRZlp4Y2pJVHkrbVRzSURMdlZyZHoxTFNzSGZ4T3Q3Vi94SHRkYjdEQVZmeC9kVThnbVNmYmhVZytDbTh2blk2dW1UcityMGR3QzBWRGJ2M3l1Q2NFaFRBNS92SWgzMEh3UHE3MVlrTVhpOTVKS0QwUW9JQzV4MkxaY05HSGlCQmttL0I4ajRXYzRULzMwWVlNR1FZMzFqWWIiLCJtYWMiOiJmMTFhYTQ3OTI0OTJiNDQ4OWE1ZWVhYzMwNTU1MTgxYTc5MTQxYmE3OGFkNmFjODg0ZDlkMjE5MTEzNDgyYmM0IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:31:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVSTm1DMkVJZDdxeXB5THE1SFZxWUE9PSIsInZhbHVlIjoiK2lYRDI3Smk3amQ1RFpEb0pYVTVDNzBCRlEwZkg0Z1MvaWxTVjd1L0RaazJ5UE5GdWJoNDFBMEs5ZTZ3UWF5cUo5TnhCa1JyWDdLdFJ1eWxmaVJOQ1pVeStCKysrOXo5QnEwSEtER1pEeTBVSWZrR0tvVFcrbk5qZkN6NGk5TzM2bDc3WlNzakRmMkJXZEc0SWE5U2ZEK0dobWxOQWsrdEw3NTVsVkZMU2UyOEkrTE1vTVdITm10cGRhN3ZPMTVlbVEyaktLU3pyL1dyNCtsdGg2LzUybFRnZ09PdlF1VXE5Wk81QTV0cFNqL2pBRzlGQlltUTUrMnExcjdENlZMOTNydjRiREhJZGVuRFdQS09kUW1NbXNaTkFtakpsRHpNR1BCQ1VQakt6NjdRMU42VUlicUFBekZ5bkdwdVNqcm9xOWtlNXNSNXh5VjZJRzB0ZGdnSWxmWnJTM3VSRys4ZFFBeUpkT3hEOHl1RnkxTUljRmlsS1BqeUVaNVE5aXhCcEFPNnNyTU5jcWNzTHVFKzBPYkhRTG1rU0lFTzM3L0JZS2FZTVpjUkxJRjNGZEpoQUxnMCs0YkRGR3NCK3FUSUh2aFFlK1A0MmlzY2RyTlE4MWpsNVdRNm50TEsxeWlrOWNRZEdrVXo0TDhZNVFmUnVuOGM0cGY2S0RSVzRDOFEiLCJtYWMiOiI4NGVlY2ViNDM2ZDE0MDkzNDA1MTJiNTg0YmFjYjZkOTg5YWNlOTE3OGU2ZDVjOGY3ZDFhNWQwYjk2OGI0MTY1IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:31:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitBelU4OXBKZythc3lOOWROcUlNT0E9PSIsInZhbHVlIjoiNnVMK0cvZG03Zm41Y2NDNWpkUFMxazVQcDJTYlVQY3YxZlM3d041aXRBV0ZNaWJIYjFuVmo1VUYwc29tVzdvbmhieXZob20zSDNJdTVkZmo4U2Q3WlptTVhaRmVCYWs4bk94VXhVZXRJb0RVL0Y5dHJVajVnMUJDdDlrY3hOMU9rUzlZdW85NFBNc1IwSERCYTdyelRpNUI4dFRwSmQwOFdOaFdaQ2tUN0FyQUlBcG15MUtlYXpxUi8wa28zejdVS29TR1NyWGtCNGZ5elFpT0ZBWi9WOFlWaVVIQWFjNTZSbUthM3RUSk5FWmNseWtLWkVQeEhwM0VUbHJUVWt6NmRuNzVsZ3ZMWGk5Yk00Z05UQWJreHgrelVZUmVyK3M2VUJlQ0Y0cnJoaUU2SjN5bjFaTzVGUUpWbHBFWlV0QWR5bkNVVVpuQ0RrU0I5QjhxN0xuektFNGh3dE11K0U3dWM4OEM1Y3RRZlp4Y2pJVHkrbVRzSURMdlZyZHoxTFNzSGZ4T3Q3Vi94SHRkYjdEQVZmeC9kVThnbVNmYmhVZytDbTh2blk2dW1UcityMGR3QzBWRGJ2M3l1Q2NFaFRBNS92SWgzMEh3UHE3MVlrTVhpOTVKS0QwUW9JQzV4MkxaY05HSGlCQmttL0I4ajRXYzRULzMwWVlNR1FZMzFqWWIiLCJtYWMiOiJmMTFhYTQ3OTI0OTJiNDQ4OWE1ZWVhYzMwNTU1MTgxYTc5MTQxYmE3OGFkNmFjODg0ZDlkMjE5MTEzNDgyYmM0IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:31:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873537340\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1291917841 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291917841\", {\"maxDepth\":0})</script>\n"}}