{"__meta": {"id": "X3a8d736e8100bb40fd0ae81de3a88d3c", "datetime": "2025-07-23 18:18:22", "utime": **********.683934, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.229036, "end": **********.683951, "duration": 0.45491480827331543, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.229036, "relative_start": 0, "end": **********.618586, "relative_end": **********.618586, "duration": 0.38954997062683105, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.618595, "relative_start": 0.3895587921142578, "end": **********.683953, "relative_end": 2.1457672119140625e-06, "duration": 0.06535816192626953, "duration_str": "65.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46533576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013389999999999999, "accumulated_duration_str": "13.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6471188, "duration": 0.012369999999999999, "duration_str": "12.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.382}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.667636, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.382, "width_percent": 2.913}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.674287, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.295, "width_percent": 4.705}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-403405297 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-403405297\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-973644791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-973644791\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1171400756 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171400756\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-294137083 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294695171%7C3%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRwd2FPa25kM3NOMDRXUFlKWUtoYVE9PSIsInZhbHVlIjoiSUt0SkxrL3VnS3lVbG1JdTBPQ0dBcElpZUZuV1dHNzU1RWRSQWpFZlYxZU1QeHBpRVc4REJwaTdkb1kxNzYzN3NaV0pVaXBMNjJ6TURLN0lMc3BrMm5ENmJsSEExa212TEFlUWpJZ1o3QUtJbUcraVg1UnIvejNwS2ZEVTlTMlA2MWZhZCtXeGxvT0NqbktKQ2ExRElRMnlSalRLRjlUQTlCWGRHUjVDTWxCSG1ITmVuL1NQcTkzY21JemtMazVBTHhDM0FFTStPeGgyM2JNaXBqMzYzWWs5SEMxam9pUUhtVjN4QVRiSFpuQUVjcEIwYS9WeTFseTd1Rit2d1FxT0o2QnJtejFxOWtRd0xFNnJwODduRzlRQWdhcGFLelIwY256K3lSMmU1bmY2QUlnZ1o1MUJORDNJbU5HcG5nNTFxVDRDMVByUDJ2dzFzWEM0N2VHYnBacml1NFE4YUd4bGhqdmMyWXdua2pSeHdIWkQ3VzdYcVFXZmNEMzA3M1plODk3eGgxTEI1YjFRVlM4WmJmdkpma2U1Uk56VE1zbFpjMEN6bFZFNHduUVByMy95WFFlYmRYMDVZVWdseldoL2RibStsaFFHN3F3cS8vdkVwZTRnNFZhZFlGZ2RQVDQyWnNJZnRrMnBhTGlwRnJhOHdnQ2Y1TERYSEhqWXZRQTQiLCJtYWMiOiI1ZWE5ZTE1MTk1NjZmNTY4OTJmNjEyMTc0ZTRiYThlNTgwYzk3MjNhNDg0MWEyZjVhOTI1ZjdmZGZjYzkyMjRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImIrbk9tREh1dHgrYVpoMWpFbnNKQnc9PSIsInZhbHVlIjoiS2RzbzhiMEpRcy82aEgxbFEvWHNYdTYrRC92WFJrbkNwWVBYNHpKd1U2RGVxRWFGL2gwckE5NGlIRkg1dEhiZVJmcjVCeW44MzRkRU4yY0xqMDVnV09aeGhaUjJ6dnl1QjdOZUFHOC96SjNseFpaOEZVcC9yOU9Ib3luN1l6c2JTRjZGcGErbnNMams3SzlnamVjUmJmRm95WjhxdWU4UThUb09sbjVEZjBjc3pIQjJRQlFvbHJDckpjdGp0Uy95dG9TVFVxSHh5bHhnVDFoN2F3ZHN4WkwxK2hyUzFRVnFuUVZXWUxKVmwvM2ordFJnMmlNMHo5Ylp1OTdwVWtpZnVwNVd3MkRIaUZuWHZLc2dTSzNYMjgyQzgvQ1E0YWxHNDNUN0NmQTZKK0lSSFFWWEtxYUQ0WTYrN2tteGxSNmhTOTRvMkt4T3hiRFJySFR5MStmM2RDUU40ZXhmSzZPVHZwaTBFSlVLTHhrODM3QmpkVjQ0RnhlYzdab05LM2dLWjdqdjl2T0lWeU5abDFXcjgvbVB4bU9EQ0pGU1JCbHFqRVMzS3Jsb2JBR2IxbmRsZDhpMFV0T3lTazI1dDllNUxEYTF5VjVNUUJiU0pBTlBEaGs3dHE3T3NtNXhPYWF5cXVwdU1ORDhIYnU2ZlNxRThEdXF2VzF2ejU0bGc3a04iLCJtYWMiOiJmM2UyM2M4M2MxZjkxOGI4YTY4NGYwZmQyMWI2YzIwYThlYzJhY2UyNmJjMjQxNTI1NDc5YmIyZTVhYjZlZTZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294137083\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-851852360 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851852360\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-956553066 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:18:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRHdWVmb1VJOGoyeEI1K3pUSkp2Tmc9PSIsInZhbHVlIjoieE1BajEzSGw3MUg5eDdCU29zeE9WbFk1OTRrRHNzaFVMVVp5UUFNd2hkS3NnaUpiUTl2OHNxSEg3Y1R1bi9DUUJreFZ5ZGpja3R6aVQ5ZVVjRGN6Rmk0R3gzcmVMNTRjVW5lczcrMDlKTExSRlhaZHFPTTdITU01aXZFU2MxT0dHTlF6NGN5RkdHQnJUNXpNSVBCV0dOZlB0dFEzQUlkb0cxcDFSSVhRclRHR3hKS0U3eVVIbjVJemtCUFowQWZhQkF5VCtlS3AxY1JSbXc2RUU5YU5NZ0pKKzhVSEdsTU50TXdDd3VYeHZtcVMyRzZJVW01aEJnMFNka09kc3hKd0dNL09TSWJaYUNrYlUyVFVrVlpxbkdGKzArRS94L3VTRnlhSytPNzZTK3RWQklRdFZ2OTR4VWpxK015NUJpMWlXNjhGRnE5K2FEZHQzSERVZEN0QmE0YkVVZ2F0U0M2aDNUakZIdzVkdU82bmovT3FZRFM0bGNmdEpIeGx4VkJhSC9KWngxeE96aU44M1VXSzNGWThuYjNQYWNWQThJWWF0OCtHc090eUMyRDQ5UGxCUVJkNWZ1Ti9LUVFuZHF3VzNmS1ltaHB6VUI3aGV2eTBVNkIwY1ZkSVFGYmxvNnhDZE5YQ0dPUW9CZndVM0dmQzZhb3FldXo3STZadExRL2siLCJtYWMiOiIzNDg0NzY5MWZjMmQ4ZDM0NGY0MmFjMjM5OTcwYjg3MjViNDU4NmY3OTVmNzM3Zjk0YzJiOWEwZTgzY2E4ZjUwIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:18:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZxRHBjanpXcjI5YmZ5b2VZZTlZd1E9PSIsInZhbHVlIjoiWXpDZU1OMTVIRzB4K0FjMVhFRUVmRG94aGdiMVgwaXZVdHE3N290Mkx6NndNNnVKV3IxQ3g0SVNBVWJCcUptQ0JDNE0wOE1HYXYvL1JUL0pLMGw2dDlSWDZkL0NiWit3SXVrWlVvZlkxR3c4cnlwMHFVVHB5Qk1GZTNUQ2Y2OWx1dnpWd096ODlkTU1ZbWlqOFpaU0NOdTZBQW92N0lBUDJQMmpsWk9vQVB4dUsrUTk5a3IvaCt5cGlqYTUrS2lXNmRYWjMzei90Q1J3YjZSOWZzbmhiMWd4b05CSFJ5VXk3SXhVeE5VMFVzV3NDNU9EbmFEcSt1SHNTR3hlN1RtR0VIWDJETjN5enJWU24zUjZxQlZrb2ppT1didGVqZ3J0RmJRSEQxb2MrS0FzdXEydC9oQ0trRkU2R29yeDJiNkc1TDBwb0NFakJWYlNKb2tjbk1GWHFtY0RxVXh5bE1wcU8ycC9ieUZJYzhVUW1xQVNjTU94TmZOVmxxYWtjbG0wbW0xb2w3NlFUWGFVeWJCQ1A3R0dBK3ZYSXRwdDFSeUNHeFdxUDBRdlNjWjFmUzZReEVIT0lXTnFTa2orM0JOUFRTb1l5VzNsZTRqK3VKUEFsazJoRFRVbFB3M2Y5YmtBZU82Z1lEalNTRWxNcGpGbUZ4Ym1vMUtpd0RmZnI5cHIiLCJtYWMiOiIzZDhlOGUzNGY5MjAxNzk2NWJhMzJjOGQyYTk3ZTdkYmU3MzBjNGFjYzgxNTg2OTkxYmQ0NDIwYjNkZDA5YmI0IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:18:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRHdWVmb1VJOGoyeEI1K3pUSkp2Tmc9PSIsInZhbHVlIjoieE1BajEzSGw3MUg5eDdCU29zeE9WbFk1OTRrRHNzaFVMVVp5UUFNd2hkS3NnaUpiUTl2OHNxSEg3Y1R1bi9DUUJreFZ5ZGpja3R6aVQ5ZVVjRGN6Rmk0R3gzcmVMNTRjVW5lczcrMDlKTExSRlhaZHFPTTdITU01aXZFU2MxT0dHTlF6NGN5RkdHQnJUNXpNSVBCV0dOZlB0dFEzQUlkb0cxcDFSSVhRclRHR3hKS0U3eVVIbjVJemtCUFowQWZhQkF5VCtlS3AxY1JSbXc2RUU5YU5NZ0pKKzhVSEdsTU50TXdDd3VYeHZtcVMyRzZJVW01aEJnMFNka09kc3hKd0dNL09TSWJaYUNrYlUyVFVrVlpxbkdGKzArRS94L3VTRnlhSytPNzZTK3RWQklRdFZ2OTR4VWpxK015NUJpMWlXNjhGRnE5K2FEZHQzSERVZEN0QmE0YkVVZ2F0U0M2aDNUakZIdzVkdU82bmovT3FZRFM0bGNmdEpIeGx4VkJhSC9KWngxeE96aU44M1VXSzNGWThuYjNQYWNWQThJWWF0OCtHc090eUMyRDQ5UGxCUVJkNWZ1Ti9LUVFuZHF3VzNmS1ltaHB6VUI3aGV2eTBVNkIwY1ZkSVFGYmxvNnhDZE5YQ0dPUW9CZndVM0dmQzZhb3FldXo3STZadExRL2siLCJtYWMiOiIzNDg0NzY5MWZjMmQ4ZDM0NGY0MmFjMjM5OTcwYjg3MjViNDU4NmY3OTVmNzM3Zjk0YzJiOWEwZTgzY2E4ZjUwIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:18:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZxRHBjanpXcjI5YmZ5b2VZZTlZd1E9PSIsInZhbHVlIjoiWXpDZU1OMTVIRzB4K0FjMVhFRUVmRG94aGdiMVgwaXZVdHE3N290Mkx6NndNNnVKV3IxQ3g0SVNBVWJCcUptQ0JDNE0wOE1HYXYvL1JUL0pLMGw2dDlSWDZkL0NiWit3SXVrWlVvZlkxR3c4cnlwMHFVVHB5Qk1GZTNUQ2Y2OWx1dnpWd096ODlkTU1ZbWlqOFpaU0NOdTZBQW92N0lBUDJQMmpsWk9vQVB4dUsrUTk5a3IvaCt5cGlqYTUrS2lXNmRYWjMzei90Q1J3YjZSOWZzbmhiMWd4b05CSFJ5VXk3SXhVeE5VMFVzV3NDNU9EbmFEcSt1SHNTR3hlN1RtR0VIWDJETjN5enJWU24zUjZxQlZrb2ppT1didGVqZ3J0RmJRSEQxb2MrS0FzdXEydC9oQ0trRkU2R29yeDJiNkc1TDBwb0NFakJWYlNKb2tjbk1GWHFtY0RxVXh5bE1wcU8ycC9ieUZJYzhVUW1xQVNjTU94TmZOVmxxYWtjbG0wbW0xb2w3NlFUWGFVeWJCQ1A3R0dBK3ZYSXRwdDFSeUNHeFdxUDBRdlNjWjFmUzZReEVIT0lXTnFTa2orM0JOUFRTb1l5VzNsZTRqK3VKUEFsazJoRFRVbFB3M2Y5YmtBZU82Z1lEalNTRWxNcGpGbUZ4Ym1vMUtpd0RmZnI5cHIiLCJtYWMiOiIzZDhlOGUzNGY5MjAxNzk2NWJhMzJjOGQyYTk3ZTdkYmU3MzBjNGFjYzgxNTg2OTkxYmQ0NDIwYjNkZDA5YmI0IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:18:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956553066\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1473918640 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473918640\", {\"maxDepth\":0})</script>\n"}}