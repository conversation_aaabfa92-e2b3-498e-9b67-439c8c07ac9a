{"__meta": {"id": "X07a45c40b64cef199a037948de7fd61f", "datetime": "2025-07-23 18:22:55", "utime": **********.739326, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.20053, "end": **********.739341, "duration": 0.5388109683990479, "duration_str": "539ms", "measures": [{"label": "Booting", "start": **********.20053, "relative_start": 0, "end": **********.673518, "relative_end": **********.673518, "duration": 0.4729878902435303, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.673531, "relative_start": 0.47300100326538086, "end": **********.739342, "relative_end": 9.5367431640625e-07, "duration": 0.0658109188079834, "duration_str": "65.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46007504, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029600000000000004, "accumulated_duration_str": "2.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.710417, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.595}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.723313, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.595, "width_percent": 16.892}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7298188, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.486, "width_percent": 13.514}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1996127194 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294969181%7C4%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxHVWthMm4vMjJYU2ZOcmFKUFl6VVE9PSIsInZhbHVlIjoibFZqSUxua1MwcWVtVXhLYTNFNERxcXFEOFhmNTM1QzkvVGdqbHRRbmpKUmlPS3VEejhNR2lNUTN0cHZlWm1GVEFNT2daK3h6ZUpBVURoWmxybUN4U01aajkwczZLM3lDS015dEY3amVQVnB4MkRqc0s0WHQxZTRyM3FnU0h6UktTaWc2Vjh4c001anBHdHQxSElmZ1FBTHhhYjdVNjk5a3llS2pxcVpjbTNHU2hQVFp3ckpnQ0t3eUVGUnBGbUtHbU9raU5KamFUeGZ6eXk0VlRHQUsvSis1SU4rdXZESitVNk9Udmg1WHdST1YvKzNwOGpObmduUCtXY0VwcVNtQkFxNDhxcm1GNTJZbTVMZWJlUzJoM0d1S3ZXT01MT2JiTXRmQmxjZ3NXR3Zkd2lDSTNuK2FWQWR0UWxBdkQvUlNOSEw4SmI1c2RQelA5VGUzamw5dDgvcXo3a1NVUTNUT0Yvc3NFUFdjbm9sbFQxVmZ3aC9TdHRmNFlSOUJIak5HbDQ5eUtKMGtOTVhYc2ppazFVV1FKaVFLbkJKNXYySGs3UngzV1QyVm9zU3IyWTBHQjd4OWowMEduK2w2TGsrU0xKZ21Xa0wyenRRK3ZwQjVhVU00ZTBqa1hUdUs5RVp3am56L2VGOFJqSmhHTUdCMk1SYWJnY2k2dEV6SzhyT0EiLCJtYWMiOiIyZGEwZjc0OWY5NzUzOTgzM2YxOGQyMDNhZDIzNGRkYWVlMzEyZWZkNmYyNTM4ZDNhNjUyMzgyY2E0NjFlYzA5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iks1Q3hHNlBad1ZtRVdqc0FMaWpFbHc9PSIsInZhbHVlIjoiY245SVVkdThpYjRMbFJIcnZid3MzOVdWZU00cU8vVEg3OTBzYWVvMldxMENka2wxU0xjRFM5cWcvbDRxcmtEWERyNEErdmtRTzQwZHBBSitoMGhPaTY4ZlpkWWlzR2lxRjVxWGhtQW1oR3R4Z3dNU3Y5aWZDQmpKTEpNZEpBQWh6YmI3dmhEd2JvQWl4MXRXdCtFeDZwUGhOemJJUm5veDhTU1ZtaHZyMmd6aTRMWHhZaElSTjdnYjRCNXpuOFJONnlTcHRLaC9EZEluNElWaW84QzVyTHlMUnowc1JiNG5RbTFqeEFZMy8yTDFwbUJYdWl2MS85WmF2NWFmWEVPTnc3OHIxemFvY1E2UVA1MGErMCtuRklqT29xcXl2OEVTV1RaK0VYVWlkZkdWZjByYm1jcFI4a1d6b1ZUOE1WekNDYmgxam9yUDRVVXdKL0hMdlFJYlpTVEdTL095Uk96UFdreVErVk54akloVXI5Z3Y0dEhWUXRFYk42VGJFOXFpZWhPbGlBRHI1bnFEZTRiTE9MdDh6R1lCaEUzL2RKUEtVYlBmb0UwejZxT01xcElwZEFLN2czbGxmTDJnQm9PUDU2WFVqb3B6Z25Bdk5iekh5MGEvelAvRUM2TlRaRHRlOG8yN21TWXBOdE5WbTB6ZDFHWUtjV2xDais0WTNSWGMiLCJtYWMiOiJiNjg3ODI0OTZiMjlmZmM1MDM1ODk4MGU0Y2ViNDEwOTIwNTA5MzNjNjExZGUzOTM1MWM0NzU0YWRhYThlNjM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996127194\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-721471623 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xWPGJpNWbmDLkwLBtG0MdpEYkMbvhJtQ6DvLx5Cj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-721471623\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-827235742 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdpUEVDbHRBaTlZeFZFYnFMd3Jkcnc9PSIsInZhbHVlIjoiSHdKcFc5L04xdWRvMTcrZWgzNnkwa0NqZFUwUEpZWmtyRjhBRjkzUkNxNXFnam9JME5iSzdCZ1ZsNk9xOHBaOVBoTGgxb082VFRJeEh2OWMwQkN0RHhLWHg4K3B3eU91am5LU3Nqd3NFQVFnR25wa3M2TFI2OFpTWGdmVWRmMmw1ZzY0c3YvaVlzWFgya0dLVlQ0aU9GK0lBUUsxRHhIYTlvQWQ0UEdLOXo4ZGhpZml0blY3YmJCQTlibFkwT3ZYdGluL3p1VFBYcVd3eFdtOTZFandqd0lzNmhTQmdLU0hzSXRSWlMwV0dvUEtaY1NJZDJaNHBRY3R1a0dhSUtNNm5OTEpiZjVjbTZEVDhBRDhETEl5UUc5SkxDTGd1azVaRmpWWmtHb3haUnJQMzJQdUYySU9CYlZYMFlzTUJjNmZMejl1bTNjL2YwMW1mQTUvMW5KOTVvNG1FSlhWUmlUSE85MzAzRFhmUzhIWlhlTURHdTlvSGNrcUUyc2FHR0VsTXZMbU9LVlBFWkhadHB6c0hLUTFYNHNOY1JydXZrQ3VCODYyY0w5NkZvS21tMy9tWlFUazdkaXFpN0lDbW0raUdWbUl6STcxRGtqcDEranhxOXZCNEdjVC9XZnhsQSt1RUo1UXBOdnNJdlE0QXl0TERZQ3lBUlJZdUdBUDdoVVUiLCJtYWMiOiJmNGI3MDY5MmQ2OWE4Y2Y0ZjFlZWM2ZjVkNzMyZTU4NTg4YTc2MDQwNmYwMTY1ZDBmMzQ1ZGMyNGI2MzM0MjczIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVwd3ZaRVhqY0k0SFN4OHdscUhub1E9PSIsInZhbHVlIjoiUzZSZ3lPVHowSzQ5UThpWTJGbUVQamplQjRyTUtrcHM2Z0hSOWZRdVhlaHFBTjkvN2YySGNLRVIxNk93MGR1TWxRNjZ1RFkxRjhPeEg0RTd5OEVKL2ppNmh5bHpWQSszMm9FemxCQnJEUCtSb2RiL0IvMDg1U3M1T2xQK2RWN0JLcEx4MERKRGRRVmtGYzZNKzhncE95NFljalgycFVTV1hyZTh1SUdCYjZrUURrejJZRkJZUGNMWDEvWGpldG5vL0h6QVNabW1iUnJibzUrN0FhTVNZZVdsbE44TjQ4NlVGL1ZpajU5YVlNOEIxSnByNFR0cG9sV2dUMjkwdHlFc3JPd1pFOUppNVBseUdlUXlmU0ZsMGR0MG5zSVJpUmtqZXdkTkZWNmtJcEE1NCtTVTZtcUc2ZFRNRjRoRUlJRS9hY2IzT05ETUdkS3RrSW5YOGlNWmNoeEh4bFFrUVFPSGRpWFR0OWxmNythOUc2dU91UnU3MTdwRlJIWDUrV3RZSUJwanpEakFkNnhtTW5IVXFCcWFNZzNodHI1SjlhckxnQW0ybUwvbU9VS3o3NWlqaW9NV1dRWStYWG96aHVrUkpkNjJkdEZxMVVUVjJmdFB4ZXB0RDFLRkREWWVld1RNeWI1dkFEeVA4NG8vWGdpZFY4L2E5YmMrTElJelVzL00iLCJtYWMiOiJkNzZkZDVkYzI0ODdlMTE4MmFkN2E0NmM1MjI4OWRiNDY1ZTU2OTZhNTc5OTQ3N2FlNTE2MDUyMzNlYmI1YmI0IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdpUEVDbHRBaTlZeFZFYnFMd3Jkcnc9PSIsInZhbHVlIjoiSHdKcFc5L04xdWRvMTcrZWgzNnkwa0NqZFUwUEpZWmtyRjhBRjkzUkNxNXFnam9JME5iSzdCZ1ZsNk9xOHBaOVBoTGgxb082VFRJeEh2OWMwQkN0RHhLWHg4K3B3eU91am5LU3Nqd3NFQVFnR25wa3M2TFI2OFpTWGdmVWRmMmw1ZzY0c3YvaVlzWFgya0dLVlQ0aU9GK0lBUUsxRHhIYTlvQWQ0UEdLOXo4ZGhpZml0blY3YmJCQTlibFkwT3ZYdGluL3p1VFBYcVd3eFdtOTZFandqd0lzNmhTQmdLU0hzSXRSWlMwV0dvUEtaY1NJZDJaNHBRY3R1a0dhSUtNNm5OTEpiZjVjbTZEVDhBRDhETEl5UUc5SkxDTGd1azVaRmpWWmtHb3haUnJQMzJQdUYySU9CYlZYMFlzTUJjNmZMejl1bTNjL2YwMW1mQTUvMW5KOTVvNG1FSlhWUmlUSE85MzAzRFhmUzhIWlhlTURHdTlvSGNrcUUyc2FHR0VsTXZMbU9LVlBFWkhadHB6c0hLUTFYNHNOY1JydXZrQ3VCODYyY0w5NkZvS21tMy9tWlFUazdkaXFpN0lDbW0raUdWbUl6STcxRGtqcDEranhxOXZCNEdjVC9XZnhsQSt1RUo1UXBOdnNJdlE0QXl0TERZQ3lBUlJZdUdBUDdoVVUiLCJtYWMiOiJmNGI3MDY5MmQ2OWE4Y2Y0ZjFlZWM2ZjVkNzMyZTU4NTg4YTc2MDQwNmYwMTY1ZDBmMzQ1ZGMyNGI2MzM0MjczIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVwd3ZaRVhqY0k0SFN4OHdscUhub1E9PSIsInZhbHVlIjoiUzZSZ3lPVHowSzQ5UThpWTJGbUVQamplQjRyTUtrcHM2Z0hSOWZRdVhlaHFBTjkvN2YySGNLRVIxNk93MGR1TWxRNjZ1RFkxRjhPeEg0RTd5OEVKL2ppNmh5bHpWQSszMm9FemxCQnJEUCtSb2RiL0IvMDg1U3M1T2xQK2RWN0JLcEx4MERKRGRRVmtGYzZNKzhncE95NFljalgycFVTV1hyZTh1SUdCYjZrUURrejJZRkJZUGNMWDEvWGpldG5vL0h6QVNabW1iUnJibzUrN0FhTVNZZVdsbE44TjQ4NlVGL1ZpajU5YVlNOEIxSnByNFR0cG9sV2dUMjkwdHlFc3JPd1pFOUppNVBseUdlUXlmU0ZsMGR0MG5zSVJpUmtqZXdkTkZWNmtJcEE1NCtTVTZtcUc2ZFRNRjRoRUlJRS9hY2IzT05ETUdkS3RrSW5YOGlNWmNoeEh4bFFrUVFPSGRpWFR0OWxmNythOUc2dU91UnU3MTdwRlJIWDUrV3RZSUJwanpEakFkNnhtTW5IVXFCcWFNZzNodHI1SjlhckxnQW0ybUwvbU9VS3o3NWlqaW9NV1dRWStYWG96aHVrUkpkNjJkdEZxMVVUVjJmdFB4ZXB0RDFLRkREWWVld1RNeWI1dkFEeVA4NG8vWGdpZFY4L2E5YmMrTElJelVzL00iLCJtYWMiOiJkNzZkZDVkYzI0ODdlMTE4MmFkN2E0NmM1MjI4OWRiNDY1ZTU2OTZhNTc5OTQ3N2FlNTE2MDUyMzNlYmI1YmI0IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827235742\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}