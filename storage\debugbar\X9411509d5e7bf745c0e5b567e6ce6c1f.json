{"__meta": {"id": "X9411509d5e7bf745c0e5b567e6ce6c1f", "datetime": "2025-07-23 18:20:49", "utime": **********.917449, "method": "GET", "uri": "/receipt-voucher/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.426841, "end": **********.917464, "duration": 0.49062299728393555, "duration_str": "491ms", "measures": [{"label": "Booting", "start": **********.426841, "relative_start": 0, "end": **********.820597, "relative_end": **********.820597, "duration": 0.3937559127807617, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.820607, "relative_start": 0.393765926361084, "end": **********.917465, "relative_end": 9.5367431640625e-07, "duration": 0.09685802459716797, "duration_str": "96.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47718672, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.receipt.create", "param_count": null, "params": [], "start": **********.877846, "type": "blade", "hash": "bladeC:\\laragon\\www\\تشليح ملفات\\erpq24\\resources\\views/voucher/receipt/create.blade.phpvoucher.receipt.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fresources%2Fviews%2Fvoucher%2Freceipt%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.receipt.create"}]}, "route": {"uri": "GET receipt-voucher/create", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptVoucherController@create", "namespace": null, "prefix": "", "where": [], "as": "receipt.voucher.create", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=54\" onclick=\"\">app/Http/Controllers/ReceiptVoucherController.php:54-59</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0033699999999999997, "accumulated_duration_str": "3.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.855187, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.81}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.866808, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.81, "width_percent": 14.54}, {"sql": "select * from `users` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8698251, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:57", "source": "app/Http/Controllers/ReceiptVoucherController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=57", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "57"}, "connection": "kdmkjkqknb", "start_percent": 86.35, "width_percent": 13.65}]}, "models": {"data": {"App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]"}, "request": {"path_info": "/receipt-voucher/create", "status_code": "<pre class=sf-dump id=sf-dump-1549328281 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1549328281\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-910323231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-910323231\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-273355895 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-273355895\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294847789%7C9%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikp0M0NkODd3Nk9LTDVpRWkrdDdvbXc9PSIsInZhbHVlIjoiRzBuOEgvVEpHMktmU1hpOXp0YTlLSW1vMWN3Q1BWSVJtYlRiK21INzFOQ08zcm1qNlV6amFxWDdaZmU3dThueHFxY256N0JMUWV5SG5qaWl6ZXExaVdUTW80eDJzdElEYlFkcHRZbXk5cUR1NmhsL3dnbG4yWUpQNXdXOVNPNlpDbFVJMWtwd1o5Yy9vTHNhV1hSZFJiSE1WMEFsSmpqNHo0c1Z0QlBhMXBGdDhEZS9PdW84TGxvaGNJQTRRRlF3TGJVeXpFb29BaHNOSTRpZlRpUVRtRkJVeDgxNTZXQkN2NmM5bEF4WjIwenV1WWVBOXpOS2hVS1diNnNuZkUxenN1RmJRbHRPajhqOStub3BSS2pJaitLQ05IMTZxZllHQUViaW92UXk2QVF5a0R4N3prUDIrL1Y3dW1XcC9UaS9hVUcyZWM1UXVBamdYV1J3VGxNR3dyQW1OOE5WMThjVmhxUHBnQ0kxRjloVTgvdWMxakgvNG5Zb3lCd3VsVWJSNGpueEVKYi96V3ZnMEpmTmRTd0ljNmJpTW9VcE9SM3R0UStYTG9aeVRQaklBRlBPZ3dpV0Fud00xNW42cFZZQU4zWDNvNGsrb0ZYQkJvZ2hvNm5OZUZxM3N3ME9uYWc5MjZQcEJzamM1bHpzMVlFU1M1aStNNy9yZmR3Qmo3bE4iLCJtYWMiOiI0ZmJjOTAwNTMwNGNmZmEzNDAyZDA5NzhlNjg2NmNjNDExYWM5ZWJmNmZhMzMxYmIwYjIxMjljYzZiMDdhYTc4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdnM3Fld291aWI5NE92V0szc0FFaXc9PSIsInZhbHVlIjoiVnlpRmlUNmNvR3pqMTlmM0l0KzZtSm0zRzRCNno3L21jb09oTkZVajdoR0htcjN0TUp5R01qNWcyZkZwSHUxRGE5Z0VXd1RCbnFTdlpRQVFFdmtKZTYwVnJwMHlCRHg2eDl2M2pxdHhRbjBlTitnUE5Bc09NVFhUc0JGREkwSE5DamR0TFI2NWkxNEk2RGJ3enpYWUZrUjRVSlVYZDhYRkhXTllla2JRTGhXZ01Xc2FpSmNSa0UvUjk0MTZlWXl4bjh0aW1UdndNWnFnMnNGdHBjcUFReHFJdVc1Y0xJTE1aS0hKdDdYRlJldUpsUm5vMGd4Y28yQ2VzN2NVNWZRUDFaNmFpV3BXbGxvWlVJWjBCejZuWExhN0JjK0I1TkZrMDYxMVBDUE1NRndNbHROd2s5b0RCakVYcjlwQUJSb2xVYkJFMnpydDBSekU1eU54cXZRczVWNmF2SlhudkFyV1F0QXhYdGhLSGh1OEx3YlhEV01XN1o4N1VEenExSEhrR1FCNU9hYVVrRGR6QWRZekNKNTJxejdUczBNenV5TmdNRXN6MENHOEhXVDR2L3pIalc1RlIxU0JsM0VNbHBBUUVvZ1pER29ZaDlFUHRndnBlTWtHN1JVUGYzeHJ1WE5GK1hyclg0VWFUdnRNOFBrV08vRVU0L1NUbVJ5akRSOFYiLCJtYWMiOiI3NzY1N2MxMmMzMjY0NGIzYzU0YjZkNDNlNjEyYTdjYjVhZWZjYjQ3MjI0Y2RlZWIxYmU5ZmViMjlkMWQyNDBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1899349521 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1899349521\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1228586482 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:20:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRxcmk4RW9tazhIY1djb2R2YStmQ2c9PSIsInZhbHVlIjoiSytzUFcrdUdwd2VoQVdFUE4rRHp2UnF4RlU2TVI3RitKVE5yZ01DNDhBb01FZWJnZkZScU5MWVEvZy9PT1lkMFA4RmE4Nk9rd3NVV1RTZDc3Mlh3RGhFbFF1NWhOK3p4ZFRNaWx0WGovanpYOFFLSURhQTJlQ21rQVRuZDVGUEh4L2xmWitOVlRvbEU1SnB1VFpnU2xUUk9VQTFXQ2pRdGZSKytvanFNeUgrWWdBM2E4TVBzOXNMRDc0ejhvRUVJejR5ZDZnRVoxMzdQOHNORW5ObklTVllSVEZXSy81T2h5TmhYYWYxMFNicWVzQXpaOE1rSGF3enpVVllWa0VJZ3hKQ04yLzErcWoyaHEvSXVRNng0Z2xmTy9DQUVwTmNSTUpHb01Vclh3V2pQL2N1NlQ0WGJyc3QvSXBkTk4xTTZDYSs3dlNnQlhzbE5lRFpDeE91SWkreGRYR0w0enFKQm5rZUFkVTFXeUVmZEgweEVDMDdPdENhYnJldUpCVlhYOVowSGhSSlNqVThPSjVCQjRvNm1ndHV4YXZCR3VRU1A1Y08wM1dIZUo1cGlPNm1FTHA3eEFQUktQMjREYjNNZzhNWVpiTlIrdEVOTlI0cHdSQjRaaWxqUVVTMXpTUFpobklMWlY0ZkNJV0FiazF4YVFCd2JqQWlOcndHdXpPSjgiLCJtYWMiOiIwNjcxZmM0NWE3YTFmZWFjMTZjOTg2OGY5YWI0NTM3OThlZDQwMzZmMTVlNjAyZmEzZDM2NDkzNjY3MDZkNmY4IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImUwYlBUaGNKcWJkSERJT0p0OWJFU2c9PSIsInZhbHVlIjoiVzh4Q2JwMSsvYzdGL212elFyaEg0UlpKR3lTLzVtZGtxUDA2Uk5lSVltQkRpaEtZRnlKZ2svelF6VlFWb0hadmdRRWs4VG8wTCt4S2h2d1ZPMDZDQU9RVW54NFNwcGg2OFZ4WVVFdEtrSllnbm5MYkoxVkV1Q2RWdEJKWS8vOFl4ZUNGM2dVeDVXaUhLZkpLUGVDejJMcGZLeDBIZTgrcXdQREJ2eFZuVGFhK0xSZXhWY01yZDU5dUYrRkx3RC85ZFROTWZxQWpZZ1IrVVlSZ0ZUVkdKVTZjSkxRQXBONkMvM2lYNG1zem1QVDJJRmdWY2JRTGtXTEV1YU4rcWVsZm1ONjkwNmxNbmRIRkdjcEhWV2J1OWxJcllDZzhGWWtqZUdSU2gwVHJKN0hUUkVPdlVlSzhCK2hiYzZVdHFQQ3lPK0dEZGFXUHhHZUl4V2FzdzllemNzdzJUWlZ1V2pudkxPZzZ0Nll1dWhObWtJd3dMOTJsL0U1emZOZHF5b3ZPZ1FjM0JKUFgrM1FJUEgxL1N3SlcrVEY2ckJ1Y3VVMElMM3ZJdkVqcXJQZHhlSzloNlRJVk5EUWl6NUpkVzY4YmF3YWlqekRmaWRzT1cycmV1eldubmNLNzJOV1RTVnlPemVKZ1RGTzdidzlQWTRyRysySlFqY2NrSW1oR3phMFYiLCJtYWMiOiJjZDc2ZmY5OTQxYWQ3Y2Q3YTVhZTRiZjYwNzg3YzBhZDM2MWQwMWYwMTU3MDdkYmNkYzc1OTZjZjAwZjYyODBjIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRxcmk4RW9tazhIY1djb2R2YStmQ2c9PSIsInZhbHVlIjoiSytzUFcrdUdwd2VoQVdFUE4rRHp2UnF4RlU2TVI3RitKVE5yZ01DNDhBb01FZWJnZkZScU5MWVEvZy9PT1lkMFA4RmE4Nk9rd3NVV1RTZDc3Mlh3RGhFbFF1NWhOK3p4ZFRNaWx0WGovanpYOFFLSURhQTJlQ21rQVRuZDVGUEh4L2xmWitOVlRvbEU1SnB1VFpnU2xUUk9VQTFXQ2pRdGZSKytvanFNeUgrWWdBM2E4TVBzOXNMRDc0ejhvRUVJejR5ZDZnRVoxMzdQOHNORW5ObklTVllSVEZXSy81T2h5TmhYYWYxMFNicWVzQXpaOE1rSGF3enpVVllWa0VJZ3hKQ04yLzErcWoyaHEvSXVRNng0Z2xmTy9DQUVwTmNSTUpHb01Vclh3V2pQL2N1NlQ0WGJyc3QvSXBkTk4xTTZDYSs3dlNnQlhzbE5lRFpDeE91SWkreGRYR0w0enFKQm5rZUFkVTFXeUVmZEgweEVDMDdPdENhYnJldUpCVlhYOVowSGhSSlNqVThPSjVCQjRvNm1ndHV4YXZCR3VRU1A1Y08wM1dIZUo1cGlPNm1FTHA3eEFQUktQMjREYjNNZzhNWVpiTlIrdEVOTlI0cHdSQjRaaWxqUVVTMXpTUFpobklMWlY0ZkNJV0FiazF4YVFCd2JqQWlOcndHdXpPSjgiLCJtYWMiOiIwNjcxZmM0NWE3YTFmZWFjMTZjOTg2OGY5YWI0NTM3OThlZDQwMzZmMTVlNjAyZmEzZDM2NDkzNjY3MDZkNmY4IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImUwYlBUaGNKcWJkSERJT0p0OWJFU2c9PSIsInZhbHVlIjoiVzh4Q2JwMSsvYzdGL212elFyaEg0UlpKR3lTLzVtZGtxUDA2Uk5lSVltQkRpaEtZRnlKZ2svelF6VlFWb0hadmdRRWs4VG8wTCt4S2h2d1ZPMDZDQU9RVW54NFNwcGg2OFZ4WVVFdEtrSllnbm5MYkoxVkV1Q2RWdEJKWS8vOFl4ZUNGM2dVeDVXaUhLZkpLUGVDejJMcGZLeDBIZTgrcXdQREJ2eFZuVGFhK0xSZXhWY01yZDU5dUYrRkx3RC85ZFROTWZxQWpZZ1IrVVlSZ0ZUVkdKVTZjSkxRQXBONkMvM2lYNG1zem1QVDJJRmdWY2JRTGtXTEV1YU4rcWVsZm1ONjkwNmxNbmRIRkdjcEhWV2J1OWxJcllDZzhGWWtqZUdSU2gwVHJKN0hUUkVPdlVlSzhCK2hiYzZVdHFQQ3lPK0dEZGFXUHhHZUl4V2FzdzllemNzdzJUWlZ1V2pudkxPZzZ0Nll1dWhObWtJd3dMOTJsL0U1emZOZHF5b3ZPZ1FjM0JKUFgrM1FJUEgxL1N3SlcrVEY2ckJ1Y3VVMElMM3ZJdkVqcXJQZHhlSzloNlRJVk5EUWl6NUpkVzY4YmF3YWlqekRmaWRzT1cycmV1eldubmNLNzJOV1RTVnlPemVKZ1RGTzdidzlQWTRyRysySlFqY2NrSW1oR3phMFYiLCJtYWMiOiJjZDc2ZmY5OTQxYWQ3Y2Q3YTVhZTRiZjYwNzg3YzBhZDM2MWQwMWYwMTU3MDdkYmNkYzc1OTZjZjAwZjYyODBjIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228586482\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1339275501 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339275501\", {\"maxDepth\":0})</script>\n"}}