{"__meta": {"id": "Xa105fdbe77cd455db577b072ab2c3eb2", "datetime": "2025-07-14 18:33:42", "utime": **********.700639, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.236345, "end": **********.700656, "duration": 0.464310884475708, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.236345, "relative_start": 0, "end": **********.624893, "relative_end": **********.624893, "duration": 0.3885478973388672, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.624902, "relative_start": 0.38855695724487305, "end": **********.700657, "relative_end": 9.5367431640625e-07, "duration": 0.07575488090515137, "duration_str": "75.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46003656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021459999999999996, "accumulated_duration_str": "21.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6552832, "duration": 0.02029, "duration_str": "20.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.548}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.686017, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.548, "width_percent": 2.563}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6923752, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.111, "width_percent": 2.889}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1242725567 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1242725567\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-888206377 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-888206377\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1465237149 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465237149\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-916615422 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752517896149%7C46%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imo3ajNZdUhUaUx6ck4vV1dsbVcvRUE9PSIsInZhbHVlIjoicktFZGlZZkx5aEdJemEyck1sNVhHYWY3MjRHbHNzYWtEak1HWkpkaXp4WTJMamp0cFRJdWM5enBzYzhyMGozeUduTUJpck9sRmlKSGZmZkdhaTNoWEtBaDBRVjNoay9CdnNNWVExZGQ2R0pXaXFzMkxpZHQ4VGtkZGxMcWJFK0o1RUh6bklPSGtOZVB2THpOMFJmVmxaUWNQRDkzeFc0cHcvRTQrTThCUldtL0k2YVRXcEZwTHFJa1VmVnAramZOWTNTc2s1RWhPTFBIT0lWUTdrRmFqdVlUVkdCMG9KM1UvTDhGcVFjM08yeVZhK3VDNVkxakFraUE2NXlLbUhLKysxNm9pYk53Vk0vRk9jUW1oZUkwemRJTVpkajFUTGhVekUyUFRTK2ZEU3cvRDc1cTEwRVBWTkMzWGRyc3dOak5UUTc1SitiRkRKY1djM2xHeCtEc0hvTGc3dTRyQXZZaDRwTDAxN3F2M1hBazlVaFpTUGQ5WFM3ZGhYUEU4TUhvdnptYjI2MHJaQlYwVlhSZVhJSkFYNlFpejQ4eFVHa0hMOE96UXQyajJEaHdOOEIzS1pTWFZRa0ZzZUx4eXUrR0dFY01raG1zSkVKbEV4M0dacnAzVDh2Y2pUQVNyakptT1E0Z3hqZVhwbWtYRExQUDNqNkNpSjlwN1Z2YTNETW0iLCJtYWMiOiI0YjYzZTg1ZmVkNTlkNTQwNTE2YzQ1ZmNhOWE5ZmI1ODE3MmYwN2Q0MTFlNzVmNmVmZDFiNTY1NzdkZTVkNWU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InlOc1phMERIMGdlek0rUHBPcDRMaWc9PSIsInZhbHVlIjoibGxyWjZLczhuVnAyTXJteHZHNHhOaW1qREpITkJ2azJ5QmM0QUQ0Z3hkL24wdUNaZDBtUGhjVktvbEFYVU9pSXRCVnluZE5aU2JKUG1Oa2xrMk9iQVVwWHpoWU1lQVV5RjhwK1M0clo2SElPMkV4TVRlQkx3VFNOOWhCQ0l5b3RNRjh6b0RyZWlaam1ZVDNrc0h2TEZFdngxeklkdU1nNnprdHErZ1Z0Y2diUXFFb2wyZzFZamFjbXZVdTJSMldNMEJyb1Rmc2ZUTkR5SG1LWDVJcHhSRUdDMkVacXYyK01LbUhtL1FwOXkxSUlXSDFLVmFmTFFoZVY1czlVSE1hMkxJcHJ0MTVEbFJSZG9KWmYwTno5Y3pwWU9kdmhoVVJyaGZ4bGV3ZkVsN2hOY1RTeVFEbU0wZVliNHZtTzdFQ1hFMzArNlR5QW1aUHczU3YzcnFPbEMyaTM2SG9ZMmVWV2Yya2w1bDFWTkRpTWZMTmdlUDMvV1UxaXU4bEVKMnBHenlFM3Z3ZzNlNGlvU0lhSkhvd1FHTmliaGhvOWRwb25YRXFwOXVIU0tzcjFiVUdJMUQwcjVoOTNhS2pTZmMvRWM4OEZPR2R3NkNJUkcvY0pCQjBWS29vSVF1ZWJtRnRDaVBUYW1UMnJvdDRmdE9kTjZidEpLTWRCUWhzR1hKeXYiLCJtYWMiOiI3NjNmMjk3NDI1N2M1YjRjZmNiMjk3MWU3ZWEzMDA2MDdhMGZmY2Q2YmE4ZWFjYzk1MjUxYTE2ZmI2YmEwMTcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916615422\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-15967897 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15967897\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-226912860 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 18:33:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZNenNPL21QNTgzaGpQUW9nRjlDU0E9PSIsInZhbHVlIjoiK1RNa0VhSGcybWpUaWVZNW1oTmdPK0t0RzZHeVpRMFRCMTJpakNIaUYwT2h6TE4xdXBlTEQrZTVwWUkyNG10Zk5jbVNSbUFlTS9lbTdOQkN2NFhPUlJseWRJSTVVYXZDZDFhY1dia0FVUnJUU2hKcmU5RVYxQ3pieWFQTGpnc3RDSkFIM1hNYi9Ma0d6Y2Nhdnd4ZE9vVjZGK1FGbU1LMzVaVktTV3k0c0J5bWFjWXVEM3ZUWGFrYTZsdytaaTQ2WndYWUdjQXZpTkpzWit1bkdKZGhVR084M0drbUUrcitCZVNBdkFyRklVT3NDWmNVY3lZU0gydTd0WU9rRW1BbU54R1ZraEZKTWVlNHlUT1VIVFRJT0x6ODgxbno2dldLZk5Nam84SjY0QWdlRW9GR1dVeUtncHlCS1c2MElkUVVOdHdKSkdUVEk3dElLSzZlNm5ydXoxejRvcnRPUCtPdVNyQUdSbDB5aVdaaStXS055TnoyQ2xkQ2FFRWdHVno2ZEdWMWtBYzFyRy9yTTYwdmVxVG8vOWpUbnFNVnlDSG5Lb0t3dFpDVFpCKzA2M0RYS1BBUStxeDlzdCtPbnBFU1MvV2FkU293V29GdUFPTTE4UE9sMmRLMXArMDNxZjNDMDNHV2RnTE5TV2wyWEREb1VYbXM0R1dFa0RlTFRiZ3IiLCJtYWMiOiI4NGEwY2Y3ZjBjNzllNmYyNWI5N2EwZjQ4NTE5YWQ3ODdjOGI5MDJiNjExNWI3NjYwZDE0ZjFiNDNiOGJhYjhmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:33:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBCc282dTBtMGhueWZhZFZ5VDJ0dlE9PSIsInZhbHVlIjoiZGpSTFM0M1VZakI5MlhqTHhqNGIzaFFHc0x5ZHhCWGI4dkV4NG1zNXhkZS9kbGllWWt2VkJoVXBuRnpORjIzbm9LMWJBRkF0a3p2dmt6VjRqL2c3bVhUZW5qSjJZamNUZ1E2aTlXVStBU016alY0YVlONTVVWnFRckE2TWFmTjcxejFCbHE3bXIzUjc3azhiYUVSNDZ2UzRnTk96bktoblFJVWNBS1VxK1BlMW1xMENqVkJndm9PWWVDODhVSFZtOGYxTjlsZGNyazVoNmIxbVZ3dmk0bHNsYWl6aDRBbnRwTStOdGpvd3VoYjl3enRsZit5ODJ6bFZtOVA0TG1SU2IxalE1RWFlbTFaWnBvMTkzZEwyNlNwdjlMYk1WQ2U3d0ZQekpwdGw2Z1BMUFFiRVoyT1pQTGhzYUhxZWc1RGhjZTdUbnQwWC9GZkxncTVrQmZUSVhyY1h0emIxb3c4cnZNcmdxN0k1NEFuK0o5WVhMZnh4b3Y5QnNNTUF0cmk3M3NSaHo2WDAvZkVhV3pHMGdZU3NsVGNJa0pqdCt4cWwzY1hlQU9mOTFXNVR0NzZNaHIwd2JoT21aRUhBeHZhUkpZNkU3NDRjUlRaQjNOdFp6eEhZYmNrTEtmTVgxRWZCYnYyd0cvOG1rcVJFbTQxOTZrWWZFbzlsZ0VqMWJJS1YiLCJtYWMiOiI4ZjYzZWE4MmU5ZjA5NzEyM2EzNWJkYzk0ZWMzMTBjODAzMGQwYmM4MmJmZWNkZGExMzM4MmFmNWM0N2FjMmRkIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 20:33:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZNenNPL21QNTgzaGpQUW9nRjlDU0E9PSIsInZhbHVlIjoiK1RNa0VhSGcybWpUaWVZNW1oTmdPK0t0RzZHeVpRMFRCMTJpakNIaUYwT2h6TE4xdXBlTEQrZTVwWUkyNG10Zk5jbVNSbUFlTS9lbTdOQkN2NFhPUlJseWRJSTVVYXZDZDFhY1dia0FVUnJUU2hKcmU5RVYxQ3pieWFQTGpnc3RDSkFIM1hNYi9Ma0d6Y2Nhdnd4ZE9vVjZGK1FGbU1LMzVaVktTV3k0c0J5bWFjWXVEM3ZUWGFrYTZsdytaaTQ2WndYWUdjQXZpTkpzWit1bkdKZGhVR084M0drbUUrcitCZVNBdkFyRklVT3NDWmNVY3lZU0gydTd0WU9rRW1BbU54R1ZraEZKTWVlNHlUT1VIVFRJT0x6ODgxbno2dldLZk5Nam84SjY0QWdlRW9GR1dVeUtncHlCS1c2MElkUVVOdHdKSkdUVEk3dElLSzZlNm5ydXoxejRvcnRPUCtPdVNyQUdSbDB5aVdaaStXS055TnoyQ2xkQ2FFRWdHVno2ZEdWMWtBYzFyRy9yTTYwdmVxVG8vOWpUbnFNVnlDSG5Lb0t3dFpDVFpCKzA2M0RYS1BBUStxeDlzdCtPbnBFU1MvV2FkU293V29GdUFPTTE4UE9sMmRLMXArMDNxZjNDMDNHV2RnTE5TV2wyWEREb1VYbXM0R1dFa0RlTFRiZ3IiLCJtYWMiOiI4NGEwY2Y3ZjBjNzllNmYyNWI5N2EwZjQ4NTE5YWQ3ODdjOGI5MDJiNjExNWI3NjYwZDE0ZjFiNDNiOGJhYjhmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:33:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBCc282dTBtMGhueWZhZFZ5VDJ0dlE9PSIsInZhbHVlIjoiZGpSTFM0M1VZakI5MlhqTHhqNGIzaFFHc0x5ZHhCWGI4dkV4NG1zNXhkZS9kbGllWWt2VkJoVXBuRnpORjIzbm9LMWJBRkF0a3p2dmt6VjRqL2c3bVhUZW5qSjJZamNUZ1E2aTlXVStBU016alY0YVlONTVVWnFRckE2TWFmTjcxejFCbHE3bXIzUjc3azhiYUVSNDZ2UzRnTk96bktoblFJVWNBS1VxK1BlMW1xMENqVkJndm9PWWVDODhVSFZtOGYxTjlsZGNyazVoNmIxbVZ3dmk0bHNsYWl6aDRBbnRwTStOdGpvd3VoYjl3enRsZit5ODJ6bFZtOVA0TG1SU2IxalE1RWFlbTFaWnBvMTkzZEwyNlNwdjlMYk1WQ2U3d0ZQekpwdGw2Z1BMUFFiRVoyT1pQTGhzYUhxZWc1RGhjZTdUbnQwWC9GZkxncTVrQmZUSVhyY1h0emIxb3c4cnZNcmdxN0k1NEFuK0o5WVhMZnh4b3Y5QnNNTUF0cmk3M3NSaHo2WDAvZkVhV3pHMGdZU3NsVGNJa0pqdCt4cWwzY1hlQU9mOTFXNVR0NzZNaHIwd2JoT21aRUhBeHZhUkpZNkU3NDRjUlRaQjNOdFp6eEhZYmNrTEtmTVgxRWZCYnYyd0cvOG1rcVJFbTQxOTZrWWZFbzlsZ0VqMWJJS1YiLCJtYWMiOiI4ZjYzZWE4MmU5ZjA5NzEyM2EzNWJkYzk0ZWMzMTBjODAzMGQwYmM4MmJmZWNkZGExMzM4MmFmNWM0N2FjMmRkIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 20:33:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226912860\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1053438402 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053438402\", {\"maxDepth\":0})</script>\n"}}