{"__meta": {"id": "X981c45fbda2ddde40150649e1f2f8b9b", "datetime": "2025-07-14 14:40:35", "utime": **********.311403, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752504034.832704, "end": **********.311422, "duration": 0.4787180423736572, "duration_str": "479ms", "measures": [{"label": "Booting", "start": 1752504034.832704, "relative_start": 0, "end": **********.225997, "relative_end": **********.225997, "duration": 0.3932929039001465, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.226005, "relative_start": 0.39330101013183594, "end": **********.311424, "relative_end": 1.9073486328125e-06, "duration": 0.0854189395904541, "duration_str": "85.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46007040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025809999999999996, "accumulated_duration_str": "25.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.253186, "duration": 0.024579999999999998, "duration_str": "24.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.234}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.294426, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.234, "width_percent": 2.712}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.30422, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.947, "width_percent": 2.053}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-997898630 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-997898630\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-87305923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-87305923\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-824409555 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824409555\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2061 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752504011682%7C21%7C1%7Cl.clarity.ms%2Fcollect; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; __stripe_sid=96dcadf1-5924-47d1-802c-3f7a26447696e3a6c0; XSRF-TOKEN=eyJpdiI6InAzNVEwTlBGb2c1cnhjc0N4U0YyclE9PSIsInZhbHVlIjoiYU1JUi96bVo3bXdaUnB3RXVuZWxUbTVKejVUdVJyVndEYzhrcTFML1JaUUtYWkNYbDJ4ek5jaGhCdnJ6OTB3UVlaWi9vR094Mkh3RU91eHhXZE1EVVFoaUZwZ1d0V2U2RzY0RDkzbm9MZzdTUEd2UTNTbUY4MzA2NFpQSjN6TEd3SFpVQTBmdm1DSVk4aGUydnp5TXQ0N0dOSjlkZlozVW05ZWM0djg2MXpJOGVYeHZqRHNKclBpeE5jYkVLclNEd2k3dnYveEU5UWJMdkMvRk10SnQ4d29oQThxTkUrdklxa0Q4cWdZZytrNU9jbnE3aDhnalVjM25hbE5IVVpaKytUQXRHeCsxZDI4M2NMdDBLdmR0eWpaK0pCVWovRUJXNXFDdzUrM01yYjBPc29kMmVMWHJZdGxRQlIwZ1BvZm5rcmlyb1ZhTklINVl2R0VaeURORGZwQlc5ZFBxUThraGhQMFdVR3daT0x0RnkzajhvTElPRzU1MzdpdHAxUVRwVzcvREVhNlU1VC9adUtYNFRzclFiQXNxMkN4ZEVqamREenprd3dqVER3Ym5wNFdPbWc5SXJsM3JHbnN3cTY3REJ5Kyt3WFNZckEyVml0SkNPR2E4L2V5Z1hsakV1cXBSblVmQTlza1BQYkVpMXRyTnU4Y3BYQk9WQTlNRW9ucy8iLCJtYWMiOiIzZmVlMzRiNjFhOGU0Njk2ZGVlM2E0M2U4YTY1NzJlOTFkYjIxMjE1M2ViNDY4ZjEwMWFmZTVlNWI3NTE0YWE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkR5TkI3UkZsVGxLY3VwOVZFVU5WQUE9PSIsInZhbHVlIjoiRmpKL1Mwb241YTdVcldvZEJWL3lXMmhtcTBWWUpQSWtZQ1Z4RUdzcjl3MHhGNCtpVi80NUZ2QUxiV1B1UGYwNGFrUUpSTjAwTVpHVVc4aEZLL2ZkcUVsZ0FXMVNJT2JPczhPb3BVNkVzTityRDNvK1VaRWZDNGZLdTlpWXkzbkJtbWVaNWdBZ0tGSE9NLzQxaWZCU2hyQWZnS3psYVJQcnNHb0NYdElPYTViQ1UzZWNHSGE5Z1lpQld6eXd2dUpxN1h5d0lJSGpPOW16YXg0T2t0Z1VkeDVLbzdjNU9qcHExZWsvQlBnOURPaFNza0V0Ynd3eCtCWVB6R2ZYRTB3MlFjYTk5c0crQThEcXloS1ZJU1lDVlU2V3dmY0V2V2dTa3VpZWtJRWdycSsxSC9xMVRlNmJNVG1ZNU51WTVHRXc4KzJCaVlsMmVKZjNpTzBpYWs1YkpCUEoyb2hWYlE4ZURQVUlJMmhlQzJ3VzllV2JvMG1lKzNoRHlXR3RZaFdEV0VkNlNQZ0tUSVFMdVIwYStVWjc1azdleHN5TlNTUUszdFJxMGdocUhkVzVIY1dhcmY3REtLZmlwamZMOEk5cDdtUTc0NTJ3UkVPb1p2MTBMK3JxY0wrUFQrUUUwaGY5b0lJbjVRcWhZR1psaUs4ZFlLeGEwU0FsUmNnMG42YlIiLCJtYWMiOiJiYzc3NTFhZjNkYzhiYjk5MGQ3YWQ2Y2RmMTIxMTdmYWY0ZjM1NTQyOWEwY2E5MDI0YTk1OTM5Yzg5NDI0MjA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-218387102 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218387102\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-623551984 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:40:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpKRFp6ZW5rQ3NyMFp4R20vWWE4ekE9PSIsInZhbHVlIjoiUTFFYi9oaHlSYTd4TWI5QUVwbVRUVFVtVmhIR1o3UU5GaHRxK2t4TEllVmNyV3FDc3Flay9oUVlaUWxRQ25HVVBTMlkra3YvUmZOSjlMVFh5RlF1c0dnNzBrZ2t3RFBQaGVGVW1ycERmZTZ2Y3BjMHlZVEUvdmtZR3NZYW1saUlNN0dXVDlqT1I1WUhCZ2c1WkZJcG93MmhzazhJc3ZWL25CQUdoUVFyWnMyMXd0L25vSDQyNEp4eW1lM1J0ZFZPekoxK3dtNHluMjBhWHBsd3o4T3ZsU1pwUm85ZWJJK3h6S3lhRXR4amtvQmN4eGVaQWV6T0hJZ1J4c1lxL2w5Zy9udFM0S3FCVUpBNTI4ZldEZW96U2VJZElmeXFwSjk2UlllUGxJaklQcUlpZDllVjRtNHMxM25GNm5TMFVZQkhFQlpEMmVFa1YxWGFuWTJvVDVrK0JoakpveXA3U2FTWFYxTWp2cHlxazhMMWZwdmJtWFNLYnBYNStIbXorMUtwdXVZVlVSa1MrNC9lV1JaQUl6Z1ZaTUxwNDl0OXcrSEFyUUtpbGFlMnZ5SXV5R1FLV3BPSUFsQUZkTHhsclpIT2RPTTJPUlc2eHkyZUtlMW55a3Z2MDQwR0w0Ny91eW9vdGovcWZST2NuVVJ0TEo3TU8wRUxjUmZaWWlRb3QrZ0wiLCJtYWMiOiI5NjgwZjliNTY5N2I0NmFlODY4MmZkZDEzMzRjMTAwMWVjOWVhYzYzZWMxNDhiMGIxMjI4NDZhODk2Nzc2M2NjIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNSOVJsS0hiWGRvTFd4WXErWmZKK3c9PSIsInZhbHVlIjoidFBlZ0gyeGNUelo2M3MrVlpOMU9EOEZSVmpDaXhCNHpNeiswOWdtUVI3djhpaXYwSFlTSWszOU4rUmtGQm0wcDlwMVJ6RnByNXRzZjZnUGxNbnhOTUVNRi9EeFhIZFM0WG1oS1F1SlhwYTd0TXlGSXRhcTI1TXdJYnVVdHZDYktkcFlOak4wK2hGN0MxQUZzWG9pVSsxYlZtc3ExNzNHdHE0L1ZYMWZrdksvbFVyOUpDUk1SbkVxVHV2M01EeDZOM3V4VlUyZHNOdUVmTE95b0s4NjhrWjZDaWRiMGswaXlCZkZTT3NGcmRtSUJKZnVEY2RUMzNDU1pDcmxRRTliYXJEVWphK3NoVzQwUUJNa3JRZWM4OU11WXE2MXVlREtGS3dGNGNTSThBMFZzUUkrUnAzallEZUlpeHVVQ01obytVL2M4ODAvQzJURC9pUFlRNjl6aXF1Z1RyTE16ZzdNNlRrUUVyTFZPUitkMFhkaG1xbVdCRitWNUZLd0MxOWZkQW1XQ2hOdGhxRFovNTZvTG80RWJkejg2Z3dVNHo1S3pUSGJlU0JoQXN5OVRyZUdvdllNRVh0Y0Q4b1JrSHY5N2JXWGVhaENGWVJSd01icnlmUVhlOWFkcDFaOVptSmxzcU1FYjJBdFR1em9YRFZHR2ZSeEZpamlmVkNnalpMcnAiLCJtYWMiOiIxOTdlZjc1MDIzZjRlOWZmY2RjZjI5YWJhOGYzNDQ5ZmY5YzA5M2YwMWJiODcyZDZhYzc0YmJlMjY1NzgwNzc3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:40:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpKRFp6ZW5rQ3NyMFp4R20vWWE4ekE9PSIsInZhbHVlIjoiUTFFYi9oaHlSYTd4TWI5QUVwbVRUVFVtVmhIR1o3UU5GaHRxK2t4TEllVmNyV3FDc3Flay9oUVlaUWxRQ25HVVBTMlkra3YvUmZOSjlMVFh5RlF1c0dnNzBrZ2t3RFBQaGVGVW1ycERmZTZ2Y3BjMHlZVEUvdmtZR3NZYW1saUlNN0dXVDlqT1I1WUhCZ2c1WkZJcG93MmhzazhJc3ZWL25CQUdoUVFyWnMyMXd0L25vSDQyNEp4eW1lM1J0ZFZPekoxK3dtNHluMjBhWHBsd3o4T3ZsU1pwUm85ZWJJK3h6S3lhRXR4amtvQmN4eGVaQWV6T0hJZ1J4c1lxL2w5Zy9udFM0S3FCVUpBNTI4ZldEZW96U2VJZElmeXFwSjk2UlllUGxJaklQcUlpZDllVjRtNHMxM25GNm5TMFVZQkhFQlpEMmVFa1YxWGFuWTJvVDVrK0JoakpveXA3U2FTWFYxTWp2cHlxazhMMWZwdmJtWFNLYnBYNStIbXorMUtwdXVZVlVSa1MrNC9lV1JaQUl6Z1ZaTUxwNDl0OXcrSEFyUUtpbGFlMnZ5SXV5R1FLV3BPSUFsQUZkTHhsclpIT2RPTTJPUlc2eHkyZUtlMW55a3Z2MDQwR0w0Ny91eW9vdGovcWZST2NuVVJ0TEo3TU8wRUxjUmZaWWlRb3QrZ0wiLCJtYWMiOiI5NjgwZjliNTY5N2I0NmFlODY4MmZkZDEzMzRjMTAwMWVjOWVhYzYzZWMxNDhiMGIxMjI4NDZhODk2Nzc2M2NjIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNSOVJsS0hiWGRvTFd4WXErWmZKK3c9PSIsInZhbHVlIjoidFBlZ0gyeGNUelo2M3MrVlpOMU9EOEZSVmpDaXhCNHpNeiswOWdtUVI3djhpaXYwSFlTSWszOU4rUmtGQm0wcDlwMVJ6RnByNXRzZjZnUGxNbnhOTUVNRi9EeFhIZFM0WG1oS1F1SlhwYTd0TXlGSXRhcTI1TXdJYnVVdHZDYktkcFlOak4wK2hGN0MxQUZzWG9pVSsxYlZtc3ExNzNHdHE0L1ZYMWZrdksvbFVyOUpDUk1SbkVxVHV2M01EeDZOM3V4VlUyZHNOdUVmTE95b0s4NjhrWjZDaWRiMGswaXlCZkZTT3NGcmRtSUJKZnVEY2RUMzNDU1pDcmxRRTliYXJEVWphK3NoVzQwUUJNa3JRZWM4OU11WXE2MXVlREtGS3dGNGNTSThBMFZzUUkrUnAzallEZUlpeHVVQ01obytVL2M4ODAvQzJURC9pUFlRNjl6aXF1Z1RyTE16ZzdNNlRrUUVyTFZPUitkMFhkaG1xbVdCRitWNUZLd0MxOWZkQW1XQ2hOdGhxRFovNTZvTG80RWJkejg2Z3dVNHo1S3pUSGJlU0JoQXN5OVRyZUdvdllNRVh0Y0Q4b1JrSHY5N2JXWGVhaENGWVJSd01icnlmUVhlOWFkcDFaOVptSmxzcU1FYjJBdFR1em9YRFZHR2ZSeEZpamlmVkNnalpMcnAiLCJtYWMiOiIxOTdlZjc1MDIzZjRlOWZmY2RjZjI5YWJhOGYzNDQ5ZmY5YzA5M2YwMWJiODcyZDZhYzc0YmJlMjY1NzgwNzc3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:40:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623551984\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-869603256 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869603256\", {\"maxDepth\":0})</script>\n"}}