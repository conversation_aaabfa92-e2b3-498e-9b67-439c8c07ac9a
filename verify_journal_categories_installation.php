<?php
/**
 * Verification script for Journal Entry Categories installation
 * This script checks if all components are properly installed
 */

echo "<h1>🔍 فحص تثبيت نظام الفئات</h1>";

// Check if files exist
$files_to_check = [
    'app/Models/JournalEntry.php',
    'app/Http/Controllers/JournalEntryController.php',
    'resources/views/journalEntry/create.blade.php',
    'resources/views/journalEntry/edit.blade.php',
    'resources/views/journalEntry/index.blade.php',
    'resources/views/journalEntry/view.blade.php',
    'database/migrations/2024_07_14_000000_add_category_fields_to_journal_entries_table.php'
];

echo "<h2>📁 فحص الملفات</h2>";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ {$file}</p>";
    } else {
        echo "<p style='color: red;'>❌ {$file} - غير موجود</p>";
    }
}

// Check specific code additions
echo "<h2>🔧 فحص التحديثات في الكود</h2>";

// Check JournalEntry model
$journalEntryModel = file_get_contents('app/Models/JournalEntry.php');
if (strpos($journalEntryModel, 'category_id') !== false) {
    echo "<p style='color: green;'>✅ JournalEntry Model - تم إضافة category_id</p>";
} else {
    echo "<p style='color: red;'>❌ JournalEntry Model - لم يتم إضافة category_id</p>";
}

if (strpos($journalEntryModel, 'public function category()') !== false) {
    echo "<p style='color: green;'>✅ JournalEntry Model - تم إضافة علاقة الفئة</p>";
} else {
    echo "<p style='color: red;'>❌ JournalEntry Model - لم يتم إضافة علاقة الفئة</p>";
}

// Check Controller
$controller = file_get_contents('app/Http/Controllers/JournalEntryController.php');
if (strpos($controller, 'ProductServiceCategory') !== false) {
    echo "<p style='color: green;'>✅ Controller - تم إضافة استيراد ProductServiceCategory</p>";
} else {
    echo "<p style='color: red;'>❌ Controller - لم يتم إضافة استيراد ProductServiceCategory</p>";
}

if (strpos($controller, 'incomeCategories') !== false) {
    echo "<p style='color: green;'>✅ Controller - تم إضافة معالجة فئات الدخل</p>";
} else {
    echo "<p style='color: red;'>❌ Controller - لم يتم إضافة معالجة فئات الدخل</p>";
}

// Check Create View
$createView = file_get_contents('resources/views/journalEntry/create.blade.php');
if (strpos($createView, 'category_type') !== false) {
    echo "<p style='color: green;'>✅ Create View - تم إضافة حقول الفئة</p>";
} else {
    echo "<p style='color: red;'>❌ Create View - لم يتم إضافة حقول الفئة</p>";
}

if (strpos($createView, 'updateCategoryTotal') !== false) {
    echo "<p style='color: green;'>✅ Create View - تم إضافة JavaScript للفئات</p>";
} else {
    echo "<p style='color: red;'>❌ Create View - لم يتم إضافة JavaScript للفئات</p>";
}

// Check Edit View
$editView = file_get_contents('resources/views/journalEntry/edit.blade.php');
if (strpos($editView, 'category_type') !== false) {
    echo "<p style='color: green;'>✅ Edit View - تم إضافة حقول الفئة</p>";
} else {
    echo "<p style='color: red;'>❌ Edit View - لم يتم إضافة حقول الفئة</p>";
}

// Check Index View
$indexView = file_get_contents('resources/views/journalEntry/index.blade.php');
if (strpos($indexView, 'Category') !== false) {
    echo "<p style='color: green;'>✅ Index View - تم إضافة عمود الفئة</p>";
} else {
    echo "<p style='color: red;'>❌ Index View - لم يتم إضافة عمود الفئة</p>";
}

// Check View page
$viewPage = file_get_contents('resources/views/journalEntry/view.blade.php');
if (strpos($viewPage, 'Category Information') !== false) {
    echo "<p style='color: green;'>✅ View Page - تم إضافة قسم معلومات الفئة</p>";
} else {
    echo "<p style='color: red;'>❌ View Page - لم يتم إضافة قسم معلومات الفئة</p>";
}

echo "<h2>📋 قائمة المراجعة</h2>";
echo "<p>للتأكد من عمل النظام بشكل كامل:</p>";
echo "<ol>";
echo "<li>تشغيل الهجرة: <code>php artisan migrate</code></li>";
echo "<li>أو تشغيل ملف SQL: <code>add_journal_categories_fields.sql</code></li>";
echo "<li>إنشاء فئات دخل ومصروف من إدارة الفئات</li>";
echo "<li>تشغيل ملف الاختبار: <code>test_journal_categories.php</code></li>";
echo "<li>تحديث القيود الموجودة: <code>update_existing_journal_entries.php</code></li>";
echo "</ol>";

echo "<h2>🎯 الخطوات التالية</h2>";
echo "<p>1. اذهب إلى إدارة الفئات وأنشئ فئات من نوع 'Income' و 'Expense'</p>";
echo "<p>2. جرب إنشاء قيد جديد مع فئة</p>";
echo "<p>3. تحقق من عرض الفئات في قائمة القيود</p>";

echo "<p style='color: blue;'>📘 للمزيد من المعلومات، راجع ملف: JOURNAL_CATEGORIES_README.md</p>";
?>
