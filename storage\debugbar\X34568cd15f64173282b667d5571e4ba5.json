{"__meta": {"id": "X34568cd15f64173282b667d5571e4ba5", "datetime": "2025-07-23 18:21:44", "utime": **********.131947, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294903.655309, "end": **********.131964, "duration": 0.4766550064086914, "duration_str": "477ms", "measures": [{"label": "Booting", "start": 1753294903.655309, "relative_start": 0, "end": **********.070526, "relative_end": **********.070526, "duration": 0.41521692276000977, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.070537, "relative_start": 0.41522812843322754, "end": **********.131966, "relative_end": 2.1457672119140625e-06, "duration": 0.06142902374267578, "duration_str": "61.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46533992, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00316, "accumulated_duration_str": "3.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.102404, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.759}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1140509, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.759, "width_percent": 22.785}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.121135, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.544, "width_percent": 16.456}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-934041722 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-934041722\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1795462242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1795462242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-59629654 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59629654\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-697518419 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294893959%7C18%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNpK3FQTEZDaDQzUWpaY2hrWnp3bkE9PSIsInZhbHVlIjoiZGlmMWU0L1ZoWTNQellRc3NlV05NNlN1NjV6eElMOXRYbHFyc2dkWlVoaFVJaDh1NWQxQUdPNE11eU0yQXNXTEwvMC9iRGdUeXpidHpNMEViRDhDK3RzamlDdXZ2M1JLc3ZmV3cxZ29vT2M1LzY3L09BTG9RRnkzbzNzbG5qNndveDdscDBhVjUwYzVLMWdpVkxnSWViUTRmbHd1bXV4bExiT0pGcnk3MG9BZlNYdzFDbXQyVDZvajlmQXYzK1Z5V0ZsMmE0RDR1c2ptSHViNGtUdnhtWUhSeWJPWjZmMFE3dVNXdDdqSkJlM0U2Qm9zSmw4WXdXeFhOQWVqeWdJTDNtR3RwWE5vSUd1ZWlkb0JIZVA3SysyanVBMjNtSkFTd2czMUg1bVV6KzFaeC9Ec1gweFNqVGxPd214S0ViRDdhY2puVDg1dVlCanZSNW5tUnIyek1VYTR0cU1RQVRBN1R6eFEvd2dBOW5XOG9CbTljQnBKcEVNTUZ1N0Uya2R4c25LZnZMVk1iaWNyRmxVTFZ6ZEhIVWZUSWpic3R4TmV0QjRzanBVTkU2QUpVRmZ2bHlSejEwTGRnbVRXd3lESDVlU2tTNjVpOUc5dXRMZVNhWlBuSlJmOG1Hd0lFZi9xdEJmdkxGeC94d05oRlliYzVQZmN3MU1tVXNYYWduangiLCJtYWMiOiJiMTk0MDZlZjJlNjA0ZTcxNTg0MjJiNTRhM2QyZWFhYWYxMTFkOTQwMjJlOTY1NWNhZmQ2NzgxMTMzYmIzNmVhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZhOGxWTXpVMDM1MytXZDlRRnV6c1E9PSIsInZhbHVlIjoiY09DeFNIQUdkRUVucVhTazQwWTEzeDB6a0kyMlJ2KzIwZytOdGFja05qSEtoRHFldzJSZ3MzWmVGTmplWU4ybk1HVHdWR0pQZkhYdFpobTJJak1zV3NMSUpzdFJzTjU5akNUVjkwQVBuZnJRYnZDV2VIdlNJc2tiSkJCeWVPR2ZMQjVIcDNmUStPVDc3dXZ4Z0RST1JycmVERnZheko3YVVFUFYzSmJweUxnc2x4QWtZYnVXTnJNbDM0T3pNTGs0Z1FPa2EzZC9udHp2TVEvWi9OVlliWHNrWS80MnhQcmFlVkhQWlI4ek5Ja041dFJ4WndZSVJvN3dONmVGKzMrbUliaWZDVzJHN00wV0ZHaFdRZTlXUCtPT2FNMitNOHB0dGc1R0hyZXZXV3M2RHFHUDVhZnp4NjQyK3R4aTJLWWt3bDV2enR1QXg2MmYwMlRVdlRSRzZGYlQ5WFhleHIrWk1PT3FoTnM0WGFDSG41aVZ5U1NRRFRaVy9IUlVSdDV2V1E2N21wdmIwOEZuZ3NIczYxRTdWMEJSUDJaMk9JRmxzbnRvYkhiQ2RUQUswc1M0M2Z0VUhxMFdLR05XNDRKWXpzaGhuVTNnZ3ptUjZNU1R2elQ2a1NnMFZHcUNhRmc4NmJOcTZXSWxqQnhpcmpkRkRkMmhHZGwzUGJCODBJNGgiLCJtYWMiOiJmNmJjNGRkMWZmYmJjMGM3YzhmNWNmMGJjODQ2ZjdmNmY0MzEwOWY4ZDFjMGU1ODdhZGNjZjI0NTA5ODFmOTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697518419\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1825376029 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825376029\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-380443424 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZjcmtvRDlCNU9DVTZ3VUp6bTBJMEE9PSIsInZhbHVlIjoiQ1A2ejg3aUx4UDVoNnZYc0VYc0xKVjdrQ1o1UkRWMThUL1k0OStPcWNiTWVPbDdTTFRwQVU5L3ZoRDlEUllnV3VRWGtsa1BDbmhuZ1kvbXI0NzVDK0RsRi9FdjdXeDk1NVJhYi90bytEbWRHNGE5bzMzNkU4V1E0SUNBbW14eG5wZkdPVTZNaWhmY1d2bkxma2pVdGx2VjNYVSs4ZzRqcERKa2pEYmhNUFN6OURLOFB2WmpJaTZnRVoyamt1cGdoTGRYcitMY29EV3VDV0NyYXlBR2tPTVQ2bkVIY2R6RzZLd3JXL2E1OXdJU3ArTjg1ZEpxeXRaWnVFL045WEpndzNSa3hHN0FZUnMxUzNZTEFDc0s1RncrUTRaRUZzVll6VlU4UXpNNk1vbEsyQVdGblQ0cWZma21JK1FiS0M0czk5N1lSTVhkZjBuNERIRDNXSWMyK25rN3hpUWZaeEdjWFdaTU0xK2UzTnBMSVJ4Q0JTTm90VU1LcFR0SXcvOHhjYks5ZHMxek9LbFN5NnJpVmF1YVRjL2l4M1hZTHlpcElsSkovVThPMEJFMHFiaFNoVXJWazd2by9qOVpOYVl5YlVuVk5mYmdHTEpxZkdYMlI1aEdYTzVGTExDclZKd29qemwwVk16dk5yOWlQbGFpaVBlTHpWYitUZG9sb01wUFUiLCJtYWMiOiIyYzFmMTllMmUxMTU2NWZjOGRjNGQ3ZThhNDc0NjNiNmViYTlmYTgwN2E4Y2E5OWU3ODlmYTE3MWU2ODUxNGUwIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRrRlNDZ1F0UXp0a1JaNzJtMTVGbmc9PSIsInZhbHVlIjoiZFBJTkIxM0NNYlBVT1VIaC80aUdvUk5qYkZYMTUwV1FvdXFFSW53NmZYMWRnQ0Z6aHlPdlhEZG40UUhWU3dsbVJWeERCaWhkQ2todUVmektwQndlNWxpbDVXdlBUbGpsQWZBV0tsRXFjNUgxa01iaVJPcURQTVBHOFVIVXk2U1Bxdlg4QmZ2Vm5jRlV3bkF6aTIwczRqRU44R2ZsaitJS2hTNk5pbXY5WWZLQkF4RnRpY3J1MjVKamhYWDloNEcySmVjODZ3REJSTEtNSFR6azk3dEVnMzRKNlBpNGJYazRqcHJQZmdlK05rWGpLeGpQWks3RzVkV1dnTHQwOElRNzBLWWhxWGhINmdSOUx5YXRKU01Fd01mQzlZeGV6WmMraDJCSWNSUmFlTjYxOEFJMlNnU3pDd0pjQmNVdzlIMVA0bnErcEUwbmZnbEt5bXJlZlFqNXp4TWp2Sm1DY2pnaU5ZaFlGNVM3T0RVOWp6MTZHQXd3R3kvR093OVRDNU4yNDUrL3Jya0ppTWYwaXROOFhKdTJGSThJdXdwN0ZzR3BZREIxS1QvNEQxRGxjL3Fyd3hlWnFCeWN1Z2RSdXQvdlM5VFNmUzFwMkhZc0pmSG5jcTNHMnE1bWdEK0VqTktzMzZxdkgvdWNIRjNKdFhTQ3B6S0tWYzlaZmNJT0tKb3QiLCJtYWMiOiJhNGJlYzVhMmQ1NTI3MWZjNTZiZDdlNzZiNjU4ZDkyOTM5MmEyNTkyZTMxOTU1YzcyYWE4MGUzMTcxZTNhZTkzIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZjcmtvRDlCNU9DVTZ3VUp6bTBJMEE9PSIsInZhbHVlIjoiQ1A2ejg3aUx4UDVoNnZYc0VYc0xKVjdrQ1o1UkRWMThUL1k0OStPcWNiTWVPbDdTTFRwQVU5L3ZoRDlEUllnV3VRWGtsa1BDbmhuZ1kvbXI0NzVDK0RsRi9FdjdXeDk1NVJhYi90bytEbWRHNGE5bzMzNkU4V1E0SUNBbW14eG5wZkdPVTZNaWhmY1d2bkxma2pVdGx2VjNYVSs4ZzRqcERKa2pEYmhNUFN6OURLOFB2WmpJaTZnRVoyamt1cGdoTGRYcitMY29EV3VDV0NyYXlBR2tPTVQ2bkVIY2R6RzZLd3JXL2E1OXdJU3ArTjg1ZEpxeXRaWnVFL045WEpndzNSa3hHN0FZUnMxUzNZTEFDc0s1RncrUTRaRUZzVll6VlU4UXpNNk1vbEsyQVdGblQ0cWZma21JK1FiS0M0czk5N1lSTVhkZjBuNERIRDNXSWMyK25rN3hpUWZaeEdjWFdaTU0xK2UzTnBMSVJ4Q0JTTm90VU1LcFR0SXcvOHhjYks5ZHMxek9LbFN5NnJpVmF1YVRjL2l4M1hZTHlpcElsSkovVThPMEJFMHFiaFNoVXJWazd2by9qOVpOYVl5YlVuVk5mYmdHTEpxZkdYMlI1aEdYTzVGTExDclZKd29qemwwVk16dk5yOWlQbGFpaVBlTHpWYitUZG9sb01wUFUiLCJtYWMiOiIyYzFmMTllMmUxMTU2NWZjOGRjNGQ3ZThhNDc0NjNiNmViYTlmYTgwN2E4Y2E5OWU3ODlmYTE3MWU2ODUxNGUwIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRrRlNDZ1F0UXp0a1JaNzJtMTVGbmc9PSIsInZhbHVlIjoiZFBJTkIxM0NNYlBVT1VIaC80aUdvUk5qYkZYMTUwV1FvdXFFSW53NmZYMWRnQ0Z6aHlPdlhEZG40UUhWU3dsbVJWeERCaWhkQ2todUVmektwQndlNWxpbDVXdlBUbGpsQWZBV0tsRXFjNUgxa01iaVJPcURQTVBHOFVIVXk2U1Bxdlg4QmZ2Vm5jRlV3bkF6aTIwczRqRU44R2ZsaitJS2hTNk5pbXY5WWZLQkF4RnRpY3J1MjVKamhYWDloNEcySmVjODZ3REJSTEtNSFR6azk3dEVnMzRKNlBpNGJYazRqcHJQZmdlK05rWGpLeGpQWks3RzVkV1dnTHQwOElRNzBLWWhxWGhINmdSOUx5YXRKU01Fd01mQzlZeGV6WmMraDJCSWNSUmFlTjYxOEFJMlNnU3pDd0pjQmNVdzlIMVA0bnErcEUwbmZnbEt5bXJlZlFqNXp4TWp2Sm1DY2pnaU5ZaFlGNVM3T0RVOWp6MTZHQXd3R3kvR093OVRDNU4yNDUrL3Jya0ppTWYwaXROOFhKdTJGSThJdXdwN0ZzR3BZREIxS1QvNEQxRGxjL3Fyd3hlWnFCeWN1Z2RSdXQvdlM5VFNmUzFwMkhZc0pmSG5jcTNHMnE1bWdEK0VqTktzMzZxdkgvdWNIRjNKdFhTQ3B6S0tWYzlaZmNJT0tKb3QiLCJtYWMiOiJhNGJlYzVhMmQ1NTI3MWZjNTZiZDdlNzZiNjU4ZDkyOTM5MmEyNTkyZTMxOTU1YzcyYWE4MGUzMTcxZTNhZTkzIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380443424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-486969300 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486969300\", {\"maxDepth\":0})</script>\n"}}