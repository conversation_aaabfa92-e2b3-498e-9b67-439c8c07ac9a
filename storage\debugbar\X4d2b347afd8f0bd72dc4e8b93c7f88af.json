{"__meta": {"id": "X4d2b347afd8f0bd72dc4e8b93c7f88af", "datetime": "2025-07-23 18:21:09", "utime": **********.782174, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.291554, "end": **********.782188, "duration": 0.4906339645385742, "duration_str": "491ms", "measures": [{"label": "Booting", "start": **********.291554, "relative_start": 0, "end": **********.723146, "relative_end": **********.723146, "duration": 0.4315919876098633, "duration_str": "432ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.723158, "relative_start": 0.43160390853881836, "end": **********.782189, "relative_end": 9.5367431640625e-07, "duration": 0.059031009674072266, "duration_str": "59.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46521464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00276, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.756754, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.377}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7672591, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.377, "width_percent": 17.754}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7729871, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.13, "width_percent": 10.87}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-506435521 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-506435521\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-572587250 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-572587250\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1987129260 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987129260\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1383238146 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294850069%7C10%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVZdlZmb0E5bGFydWNxWThjYklNZnc9PSIsInZhbHVlIjoiMmtPNENvVndOWUtpSHYwQzU5NUFhOWwzczhwRlBtbXdkTGVDZ2JQR2FZaXBOQmNCTm03cUdMdTdGNFZTblNjc3lRZWtmRkhZOFNHS2RaSEhnYlFzY0VxeThHYnZub3BwWFRxUURDMFNaSW9hcEdsRzJHNTg1YlZhNG5TWVJ0OVdmbHBlS2RPdzF1Z2I1Q3k2blpzZ0xVUXJwYnlTb3dQczRJVFZhelJBNFlUWlE1MjV0V2xtMTU3OENSR1VQVnJ3T09jR3hsZFJITWRMa3lVbEJPSTN4RVNnbTExQWZDQVU5LzRDSlY2aS9pZHgveDFLMGdvb2hkZ2pFeXdHMlFVT2tlNVNKUkIyMEFORHdKSXVFdUVVNzhNd0VlRWFwMGk5WTljU2I5cmtkN0c5N3Bpd3J5cVh4N2l4eEI0UVZKeEtZQzhaTzZGUkhmWm9kbjlWc3BlUlJteEVRTU1nUjNNdFc3dVEzbFc5TCszUWQrNjFqYUh2dHdoRm02emduNlNZVVRSd3VLVTF0ZkZVVjdhWWZORDZxN2tDV05yVlJZNkNpRkFJeVVIZEY0VE9kRms2RHVRZE5VV0RmbXlTOElNSm9rRldSaTQzNDFER0FXTkxISlpGRjNWU1BDWWN1V0hGNjlIKzd2RG1EamMxTXEzZmRUR3dMRXVDTkozYUZtTysiLCJtYWMiOiI5NGY4ZTZkYTQ2NWM5ODY2NTA4YmZjYzIyYWFhMjhmMTIxN2E2OTliNmE2ZWQ2NDAzZWM5NTA5MWI2NDdjNGIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InRuazBqVWRkVS9kWGJUWlB4VDBhMkE9PSIsInZhbHVlIjoiT0pTUTAraXF5NVJ1YUhUWUpoWmU4YVBrNFY5UHJGMEFMSGljQ2JvUmp0amdpZXNOcmJhdW9ZdVkvUFpmMm1ZUzd5VmZKRkJYRmJPb2dkQ0pzdUVrcEc3cUZoSUlwVGxuUytxUjRxb0VRYjNzZWxOLzFsdzVkOUxkQ1JCOW1EeU9FU1NOYW1FYVMrcE9HT3o0NFZpK1lUOXFEMFhqWm1TSjh5SGNjQXh6NURLcnpXT3JFM2pXSXQrNXR3NXJPYW80QStLUWE1UTNTYUdqcjlnKzJWZzFJVkhYYTBrOG5UVlBISkdyUmZXK0Nta1hhRWZ3U0NIbTdYcHlsaUdYb0FkQWtESGJoM0NPbHduaVlRZTUwMVFQaDFxQWhIUjZPZ0wxZ0ZVVWl0ekcwZlR2U29CSC9zMEJ2TWE5K2oxMnN1Q2dxVGx2ZmViZXhpZGpINHRvU3hCcm1iMTlYUFZTdEszbzBxZVd2YjFMK3BJc0dLNHpHTFVLUDV0aktXV1k3a2VPMUJNQlYrRE8yUEJjcnYveGVmRTFEd0dSZWxvT0dqZXRyU1JLYnBjQlZRN0hWc1dycWQvMXNYdkt0aXlNcnRiN2VFL0Y2QmY1MHU3d3l6NUNjOStSYUJ6eFkzRWRNaTRCalF1Y0tHYlFGM3BwaU1wZUg4OWc0Q1hxQ3RoRmN0cmgiLCJtYWMiOiJlYzQ5Y2FjZGNhNzRjY2QyZjQ0MTQ1YWNiODE0YzA1ZTVmNzU0MzhmMDc4YmI4MDBlOGQ1YWFjZTM1MGY1MjcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383238146\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-677016644 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677016644\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNaejI2TG5kZnpPdU93aVV3WEhWSVE9PSIsInZhbHVlIjoiUWE3RXF6bkl5b0pZL2I2Umh1UWNjbWhwVWNsV3ZJbnV0bzNrZTJQZ2VZVWJhdlY4MVNucW9zNm82UjE3L1NnRWxsVlNvS21yUlFsbEp0d1Zvd3Z0TjJwdFJ0ZmMrTTNVMC9YRWpYbGdWaEJLMnNNS2ZTNHJaNnhQMlA0cW5IYUNsZEs4aFBHMkpiRUhzR2I4ZVFxb0JYeS9BRWUrbU15NTVIQVU5OGFzL2tZWk9ta2tTWDZjVzR3YWd5VVRpTXVYMmhGN3Nud1ZQQXhsSGhSVlNqbHdLMVI4cHB3Z3hzZWQ5MU1jWFZ1V3M5dzJQSXl0eDhrcDlITlQrRjAyb1FjWG1zR0UxT1BlSWxacDZZd2xwZ1dvZGNIcC9HeitML29DUHBYYURGMERicDErMlVrZnRueVZZSStuNkp1N29yZng3T2VIeGVxK0o2bmN1RGFJRlM4OU1peUFxdlZFcGdNbUxaMWZLb2YxL1pnR2ZUa3Zucyt3WDNBMmQ0L1dWNjJZWHhOYzFuUkxWVUtieGsxMG42K0VxQXdCTW16MnN4SEQ2K2VxRmMya3RSL3lRZEFnczhuZ1ZLclo4ZkY0a0xjTHRKSDVuQWMzS05mMUkrS0t1UVFBVnhNd2FlQnVXc1kydUlTaEpwRnJwQWdYZWFVbENPYXJzVSswZTZaeEs4bloiLCJtYWMiOiIzNDcxYmZiNWYxNWZlYjI4NTBkZTM5OGJhMWJjOWZmMDEzZTZkZDE5OThmMWIxOGVkMTkwYTg5ZDE1YmI2NDBiIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5CcmJtM0c1dFllY2hhTWV2b21uUEE9PSIsInZhbHVlIjoieHQrTFZ3QW9yT0pTdzhCdzRGRWZKYjMrY3ZxbmpmTUZZeENoTnZlZVhMckNFaHd0MitUN2dCc204U0NFck9JZGVRUGRRWVZ3YmZmdG9ieHJnZzVHR3l3bzZNREt6QUFOeGwwcVZUVnQ0UkQrbHNtWmFhNWR4NnRuTysyVkdUeEVsTzl5aFdQSjhVRG50dExyODQyN2NCdnc1S2s3RHJCRUh0Rm5tY3hGczRZMXc0T25LTU96c1FuSVZoN0treVl4d1NxdzJvZ1J4QXJmbkpTYnFkNXgxLzNod3JBa2V6Y1NycGVMN1VPekpOSnZRU2h2YlJ5MHhoNUl6UzUzLzhVT0NreXoySEdqV3pBdzdaM1dsYUJVWndwOEdjdTR2NFhtMzFlc2J4TVpUOUlsakNDaEhnaGp6MFFMdVhwckNDWVhVRDJQSTZGQWdzMEVRNnBlME1aUGNnbjFkT3dRMFd2ZVA4MC9USHRVamNQYUhyZXZsNTEvNzY2azVHZWVJWEh4aHV2TWlMRFFyTUV3dzVFTDA4QTRLYlE4RWh0YVNva3ZYa2xBZ29Wc2NZWHRaRUppOUI3OGF5NUlrcHVScHJRWXNhR3E1bGlhL3o3b3RSeVNWZmdZa3FKV2l6Nmh6UnE3eVkxVFlGK0NMNUVlNnhZdS9OWmM0NXRqRnpsSStzVjEiLCJtYWMiOiI0NzM1MTkzZjNmYjEzZDAyOWYwNjg1NDRhZjJjMDllYTQ3N2UyNTA2ZjgwZDg1ZGFjMmE2OGM3ZDlkNmIwNzVhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNaejI2TG5kZnpPdU93aVV3WEhWSVE9PSIsInZhbHVlIjoiUWE3RXF6bkl5b0pZL2I2Umh1UWNjbWhwVWNsV3ZJbnV0bzNrZTJQZ2VZVWJhdlY4MVNucW9zNm82UjE3L1NnRWxsVlNvS21yUlFsbEp0d1Zvd3Z0TjJwdFJ0ZmMrTTNVMC9YRWpYbGdWaEJLMnNNS2ZTNHJaNnhQMlA0cW5IYUNsZEs4aFBHMkpiRUhzR2I4ZVFxb0JYeS9BRWUrbU15NTVIQVU5OGFzL2tZWk9ta2tTWDZjVzR3YWd5VVRpTXVYMmhGN3Nud1ZQQXhsSGhSVlNqbHdLMVI4cHB3Z3hzZWQ5MU1jWFZ1V3M5dzJQSXl0eDhrcDlITlQrRjAyb1FjWG1zR0UxT1BlSWxacDZZd2xwZ1dvZGNIcC9HeitML29DUHBYYURGMERicDErMlVrZnRueVZZSStuNkp1N29yZng3T2VIeGVxK0o2bmN1RGFJRlM4OU1peUFxdlZFcGdNbUxaMWZLb2YxL1pnR2ZUa3Zucyt3WDNBMmQ0L1dWNjJZWHhOYzFuUkxWVUtieGsxMG42K0VxQXdCTW16MnN4SEQ2K2VxRmMya3RSL3lRZEFnczhuZ1ZLclo4ZkY0a0xjTHRKSDVuQWMzS05mMUkrS0t1UVFBVnhNd2FlQnVXc1kydUlTaEpwRnJwQWdYZWFVbENPYXJzVSswZTZaeEs4bloiLCJtYWMiOiIzNDcxYmZiNWYxNWZlYjI4NTBkZTM5OGJhMWJjOWZmMDEzZTZkZDE5OThmMWIxOGVkMTkwYTg5ZDE1YmI2NDBiIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5CcmJtM0c1dFllY2hhTWV2b21uUEE9PSIsInZhbHVlIjoieHQrTFZ3QW9yT0pTdzhCdzRGRWZKYjMrY3ZxbmpmTUZZeENoTnZlZVhMckNFaHd0MitUN2dCc204U0NFck9JZGVRUGRRWVZ3YmZmdG9ieHJnZzVHR3l3bzZNREt6QUFOeGwwcVZUVnQ0UkQrbHNtWmFhNWR4NnRuTysyVkdUeEVsTzl5aFdQSjhVRG50dExyODQyN2NCdnc1S2s3RHJCRUh0Rm5tY3hGczRZMXc0T25LTU96c1FuSVZoN0treVl4d1NxdzJvZ1J4QXJmbkpTYnFkNXgxLzNod3JBa2V6Y1NycGVMN1VPekpOSnZRU2h2YlJ5MHhoNUl6UzUzLzhVT0NreXoySEdqV3pBdzdaM1dsYUJVWndwOEdjdTR2NFhtMzFlc2J4TVpUOUlsakNDaEhnaGp6MFFMdVhwckNDWVhVRDJQSTZGQWdzMEVRNnBlME1aUGNnbjFkT3dRMFd2ZVA4MC9USHRVamNQYUhyZXZsNTEvNzY2azVHZWVJWEh4aHV2TWlMRFFyTUV3dzVFTDA4QTRLYlE4RWh0YVNva3ZYa2xBZ29Wc2NZWHRaRUppOUI3OGF5NUlrcHVScHJRWXNhR3E1bGlhL3o3b3RSeVNWZmdZa3FKV2l6Nmh6UnE3eVkxVFlGK0NMNUVlNnhZdS9OWmM0NXRqRnpsSStzVjEiLCJtYWMiOiI0NzM1MTkzZjNmYjEzZDAyOWYwNjg1NDRhZjJjMDllYTQ3N2UyNTA2ZjgwZDg1ZGFjMmE2OGM3ZDlkNmIwNzVhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1361880926 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361880926\", {\"maxDepth\":0})</script>\n"}}