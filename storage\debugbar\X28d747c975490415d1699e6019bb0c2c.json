{"__meta": {"id": "X28d747c975490415d1699e6019bb0c2c", "datetime": "2025-07-23 18:21:23", "utime": **********.422051, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294882.9766, "end": **********.422067, "duration": 0.4454669952392578, "duration_str": "445ms", "measures": [{"label": "Booting", "start": 1753294882.9766, "relative_start": 0, "end": **********.353072, "relative_end": **********.353072, "duration": 0.37647199630737305, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.353081, "relative_start": 0.3764810562133789, "end": **********.422069, "relative_end": 2.1457672119140625e-06, "duration": 0.06898808479309082, "duration_str": "68.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46519304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01928, "accumulated_duration_str": "19.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.379303, "duration": 0.01789, "duration_str": "17.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.79}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.406241, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.79, "width_percent": 3.527}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.412154, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.317, "width_percent": 3.683}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1483068784 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1483068784\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1625298198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1625298198\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1105156255 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105156255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-108241834 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294880614%7C15%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJkY2srLzF1K0tJcVJ4eElHK0VwSEE9PSIsInZhbHVlIjoiZEVUUVYvYnd4ZnpFaSsreW9FN2UyRmMrdmxGQVh1WUZNWFVLVDVpNVVYd3daYzExM2tpK3hJMlBkSy9ITEhnK0lGWHNxTE4vYVpFM0JhTkJDV0ozYkdmbGJvWlp3SXcrQ2ZWSkwrQ1pkcGtvSUxRdG13TmRXQWpPa3Zta2N0LzFkcDdkMFdDbnZqc0RPS1k1SDYwR3UrZDJHU0I5TEQvaVVpMmZGMmk3NGxWdzRiaGl5V1MyR1hNTkk2VWo5RGFid0RIcVpsTGR5WExuRmtJb2ZoZHhaZ0NqR3dtK0xmeFdjTy94MmVma3ZtQXFEaVpGN0NPem5wTTRCVVE3NWJ6Sy9xTlNKUWJPV0hIM0V6dUhKemFvcU52WEQrTzF2UTJYaWNZakhSSDJiVUlFVVZjOFE4ZXduZkJoRWc3TXV1SU1oQ0hIemJlMkw4dnBmWUtIcmNOekJ4ejBVL1A4VGVlWlJnQk5hNmxuZWFvbnc2S2l0SzNYYzN0RDNVb0VCUUdGNDNHSmI4aWRDRlZsVHNzZHF6UUVMQk1HamRiemdIemNFSFVmTUtZcXFNWjMzc3ZHeDByS0x1RmgvaEdyNGI5aVZIdU03aG9mS0dYV3A4Qmx5NUNpRVQ0aUJ3RmV1bFVkdmxWcThiY0pOSmE0STNROFpSNnNNSlVXTmRLUGxGZWUiLCJtYWMiOiJlMDY4NThjYjQ1YjQ5NGQ4OTEyMTBkMTFmNDlkMjQ5NTlkNWUzOWJmMmVkZmQ4NTA3NDk0YzQyNjMxMGNjYTAzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdjVktmK2xoM1lFOE5XTjlZZlVBRlE9PSIsInZhbHVlIjoidTNMd2oxRmF5cnRpRWkyRlVOMnJ6RmkwaC9NRnlsT2NnMWlsNFRPN2I2SGxqdklnbVgxcGlrM21la2M4VFkvZ20yQVFhK3gvU2hrNDMrdWdUcHdUTWt3R3owV0VxN1pJQlovUlIyUVFpWTdINUFnK3BSRmJNdHVJdThHcG1UUnd6aVhWbDRwRzBhL1BRcjM4QzhacC9MNTlIRmJyN2lYaHV3OWFyRzBtY3RKbHpSOUN4dFg4NHBIaFUrc3kyVTZXTUwzSEVDYkRnRC9ma0NPOUpGb2NXVzhJYUVhV0RFb0ZsOXdTZnkyUGJqT2VCcSsxb0dUYWlxZlFPcHZUM0U1WkJTWlNIOTl4TFpnYk5NM2s0WkhOSm5Xd2U4cjVpVWJhNXNkakJMOWwyUnpKSXpTdlRnYjJiQ1F1TmtkVXlwb3I4N3M1emJialViYkVwZldDeFBCTXlDcmZFandzeUR2eHYxUjE5N3h6OTh4SjQ4VldsUDBDZ051SWMwNldFWk8ydTZSRHhBamFGMWdxeS9QUlQ5bzBQRVozQjNiS09TcGp0RDI5UnZXTzB3ckorMVFINjNkK3k5ZC9NTlpYbHJmWnM0OGdTQXE0VWxKWFArTTZxUWlOekF3RVhqdVh2M3hDb1FuRVdoSldLdkoxcjh1ZVNGVFEvRkxLdlRIb05pQ0oiLCJtYWMiOiJhMjM3ZGNhNWE0YjAyMDQyOTIyNTI3ZDY0ZTRiMGMwYWNkNTM3Y2Y2NTU2MTE4ZjZhNzg3NGI4ZTk1NDAxMjA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108241834\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1620531434 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620531434\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1151793745 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkM4alR5Uk5QNGFFMXU3MXpXVFdyOEE9PSIsInZhbHVlIjoiTDRaZlZUUS9ENVg5cFNsUGlNKzZoRHloNWFYWGpqT24rTVhqQzY5YXBESHZNalBiRHdDWHdnc0RzMyt2L1pSYTRSenVTYjJQVWV4TExNTEo3UnFSWHFSZ0lvUVFUc0x1bFVYRnN1RmQvcWYxcEJTNk02dlhScXdCcjVGZ0ZENHV4L3N4czFJcnBNdHFKdzVaalpUeFI0RFFiOUtocU52VjZPZkc2TmE2NS9vTitPc2Npc2JORlZFTDdMbEE0NmlsQ093VGFXaWF0Rjd3TWREUjRoN2ZKbkpOVUJ5MnhETnNkMHhubGpUR0R3ajVLMm9NNU1UeDZzUjkwbXk1Z21xQ2djSitkaFdZcnNhTFdzaWVvblE1aitYTDNCS0tEL2VnR3NpTFNzV3hFd0NGNFpSMmg0MUtNRmpaOU1IMHY2ZUthOFdqdGc3VDFVaFNlM1laVzF0aTlLWkk1dXlDOTkyTnVhRXYxK2tQMjA2V2FLWExhS09WSWJOdXFOMUI0ZkVCZWZZTk9UUk9HalJ5Vzd2R2V0YkszVkxDZVpGQ20xYko5R3FlSXNtSm5tZTNKL0hJU1JlcnVlM2V3a1I2RVlwcFE5Tm5Ba1lwWEQyY2JZUlk3eloyUXlqYkFWU01ueVI3d0w2c2N3NVJqMWkxZXdFNzZ4ZEZYSVBZU3BHZlhOdmIiLCJtYWMiOiJkNzdiNjMwYmViNjY1ZTZjNWVlOTQzNTdhNWE0YTVkMWNiYzQzMWRiZTIzNmI5YWI5ZGQyNThhMzUyYzExODgzIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ind6RFJDc2QxQ2IwOXFNckdJVlZWYmc9PSIsInZhbHVlIjoidlpxZTlFRTZEaWtDTjFzVUdHOWdVVW1EK3FpblRRT2RadVNweUdzYnJ6VDJzNTFLekczVU5QUXNBNENlRkJrS2s3WGxMRjZBYXhUMzFsR0g3a0NHR2VvWWcyK0xxK0tJQTJnS2FJS201N01ZK2VaMWVnVHhJbVlNRlNDQVRkWW9TdHAwWWh3cmttbUZ3UDczRCtPMVNEYVAxRkxGdkE5ck82MitNZ0NpTTd5YnAwUHUwREpZMmxoUjlqM3hhdStXMndzUWp1VGZObkpna3NLdkdVOGlDV1dBRXpVSmJhb2tJMzFTQkQrWXQ4cWJyRFB1WWlIWjlYcHk0NTZJYTNmT3dKNWlhc2FaVXFqeTB4eGtraTBBbTlvRFZGOFduMTg4VGxicFJvRFc4cXFJRmxZNXc4WGVaWjhDWmxDVjA3eDJaNHhtVTZNdmFtZ0hoRjgyR0FNNTBPUUQ4b2F2QndpelZySnhtUzZ0QU53N2lMdCtOSmUyRForZENud2FYckFYQzU4Tk5mMlV1aGJsVzBOdlFLSTI5eUlTSVFpbGZGeDFVWmllUGNtUjVtc21WZjd4L3MzcUdQY2VvZ3FVZ2l3b1ZQMkg1aVMxeGZqY3h4eUo1SGhqQ3VZdGI3RFVjMi93YWxHMWV6aWtpbCtYaFBETzVINzh6ek1jSTcyK0haNW4iLCJtYWMiOiI0NDQ3MzE4NWM0YWFmNGE3MzJjM2IwM2Y3MzJiNGMyMjJkODgxNjY3MmEzYjcyNmFjNjM2NGM5NmI4NDIzYzJhIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkM4alR5Uk5QNGFFMXU3MXpXVFdyOEE9PSIsInZhbHVlIjoiTDRaZlZUUS9ENVg5cFNsUGlNKzZoRHloNWFYWGpqT24rTVhqQzY5YXBESHZNalBiRHdDWHdnc0RzMyt2L1pSYTRSenVTYjJQVWV4TExNTEo3UnFSWHFSZ0lvUVFUc0x1bFVYRnN1RmQvcWYxcEJTNk02dlhScXdCcjVGZ0ZENHV4L3N4czFJcnBNdHFKdzVaalpUeFI0RFFiOUtocU52VjZPZkc2TmE2NS9vTitPc2Npc2JORlZFTDdMbEE0NmlsQ093VGFXaWF0Rjd3TWREUjRoN2ZKbkpOVUJ5MnhETnNkMHhubGpUR0R3ajVLMm9NNU1UeDZzUjkwbXk1Z21xQ2djSitkaFdZcnNhTFdzaWVvblE1aitYTDNCS0tEL2VnR3NpTFNzV3hFd0NGNFpSMmg0MUtNRmpaOU1IMHY2ZUthOFdqdGc3VDFVaFNlM1laVzF0aTlLWkk1dXlDOTkyTnVhRXYxK2tQMjA2V2FLWExhS09WSWJOdXFOMUI0ZkVCZWZZTk9UUk9HalJ5Vzd2R2V0YkszVkxDZVpGQ20xYko5R3FlSXNtSm5tZTNKL0hJU1JlcnVlM2V3a1I2RVlwcFE5Tm5Ba1lwWEQyY2JZUlk3eloyUXlqYkFWU01ueVI3d0w2c2N3NVJqMWkxZXdFNzZ4ZEZYSVBZU3BHZlhOdmIiLCJtYWMiOiJkNzdiNjMwYmViNjY1ZTZjNWVlOTQzNTdhNWE0YTVkMWNiYzQzMWRiZTIzNmI5YWI5ZGQyNThhMzUyYzExODgzIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ind6RFJDc2QxQ2IwOXFNckdJVlZWYmc9PSIsInZhbHVlIjoidlpxZTlFRTZEaWtDTjFzVUdHOWdVVW1EK3FpblRRT2RadVNweUdzYnJ6VDJzNTFLekczVU5QUXNBNENlRkJrS2s3WGxMRjZBYXhUMzFsR0g3a0NHR2VvWWcyK0xxK0tJQTJnS2FJS201N01ZK2VaMWVnVHhJbVlNRlNDQVRkWW9TdHAwWWh3cmttbUZ3UDczRCtPMVNEYVAxRkxGdkE5ck82MitNZ0NpTTd5YnAwUHUwREpZMmxoUjlqM3hhdStXMndzUWp1VGZObkpna3NLdkdVOGlDV1dBRXpVSmJhb2tJMzFTQkQrWXQ4cWJyRFB1WWlIWjlYcHk0NTZJYTNmT3dKNWlhc2FaVXFqeTB4eGtraTBBbTlvRFZGOFduMTg4VGxicFJvRFc4cXFJRmxZNXc4WGVaWjhDWmxDVjA3eDJaNHhtVTZNdmFtZ0hoRjgyR0FNNTBPUUQ4b2F2QndpelZySnhtUzZ0QU53N2lMdCtOSmUyRForZENud2FYckFYQzU4Tk5mMlV1aGJsVzBOdlFLSTI5eUlTSVFpbGZGeDFVWmllUGNtUjVtc21WZjd4L3MzcUdQY2VvZ3FVZ2l3b1ZQMkg1aVMxeGZqY3h4eUo1SGhqQ3VZdGI3RFVjMi93YWxHMWV6aWtpbCtYaFBETzVINzh6ek1jSTcyK0haNW4iLCJtYWMiOiI0NDQ3MzE4NWM0YWFmNGE3MzJjM2IwM2Y3MzJiNGMyMjJkODgxNjY3MmEzYjcyNmFjNjM2NGM5NmI4NDIzYzJhIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151793745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}