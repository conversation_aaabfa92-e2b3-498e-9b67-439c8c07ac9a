{"__meta": {"id": "Xdcf275e5b7a58fb6990240ba8e291179", "datetime": "2025-07-23 18:20:43", "utime": **********.28174, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294842.846682, "end": **********.281753, "duration": 0.4350709915161133, "duration_str": "435ms", "measures": [{"label": "Booting", "start": 1753294842.846682, "relative_start": 0, "end": **********.22958, "relative_end": **********.22958, "duration": 0.38289785385131836, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.229589, "relative_start": 0.3829069137573242, "end": **********.281755, "relative_end": 1.9073486328125e-06, "duration": 0.052165985107421875, "duration_str": "52.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46533576, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00268, "accumulated_duration_str": "2.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.256886, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.06}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2666192, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.06, "width_percent": 16.418}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.273189, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.478, "width_percent": 20.522}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1001525164 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1001525164\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1287024487 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287024487\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1211212249 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211212249\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1837199860 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294839750%7C7%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNTUmg0Q1M3SUd4TlYya2NvWFc5bUE9PSIsInZhbHVlIjoiZ2lYZEJ3U00vQ2prOTFCR2xYMC9tUDdzU1NXR0NibUcvdU5vOUR4L0pNZWVQbzY5WkhjZGFVRmRxQWsrSERsT0x0Qmg0eUdpTmI0blp1NkpLVHJiVFR5UFBmWHJiWm1wakhGbkloYk02KzRXcTZsN0xJaXNYTkJFcTExZTg4anU4OWtBRHJTcU1hSHI4di9IQzE2SmhGYjRSczdYTHhXVUZ5TnlRQ000dkVkU0xqYzJFSnptYmVsdXBEVk0xV1ZqaUMvRWVMNElVWXVpcGtOekpHc0FwbmYrZXhyOTRDbTlUNXk5YnhmRnhWaWxiS1ZDTEZ3R0lqY1g3R3hGeTZ2OVpSYktWWU81NTJpcDczcDR2RGYwb0M4Q2lBK0FnVzN2SnAxWXpyV1E1WjgzMG56cnZOY2gybnhLSW1pZ0Q3S2JLblpZVVdiVmR1L3hRcGZQSSt5N3lhbi9rNFFKbGV0T2lUanVvUzBXbVdMblZrU0hXbURnVVNPSkw1QjRiczNQQlowOW5EaVM1NFFlUFMxdlZmYTZxRld4bXdJSHNHb3c1SWZsSlhSa1NrNkRFZ3BScmxSWHpFWGp0emFkT3g3U24yTGxLVlNGMGtQV2tPbWQ0dVkrNnVOZk5nN1Zqam9qWlE0OTcyckdzT3JaU1F0NG5DazhHUlpLclpmdTU0bXEiLCJtYWMiOiJiZWExNzE4NGZkNDIzNTg4MGM4ZjE2ZWQ0MzM3OTkyNjExNmMzZTRjMzM4OTc4MWY0NzViM2YzYjUzMTM0ZDQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitrUFRPZUFNMVczbVg3bmFEeTBXcGc9PSIsInZhbHVlIjoiVFAraEcreXY5NHBWNVpTRDFBZFl4eVhyVWJ4dUJuU1RBMFkwckUyYjNNMndvd3pCWUpHM24zZmY0eHZuRmcvT1RtTUF4djFSMjJzQmJBRFNyZWNFZlJDSm1wN3UydWxDZEVBSTBic2VFdEYrMVo2RXRaV2VSZDVXdnQ5aXA1VE9uNTRFRUU3RkFRU01CcEdQSWEwc1pXN0ptVStSTXFuRnVwUzNQNUsyTVRNazJxMnQ2dklRNVpKL2V5elM3dWZnbTR6a0psYWVIWTBXbUhIZHBqSUFMYUlVenI5T0V2SlZQblhtaEdxOWU0WSs5bTlyNUIvam0xRHRrOUxvRkoyRHBVd3JoOERSSHFZVC9oSHIzM0dUYXlmV2ZGTHRSOXZjZkNaS2RKVjlkSjB2b3d4bGkva2JQTGgyM1BydmZOeEQvVDE2QlBaQ0MwTDVTd2lQTmUzeVRBRFMxc3BFVW1qRTJManRXbjFDWmZ0Wk9UMkdMR09ZNWZWOGl5SmpHVjNiRUtkUlpXN2hKRjQ1dHNqalhvNGUxVHhPRklKN0Q5dlZ0UzBCK2p1UVYxUjRZdEV2Y2NSS1Vyd3ZoUm00Zk9yUmRSbGhWZFV1SWhYVFhjUmhGT0NnU2FGZjBmRG9JWGZQYXlZQXJJT3FYSGdFdmd6eWlOT25rRTRKV1pHMkozUXAiLCJtYWMiOiIyYmNhZWZiMjQyZTJlODQ4NmYwZjRhYTE2NzgwZDQwZjRkM2UzZmM0MmM0NWM5ZGZkZjFlYTkxNTZhYTRiYmVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837199860\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2138052099 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138052099\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-916589482 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:20:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlzL0hScVQrZXFlTnE2MG1oNmJXNVE9PSIsInZhbHVlIjoiUCtPdVh3WWdBWVMvd0hqM0pLT0pnWkZBN3dza2pqZmQyOHJLd20rNnpVdHNrdFNWMHFhcUJoKzhaZXF3ZHgyYU1wWjBwWmVLbXVramxQelE1REZLbmlOcHgramtqd0Jib3Uvb3N5MnZkbkNaR3lkVk5mMitYM0RMSlRmUjdwWWFycjZZbjBLd1J0clVvSWwwTCs1VkpJbjFvOXZzL2pCanpMMzJwMmNRL3dwZ1VmSFgvQjhhVmx0aVVYWFRTWEdIRmF2RFpiS3BwbXpZQXREeUh1amliWjg5ODNscVJHamNSQ0xoR3ZCaWJreW84RGdscTdlUUlaUFNINGQ5UVQ1aVF6aGJVNWlKbDBuMEg5VmlORzlOZFFBMHJlWlpuN3dDWGxTRzdoMzVTS0VBSWFTQjBwVGV4VkRqUlE4ZFlMbTdWYUdUVi9zcW5JU2I5bXd3eElBTFNXUmZuWU9QSzkzOTljSlc4L0hDWm9MbEtsSTJvOW00OVZ5ZlJMTmlmYmdlWkovdHFOOFkvR1YrMTc3bzFWZWFnZmxZOFcvQ0xnakFPSXV5amJ0Znd2RmQ2MU1OOTJYQjV0NW5jNUNaZWVMUnJFZkdmdjY4WnNuVGEwRlR2WjFhK2NvUDAwNkp3Y2NCRVVDVnhaWmUzdWZld2VhWEtSdGQybUVBMzdaNXR4VGsiLCJtYWMiOiI3MTVlNzg5NzdjN2I3N2Q3ZDkwMzhhZTYzZGI2YmZlZjBkYzM1Njk5NjJkMDIxOTk3NGNjNzRkN2RiMzFhZmRkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlllWGtRUm85cjR1WXhQR25uZWpyMFE9PSIsInZhbHVlIjoiVGNsSzB6WXE5emNxclNYRXN0WXpPV3dnMTZuaGpnTlZidzlTc3J2MGFZY2htaDUvVEQxa0c1ZUZ4LzhINUxqbjc5OGp1eG5COTNPc0tsZjZ0QnFoWGRSVFlIckxaZUdzbGlIaXdMcWYwRGdZc2lUdExLWCtoUVdhcmVlSUV5R011aXYvMDBZbDNPalNnc1QxRElNVVUrRW9BTXFrWTNPSzJ1ckptTTVtaittUmpnOFJCeVlBNU1oWGEzWXBVcjArMk8zb1NkVXJUUXdxdVlQYVA2cXNCSW04ZkdsbDE5S0ROeUxuK3dyRXhMQnpMWXk1NFh4bHFGbTBlZnJLckRpTkhuQzVZUjhQMWpia2pqT1NpQ25uUFRoeWhmWWZiL0k3cVlJRitBVTFXMEtWSEloVk5aeFZtUVdmSktvTGF3dks2NENDK0tmdVNmT1RSeVJTTHFGdm9wN0EyS3Fad3VLVU5KRGdKdUlVU0VpdlpqRkVNcXNtbStiU1QveFRZaHRpNWdsYlAvcmE3bjRtck52ejg0VFl1UXdEbTBsdk9wWnVnRTExQWlQNVBrd01IQlVzNkVoSGFnOFdpZmhaa3d1MnFDWUhlTERuZXVGRGNja012M0doK2FJOVl1TFo4TGhOZzh3bjU3N0tBaUFJdlV2WFZ4VU1KTWJnSy9TN29RWTciLCJtYWMiOiI1MGRkNGJlYzk0ZGFiMDMyZGI2MGFjMWY0NmRmYWMxNGNiMDJiYjQwZjJmMWRkOTk5ZjlmNWUwYzYwNGVkOWFiIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:20:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlzL0hScVQrZXFlTnE2MG1oNmJXNVE9PSIsInZhbHVlIjoiUCtPdVh3WWdBWVMvd0hqM0pLT0pnWkZBN3dza2pqZmQyOHJLd20rNnpVdHNrdFNWMHFhcUJoKzhaZXF3ZHgyYU1wWjBwWmVLbXVramxQelE1REZLbmlOcHgramtqd0Jib3Uvb3N5MnZkbkNaR3lkVk5mMitYM0RMSlRmUjdwWWFycjZZbjBLd1J0clVvSWwwTCs1VkpJbjFvOXZzL2pCanpMMzJwMmNRL3dwZ1VmSFgvQjhhVmx0aVVYWFRTWEdIRmF2RFpiS3BwbXpZQXREeUh1amliWjg5ODNscVJHamNSQ0xoR3ZCaWJreW84RGdscTdlUUlaUFNINGQ5UVQ1aVF6aGJVNWlKbDBuMEg5VmlORzlOZFFBMHJlWlpuN3dDWGxTRzdoMzVTS0VBSWFTQjBwVGV4VkRqUlE4ZFlMbTdWYUdUVi9zcW5JU2I5bXd3eElBTFNXUmZuWU9QSzkzOTljSlc4L0hDWm9MbEtsSTJvOW00OVZ5ZlJMTmlmYmdlWkovdHFOOFkvR1YrMTc3bzFWZWFnZmxZOFcvQ0xnakFPSXV5amJ0Znd2RmQ2MU1OOTJYQjV0NW5jNUNaZWVMUnJFZkdmdjY4WnNuVGEwRlR2WjFhK2NvUDAwNkp3Y2NCRVVDVnhaWmUzdWZld2VhWEtSdGQybUVBMzdaNXR4VGsiLCJtYWMiOiI3MTVlNzg5NzdjN2I3N2Q3ZDkwMzhhZTYzZGI2YmZlZjBkYzM1Njk5NjJkMDIxOTk3NGNjNzRkN2RiMzFhZmRkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlllWGtRUm85cjR1WXhQR25uZWpyMFE9PSIsInZhbHVlIjoiVGNsSzB6WXE5emNxclNYRXN0WXpPV3dnMTZuaGpnTlZidzlTc3J2MGFZY2htaDUvVEQxa0c1ZUZ4LzhINUxqbjc5OGp1eG5COTNPc0tsZjZ0QnFoWGRSVFlIckxaZUdzbGlIaXdMcWYwRGdZc2lUdExLWCtoUVdhcmVlSUV5R011aXYvMDBZbDNPalNnc1QxRElNVVUrRW9BTXFrWTNPSzJ1ckptTTVtaittUmpnOFJCeVlBNU1oWGEzWXBVcjArMk8zb1NkVXJUUXdxdVlQYVA2cXNCSW04ZkdsbDE5S0ROeUxuK3dyRXhMQnpMWXk1NFh4bHFGbTBlZnJLckRpTkhuQzVZUjhQMWpia2pqT1NpQ25uUFRoeWhmWWZiL0k3cVlJRitBVTFXMEtWSEloVk5aeFZtUVdmSktvTGF3dks2NENDK0tmdVNmT1RSeVJTTHFGdm9wN0EyS3Fad3VLVU5KRGdKdUlVU0VpdlpqRkVNcXNtbStiU1QveFRZaHRpNWdsYlAvcmE3bjRtck52ejg0VFl1UXdEbTBsdk9wWnVnRTExQWlQNVBrd01IQlVzNkVoSGFnOFdpZmhaa3d1MnFDWUhlTERuZXVGRGNja012M0doK2FJOVl1TFo4TGhOZzh3bjU3N0tBaUFJdlV2WFZ4VU1KTWJnSy9TN29RWTciLCJtYWMiOiI1MGRkNGJlYzk0ZGFiMDMyZGI2MGFjMWY0NmRmYWMxNGNiMDJiYjQwZjJmMWRkOTk5ZjlmNWUwYzYwNGVkOWFiIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:20:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916589482\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1749276282 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749276282\", {\"maxDepth\":0})</script>\n"}}