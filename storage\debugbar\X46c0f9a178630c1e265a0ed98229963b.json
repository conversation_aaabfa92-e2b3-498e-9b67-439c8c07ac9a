{"__meta": {"id": "X46c0f9a178630c1e265a0ed98229963b", "datetime": "2025-07-23 18:22:46", "utime": **********.537943, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.108134, "end": **********.537956, "duration": 0.****************, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.108134, "relative_start": 0, "end": **********.484649, "relative_end": **********.484649, "duration": 0.*****************, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.484658, "relative_start": 0.****************, "end": **********.537958, "relative_end": 1.9073486328125e-06, "duration": 0.053299903869628906, "duration_str": "53.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00293, "accumulated_duration_str": "2.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.513551, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.846}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.523587, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.846, "width_percent": 12.628}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.530652, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 77.474, "width_percent": 22.526}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-6385863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-6385863\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=fyam1q%7C2%7Cfxu%7C0%7C2030; _clsk=14zrrqn%7C1753294965225%7C3%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; quickly24erp_session=eyJpdiI6InBMZDZJNHZSeHZMZFZCdHNNK1BQbFE9PSIsInZhbHVlIjoiajRZcnlQZzc0RVMyb3pvOUlMY0xNTUhFS29FdlZ4N0ljZWxFWkRQRkx6Y0xCVFRyN05iNEhoak9zVHNEeTJENVJZanRuU0FLOEg0T1RQWXo2S2hlRDNIbkd3dmMxVFV4dFllTUtwTVo2MCtZYXd3SE82enpHOEZXallsdXhNZ1N1SUwxWHBkcTV6VE93SHBDQ1Jsdks4dmgwZlN0bEdYWW5aSytpdnhLdUl4N3dGNEV2cFoxYUJCR1p0YnRYRTNETEN1M3BrSVVCVjk2OU90WWNpY2QvcXJvTjg3RnlFc3djTU5oRC9sN0V4ckZIZEVMMlhFaXorMlVxeFR2N3hvWkN4dHF0TWpFaVZzZHNBUVZVeGdDYUNYTzBsWjFObWgxSTU2SXhSR0cvL05qbU9hNmJqZCtrK25TK2lsZi9uRndacUdYOFN3dmlUMEdmSjJ5ZitVZlpsWlplS3dCd0xCbDJ5Q25nekJ6YzMyY0N3aUN2Yk0vY0k5QlNPdW92a1NlOEVOVWVkMFkyb21PeTVIQkthNzlLaFFDTGl3UXIzWExJempSUGMxSjNIQnIrYWNNcHBRWGU4U0Q3bWJHRFVhSHBpanFpb0hNU0dvaEpUTk9OeUtvR05YRnpEVnNpNk5IN3IrbkZXVzE5SldqZlNDeUFtNVFGTC9OTjMxREJycGQiLCJtYWMiOiIyZDcxYjk0ZjU2NzQwYmMwMDE0YTJiYzEzNzU0NWE1ODcxNjljMmY1MmE4NzNmMjljYWEwNWY3ZWYxNzRhMDNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2050458688 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6f86AL2UyJbEsnP8wClSGR79UCod8lW0WDs0fxKW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050458688\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:22:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVPMnFmeU0ybVVuSnROL0xJcm9rRGc9PSIsInZhbHVlIjoiRzhveVJmUW1JcTIxcU8yOTlJTENlYmxIR0xGenNNTXVCcTkrYXJzM1EzbUpoSGcyaHRMMS9rUmwxaml5U0RhVXVLb1VvMGZYMmw1Y3NCMXdlT3dlb0pkU0NiRHRXOVBOTTh3T1RJNno2UGxQYnFkNWkyZlNiOGs5U2VUK21iZFdMOGx6dTJBQnJXYUJKQ3l0cSt6dG1oKzdlZTY2RW9HbWhPNFlVcHJmZWFuMVRvbXp3QUFsb0JxbTcxVWJzTkRhd2p6aTVYeXFMUFppb2tlZkoxa0x5c1hjdFNZekJCM25yYUt0RFJrOXRpck9DQlJTNFhMcGtKb3ZLTXZpcG0zYlA4N3REczZQejVwL1lSNmFNQVh1SktVVEtZZXYyZUlGZnA0ay9WTWIxVFFWdFhBWWlrb0o4eUc4RVRNVyt3TXJ0Y0dQWXc0M2ZJOTBFakxZbmtyYi9CRlQ3N250MEFOWnRyekVOK3NTK0ptOXkxM2VkeEVQNmkrY2YyLzRreTkzN2poZ2FlMlhpMWxvMnd0a3U0TUdlek5GaTc1UUhVanl2MnF0K2RmcjF6V0ZCQTN4bnJMOWEwSXY2Yi85VW04bTFiWWl5T1R4TFZRUFJEQ1lWa3dOUUdYMERnTTBLRnBjOThSNzJDZGJ2elVEU3QrejhVVGtVWnpOY3kzQ1dDZXAiLCJtYWMiOiJmNDBlZDA3ZDE1NDc5Y2JlMDM4OTdjMDBmMzFjNjg0ZWViMzVkZTQ5NmM0MmZmMjU2OTQxMmJmMTAxZTc4ODI1IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilk0ZVVueE92eEtQNFpMWk1LYWIyVWc9PSIsInZhbHVlIjoiTnYvVGpsdXZTS1FsckcrblNvSnFwT3lzRjEvaGtLdzZkM0hHVUtCRnFRaUh3VmFWaklOSENjcnVYRnJwUjh0WWZlQmVzZVFvaDhxOE1SQnZ5L2NOaDg4Y29XNjNWeUFCY3J5WWtmSmhhWElmazNtcmZ0R1hheG1KZWl6c3FRcVdETEo3bHNlYjMxUEJMd3ZZSGwwZUl1L3BpSllsb0RWS1RUMEVnbVIwTk9TYXB4K29GLys5NGIvODhwZ1dLanlxMy9HeW1LQ3VZTVFMR1ZCK0R4TUVOZ1hUZ0xFc2owWXk4anhQWXI1QWlPb3JOaE9BZTRGaUxwMDA5ZXBLa0JvVHBSQ0tBSlE3ZEp1dmZ5RVI4dUs1TUx5Y0VXL3NPWHRUQys4cDFwMVlVK0pVditwM283dEQ5K2IxVnhlQiswejZzRUdCR28vd1hmWjFnNENydEJQZXVMUnBGUTBiT0ljUmdXT29PL3VQMFQzb0NzQ01BYmtlbFg5UE9JN05Ed0lCTjlpWUxZK0ZNd3pWRk1rTmRLNFk3SGZZMW1RUFJjWEJhRFZmOUdwRDlzc3N0c0dHQjJuWnovRW1teGh1VThUc0dPNW4wNDQrZGhyVkk2Mi92dWFJMC9qN1NBV3BNMUREUE9PUWg2NDJkZG1GaC9nSDR0MUpoSElLYmpUaWNtWWIiLCJtYWMiOiJmMWRkYmQ2ZDM5NWFlNTkyZjgyNmQ1ZTcyYWUwZDNkZDc2Mzk1MTMyOTg0NDU0NmNiMjA1YTg5YzkzNWUwODA5IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:22:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVPMnFmeU0ybVVuSnROL0xJcm9rRGc9PSIsInZhbHVlIjoiRzhveVJmUW1JcTIxcU8yOTlJTENlYmxIR0xGenNNTXVCcTkrYXJzM1EzbUpoSGcyaHRMMS9rUmwxaml5U0RhVXVLb1VvMGZYMmw1Y3NCMXdlT3dlb0pkU0NiRHRXOVBOTTh3T1RJNno2UGxQYnFkNWkyZlNiOGs5U2VUK21iZFdMOGx6dTJBQnJXYUJKQ3l0cSt6dG1oKzdlZTY2RW9HbWhPNFlVcHJmZWFuMVRvbXp3QUFsb0JxbTcxVWJzTkRhd2p6aTVYeXFMUFppb2tlZkoxa0x5c1hjdFNZekJCM25yYUt0RFJrOXRpck9DQlJTNFhMcGtKb3ZLTXZpcG0zYlA4N3REczZQejVwL1lSNmFNQVh1SktVVEtZZXYyZUlGZnA0ay9WTWIxVFFWdFhBWWlrb0o4eUc4RVRNVyt3TXJ0Y0dQWXc0M2ZJOTBFakxZbmtyYi9CRlQ3N250MEFOWnRyekVOK3NTK0ptOXkxM2VkeEVQNmkrY2YyLzRreTkzN2poZ2FlMlhpMWxvMnd0a3U0TUdlek5GaTc1UUhVanl2MnF0K2RmcjF6V0ZCQTN4bnJMOWEwSXY2Yi85VW04bTFiWWl5T1R4TFZRUFJEQ1lWa3dOUUdYMERnTTBLRnBjOThSNzJDZGJ2elVEU3QrejhVVGtVWnpOY3kzQ1dDZXAiLCJtYWMiOiJmNDBlZDA3ZDE1NDc5Y2JlMDM4OTdjMDBmMzFjNjg0ZWViMzVkZTQ5NmM0MmZmMjU2OTQxMmJmMTAxZTc4ODI1IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilk0ZVVueE92eEtQNFpMWk1LYWIyVWc9PSIsInZhbHVlIjoiTnYvVGpsdXZTS1FsckcrblNvSnFwT3lzRjEvaGtLdzZkM0hHVUtCRnFRaUh3VmFWaklOSENjcnVYRnJwUjh0WWZlQmVzZVFvaDhxOE1SQnZ5L2NOaDg4Y29XNjNWeUFCY3J5WWtmSmhhWElmazNtcmZ0R1hheG1KZWl6c3FRcVdETEo3bHNlYjMxUEJMd3ZZSGwwZUl1L3BpSllsb0RWS1RUMEVnbVIwTk9TYXB4K29GLys5NGIvODhwZ1dLanlxMy9HeW1LQ3VZTVFMR1ZCK0R4TUVOZ1hUZ0xFc2owWXk4anhQWXI1QWlPb3JOaE9BZTRGaUxwMDA5ZXBLa0JvVHBSQ0tBSlE3ZEp1dmZ5RVI4dUs1TUx5Y0VXL3NPWHRUQys4cDFwMVlVK0pVditwM283dEQ5K2IxVnhlQiswejZzRUdCR28vd1hmWjFnNENydEJQZXVMUnBGUTBiT0ljUmdXT29PL3VQMFQzb0NzQ01BYmtlbFg5UE9JN05Ed0lCTjlpWUxZK0ZNd3pWRk1rTmRLNFk3SGZZMW1RUFJjWEJhRFZmOUdwRDlzc3N0c0dHQjJuWnovRW1teGh1VThUc0dPNW4wNDQrZGhyVkk2Mi92dWFJMC9qN1NBV3BNMUREUE9PUWg2NDJkZG1GaC9nSDR0MUpoSElLYmpUaWNtWWIiLCJtYWMiOiJmMWRkYmQ2ZDM5NWFlNTkyZjgyNmQ1ZTcyYWUwZDNkZDc2Mzk1MTMyOTg0NDU0NmNiMjA1YTg5YzkzNWUwODA5IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:22:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1563663653 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jr8XK0kpDf0c0wO8Lboq86K9RSKyep1ezyXXpscN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563663653\", {\"maxDepth\":0})</script>\n"}}