{"__meta": {"id": "X063870c8996543d3840c7930a7b975fb", "datetime": "2025-07-21 01:34:16", "utime": **********.085437, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753061655.644767, "end": **********.085454, "duration": 0.4406869411468506, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1753061655.644767, "relative_start": 0, "end": **********.015879, "relative_end": **********.015879, "duration": 0.3711118698120117, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.015886, "relative_start": 0.37111902236938477, "end": **********.085456, "relative_end": 1.9073486328125e-06, "duration": 0.06956982612609863, "duration_str": "69.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991608, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01764, "accumulated_duration_str": "17.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0450609, "duration": 0.01654, "duration_str": "16.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.764}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0701678, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.764, "width_percent": 2.438}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0774531, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.202, "width_percent": 3.798}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor?creator_id=20&customer_id=8&date_from=2025-07-21&date_to=2025-07-21&payment_method=&warehouse_id=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-977737447 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-977737447\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-526618924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-526618924\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-583033524 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583033524\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-951888384 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?date_from=2025-07-21&amp;date_to=2025-07-21&amp;warehouse_id=&amp;payment_method=&amp;customer_id=8&amp;creator_id=20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753061646451%7C10%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhJQmJDQzNkT1d5N2FxYUVCU2NKVEE9PSIsInZhbHVlIjoiR0x1RWpLUlE4cUZtSlBkSi92Um1oY1JSdldMVTFEbkJQUzJwZmFQVWVzVGpEV3lTMmsrVEFsRnpDWm1vbzk3NXVRcGo5cUVnQUE5YmNSdDMrdFEzQXJXR0NGa3hTRHNPblY0MFJaR3FlTDllNnBhZldKWC9Ic2dkUzkvQ1lLa3BEcnJrRlk0alpDV01LZEF1eU04VTV4Wm5Fd1FkSnIxT3NpQ0daOUp4Ly9UV3E1REVFdE9wSXRFL0lIZmRPU2lNc3VJZmR6Z05SOWZZNWRQdUcrVEUwMURiUEFpUGkwQUQ4U3d6bTBQdDZocmFrM1l0TWRVNGJDeDVMT0Y0bXI2bWFtWVo0NCswcnlETU16V25ReDgvb0xLY2lPU1UwT1FhK2dKeDJiWGpiT3IzK0hGdVVyZldtZXowdkJBcDg4UEFLR1NzMWdHdmpxOTBXM01BWWNlWTlteS84bUk0dGVYU0lORGlhV0FYK2VINGpNY0MvczNzdGR0SjRXUE1LTkpJL0ttclZoRGdIL0puRzlqM3RxemhDTlpGN0lhb2o3UU5jN1U5OFJSKzZEWVVoTTJiNVprd3dPUkEwaFE3eGFXdG53aFdjUDZxL1preVNDMmthMmVHQSt1T3kybTMrU2FzWUs5ejhhNWJCRzl2OS9CQjFtK3hDS1laNUJwZWJqQjgiLCJtYWMiOiI4MWJkMWU2M2U5MWFlZjk3MzllMDZkZjg0NzYyMzA5MDkyYTA0YTFlMGZlMGNlM2Y0YzdmMTNiOWUzYjc2ODdiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRxTmdoaGVCWHNubmxianlDK21PTEE9PSIsInZhbHVlIjoiTzI4ajdhQk05TXZ6cHF5Z2VUTFRwMENkUHRUVG9SZllaZ1FybUdIV21aM1ZvWnRVQ1ZQQ29IZklCQmc5RjZNc0YvYzZJQXU2eFBhdXppbWhSK1N1WUJzOFNETm5zeUJWMDQvWXFmSk5lRDQ5ZmQ3bTFlQXZPeWFVa21JZC9qTm14bnRyM1hBeWJMV05PYmpsV3BCbWpNT1haZlVaOWdzY0RYbkcwQ3Q1MnJ0aG5vbjU0Sk80cDlrVmlSUmpSTXdubG1ZQnBLS0RQeFNrQVJ4a25nYUthUUl5bkN5Q0xVaHMrMzZLM0o4bWZMc0RIVFJrV3hQWUFoaFNRZXdacDg5UkZzbUFlVUYxbW81MGtFVXFEMzkyVDd3UklZMmhzdmJSbzJucThFV21LSmI5OGNJcGFPaFg3ekJFaW91OEFyWFBNUUlrUFdGM3Fvbk80UHdvTUMxblJrVVN3UGo3VG5XWlorNXNZVDVnY1ljTWRpcTROaHdSWGlETWJZMVAxRzR1QXJ4UjVlbm95bEFSVVJGQ1pJejJ1N1loVzIzMk1QcFQrUE12MHdnaGlWZGZPUVFYczZwQVRBVWFGK2kzN3h5eFVyNUFVTk1HOEVmR2dvbUlsOHQxYWp0dk55K2JiSzFta2tXZFJZWUg2UHQ0S25odzJHZHNWSEEzQWxVekFFN2IiLCJtYWMiOiI3N2I5M2QyZjg1YTg5ZGY1YmU2NGIxNTNmMzFkYmQzZjE0OGM3NjM2MGUwYzg5OGVhZDBjMzhlYWUxMmQxZTMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951888384\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1345769872 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqoyJx8koQwvTls9Y2xAkbxYwb9Lv3Fr8ecmjm02</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345769872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-950341405 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 01:34:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRyNDladnAxZU40QXVlVkVYOGgvSmc9PSIsInZhbHVlIjoicGkyUmFCcitCY0lPV1ZDZVdqRGFOaDdMSnhneVV0NjFHMitDTkFZeXZ4OVRSWXBMUE4vcGpjRERTbXdLak9WRVZMcHlrQ1UzbFJRaDRLeXI5K2dNYXpYL3ZYZklFa1BLc3MxOE1lWGhlNGxLOW9LeWYwUE90RnVKNStLSVY1Vkl4aGcwSmNsV1FvUithWWRTVVB6STFJRnAzN2trcG11a2NpUGZMVkk2dGhDT2dWZGNZbXhRQi8yVHNFekhIWmsyUTJNaWQ0RlB5WU1VOGMvQ3dtTDNtT1p6WGpCcUNGTHRtaE9UZVh6STB6cVRYWnpKVEVEMTVuK3F5TnhXVWYyc2JCWDV2dXVzNktMSlJZMWZPWlhPcHJaQjVKLytzMjFFVit0SXJIRjUwOUhWbnJUVVdaVTB5MHRrS0hOWkhNYk1vdUpGK25jZmp4cG5PRDNxa2cxK2F6aXNOSVU5bXN0TXdGbFdRR0FvWFFIMDFKNU1CZ0JtWWZ4ZHdXNmhxY1RLdHVMQVR2QmUwaG02VHVocXZwOXVmS2prUE5NU1VxUmJqdFMzcTVwVzRSTVpKUkFIT1lZdG9YV01Wb3c5eWtLbFEza1BsQmdOb0JDSElqckVVejV2cVB1emxzR0hZbnoxVWlGQmRRZEs5d1QzRHFvNVR0Y0Z3WFcxVXl2cGREZm8iLCJtYWMiOiJlM2RmZWY0MzlkMjJmNDJlYjRlNWQ5ZDYxMTk2OTFkNDQ3MGI0NGI3Mzk2MTcyYjBmYjgwYjQwYzVkMDRlMmNmIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNvbWF3UzhDRTZsejkwNEUydDdPN1E9PSIsInZhbHVlIjoiNUdJaE1VMzA5Wi8vVEpveUZ1dWlOZ1NzN0hhNTNGZGd5VG1tcVYrTFQ0UGhFTGJTZ1BOb1NueFZhQ1V5VWNhWXFEY3J4UG5rRlBneXpPT0lsZDZ6VW5GQXJ6aXJjWHFpcXIrS3BGZlpqdWtyL3NFa2RFVkhwY3EwUGpkMTE5WGY2eHpHeFhxS2hyRVNMdFpmS0JUeDdZQmVOT0dOWUxIUmpjVE9KUExlQlJocU9KRjVIekVIUUJBbUZaZzVpckpFcE5rdUlTVElJQjI5Y2NmaFpHaTNkbEE2ZzE4RjlHVmZTVExsSDdGazREV2VTOFF1R3NFQk96MGJtSlNUR0FYRWgyTndaWWJGYU9Uc1dYUkRBdkt6YkY3dDIrbFlobmsrc2hwSHpYeUZYazdxbUlMcEpsMk9RRC9rWno0NGppeVg4K1JHL3R2ZllFRHNwT3VNYm12eXpXVU1EQXBEUnNlMm4zRkRUMGJsaVZCdU1iSE5QSHRYMzhyemgvSnQxL1I0RTFJT2tzbUNFYnl6ZTdYa0JkbGxSWUdLQ2lCVmc1QnIrQ083UzU1bTBrUVdRY082a2s0Z3dQZDRKNUV1RXdSdnZ4Y3h1dUFGcUN4YTI3THJOVFBKRGpaWGdFeXowb1hndXJFUk9QN2R2Nmh5N3Y1SlVLbG5Ib0dyVGJES0ptVlIiLCJtYWMiOiI0MzUyMDM0ZTAzYTgzMTUxMzAyODI1ODRlMDQ5NTZiMWRhZTQ3NzEyNDY5ODg4ZWU4NGI4OTQ4MjlkMzk1NDE4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 03:34:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRyNDladnAxZU40QXVlVkVYOGgvSmc9PSIsInZhbHVlIjoicGkyUmFCcitCY0lPV1ZDZVdqRGFOaDdMSnhneVV0NjFHMitDTkFZeXZ4OVRSWXBMUE4vcGpjRERTbXdLak9WRVZMcHlrQ1UzbFJRaDRLeXI5K2dNYXpYL3ZYZklFa1BLc3MxOE1lWGhlNGxLOW9LeWYwUE90RnVKNStLSVY1Vkl4aGcwSmNsV1FvUithWWRTVVB6STFJRnAzN2trcG11a2NpUGZMVkk2dGhDT2dWZGNZbXhRQi8yVHNFekhIWmsyUTJNaWQ0RlB5WU1VOGMvQ3dtTDNtT1p6WGpCcUNGTHRtaE9UZVh6STB6cVRYWnpKVEVEMTVuK3F5TnhXVWYyc2JCWDV2dXVzNktMSlJZMWZPWlhPcHJaQjVKLytzMjFFVit0SXJIRjUwOUhWbnJUVVdaVTB5MHRrS0hOWkhNYk1vdUpGK25jZmp4cG5PRDNxa2cxK2F6aXNOSVU5bXN0TXdGbFdRR0FvWFFIMDFKNU1CZ0JtWWZ4ZHdXNmhxY1RLdHVMQVR2QmUwaG02VHVocXZwOXVmS2prUE5NU1VxUmJqdFMzcTVwVzRSTVpKUkFIT1lZdG9YV01Wb3c5eWtLbFEza1BsQmdOb0JDSElqckVVejV2cVB1emxzR0hZbnoxVWlGQmRRZEs5d1QzRHFvNVR0Y0Z3WFcxVXl2cGREZm8iLCJtYWMiOiJlM2RmZWY0MzlkMjJmNDJlYjRlNWQ5ZDYxMTk2OTFkNDQ3MGI0NGI3Mzk2MTcyYjBmYjgwYjQwYzVkMDRlMmNmIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNvbWF3UzhDRTZsejkwNEUydDdPN1E9PSIsInZhbHVlIjoiNUdJaE1VMzA5Wi8vVEpveUZ1dWlOZ1NzN0hhNTNGZGd5VG1tcVYrTFQ0UGhFTGJTZ1BOb1NueFZhQ1V5VWNhWXFEY3J4UG5rRlBneXpPT0lsZDZ6VW5GQXJ6aXJjWHFpcXIrS3BGZlpqdWtyL3NFa2RFVkhwY3EwUGpkMTE5WGY2eHpHeFhxS2hyRVNMdFpmS0JUeDdZQmVOT0dOWUxIUmpjVE9KUExlQlJocU9KRjVIekVIUUJBbUZaZzVpckpFcE5rdUlTVElJQjI5Y2NmaFpHaTNkbEE2ZzE4RjlHVmZTVExsSDdGazREV2VTOFF1R3NFQk96MGJtSlNUR0FYRWgyTndaWWJGYU9Uc1dYUkRBdkt6YkY3dDIrbFlobmsrc2hwSHpYeUZYazdxbUlMcEpsMk9RRC9rWno0NGppeVg4K1JHL3R2ZllFRHNwT3VNYm12eXpXVU1EQXBEUnNlMm4zRkRUMGJsaVZCdU1iSE5QSHRYMzhyemgvSnQxL1I0RTFJT2tzbUNFYnl6ZTdYa0JkbGxSWUdLQ2lCVmc1QnIrQ083UzU1bTBrUVdRY082a2s0Z3dQZDRKNUV1RXdSdnZ4Y3h1dUFGcUN4YTI3THJOVFBKRGpaWGdFeXowb1hndXJFUk9QN2R2Nmh5N3Y1SlVLbG5Ib0dyVGJES0ptVlIiLCJtYWMiOiI0MzUyMDM0ZTAzYTgzMTUxMzAyODI1ODRlMDQ5NTZiMWRhZTQ3NzEyNDY5ODg4ZWU4NGI4OTQ4MjlkMzk1NDE4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 03:34:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950341405\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-887060002 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"151 characters\">http://localhost/invoice/processing/invoice-processor?creator_id=20&amp;customer_id=8&amp;date_from=2025-07-21&amp;date_to=2025-07-21&amp;payment_method=&amp;warehouse_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887060002\", {\"maxDepth\":0})</script>\n"}}