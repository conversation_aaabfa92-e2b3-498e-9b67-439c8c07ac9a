{"__meta": {"id": "X6859b3a5c57bbac993d86356b567e12b", "datetime": "2025-07-14 17:58:32", "utime": **********.262087, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.862104, "end": **********.2621, "duration": 0.****************, "duration_str": "400ms", "measures": [{"label": "Booting", "start": **********.862104, "relative_start": 0, "end": **********.210481, "relative_end": **********.210481, "duration": 0.***************, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.210491, "relative_start": 0.****************, "end": **********.262101, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "51.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00291, "accumulated_duration_str": "2.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2374792, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.918}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2476761, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.918, "width_percent": 15.12}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2551959, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 79.038, "width_percent": 20.962}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C**********303%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkIxRHNCZUk5S2tGN0RvM3dsQ1lTL0E9PSIsInZhbHVlIjoiK0ZtOTRjSVEySG1UUkpkcW9yZ3oraDIwaHJBVW5tYk4raEpWT3dSOXp3dG1qU0ZaMjU3V3dnVUtvOFpUdDZrN2tNQndNVzNGUlBkclJDNXNxUVVEV05FaVhHSXUraUZ2QlFEOGtyVTVOMFl1RUdxU01BMEVMbkpZNXZ2MzIxU1I4TzkxcGRublk4STArOGdPNDVzRjB5U2EyWVh5MExKdUxlWVdKcktQYVlQSDFHT0JuRGgwUUwyOWNuNjkway9FTFFwMjNLclprM1UraFdPVlUxb29aK1FsWWdkNytnMUNQMlErRDVPOG5od01QNkhyWmo0OUF0R1Y3TXc0c0ViYWhOZHZHKzcwSmw0WW5FbDdOMWNNdVJrZmVDcGJvcmVrbnJ6dmVpanB5TEw5S1k5d2o4VlY5ZWJZOHBWQ2tkK1ExZktua0hBWmZGSmkwMHFWdHYybFJGNUcwUXFZMnExY084VVZ3SlRtZW4xb1N4QUF3cEgyYUhvVXRuTkdpSUJmb3lQc2sxdlg2M3FOaUhPRWdLcldHdUQxQlZCcjYyaHZ6ZWxlVXFTeWxMVG1Nb2cra24zUmxKdFU2cEpUei9Ud0tCL0ZWZ1pKZlBYR04zMGdkb0FXbzE1a3RuQ3ozWkpKenM2SnY0K0F0b1gvOUQvQnBua3hsM25lL0ppWVVlN3YiLCJtYWMiOiI3YjEyMGU2M2UwNzZjMjdmMDQxNTkyYzY4MzZiYTg1Mjk1MGQ0NWY5MTM4NzI3YWIwM2RkNzQxMTY1OTY3NzEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1PbDhSMnpSOWQxZjMveVA0cmJUMXc9PSIsInZhbHVlIjoiR1R3N1NqY3hLVjdrcjZjYzJ6T1lJdEtKQkFWT3hHZmRMMys4ZnNLZXhURzNlNGZqU1MzRFNsR1ArYk15VTZxL1hPY1kzWm1sTXVpTmROU0RUUlc1VWpEbjE5dzQ3NTU0ZlU1R2VMbHJJRmlqblFGZXBpV0FGRlhjalNxZzBuVmFUNGxzdFJkd2RkbVl0UThPSktZenJJWVdDYWw4T1VvSE0xbGMzQXFLb0ZUdnd3TkJQanZXNVljN1Q5R0JzK0VwMHBIM0VURGIzaHFZRVE4d0JMWHk4YUQrNmdDNTdTTHQxT0RPa3lhU2NtNk13NnNIaW9rN0JlNzBTalE5Zi9OMGE3QWthSG9RSkg2ZE1qOEYwZUMydHBFVXB6WFhrTG1vMkh1czA0Ty85b0FJU09Fd1ZWd0twRXIrdG9KY2VBSlI5WUNHbHpPV0lOR29sdEJ2NmVIK2xlanRpeGUyUmlDcDFFSC9VQndTY1dlaldqSUZDdGhCZ09zTTF4UE9rbWFjbFZaa2d3SHgvcElvMytsSGFXY1F0WFMrSHZ2cW8vQlMzWmQ2aEc1N0QzTEw4dU01Ti9mTGVTb1M2VmE0UnNJZGI5TGhnRllEbXd5UVk1TCtoUmNMangxbjR6ZEZxS3dvVGJMUUF5UnIyNElHNFZxd0pSVWZTSGQ0dm10dWh3MzgiLCJtYWMiOiI4YTMxYWRlMmFiMTIwYTZjMDlhMWIyYzU2OWYyODU4OGZhNGMwZDM0MWMyYmYwZjhkZDNhNThlMTlmZGQxNzlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1939122770 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939122770\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1816006704 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkwwdTFaSFdRcHNNMEZEcTY5Sk1ZSmc9PSIsInZhbHVlIjoic3RLZXk4WXdjdXBiVXVGRXl3TkdLN1prRS80NG9WRFhKTDFudXZNbk1pck52TkFFZU1kVWg0clZpYXFVcG4rVmo1MzZ2N3NmKy94d0VFNHRtT2VRWVpHYVc1UkxqTElvbHBkak5yeWp3YlNwbTNBVnhYZUNndXAzMFp2akpJU05kWjlhdjVRQUJwWEsyVEVKTkUvaStseDYzcUt6OVVhSWljaWEvTU9ST0k1VWRmOUR6bklGUHl4bjRVSXh6Y2lqbVRnMWJuU2I5RFlobG5UT094ZVVmU0kwbjNFMEdlTEZPQ3BHQTBvTEZsQmZHYWlQaFEvUGswaXVvcGpEQ094MU1Wc0RMS0hZa2FRaWl6NzJKTEk3c21nQm41U3VLOFM2UWk4NjM1MjZzcFhTVGZNdDUrS2Z2MTk4ckhIWmx5Vk1TK3hNSE9KKzhFNlA4SEY3N3FEa3F5MGhoNHpYeDlvWlhOKzY3Rm9YNk1JZ1pWbEtBSDNwSnNSWEt6YTBkM2dTMjlPM2tHNWZCRGxuVDJXUTJjS1VyampPeEhaNVQ0d0VhZENtQ2txVmRjT0g0Y3p3Q0JoN01ZMy9nc2JCaFB0MDE5RkdlSVBaUFA1V2s5bnJGYW9RUFdHR0Y4c29yTWo0WjFUZ3Ara01la3EzMW9xRGVPbFA5UHVPS0ZnZTlEMGkiLCJtYWMiOiJiZDMxMjA0NTE0MGZjZDVjMmEwYzdjNDU2MGUxMzFlZjI5Y2I4MzBlMzAxM2M2MzFhOWFjZDkwZDg4ZTY0NzE3IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1raVB5V1Z2VTFXREtTeXhHMmVaYkE9PSIsInZhbHVlIjoiL0E4TWwvL3BXc3FTYUlkcTBzT21FT0VHQzlxb3FKMndOTUVBa2J5UEdSRDZCeThyRmR3ajZSb0tWTEx2V2VQS2hzWkhMeGVXT28vTzE4bVMwQnFhOFR0U3BMNnloWkpsV2VOZFZoMHRCZDg3UjYyNis0OUJoQWt6Vk5qbWJuVTFtR29jMkI4dldWWGNzYjhyN0VIQWk2R25OUk9kYWhZTnBmdHNxZTc0dzVETGpabW43WUx4SDMrQkZGbi8vVUFWS0g4WVZSdDIvN0hVZjBWeTlBTzFhQVdhOEVIVDJDT25HbHBpZU1pRUkwazRIWEpKNm43SDh2TlR5YVhmakxuYXU3WnJvc2NiWWhMSmxMSkx1SzN4Q25tNGNyQVQ4ZGVGQTFSUVFJenV6U0JWTmZXWU14TFFhekRCc3kwdGl2NHZNYmZlSjhiOXg5WHpyV0FMYldIQThIQ1lxN1JLcURPajMxZ3FqOEJJR0E2a0RnRVJicEx1OE1DNE5ETTU4R1d0L1RrNWJOUjhnMEZEMlBiVE5hM1VUZG82Q1U2bUd1QjFVVndKeW1SbmdVK2wyaHNXNDhuYnVMbml6STRGcVk4SEk5UzQ0c3BweGpNZWlTdXd5Q1R0SkJzVXhpY3B0UHBiSWZLSUViNWd6TUlHM0JhMWtVUjQ0ZVpXUmIzL0paSkwiLCJtYWMiOiIyN2ExZTBhOThlNjIwY2VkMGVkMzVlODZkMTcyODZiMmZkNmU5ZDNlNDMwNzQwZDA3ZDUzYTc5YTk5YzBlZTUzIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkwwdTFaSFdRcHNNMEZEcTY5Sk1ZSmc9PSIsInZhbHVlIjoic3RLZXk4WXdjdXBiVXVGRXl3TkdLN1prRS80NG9WRFhKTDFudXZNbk1pck52TkFFZU1kVWg0clZpYXFVcG4rVmo1MzZ2N3NmKy94d0VFNHRtT2VRWVpHYVc1UkxqTElvbHBkak5yeWp3YlNwbTNBVnhYZUNndXAzMFp2akpJU05kWjlhdjVRQUJwWEsyVEVKTkUvaStseDYzcUt6OVVhSWljaWEvTU9ST0k1VWRmOUR6bklGUHl4bjRVSXh6Y2lqbVRnMWJuU2I5RFlobG5UT094ZVVmU0kwbjNFMEdlTEZPQ3BHQTBvTEZsQmZHYWlQaFEvUGswaXVvcGpEQ094MU1Wc0RMS0hZa2FRaWl6NzJKTEk3c21nQm41U3VLOFM2UWk4NjM1MjZzcFhTVGZNdDUrS2Z2MTk4ckhIWmx5Vk1TK3hNSE9KKzhFNlA4SEY3N3FEa3F5MGhoNHpYeDlvWlhOKzY3Rm9YNk1JZ1pWbEtBSDNwSnNSWEt6YTBkM2dTMjlPM2tHNWZCRGxuVDJXUTJjS1VyampPeEhaNVQ0d0VhZENtQ2txVmRjT0g0Y3p3Q0JoN01ZMy9nc2JCaFB0MDE5RkdlSVBaUFA1V2s5bnJGYW9RUFdHR0Y4c29yTWo0WjFUZ3Ara01la3EzMW9xRGVPbFA5UHVPS0ZnZTlEMGkiLCJtYWMiOiJiZDMxMjA0NTE0MGZjZDVjMmEwYzdjNDU2MGUxMzFlZjI5Y2I4MzBlMzAxM2M2MzFhOWFjZDkwZDg4ZTY0NzE3IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1raVB5V1Z2VTFXREtTeXhHMmVaYkE9PSIsInZhbHVlIjoiL0E4TWwvL3BXc3FTYUlkcTBzT21FT0VHQzlxb3FKMndOTUVBa2J5UEdSRDZCeThyRmR3ajZSb0tWTEx2V2VQS2hzWkhMeGVXT28vTzE4bVMwQnFhOFR0U3BMNnloWkpsV2VOZFZoMHRCZDg3UjYyNis0OUJoQWt6Vk5qbWJuVTFtR29jMkI4dldWWGNzYjhyN0VIQWk2R25OUk9kYWhZTnBmdHNxZTc0dzVETGpabW43WUx4SDMrQkZGbi8vVUFWS0g4WVZSdDIvN0hVZjBWeTlBTzFhQVdhOEVIVDJDT25HbHBpZU1pRUkwazRIWEpKNm43SDh2TlR5YVhmakxuYXU3WnJvc2NiWWhMSmxMSkx1SzN4Q25tNGNyQVQ4ZGVGQTFSUVFJenV6U0JWTmZXWU14TFFhekRCc3kwdGl2NHZNYmZlSjhiOXg5WHpyV0FMYldIQThIQ1lxN1JLcURPajMxZ3FqOEJJR0E2a0RnRVJicEx1OE1DNE5ETTU4R1d0L1RrNWJOUjhnMEZEMlBiVE5hM1VUZG82Q1U2bUd1QjFVVndKeW1SbmdVK2wyaHNXNDhuYnVMbml6STRGcVk4SEk5UzQ0c3BweGpNZWlTdXd5Q1R0SkJzVXhpY3B0UHBiSWZLSUViNWd6TUlHM0JhMWtVUjQ0ZVpXUmIzL0paSkwiLCJtYWMiOiIyN2ExZTBhOThlNjIwY2VkMGVkMzVlODZkMTcyODZiMmZkNmU5ZDNlNDMwNzQwZDA3ZDUzYTc5YTk5YzBlZTUzIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816006704\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-527262771 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527262771\", {\"maxDepth\":0})</script>\n"}}