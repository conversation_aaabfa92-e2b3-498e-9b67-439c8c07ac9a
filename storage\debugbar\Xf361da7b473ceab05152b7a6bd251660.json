{"__meta": {"id": "Xf361da7b473ceab05152b7a6bd251660", "datetime": "2025-07-14 17:58:55", "utime": **********.718197, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.283291, "end": **********.718212, "duration": 0.4349207878112793, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.283291, "relative_start": 0, "end": **********.657242, "relative_end": **********.657242, "duration": 0.3739509582519531, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.657251, "relative_start": 0.3739597797393799, "end": **********.718213, "relative_end": 1.1920928955078125e-06, "duration": 0.06096220016479492, "duration_str": "60.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45988944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00285, "accumulated_duration_str": "2.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.692326, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.316}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7024782, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.316, "width_percent": 15.789}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.710319, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.105, "width_percent": 17.895}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1833222794 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1833222794\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-720080334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-720080334\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-540576392 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540576392\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-959521505 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2004 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515933613%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhxUUNHYlhjRldqREtML1pvS1VtR2c9PSIsInZhbHVlIjoiZktiRVIvZ1BsejdjWkxFazZlUE9yRE1tQXI3TDZIUWU2a3dZUDdQT1U5cnJoVXdDQS8vQ2pwZnRWZ0xaMUdBRWRrYVkyS2h1OVVlVUlqUDMrQ1ZIMC9rc0pqMjRqcnYxU2pSeURkMVhkT3hodE1INmZBTnRkRWJTZkJ3WXg1RW1UOG9rY1RVTXdqNUlHM05vdUNwUlRCVTd1UloyTEQzN1FlamtRT3hPWElSdTNybUh3T3pZeWFvY1JzdERDRG9VOXBXc0lqK3hoRWlYMDRDUTFXa3Z4cjcvNDRpQ1k0V0lLaVNURG5zVE40M0hKOWIvaXFGbzhhYlBoV0ZGelE4L3lvWk55RVZCLzVaYnF0d3JzdHlIN3R2c2Zmd09OTXc2UjRIeUEvTnJaRGdRK2xiVEhndjhBQlZuUncrY3hGQzlJTllHaGlGL3JSWXRDTVptMjlVMlRxN1VhUDZ0OWNreEkwQlVnU2xnc2NRemdmVXZSOXNsaHJyNjNTdGw4L1BVODc0UDBPWG5UaHVxR1Q1VHdQS0xMMmluTm80NkdnRDF6c3JuME9kQkZpRndYclBreHUrdkpFTGhDaElCc2F1UlFCK280YzVYbXhXVFY2N3puQmRtVVlqQUlWSkxZQlRzYzNuUEVnMlFKQWRTTXBFYWVaMXRHaGxoS3l0MGR0RWkiLCJtYWMiOiI5MmFiM2E2NGEzY2MzMjk2MDg3Yjc5NGYzOWI5ODY0OWFmYjQzMGMyM2JjMDgyMjZiODE3OWVkNWY1N2ZiZWFjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVhdmlheEU4L3pLUHNydUtMSll4TGc9PSIsInZhbHVlIjoiMFVMU2xZcUhYM2E0Z2liMy9jY2xFZS80OXRRWm5ic0Y5cnhMek5nZmVoYStGTXlWd3hxb3Q3SEhpZjVDTjNTbHN1SVM1TjhJNE05NXlJZ3N0aDFCTFZDVUVPWEhoQ1NzWnRTN3RjdUg4U3J3OTZQeklxM0hQbll1aXZVTkJTbkd5ZzdOVlgxb2pXbnNQN3c2dFFvVVN5cC9ObndFOXV3b2NtYXgwbXFZdDNPM0duZlhHVmpyZlcrVmhNMXlhSmdHSXlVOWdXYmZETlUwelRlMXhXay8xcmI3dVZCaC9hTDRzTGs1TkxqN244bFlscUhzbFVweUM5b2dYaTVkM3FzNVloekI2MnV2NW5ONTRsQTZNQm9TQklRblhORlRUUE1aWkQ3emQ4YTM0RWpsc1JIWWkzeXZ3Y3Y0K093UlgzVDJtVklISXlVKy9qeGpjZ2pDendBL1hEWXVnMzV5WEY1ZUh6cDhYQmdxRDAxU21yVHdrOElkT3ZTRGVnZVl2WTNsV1pIRndGZk1uWGN6R2s2ZTlTSkp4N1hYdDRNOWRQSzZFVC9ZUDlXa0NGUUdMV2FIeVc2WWZZamMvbjFmQ040a2s4bE91SDhGdUZuSU5wSU1DZ2I4Y2dWY296cTZ1OFUzZkVoVUZyUXdNRFVuSDlTZ2lFSTFFWndDd3JCUzJROTciLCJtYWMiOiJjZmQ4NjEwNzc2ZTMzMjEwNzgyNmYzOTY1NzI3ZmFjMjFhMmMxMDAzNTNmOWQ5ZDQ2N2VmZmZlNzQxM2E0N2Q1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959521505\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2xIzeapp2kgiC1rfaCvN6XWY8c9dmIsYBY4QcQgG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1098584552 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlPbVlPRkZyZUZ5KzBZclMzdlBuRFE9PSIsInZhbHVlIjoiSUJ4MEsvZzQxZWw1a1h0MElRVEZwUlpTTWJTbXRPY1kxUEFUSEdLanBuVXVncEtzdmtaZllZUnFOQnI1c2xjdFkvZjRsS3oxL09PMkNIY1B5Sk8yUklXc1EwYVRHUk9GR2FtSTRCQk1MNUNUOTlvNHREeTVJVWEzRlFvY1VqWHIwOWNoWWpvSmhtYTFiWi9TaEFJVjdaNWkzQlN1bzdqTXI0SmZ3eUJ0RTRGczVVVDNNbWZ6WVhkVzFrRHorWnI1eXFESUFTRXoxUHNJUThQbVF1UFUxTEkybGRXSDNLQmdkL2hJUlczbWlxVVR0WG04M01taitlekM1c3NuVW90T0RjMFNiNm5uOTRYaEhCWjBYTW1lejZwTmt2ZnY1RFgwNlhoRy9acUNIUzBBbFU3UGZ2S0k1UDFBajBJS0JEaGo3enNwdCt6ZXF0MFJYSWwzam5sNkJMYjlLY29Uc3lLVUZ2bFNram9kY3VLQ3ZhN1pKZm13bHd6N0k2L2Q0NnRweFBReW5GSVUxU1ZFVnl6UDI5eDViSUlJS3hieWp2QjJMUGNRV1pUZDdFb285YWtlVDhXb3B5OS9pOXdtSHdldUNhSE5nQXVKS0lIZGpQTzI3aG1PUFlwaU1uaDdEODczbHR2TGxLa0RlYU5JbzBzTGt2aWMvVFFKR2ZsVWt0anQiLCJtYWMiOiJjMTNkNTIxY2Y3MjcyM2M1OTk3M2JlZWJjZGExMDM2YjQ4NzQzNTQ0Mzg1YjU0MmY3MjNiMjE4ODNmZGQ4YzUwIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBCeUh4QkV2WHowenlYZ1NWcWJzS0E9PSIsInZhbHVlIjoiR2F0QVZ1dmpjRjBOMkM1M3FDMHMrOUxHSnB0WXpXQVBZOVBUOTdCSmdvZHN4TlAvMzNLV1piV014bXQ1ZVBoT0MrK29xS3dyWEVjbnJwN0lVZk9UR25udVRUSDJZUm1xSWdrYmZoMnJ1YStSWmJ1ejRZUzJoVUlXMk1kc2NySTZsYzVmY2lMdXJKdmRFV2ZPWmQzNTM1cGpzZitSNjJnQ3U2Z09PcW42aFI1NEc2ZERDMnVIUkpCWFplYnJIdWtZOW9YUjg0UkxZb1VjZ291MGcwSGthRmFickFYLzN4SVE0bG1LOFFqTzNjaENxdnozblBVU3RjQTY4SFFEU2tKYndSaHFiRW5xZmNBbG9JeTgvLzhURmtqRTNXVDFQNVhRSXpwK01BV3J5Qkg5bmM2VFlVdXdZMFVUSnY4YWthS0l4MWhPSm9SUmExVHVKOENabWlZMEZpYzRJbjlmUDJNL1gyeThPcEJKUVBTOVRhMWxrNGRaNVRZb0xZc3dqL3Z0Vk01VWV2eG5tQ1E1K2NRMDR0WE5NbFhPazg2WkRCQVBrc21FMHNIek5EVlRBUERyOWo3K0lRSHhkdm9UZmVBbzVZL0k2UXNhdlNDRTFUY1NFVVpLNGhXcEJQdWs3N3BLV0QzVFZuZjM3Zkc3L05nUk9uOFB6WnVLWDFsWEZITnciLCJtYWMiOiJlYWZjYjM4MmE2YjgyYTEwZGU3YzcwZTIzYmIyZTlhYzNmOGQzNTgyNTg1MWRlMGVkMzg0N2UzNjkxMWNhZjEyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlPbVlPRkZyZUZ5KzBZclMzdlBuRFE9PSIsInZhbHVlIjoiSUJ4MEsvZzQxZWw1a1h0MElRVEZwUlpTTWJTbXRPY1kxUEFUSEdLanBuVXVncEtzdmtaZllZUnFOQnI1c2xjdFkvZjRsS3oxL09PMkNIY1B5Sk8yUklXc1EwYVRHUk9GR2FtSTRCQk1MNUNUOTlvNHREeTVJVWEzRlFvY1VqWHIwOWNoWWpvSmhtYTFiWi9TaEFJVjdaNWkzQlN1bzdqTXI0SmZ3eUJ0RTRGczVVVDNNbWZ6WVhkVzFrRHorWnI1eXFESUFTRXoxUHNJUThQbVF1UFUxTEkybGRXSDNLQmdkL2hJUlczbWlxVVR0WG04M01taitlekM1c3NuVW90T0RjMFNiNm5uOTRYaEhCWjBYTW1lejZwTmt2ZnY1RFgwNlhoRy9acUNIUzBBbFU3UGZ2S0k1UDFBajBJS0JEaGo3enNwdCt6ZXF0MFJYSWwzam5sNkJMYjlLY29Uc3lLVUZ2bFNram9kY3VLQ3ZhN1pKZm13bHd6N0k2L2Q0NnRweFBReW5GSVUxU1ZFVnl6UDI5eDViSUlJS3hieWp2QjJMUGNRV1pUZDdFb285YWtlVDhXb3B5OS9pOXdtSHdldUNhSE5nQXVKS0lIZGpQTzI3aG1PUFlwaU1uaDdEODczbHR2TGxLa0RlYU5JbzBzTGt2aWMvVFFKR2ZsVWt0anQiLCJtYWMiOiJjMTNkNTIxY2Y3MjcyM2M1OTk3M2JlZWJjZGExMDM2YjQ4NzQzNTQ0Mzg1YjU0MmY3MjNiMjE4ODNmZGQ4YzUwIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBCeUh4QkV2WHowenlYZ1NWcWJzS0E9PSIsInZhbHVlIjoiR2F0QVZ1dmpjRjBOMkM1M3FDMHMrOUxHSnB0WXpXQVBZOVBUOTdCSmdvZHN4TlAvMzNLV1piV014bXQ1ZVBoT0MrK29xS3dyWEVjbnJwN0lVZk9UR25udVRUSDJZUm1xSWdrYmZoMnJ1YStSWmJ1ejRZUzJoVUlXMk1kc2NySTZsYzVmY2lMdXJKdmRFV2ZPWmQzNTM1cGpzZitSNjJnQ3U2Z09PcW42aFI1NEc2ZERDMnVIUkpCWFplYnJIdWtZOW9YUjg0UkxZb1VjZ291MGcwSGthRmFickFYLzN4SVE0bG1LOFFqTzNjaENxdnozblBVU3RjQTY4SFFEU2tKYndSaHFiRW5xZmNBbG9JeTgvLzhURmtqRTNXVDFQNVhRSXpwK01BV3J5Qkg5bmM2VFlVdXdZMFVUSnY4YWthS0l4MWhPSm9SUmExVHVKOENabWlZMEZpYzRJbjlmUDJNL1gyeThPcEJKUVBTOVRhMWxrNGRaNVRZb0xZc3dqL3Z0Vk01VWV2eG5tQ1E1K2NRMDR0WE5NbFhPazg2WkRCQVBrc21FMHNIek5EVlRBUERyOWo3K0lRSHhkdm9UZmVBbzVZL0k2UXNhdlNDRTFUY1NFVVpLNGhXcEJQdWs3N3BLV0QzVFZuZjM3Zkc3L05nUk9uOFB6WnVLWDFsWEZITnciLCJtYWMiOiJlYWZjYjM4MmE2YjgyYTEwZGU3YzcwZTIzYmIyZTlhYzNmOGQzNTgyNTg1MWRlMGVkMzg0N2UzNjkxMWNhZjEyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098584552\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-987060693 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987060693\", {\"maxDepth\":0})</script>\n"}}