{"__meta": {"id": "Xb7b968484dfc96c08c3fdb21e7b4fce5", "datetime": "2025-07-14 17:58:24", "utime": **********.329334, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752515903.915901, "end": **********.329347, "duration": 0.41344594955444336, "duration_str": "413ms", "measures": [{"label": "Booting", "start": 1752515903.915901, "relative_start": 0, "end": **********.273344, "relative_end": **********.273344, "duration": 0.35744309425354004, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.273382, "relative_start": 0.3574810028076172, "end": **********.329348, "relative_end": 1.1920928955078125e-06, "duration": 0.05596613883972168, "duration_str": "55.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44539248, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02054, "accumulated_duration_str": "20.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.29991, "duration": 0.02054, "duration_str": "20.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1349953674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1349953674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-556946053 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-556946053\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-679882893 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=14bfr1r%7C1752515898579%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd1c3ZIMDFOM1lxNFU3dGxBZVhhYVE9PSIsInZhbHVlIjoiQldncFN4cEJvZnY5WEtpS1FyeVM2OEtXU3NQeUtMakpIcDdKWmNYa05qVm54TXl1cTJQQlBxRVdoZlROeU1wa0U1Z1lhUDFBZjhGaVplcHhHNVo5SnE5UFZTcTNCbmtOaDV3cGNWSkhIN3NQN0k5UmlzN3UwZlFlMlNRVjhOcGRadHVWOXNEcE5zZERoZXpZdHEvKzAvaEgybkRqZUlib2RlVDNCbG5ndDlCZzZZYksrelFqTmVUUnZNTFg0dnNkbTZibUVxclZMUDlNZDQwWFJsYUVNOUlEQ2M1V2pONUhCM3dRYnp2Sk01a0trTXh2SkxqdTg1WVpiODhmRW1xZjJIWjJId0FtQ1RMbHc2UXBTcDRVSHpVQWNMNUhGcUFvSnlzbTNEMzZPamNPZlI3NjRkQnBmeGtSVmQ2aFhGTUtmQ3FEZ3VML0prczI3QjRXUzFPaHlhNTVmcGlYNElOdnZyOWdPVmFYemw0YjBVbzlOQ0F5MHJrazRTajVJOXo3Z3lyTlIydFh3MHlTNUk5M3M5UlR1T3ZHdTNYSUgyVFdZTXIzYy9wZ1NrVzc5Zk16S2MwVHZZOUw5OFJEalo1VnJIa1Y2eTg2bE9XdU5jbFdKZ3ZybmMxRFFCRmpRN01pS0hOSE8yd3ZEdkViTkQzN2x5Q0ZqNS95emVRVUxUUGYiLCJtYWMiOiIyOTU4NDc0ZjA4ODQ4Yjc5ZTBiOGY0ZTljYjRkZmQyMDczMWFlMmI4OTkxYjcwMWVjNWVjOWE0YmM5NGM4YzM4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktKWnludExCSXh2Vk1odktaZTg0Z2c9PSIsInZhbHVlIjoidmZXcjhYZzNkQkk3d2xoK21qOGtWQjlZNjdNQnFPd1ZmQTVFYTZ0NmRONVBRdWJYTW15NFdpWlViK2U4aTJKN2tsc2E0WEplUS9ldVZQWVN0UmxiczRSWW9zZ1M2TkxuZzZCNWk0a1V1R0tORnh5cVZsOXBpRjVlUVdSdVh3VGNIT1dtc2QxamNwS1RKOWlvdy95TUlxZndGNnpHSnNHMGdEUldBTFZScy9kK01aRlhscHExelRQOERtN01iWUt3ZDRrOVBsYk80dk9aT0RWVWMrdHRiWW96dFNoaG9YTStOcUM0NmhIL0xHc29NdGE3ZkN2Q0g2enc4d0FiSnU4YWp0MUNKTnAwOGJHM1dFYVBLR05zM2U4VlhMNHZIV2RNRkZmc2JVc1MyMjJPLy9aM0ZWY0RuQ2w0ODVOVitaUWl0eUowM1BBcVVXaTNSTFRmVHBMUnlSaTAwcktjWlZSTTBrUmhGcmRqM2lrcE4yc2plOFVUZHg0VjM0RDRtWDVBM0xoYnBvUCtOOXdCcHJRYTZ1UTJ2MXFJdEZ5K0FvRnVQdjJqb0R4eUVzRk84UTMrWHRXRCt1cDhYempEU01CRFlsMXViRktUVlE4cFB0dEZsOFh0aE1QTjZLczluOFlEQ0tDelJzZWRsZTIyTnV4VkxlUjZaSG5wSXgwV0FzUEgiLCJtYWMiOiJlNzRjZTFlYjk1MzUyMDIyOWEwODlkOGExOTFjYmNlZTU4Njk0ODM0NGM5Yjk5Yzg1YzVmYWIwOGYwYjAwYjM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679882893\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-370065118 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oNFTwQ1DIbj4WfsiDgxxywznpCMkbh9HCM98YLHi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370065118\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-594413681 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:58:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVaek9UOXhDMjhjY0grVlVZZnViVEE9PSIsInZhbHVlIjoidjV0RGNZSEY4YkkwZDR2UVZKcWVBNE8zeXlKaDZGMmlhV2dTRDQvbGFuNTFMUG85M2lwWjdTSU9hd0RVNlIvMEdMUW56T2pWczRQT2JFS09TM0psQ1RnQllzVGxUYjlob3Fmb1E4SE4wQjZWQW96SFl3RjNKeHJsMEp2RmhXUEl0UmNBRkJRZFdwd01rUXZ3TFcySkZ4QnhsSkZqNFgvS0dxKy9aQWREUEJZNDNPUThMWHlSNTN0bE5KdHl0NW1UMWs4NE0zU2ZqQUdCNXNjdVcreVYzUzJOeGRhU0V2emdnQjhyMktZeHhtSlh5dVVLZkRiT3ZaTC9BMEIrTzQ0dXAvWVJyTzdyeGo4T01FbVZOVkpqNkExcE83UXFldlVSeUJsSzdwTU8vd0xXanRrU1hTZ3RhVG5mcEVhOFlHNVorVnpwOFdFcGt5eHNoczZwV004eldJbUJSQUs1b3QzaGtSR2xNUWlWM0F5WVlNWWprN21lUmF5ZWl5dnFQbzg3WDRYUzF5VTcwK2ZmODhhV0dEcHpRZFppL2RiVUxSWFVnckg5dGZSUnAyOFJJWjQxWERDMmdKcmp5TXE1RUVrVzZmOUYyTFZNNkFNQm8zMzc0Ky9rVmdNRUttL3hNR280ZUd3UERnM0FTNmd0SzFnSG8xVWV6UkoyaG92Q0QyaVEiLCJtYWMiOiI5ZTUyZTlmZTA3Mzk1MTkwMzZjNjU2M2I2ZWY4Y2VmN2RmZmYyZWM5YzgzOTc5NDI4NGEzMTNjZDIzYmE5NWQxIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im85L1dXQ1NIbEtpbm56N3VTQ05JS3c9PSIsInZhbHVlIjoiaUh4eStPbHB3N3NGSUVrZWVQZElBRE80eVFSSjZWanZYcHlZdXZ3RjhvTWNrK01HMjR1SXRnVmlaT1JPUFMyMnp0RzZTWHlsbWVVazBFa0prN203QWcrUHh5R3dPWHJEODBIOUt4MUxabDIwaklyeGh5eEQ1UGRGcmkybnMyc3JUSzBpUkM4Y0IzTlB0Qm1lK1NhbEk3NXRaZkkyNEp0V2tTNlJlTmxzTWlqc3JUSURKT0I1VlNhQ1JaSlhhWjN5YmlKdHdiUGI4WFZDYWlsSC9jL2xYSCs2Q0NkdEtucUhiTFJWWDlqdGhVV21xOHEvR3FKdzRpMjVOL0xObmhDSGpIN2RNZGV2TEVwTkwycVRnZ0pEa1JRSmVHcXFqWlM5TGg0eVZEOFFYVnJmMlNBellHMTlGSmdvc0M3RE90YnNkV1M4dnlacEpvRUNON2s4bDZoVThHT2lMQUdqY2s4a0lyQzY1MTkyTU1DTFZHWGRwaVN2WkdIS0tBeHc1eVhLbXhMaDEwVjhlNis5S0hsOE5jMSt5TEczUzIrMkdhNHVsN1Q1U1lBUnI5elR0bnBjc2prNHhSMHU1b0IyNlVmN1pPcTFEN0hjRUhOZzNZV3dnSkhRekM3UEg0N1RlTVQwUnUxYXduSFRnemNkMVFpU1hYNHN0enZuek9sTU85WjAiLCJtYWMiOiJkMjIxOTQwMjM3ODljNWMxMWE5ZWE1YjVmZTU1ZjYwOWUyNDJkNGI3NWE3NjQ4NjEyODQ2ZWU5NzRkZmY2NDdiIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 19:58:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVaek9UOXhDMjhjY0grVlVZZnViVEE9PSIsInZhbHVlIjoidjV0RGNZSEY4YkkwZDR2UVZKcWVBNE8zeXlKaDZGMmlhV2dTRDQvbGFuNTFMUG85M2lwWjdTSU9hd0RVNlIvMEdMUW56T2pWczRQT2JFS09TM0psQ1RnQllzVGxUYjlob3Fmb1E4SE4wQjZWQW96SFl3RjNKeHJsMEp2RmhXUEl0UmNBRkJRZFdwd01rUXZ3TFcySkZ4QnhsSkZqNFgvS0dxKy9aQWREUEJZNDNPUThMWHlSNTN0bE5KdHl0NW1UMWs4NE0zU2ZqQUdCNXNjdVcreVYzUzJOeGRhU0V2emdnQjhyMktZeHhtSlh5dVVLZkRiT3ZaTC9BMEIrTzQ0dXAvWVJyTzdyeGo4T01FbVZOVkpqNkExcE83UXFldlVSeUJsSzdwTU8vd0xXanRrU1hTZ3RhVG5mcEVhOFlHNVorVnpwOFdFcGt5eHNoczZwV004eldJbUJSQUs1b3QzaGtSR2xNUWlWM0F5WVlNWWprN21lUmF5ZWl5dnFQbzg3WDRYUzF5VTcwK2ZmODhhV0dEcHpRZFppL2RiVUxSWFVnckg5dGZSUnAyOFJJWjQxWERDMmdKcmp5TXE1RUVrVzZmOUYyTFZNNkFNQm8zMzc0Ky9rVmdNRUttL3hNR280ZUd3UERnM0FTNmd0SzFnSG8xVWV6UkoyaG92Q0QyaVEiLCJtYWMiOiI5ZTUyZTlmZTA3Mzk1MTkwMzZjNjU2M2I2ZWY4Y2VmN2RmZmYyZWM5YzgzOTc5NDI4NGEzMTNjZDIzYmE5NWQxIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im85L1dXQ1NIbEtpbm56N3VTQ05JS3c9PSIsInZhbHVlIjoiaUh4eStPbHB3N3NGSUVrZWVQZElBRE80eVFSSjZWanZYcHlZdXZ3RjhvTWNrK01HMjR1SXRnVmlaT1JPUFMyMnp0RzZTWHlsbWVVazBFa0prN203QWcrUHh5R3dPWHJEODBIOUt4MUxabDIwaklyeGh5eEQ1UGRGcmkybnMyc3JUSzBpUkM4Y0IzTlB0Qm1lK1NhbEk3NXRaZkkyNEp0V2tTNlJlTmxzTWlqc3JUSURKT0I1VlNhQ1JaSlhhWjN5YmlKdHdiUGI4WFZDYWlsSC9jL2xYSCs2Q0NkdEtucUhiTFJWWDlqdGhVV21xOHEvR3FKdzRpMjVOL0xObmhDSGpIN2RNZGV2TEVwTkwycVRnZ0pEa1JRSmVHcXFqWlM5TGg0eVZEOFFYVnJmMlNBellHMTlGSmdvc0M3RE90YnNkV1M4dnlacEpvRUNON2s4bDZoVThHT2lMQUdqY2s4a0lyQzY1MTkyTU1DTFZHWGRwaVN2WkdIS0tBeHc1eVhLbXhMaDEwVjhlNis5S0hsOE5jMSt5TEczUzIrMkdhNHVsN1Q1U1lBUnI5elR0bnBjc2prNHhSMHU1b0IyNlVmN1pPcTFEN0hjRUhOZzNZV3dnSkhRekM3UEg0N1RlTVQwUnUxYXduSFRnemNkMVFpU1hYNHN0enZuek9sTU85WjAiLCJtYWMiOiJkMjIxOTQwMjM3ODljNWMxMWE5ZWE1YjVmZTU1ZjYwOWUyNDJkNGI3NWE3NjQ4NjEyODQ2ZWU5NzRkZmY2NDdiIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 19:58:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594413681\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-498408934 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JMirxw3F34grrh5YpRfOxIrF5gjjZt4MfD8jtQyk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498408934\", {\"maxDepth\":0})</script>\n"}}