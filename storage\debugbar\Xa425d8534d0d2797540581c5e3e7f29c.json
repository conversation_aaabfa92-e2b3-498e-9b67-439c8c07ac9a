{"__meta": {"id": "Xa425d8534d0d2797540581c5e3e7f29c", "datetime": "2025-07-14 14:39:39", "utime": **********.235295, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752503978.837954, "end": **********.235308, "duration": 0.3973538875579834, "duration_str": "397ms", "measures": [{"label": "Booting", "start": 1752503978.837954, "relative_start": 0, "end": **********.196458, "relative_end": **********.196458, "duration": 0.358504056930542, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.19647, "relative_start": 0.35851597785949707, "end": **********.23531, "relative_end": 2.1457672119140625e-06, "duration": 0.03884005546569824, "duration_str": "38.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44538912, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0017900000000000001, "accumulated_duration_str": "1.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.226683, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/journal-entry/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-775073228 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-775073228\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2123276426 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2123276426\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1563429640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1563429640\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-172221685 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752503670543%7C15%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9LNm1EaHZyajFxWUx3Y2V0YU8vRUE9PSIsInZhbHVlIjoiVUJFV2N6SkZiblQxMVBNZFN1U2RoYWxCcy9iMHRoM1pkclc4SGNFTlV5TE5YaVpjNFNhSzlqc0tmSGJnRHhhUkR5NEJoSldCSm9FbndHRGJQajk1UXloZFR0eXdBS3ZNbnBnMFVwVmdWSzJmaXBwV09FN0laQTNtVXhxRUFTWnIyQ3dMbHltVzBTTzcyaDdKTU9EN1p6M0ZZd0JJVjZnanZqRGhxZ25sMEdDSklKYktxZ2Jya0wyZFlndjcwTjk4dCtYcDExNnJ4YXMwREluNHVMLzBldmU0SlJwa0xmME9VY3JwRGxWYmNhem9ZSlZjUXJJNVQwQVZFRENMZDNiV3d0eHBQTVV3bDFhNzhNWUJ3UVZTQzNUY1RuNXZhMmx0UEYvbHZjY2kyaGdKNDNJbk9LNm9yOWk5OTBvWFJ3SW43Tm4raGxHc3diblNaVktJMldFczA0UlJTOVFIZnNLeHB2dWNtY3RhdlJYbFlyLzNkQUtzV3cwNjZxYUlLaXZYL0hock9YblFHRm5BVVlLa3RUQ0hQRVZQT0Y5Rmc4ZjN0OEkzc1hVeVNaODhtbDBXY3UyYlNIUXZOVkFWSlFXaStCL3dEMHJqamhab0lZWFJ2L1VaSzBKNDBkN1FveGNFT1hhemF5MHdvN0xjWFptTHNUbkRXTmtyWi93TUI5c0IiLCJtYWMiOiJkNjlhMWIwYmU2OWRiNTAzNjZjNzRjOWMwYzY2YTg5Y2Q1YjIyZjY3ODBmNzU5MjhlYTA3MDNmOWE5ZDI2NTdhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJwTTlrQi9oOTdxQ21BZEtMVm5RWGc9PSIsInZhbHVlIjoiZGxpdWd2RlNTeTVxREkwb2QzcWpWZWlUWmxkNjc5b2ZJY2FNN05MZ0Z6N2NHM0xYYXpvRlk4MUdSMmdaUklYaURURit5a2xGMThMVEpESHhObExyUjJkNTU0TzFnbk5HdUM3blJYa0paMkdBdHpJTzFPL3l0cnBkMkVOOXJIeUYzdFZjVlpJdWZYM0lPbDJzUC9IVjhMU0VpLzFXN3hnVHNwMTREcWxuRWFGQzMrdUJ0T200eEU1RUpjdE1FVk9PajM5amoreUM1WHNmaDU0NmQwQWhmQUpTdFBuT0F4TnRiOU02cVhpKzJZSVFTbllNL21NTTBlazQ5Rk9ia0hCWFpCMld3Z0RHMlU4M1Q0ZGFkeTJpZTgyVU5kOTExQnJRditQeldvbS9Cb1VKZklGeDVBWkd1OUlnNjhOMTZtWU9lMUtoYkpYVzlFc2l5N05xbnc2c2RNQThrNUM0TEVwVTJUeGVxUVJEdkVLZ1FCaVFaVlFFYi94K2J4MjZQQmVuK1NtT0FKSHU2NzhjRE5Yck1YcVF5WXNpM1lJNHlwVnZHOW9kWFBVcVY5TWt0MDlNKysvWEloZ3h5dG4rakJYRlNyZnNaVXZNN1lSajBHa2tLWEoyR1ZibE5jTERBNnFoSVN6U05uTSt4Z3JDczZWQUFpdU5ZeVQ0YTh4NzMxV3IiLCJtYWMiOiIxMzIwMTkzMDZlY2RmMGUwMzc2YWY1ZmM0ZTNiMDhlMGU4N2NmYmJmOTgzODNiMjdjNmM5Zjk2MDA3NzAzOWUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172221685\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-534533919 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534533919\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1493505169 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:39:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImkvZkhza1dzTTdYYVFIdkJVM05BWEE9PSIsInZhbHVlIjoiKzFacG0xVjZkZnppTFhwWkJnL2tlSG95SmxsQ2RlWEJIdnluL3d2Mi9yaUdDZ2dhSWF5ODlsTlJIUmVXYUpwRGpTMU9kODdDNFJXY2puMFJXU2VTNmw2L3M5dnAyb3JRZ0ZRVDdQWXNQRmtUWlBicEN0bFNRdGtaRmkvdzVmZ1JJYWovaU45U1lucnAvRmtTY21JTE5QNmtIYjVlU0h3WlFHL3hNNEI1T3E0SjI1OW1iZ2FsTllHdkFIZGJ2VUc3QlFFN1ZwYlFjeUp6TVFUZ3hxaVlncjBFajExeW5tWDk0Qm1BdVNsclV2cmR6QjVkTWR5SDFSZzExQ0tTbmFCb2dBQUdBYWhSL21IQkVjbko3M0kxL0pYNmgxT0wwQUYxQ3N3U0RQNnNxZTM2UHhsdjlsVWlHbkZwakIrMVpFT3Uva0tyYmhYRDFVZDJnVVJpUW5iYjFjZ1c4SmMva1hiL1NRYWwya1lnc1k2WVVWbXBhRmdnOXkvV0dSM0RodnpQZDR6a3d2emZiZ2ZRaW10U1EwQ29DalBtNGM4UzZUSGNFSmhnYXh1djBRTldaSjlmU1hGdDJ1OGFmR2VvWURYcTFTbFd4Rzhyeis5Qms0MXVKQ2gvT3NtemhDT1BtYTBYRGpSOWRxYlpYTE12Q21PVk1oSlVJcGltNTZVc3dPTDAiLCJtYWMiOiI3MzRlNzk4Nzc0ZmI0MWUzYjNlY2I2ZTk1NjY4ZGVmYWVjNWVkN2RjMWU1NmUxZTJjMjkxOWViMzQ1YzQ4YjE4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRPUDBuRmZsMzkzRVNObURBM1pXWXc9PSIsInZhbHVlIjoiS0kxK29pWUgxODZZWDFybXFoZnlpb2U5OWZidm1GYVBGTUdFbWZQMW5CUlJhZVVNc3VIeFlJb0VYYU5XM3dWc1c3L05KMWJ4cjBPV1NyRUx3bHk4dCswbWRMckY3QkRpSlFyY3FEd1k3dm51enc0K2Jha01lSldXWU1MbzNNRmNXNkpybHc4ODE3L0dFbk40aVpnc2phbnVyRGZMSjEydjJMamRpZFBGQ1RVQlJTeU15aGppUTQ1WFEvMHBzYjMvUEFNYXZ6U3YySGNvRnpYL3Qxa0h3MGNWbEZEREVKcEVSM09WaHBwL0tpN0lqYlB4OGlSVGJTUEVqSEora2pHdUJzVXVKd2k4bGt0Z3V4eHZEV3NXQ3A5blBMNkRXOTlJaThQZzVMZ2p1QW9EWUs4bitQbUZlMmxyMGl0RXFwT1VRdTdFSWo1dk5NQVNpa1FiVm9wby82U0lHZXRRNEhXOXp6N1BpMEZIdHNtdWpiVHJMUlZzVFdZRVM5enlmNy8yc296RTRmbGNkMW9OL3daK1hjUHZKQTI0bDkwWlY1a3NEMjI2V2Y5K04vbUZ0cmd3NUxlZTVwc3lPMHlVQVBrL25zMUo2Q0RoVXV6MGJybFVrTWIySEJwVTVNZ0dvTVVmcXBCMDVrbzBsN1FWQUU4cS9QMzVBYkxVZU9sUmQvWisiLCJtYWMiOiIxMWJkMDk5Mzk2NmIyZGVjMGQyOTY1ZTEyMjZlNTc4ZWIyN2JiYzA3M2JjNDk5MGE1ZmRjNDYxZWUxZWYyMTkyIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:39:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImkvZkhza1dzTTdYYVFIdkJVM05BWEE9PSIsInZhbHVlIjoiKzFacG0xVjZkZnppTFhwWkJnL2tlSG95SmxsQ2RlWEJIdnluL3d2Mi9yaUdDZ2dhSWF5ODlsTlJIUmVXYUpwRGpTMU9kODdDNFJXY2puMFJXU2VTNmw2L3M5dnAyb3JRZ0ZRVDdQWXNQRmtUWlBicEN0bFNRdGtaRmkvdzVmZ1JJYWovaU45U1lucnAvRmtTY21JTE5QNmtIYjVlU0h3WlFHL3hNNEI1T3E0SjI1OW1iZ2FsTllHdkFIZGJ2VUc3QlFFN1ZwYlFjeUp6TVFUZ3hxaVlncjBFajExeW5tWDk0Qm1BdVNsclV2cmR6QjVkTWR5SDFSZzExQ0tTbmFCb2dBQUdBYWhSL21IQkVjbko3M0kxL0pYNmgxT0wwQUYxQ3N3U0RQNnNxZTM2UHhsdjlsVWlHbkZwakIrMVpFT3Uva0tyYmhYRDFVZDJnVVJpUW5iYjFjZ1c4SmMva1hiL1NRYWwya1lnc1k2WVVWbXBhRmdnOXkvV0dSM0RodnpQZDR6a3d2emZiZ2ZRaW10U1EwQ29DalBtNGM4UzZUSGNFSmhnYXh1djBRTldaSjlmU1hGdDJ1OGFmR2VvWURYcTFTbFd4Rzhyeis5Qms0MXVKQ2gvT3NtemhDT1BtYTBYRGpSOWRxYlpYTE12Q21PVk1oSlVJcGltNTZVc3dPTDAiLCJtYWMiOiI3MzRlNzk4Nzc0ZmI0MWUzYjNlY2I2ZTk1NjY4ZGVmYWVjNWVkN2RjMWU1NmUxZTJjMjkxOWViMzQ1YzQ4YjE4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRPUDBuRmZsMzkzRVNObURBM1pXWXc9PSIsInZhbHVlIjoiS0kxK29pWUgxODZZWDFybXFoZnlpb2U5OWZidm1GYVBGTUdFbWZQMW5CUlJhZVVNc3VIeFlJb0VYYU5XM3dWc1c3L05KMWJ4cjBPV1NyRUx3bHk4dCswbWRMckY3QkRpSlFyY3FEd1k3dm51enc0K2Jha01lSldXWU1MbzNNRmNXNkpybHc4ODE3L0dFbk40aVpnc2phbnVyRGZMSjEydjJMamRpZFBGQ1RVQlJTeU15aGppUTQ1WFEvMHBzYjMvUEFNYXZ6U3YySGNvRnpYL3Qxa0h3MGNWbEZEREVKcEVSM09WaHBwL0tpN0lqYlB4OGlSVGJTUEVqSEora2pHdUJzVXVKd2k4bGt0Z3V4eHZEV3NXQ3A5blBMNkRXOTlJaThQZzVMZ2p1QW9EWUs4bitQbUZlMmxyMGl0RXFwT1VRdTdFSWo1dk5NQVNpa1FiVm9wby82U0lHZXRRNEhXOXp6N1BpMEZIdHNtdWpiVHJMUlZzVFdZRVM5enlmNy8yc296RTRmbGNkMW9OL3daK1hjUHZKQTI0bDkwWlY1a3NEMjI2V2Y5K04vbUZ0cmd3NUxlZTVwc3lPMHlVQVBrL25zMUo2Q0RoVXV6MGJybFVrTWIySEJwVTVNZ0dvTVVmcXBCMDVrbzBsN1FWQUU4cS9QMzVBYkxVZU9sUmQvWisiLCJtYWMiOiIxMWJkMDk5Mzk2NmIyZGVjMGQyOTY1ZTEyMjZlNTc4ZWIyN2JiYzA3M2JjNDk5MGE1ZmRjNDYxZWUxZWYyMTkyIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:39:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493505169\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-310285105 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/journal-entry/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310285105\", {\"maxDepth\":0})</script>\n"}}