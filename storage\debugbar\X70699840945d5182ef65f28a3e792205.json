{"__meta": {"id": "X70699840945d5182ef65f28a3e792205", "datetime": "2025-07-14 14:07:51", "utime": **********.280611, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752502070.814422, "end": **********.280625, "duration": 0.4662032127380371, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1752502070.814422, "relative_start": 0, "end": **********.216527, "relative_end": **********.216527, "duration": 0.40210509300231934, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.21654, "relative_start": 0.4021182060241699, "end": **********.280627, "relative_end": 1.9073486328125e-06, "duration": 0.0640869140625, "duration_str": "64.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45994864, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030600000000000002, "accumulated_duration_str": "3.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2559981, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.765}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.266988, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.765, "width_percent": 15.359}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.272903, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.124, "width_percent": 22.876}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT", "_previous": "array:1 [\n  \"url\" => \"http://localhost/journal-entry\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-958720237 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-958720237\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2088006184 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2088006184\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-618659499 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618659499\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/journal-entry</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; _clsk=1g7ulh6%7C1752502036210%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpKWHVVVVlmV25kRWZOb1c5ZURGOXc9PSIsInZhbHVlIjoid0M4S29pVVdid05ibHAvV2hKQWREY3lBVVZlbm5RWEFEV0YwRXJYVFBxbDlKR05sK01nQzNtZ3kzV091VFJ5TllwbHFuZTIrSnlnWkhMQTN3SUVvV25kL3ErdnlBMHQyOVlqQTd4N2hCV0dhWVhvMFZDYm1KemdQOVIvU1NFTFJrc1ZMeTZ4dmVQQkFsWEpjWWtWb29NR2kxWmdCK3RNRTVFRFM2a3djZ2tUMVFFUVd3dFBnR3BnbmV0WmVYb0VCYWUrd3NtbzQrVUozQU1qTURjYTdKV1ZJZk5ET2c4eWp0ZnNRM1VZOEtSVzQ1bkg0ME5iN3doU2dhbzhZbFpOT295V0RtenhwK0NoeTBnSHdvRlNDVS9BcVNZUENuWENHakFPaW1WUWkvS2VKSmluK1pCSFFmUk5OYXVnMlNPcTNSUXRvUGJidlpZRWpWMzZyUFAzemV1dTU1U2pwSzFTWHpzQmR2S2ZxKzdjc1BGUHlDUmx2dXFOS29XUENYd0RvY05xbjBkRGZFMUJ1OTRyaVhEN1ZzRTZ1cWpxN3pXWENFYXc5UlhIQlJVY1B4ejJ5Sm9NOEZCanNuMzdUN0pRMmdyTXZKMlJIWXNEazdtN1pSdkRyaUtoOHNlcXNDOXdBZFRaVU12WklVUG9JU1FBWlVwOHdVaytkZVhPNTk0bEciLCJtYWMiOiIxYWI3NDYwMGJiOWUwYjY2MzIzNzNlOTg1MGI5ODRiZTkyMDg3NDY4ZmFiZDBjZWE1NjBmZGQ2YTNkM2QzZGY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhYUHg1cHNYYzI3Zi9pcGNVVlJhRnc9PSIsInZhbHVlIjoiNEY0dE5oTDBWNFM0OTR3c0JRK0dXcTNxalNqRmw3RnF4SXROdkx0d2RxWW5wcFFvc25IVGVTbTRwUktpdElEOS9tcm1SenBEZVFJdWtIK1lISTFNQWQ4MGwrdHpNNENDWlV4dFZia3Z6aXRudUJPeDN4M2Z1MkQ4WUF2VC9jazNTR1lPSFdCazhQUzl3QVIxM09ucERzejNtRUpqdllFUzdkNnJBWTkrVUFmbk9mLzFHc1ZxY3FPYzBoYUdSSkcvV3pLNkdlMmJvb0lmbnBMTVVlekNsTGkxRGVKVDJUT2JFNjRiT2lvL2x2UFdFbWJpZHFIa25UeklsK2tqbEVqTXNsYWMyUVd1eU9sWW81SDBoLzlSWVV2WHJORnJUSGZFbXo4cjJYdU5TTWdKbnQ2Z0lFK3JiYk9GN3NEOENBWElxdWZ4b3F1WlBwU2NDN252cFhyeGVWTUpvK1EzOHZ3Q2lKOTdXV3I4WTJvZG5hWnBYRnRCa2FGV2ZqbWhvaVFDbmhiV0Q4VUgwV2VoV1lLWERXNitoTjkxREdsZTdhZm5LZW9ldk1leGlTUjJ3eStZVzdOaEhJYm5XOUJkUk8xWHNyVEdZSHpFS2JqQ2p0ZU5QSVFvZVhBdnFoMUZ6eUdCenF0c3ZVNkw2MkY4WDV0QklyUHhDbzNGdFN3c21UaHAiLCJtYWMiOiJmOWJlNWNjMWE0ZTE2ZjI0Yzg2ZDNkMDkwMDAyNjExN2YxZGNmODA5MWM4OTQyNmI5MjQyMzFkZDVlNWU1MjZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bJ70k443NVyqSKheEfRlumdkUBcCfA670uxRBZQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-930934319 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 14:07:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhtVmMzOVp4Q2hqd2lvd3hxa0dFa1E9PSIsInZhbHVlIjoibndDUkd3UnVZZ21HM1I0N2lDakE3eWxkMXhMRGQ0Z05DRHVxUnBYV3U0UWMwek5STDhITS9hRHE0ZnoyV0I3SFNpa3hGSklpMVcwdDZ6eGpGT0draHRtNXlLZWhDWGdSZXJqOGVxVm9PYXIrc3JqalZtWlNMVnVkOFhnc2VqOGwzRU9tRE10M0lsNHZaSWxjdkgyUEpud1UvcmVvR0RpdGM3dzZKT1hsZE5ibHJhdjUxSkM1N0ppcVVXanhiRXAzdXgvM3g4QUN4d1JvMlFIQW5wRWNyaDg5OXhsQ2hWV2xDcG83cUwxcklsTkxCWW9wNUpieGk3Z2xWQ2FuNTlNeUdnSHVDVzROOEhEQm1xNXI3blJjOE8vcWJlY2VEL3orWnJsL1d5MEV5Ym5QUnF2eDA0S05KWGpFOXBucnFYVFgzVER0aHhuWE9INlUwS2QrT25rQUc2YU5ReWVYYUhhdUljVGRnMVY2d0daYVg1QmxEU3lReFJMT3ZqeWtaSGJ5Mm0ybTh2djBZTTA4Yzhidkp4YUwvYXpwWkF5R0k5S0dBSjYyTmxiS3RCbjNvY1o5T0EwMWJHY3VDNnd4TlZ5enY1YWpiTkw4eW5SSEZ0SmV0K01wRXd0ZTgwZTlOZlVyUzJmNGZWcU96aURTdUxaSTRlL204a2duV0NwcnJ3ODkiLCJtYWMiOiJmNTFhOWZhZGNjY2MyOTEzNTcwM2E3YWVjZDA2OWVlYjViZTZkYmIyODRmNDkyZjA3OTAxODRmNzQwYTA1NmY5IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:07:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJoQVQ1SGsyWkh4OTZJNm56UVczdGc9PSIsInZhbHVlIjoiL2xPd2w0K1k4Q2h0MjhpRDl2NFF5Vnp2M2llaDJmaTVFOEVjK0JDcmVEdkg4K255ZUt6TEs0RVM1ampmdUJram1jbGs1TGVwUG5hUlppMG8wUTErRUxzYzlRNXplc2l3RnRWdmY5WUwzemRXVVlnR2xZSkdxOVRQYkRhcW9hMDY3STQxV0gyRlN3aG90eGJnYzlpMlVJNU5pU3JXR2luMUNKV3NFWFJSNVpacGFuWW9mTmNNY0cxWmJYcXdCMTEwUXVzS3lXMVJla2JWcm5jbVViSVQwT0RKNjR3MzZGNEZYRHV4L1NWQitnQVlvTnJ4S1Y1aFlaUWRaSi9hL3gxNlZOeWtrakVaWXJVVzhCTVNFNGVEOXZmQlg0V0hiRlNnRXhiMmwwWTdSOE1DcFR1ZytNWWlhYUJRL2xGMmtuS0hzZ3h3RkJJM1FoVVAxaUd4RENOaU1EVVNnWlUzTVJwV1dhcU9yT2ZFRjN0bkczUUd0K1lxTDUyend1d2NERFBDZ29XRjFDWE96cWhxUEpNd09sMlZKNUtZZVZnNzlEeEEwZDJ6UnljM0xRTkVrVnd3V0FJRVU0c1lrd1crbzFaYXk2WVgwK254dEVMWE5NdXV5NTkrV2tCVEkxcUxPMkx3U0VScW5RSjRQUnZnY3VGcnIxQkdBOUZJSXZQUURJRDMiLCJtYWMiOiI5YTYxODIzNGVjNmE2MjdkMjNiMWUyMjllYTE2MzI4ZjljNGRhODBmMTVhOTlhYTlkYzc1NTFkM2M3OTRhNTMxIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 16:07:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhtVmMzOVp4Q2hqd2lvd3hxa0dFa1E9PSIsInZhbHVlIjoibndDUkd3UnVZZ21HM1I0N2lDakE3eWxkMXhMRGQ0Z05DRHVxUnBYV3U0UWMwek5STDhITS9hRHE0ZnoyV0I3SFNpa3hGSklpMVcwdDZ6eGpGT0draHRtNXlLZWhDWGdSZXJqOGVxVm9PYXIrc3JqalZtWlNMVnVkOFhnc2VqOGwzRU9tRE10M0lsNHZaSWxjdkgyUEpud1UvcmVvR0RpdGM3dzZKT1hsZE5ibHJhdjUxSkM1N0ppcVVXanhiRXAzdXgvM3g4QUN4d1JvMlFIQW5wRWNyaDg5OXhsQ2hWV2xDcG83cUwxcklsTkxCWW9wNUpieGk3Z2xWQ2FuNTlNeUdnSHVDVzROOEhEQm1xNXI3blJjOE8vcWJlY2VEL3orWnJsL1d5MEV5Ym5QUnF2eDA0S05KWGpFOXBucnFYVFgzVER0aHhuWE9INlUwS2QrT25rQUc2YU5ReWVYYUhhdUljVGRnMVY2d0daYVg1QmxEU3lReFJMT3ZqeWtaSGJ5Mm0ybTh2djBZTTA4Yzhidkp4YUwvYXpwWkF5R0k5S0dBSjYyTmxiS3RCbjNvY1o5T0EwMWJHY3VDNnd4TlZ5enY1YWpiTkw4eW5SSEZ0SmV0K01wRXd0ZTgwZTlOZlVyUzJmNGZWcU96aURTdUxaSTRlL204a2duV0NwcnJ3ODkiLCJtYWMiOiJmNTFhOWZhZGNjY2MyOTEzNTcwM2E3YWVjZDA2OWVlYjViZTZkYmIyODRmNDkyZjA3OTAxODRmNzQwYTA1NmY5IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:07:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJoQVQ1SGsyWkh4OTZJNm56UVczdGc9PSIsInZhbHVlIjoiL2xPd2w0K1k4Q2h0MjhpRDl2NFF5Vnp2M2llaDJmaTVFOEVjK0JDcmVEdkg4K255ZUt6TEs0RVM1ampmdUJram1jbGs1TGVwUG5hUlppMG8wUTErRUxzYzlRNXplc2l3RnRWdmY5WUwzemRXVVlnR2xZSkdxOVRQYkRhcW9hMDY3STQxV0gyRlN3aG90eGJnYzlpMlVJNU5pU3JXR2luMUNKV3NFWFJSNVpacGFuWW9mTmNNY0cxWmJYcXdCMTEwUXVzS3lXMVJla2JWcm5jbVViSVQwT0RKNjR3MzZGNEZYRHV4L1NWQitnQVlvTnJ4S1Y1aFlaUWRaSi9hL3gxNlZOeWtrakVaWXJVVzhCTVNFNGVEOXZmQlg0V0hiRlNnRXhiMmwwWTdSOE1DcFR1ZytNWWlhYUJRL2xGMmtuS0hzZ3h3RkJJM1FoVVAxaUd4RENOaU1EVVNnWlUzTVJwV1dhcU9yT2ZFRjN0bkczUUd0K1lxTDUyend1d2NERFBDZ29XRjFDWE96cWhxUEpNd09sMlZKNUtZZVZnNzlEeEEwZDJ6UnljM0xRTkVrVnd3V0FJRVU0c1lrd1crbzFaYXk2WVgwK254dEVMWE5NdXV5NTkrV2tCVEkxcUxPMkx3U0VScW5RSjRQUnZnY3VGcnIxQkdBOUZJSXZQUURJRDMiLCJtYWMiOiI5YTYxODIzNGVjNmE2MjdkMjNiMWUyMjllYTE2MzI4ZjljNGRhODBmMTVhOTlhYTlkYzc1NTFkM2M3OTRhNTMxIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 16:07:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930934319\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-776633919 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9qjh6yDlaKBPukGrihwuvXjTWcKXU2EXFK3wLidT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/journal-entry</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776633919\", {\"maxDepth\":0})</script>\n"}}