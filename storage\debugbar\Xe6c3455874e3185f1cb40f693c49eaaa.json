{"__meta": {"id": "Xe6c3455874e3185f1cb40f693c49eaaa", "datetime": "2025-07-23 18:21:48", "utime": **********.062117, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753294907.628205, "end": **********.062133, "duration": 0.4339280128479004, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1753294907.628205, "relative_start": 0, "end": **********.000028, "relative_end": **********.000028, "duration": 0.3718228340148926, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.000037, "relative_start": 0.37183189392089844, "end": **********.062135, "relative_end": 1.9073486328125e-06, "duration": 0.062098026275634766, "duration_str": "62.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46536752, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025299999999999997, "accumulated_duration_str": "2.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.027175, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.589}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.037484, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.589, "width_percent": 15.415}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0430949, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.004, "width_percent": 16.996}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IjBzR010VUYwa0Y4b0xEanI3WHBha2c9PSIsInZhbHVlIjoiMUpobCtOdysxd3FWOXd0QnNrSnhvQT09IiwibWFjIjoiMGI5ZDBlMTM4YmE2ZDViN2RlN2M2ZTE3MTdiYzAzOGVlNDkwMGJmZDQxMTY2OTVkYTZhYWFmODJkMjJjNDdjYSIsInRhZyI6IiJ9\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-317208240 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-317208240\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1225342370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1225342370\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-167352329 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167352329\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1935138568 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IjBzR010VUYwa0Y4b0xEanI3WHBha2c9PSIsInZhbHVlIjoiMUpobCtOdysxd3FWOXd0QnNrSnhvQT09IiwibWFjIjoiMGI5ZDBlMTM4YmE2ZDViN2RlN2M2ZTE3MTdiYzAzOGVlNDkwMGJmZDQxMTY2OTVkYTZhYWFmODJkMjJjNDdjYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxu%7C0%7C2019; _clsk=c6ed4t%7C1753294904029%7C19%7C1%7Ca.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhONE5DZDNsUkFEWTBtT1llcGlRZUE9PSIsInZhbHVlIjoibncyMmlqcnRabmZaekhTN3R3dVNXWmVodXhtM1VoelpRMUN3RzVVRG13VjU1N1NSNjJ2TGU2emZQTHI3OFhkenRvaVZXcnN6SExLNlU4OFBmcllxczd1UExXQ3V0VEtEenBjNCtRMDVCMi9Zb2JuVHNlMHBNSmdNRG9STHpMa1pyOUc4SWIyMDZhZTkwZzhSeStDdUhrWU5aK1NxUHpOTnh5c0VsaVZRaDJtclNaZVBlVmN0cUVyWG9uWXhacy9zSThUR1R2UEFrck1KcjZiQmhpcWJSam9PajNLN0ZRbytuZG5sSzdMRXlWazc0TDJ0MU54dVhpTXZPaC8yanVRRTBPa2hWYzh1RHhvMW1Xd2Q5N3hFUkZGM29PKzZ3azl2MlpocHUxdVNEdEMvY1VBZVpMKzdLSElKcDU0Wnp0cnlDYkxBZTZURzkrS3ZsUFdUaFpKaVh6NWZuRlo4ck8vK0kya1MxT1IyWkNEMkp2N3pReDRPQyswVmJyMmRGRmVab0J2Sm16RFdTc3VyMytrSWplZVlhUU1FenAwU2pHaVFjb0pwcWlERU1udmRRWGtPVGM3WlRPT0thMjVUZkxDSzNWZ0R5NTRseVhkNmRLNXQxS2RQNVoxS3FLWW91U0RWeVZJc2dyQ2hubGpzbjRRd1lOdEpadllBbWdMNndiTjQiLCJtYWMiOiIxOGQ4NTBjNGRkZjljZTAyNDg5OTI5MWUwY2IxMzUzMGNjOTQ4MTI5Njk2NjM1ZTUwZjdjZjcwNGJiYmZkMDEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJhR2hqVWZwbE5EbzVNMTVxenhCUEE9PSIsInZhbHVlIjoicGg4cXRqT0FXd0RZU243VlVJc1o1ZUlRbFpFNHlsWEt1dVJlUWtlcmI0b1AvdWhTNDh1djNVNy9oSDJ5enFONHBNejE1blUrNUZrRUc1a3ZPM3NoYzY3Q3ZzVjVES0ZKczFMNXgrV0Y4TEdEd1V3TzVSeWdOb0JqUDJjT2RueXNPdVNaVXlmVjM0amZnelZZUDhOL1kzaE5QQkVXbjdzcG01NFVEd2w4bFN4Q2JPakxsazV6Vk1pQzByRVRHZVJnNDg2S3JrNWxwK0QvVTVxK3dQVWg3T2FrUlBORDBsajdmU2ZHMGZaNHJObGRWczc0OENDU3B1UWx3SnBHUUFLQk9PU2NqQmU3cnRQYUdWcmRybG9UOHpKa1FVcXdYZm1vUmxBbGpnTnJya21ZaFpaaWZqckp2VzNqeE13YXNoa2kzeWNrdWhSd2N5dTluREFTUTJ5ME55Qk43ZFN2ZWpuT21NZjhrRWNxZyt5TkZaU1NiK2hxMkdSOGU2WmZlOVFib3BOWFRrbjNoTWJoUUFPbXpnM2Y0Um1MMG50aENtUWVZaWpVaGJtTXM2YnFVbFpqR2gydFdBbXkxZkpJVE8xeXRac1VpNTJvcExkNlBlQlBva3JaNmo2UkFjTWY4Rmk5OVNXUUNTMHZEbWZ1ZStIdUtxOWNkMGZiZHhhTlAzekciLCJtYWMiOiJmN2FiY2QzNjM3MmMwYTI3Mzg5NzI3NjZiNTI2ZmE5NDY3NjIyYmRjMzZkYjljMDQ2MjQxYmJiMWFlOTc0NjQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935138568\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1932719768 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4Yb35JoOEY8EhmCFwQSUWUYSoDayTy82TJrjlAt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932719768\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-942864342 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 23 Jul 2025 18:21:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBtRWNlYUR4anFxbjN6a2FOclBLdWc9PSIsInZhbHVlIjoiZldjMWhUZzd5MUtYc0NDSmQ0UGpCUmN4NjAyVUhaMTN2T3RBY3R3VzBQaysxd1gyMURmRHh2bTc3SnczVjM3a1pvY3FRYmxyck5iTTkySlZ4VmdybVBpamg2cThUM2lndVJSV2IzKzNTTDVyazhCVFNRM2tXRFE4T09tUlVTSGcyOGM2Rk5zY0ZDb05kd3M2TFFoZFFFeUR6dzVJMy93Y2ZqdEI1bGgwSGhsYTRpY0dXalR6aXBRUGczV25nSnpaYTRVR0tsdWx0T1MwckV6L09ObUFFajJ4ZDZqRUZOa1pwZFlkQzRLZW5ZVHVLbUFUTjUvUDZwNHlhdjltRTRWNkNucm5ud0JTVmErb0J4TXcxK1NWc0dMUUk0Z1Vka1pOUjVwMm1KQ0lWblpwWVQ0M1FkS3I4QmR3bU5VWi9hRElQVEhRdVhxK1dUWjlmNFFGeVJjTFV0REJoZ1BNUVRWb2hYVGdkc1NENTg3ZWh4MGQ0S0ZZZ1hoS3NyNFB1b0hLZUJtTVo4NVd4QURSYzlkcFpPUkhDdjdhOExLUkc4SEQzbEFKWndvcmlpUGgzRVBRU3N0bFF2MWVmc0VxSWFTVHVWWCtqZG5EejRFL0pVTzVhKzg4TWNudUtFa1hobnI0NkdTSEg4SzF1b1JZbkh5N2FqbTllVTl5WjhFcHplRVMiLCJtYWMiOiI2ZWYyMDllOWRmMjQ5YTI5Y2FmMTYxZjI1MzIzMDQ4ODMyNzlkMjMyYjk1MWNmYzcxODY0Y2FkYWRlY2I3ZWY1IiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJsb0RoUU1lcFZndTdqK2ZpR0grNUE9PSIsInZhbHVlIjoiRlRzdFkwRkJOSFVUdFZxdC9TZnBXVW04R3NuUmxuNHZHTDgvQXg2K20wcTN6aGdQQVVXUU96cU1VMVJkN2tUczZSa0x0ZzRVWnhOcTNZc1lrREIyMmhQRkVvVG9zM3prOTEzSFo2U0dYWVFZSk04L1FhSEJHVUp0cVRWSGxMRlIyR1JnVFB3UXpiV3Z6Q2hhK2Jic3ErTWRORXdIODY2TDNiODcrYnJmQ1FibXhhc1VkQkNzUVdRL0NQVTRXVlB1SXJib0QzcERvdEgzSld6VTF1aVBmdTZBbXgwKzB4bjlweEZhVG5MblNLdjZrY28vRmVYVWhvNXZzVFpvUW1kOERMT00yVDRUTXlCVFM4cEJYelIvYmNiSkdTTzR3RVRTM0lHYzhrK3lTUHh2TzNhWVN0UTBWZUJGQXhaNExzS1llRVJaSEMzM0l2NW82aS9WeEUrK3ZmdVBJRzZkM3BQcWd4SHJNczlqUHRTS0U2cWdtb2NsZUFQdHdaVXMybGhyRXVrYkk2MmtTNDJTQUZtajVVcmpvQ1ZwQmU3YnNMOWVhSnpyWEo4bnByTGFKaEtIK3FVUFZvcTZSaGZ6MVNQcXBlbVdQNzRZMEtJbnJ3MldpRDJKWTJScmVOL0RTbDBQTFYzK1ZObGtLRnhSU0EyQU1HZFREb1FrR1ZWdzFkQUMiLCJtYWMiOiI3YTAzMTA2ZTgxYzE2M2M5NTY4MTcyMjM0NjIzYjFmN2ZlMmUwYjNkMjkzZjZjYjQyNTk3NTQwZTI2OGRhNjFiIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 20:21:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBtRWNlYUR4anFxbjN6a2FOclBLdWc9PSIsInZhbHVlIjoiZldjMWhUZzd5MUtYc0NDSmQ0UGpCUmN4NjAyVUhaMTN2T3RBY3R3VzBQaysxd1gyMURmRHh2bTc3SnczVjM3a1pvY3FRYmxyck5iTTkySlZ4VmdybVBpamg2cThUM2lndVJSV2IzKzNTTDVyazhCVFNRM2tXRFE4T09tUlVTSGcyOGM2Rk5zY0ZDb05kd3M2TFFoZFFFeUR6dzVJMy93Y2ZqdEI1bGgwSGhsYTRpY0dXalR6aXBRUGczV25nSnpaYTRVR0tsdWx0T1MwckV6L09ObUFFajJ4ZDZqRUZOa1pwZFlkQzRLZW5ZVHVLbUFUTjUvUDZwNHlhdjltRTRWNkNucm5ud0JTVmErb0J4TXcxK1NWc0dMUUk0Z1Vka1pOUjVwMm1KQ0lWblpwWVQ0M1FkS3I4QmR3bU5VWi9hRElQVEhRdVhxK1dUWjlmNFFGeVJjTFV0REJoZ1BNUVRWb2hYVGdkc1NENTg3ZWh4MGQ0S0ZZZ1hoS3NyNFB1b0hLZUJtTVo4NVd4QURSYzlkcFpPUkhDdjdhOExLUkc4SEQzbEFKWndvcmlpUGgzRVBRU3N0bFF2MWVmc0VxSWFTVHVWWCtqZG5EejRFL0pVTzVhKzg4TWNudUtFa1hobnI0NkdTSEg4SzF1b1JZbkh5N2FqbTllVTl5WjhFcHplRVMiLCJtYWMiOiI2ZWYyMDllOWRmMjQ5YTI5Y2FmMTYxZjI1MzIzMDQ4ODMyNzlkMjMyYjk1MWNmYzcxODY0Y2FkYWRlY2I3ZWY1IiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJsb0RoUU1lcFZndTdqK2ZpR0grNUE9PSIsInZhbHVlIjoiRlRzdFkwRkJOSFVUdFZxdC9TZnBXVW04R3NuUmxuNHZHTDgvQXg2K20wcTN6aGdQQVVXUU96cU1VMVJkN2tUczZSa0x0ZzRVWnhOcTNZc1lrREIyMmhQRkVvVG9zM3prOTEzSFo2U0dYWVFZSk04L1FhSEJHVUp0cVRWSGxMRlIyR1JnVFB3UXpiV3Z6Q2hhK2Jic3ErTWRORXdIODY2TDNiODcrYnJmQ1FibXhhc1VkQkNzUVdRL0NQVTRXVlB1SXJib0QzcERvdEgzSld6VTF1aVBmdTZBbXgwKzB4bjlweEZhVG5MblNLdjZrY28vRmVYVWhvNXZzVFpvUW1kOERMT00yVDRUTXlCVFM4cEJYelIvYmNiSkdTTzR3RVRTM0lHYzhrK3lTUHh2TzNhWVN0UTBWZUJGQXhaNExzS1llRVJaSEMzM0l2NW82aS9WeEUrK3ZmdVBJRzZkM3BQcWd4SHJNczlqUHRTS0U2cWdtb2NsZUFQdHdaVXMybGhyRXVrYkk2MmtTNDJTQUZtajVVcmpvQ1ZwQmU3YnNMOWVhSnpyWEo4bnByTGFKaEtIK3FVUFZvcTZSaGZ6MVNQcXBlbVdQNzRZMEtJbnJ3MldpRDJKWTJScmVOL0RTbDBQTFYzK1ZObGtLRnhSU0EyQU1HZFREb1FrR1ZWdzFkQUMiLCJtYWMiOiI3YTAzMTA2ZTgxYzE2M2M5NTY4MTcyMjM0NjIzYjFmN2ZlMmUwYjNkMjkzZjZjYjQyNTk3NTQwZTI2OGRhNjFiIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 20:21:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942864342\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-673898522 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wuAJpHBiObqcaROe5DgKzM2sXxqVGGtsoWumNBKB</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IjBzR010VUYwa0Y4b0xEanI3WHBha2c9PSIsInZhbHVlIjoiMUpobCtOdysxd3FWOXd0QnNrSnhvQT09IiwibWFjIjoiMGI5ZDBlMTM4YmE2ZDViN2RlN2M2ZTE3MTdiYzAzOGVlNDkwMGJmZDQxMTY2OTVkYTZhYWFmODJkMjJjNDdjYSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673898522\", {\"maxDepth\":0})</script>\n"}}